# 🎓 AiDex 教育入学报名系统 - 微服务版

<div align="center">
  <h3>基于Spring Cloud Alibaba微服务架构的教育入学报名管理系统</h3>
  <p>
    <img src="https://img.shields.io/badge/Spring%20Boot-3.4.1-brightgreen.svg" alt="Spring Boot">
    <img src="https://img.shields.io/badge/Spring%20Cloud-2023.0.3-blue.svg" alt="Spring Cloud">
    <img src="https://img.shields.io/badge/Spring%20Cloud%20Alibaba-2023.0.1.2-orange.svg" alt="Spring Cloud Alibaba">
    <img src="https://img.shields.io/badge/Nacos-2.4.3-red.svg" alt="Nacos">
    <img src="https://img.shields.io/badge/JDK-17+-green.svg" alt="JDK">
  </p>
</div>

## 🎉 微服务架构改造完成

本项目已成功从单体架构改造为**Spring Cloud Alibaba微服务架构**，具备以下特性：

### ✨ 架构特点
- 🚀 **微服务架构**: 基于Spring Cloud Alibaba 2023.0.1.2
- 🌐 **服务网关**: Spring Cloud Gateway统一入口
- 🔍 **服务发现**: Nacos注册中心
- ⚙️ **配置管理**: Nacos配置中心
- 🛡️ **熔断限流**: Sentinel流量防护
- 🔄 **分布式事务**: Seata事务管理
- 📊 **链路追踪**: 支持SkyWalking集成
- 🐳 **容器化部署**: Docker & Kubernetes支持

## 🏗️ 微服务架构

### 服务拆分
| 服务名称 | 端口 | 描述 | 技术栈 |
|---------|------|------|--------|
| aidex-gateway | 8080 | API网关 | Spring Cloud Gateway |
| aidex-auth | 9200 | 认证服务 | Spring Security + JWT |
| aidex-system | 9202 | 系统服务 | 用户管理、权限控制 |
| aidex-business | 9201 | 业务服务 | 报名业务核心逻辑 |
| aidex-cms | 9203 | 内容管理 | 文章、资讯管理 |
| aidex-mobile | 9204 | 移动端服务 | 移动端API |
| aidex-front | 9205 | 前台服务 | 前台页面服务 |

### 基础设施
- **Nacos**: 服务注册发现 + 配置中心 (8848)
- **Sentinel**: 流量防护 + 熔断降级 (8858)
- **Seata**: 分布式事务管理 (8091)
- **MySQL**: 主数据库 (3306)
- **Redis**: 缓存服务 (6379)

## 🚀 快速启动

### 环境要求
- JDK 17+
- Maven 3.6+
- Docker & Docker Compose
- 8GB+ 内存

### 一键启动
```bash
# 1. 启动基础设施
docker-compose up -d

# 2. 启动所有微服务
./microservice-start.sh

# 3. 停止所有服务
./microservice-stop.sh
```

### 访问地址
- **API网关**: http://localhost:8080
- **Nacos控制台**: http://localhost:8848/nacos (nacos/nacos)
- **Sentinel控制台**: http://localhost:8858 (sentinel/sentinel)

## 📁 项目结构

```
edu-enrollment/
├── aidex-gateway/          # API网关服务
├── aidex-auth/             # 认证授权服务
├── aidex-system/           # 系统管理服务
├── aidex-business/         # 业务核心服务
├── aidex-cms/              # 内容管理服务
├── aidex-mobile/           # 移动端服务
├── aidex-front/            # 前台服务
├── aidex-common/           # 公共模块
├── aidex-framework/        # 框架核心
├── docker-compose.yml      # 基础设施编排
├── microservice-start.sh   # 启动脚本
├── microservice-stop.sh    # 停止脚本
└── MICROSERVICE_GUIDE.md   # 详细部署指南
```

## 🔧 技术栈

### 后端技术
- **基础框架**: Spring Boot 3.4.1
- **微服务**: Spring Cloud 2023.0.3
- **微服务治理**: Spring Cloud Alibaba 2023.0.1.2
- **服务注册**: Nacos Discovery
- **配置中心**: Nacos Config
- **服务网关**: Spring Cloud Gateway
- **服务调用**: OpenFeign
- **负载均衡**: Spring Cloud LoadBalancer
- **熔断器**: Sentinel
- **分布式事务**: Seata
- **数据库**: MySQL 8.0
- **缓存**: Redis 7.0
- **ORM框架**: MyBatis
- **连接池**: HikariCP
- **安全框架**: Spring Security
- **权限认证**: JWT

### 运维技术
- **容器化**: Docker
- **编排工具**: Docker Compose
- **监控**: Spring Boot Actuator
- **日志**: Logback
- **构建工具**: Maven

## 🎯 核心功能

### 业务功能
- 📝 **报名管理**: 学生报名、审核、录取
- 👥 **用户管理**: 用户注册、登录、权限管理
- 🏫 **学校管理**: 学校信息、招生计划
- 📊 **数据统计**: 报名数据分析、统计报表
- 📱 **移动端**: 微信小程序、H5页面
- 📰 **内容管理**: 新闻公告、政策发布

### 系统功能
- 🔐 **认证授权**: JWT令牌、角色权限
- 🌐 **API网关**: 统一入口、路由转发
- 📈 **监控告警**: 服务监控、性能指标
- 🔄 **配置管理**: 动态配置、热更新
- 🛡️ **流量防护**: 限流、熔断、降级
- 📋 **链路追踪**: 分布式链路跟踪

## 📖 使用指南

### 开发环境搭建
1. 克隆项目到本地
2. 安装JDK 17+、Maven 3.6+、Docker
3. 启动基础设施：`docker-compose up -d`
4. 编译项目：`mvn clean package -DskipTests`
5. 启动服务：`./microservice-start.sh`

### 生产环境部署
详细部署指南请参考：[MICROSERVICE_GUIDE.md](MICROSERVICE_GUIDE.md)

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

如有问题或建议，请通过以下方式联系：
- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 文档: [项目文档](https://docs.aidex.com)

---

<div align="center">
  <p>⭐ 如果这个项目对你有帮助，请给我们一个星标！</p>
  <p>Made with ❤️ by AiDex Team</p>
</div>
