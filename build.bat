@echo off
:: ��ȡ��ǰʱ����Ϊ�汾��
for /f "tokens=1-6 delims=/: " %%a in ("%date% %time%") do (
    set VERSION=%%a%%b%%c%%d%%e%%f
)
:: ��һ����������Ŀ
echo ��ʼ������Ŀ...
call mvn clean package
echo ��Ŀ�������...
:: �ڶ�������������
echo ��ʼ����Docker����...
cd .\aidex-admin
docker build --build-arg TASK_STATE=false -t gsedu-rxbm:%VERSION% --platform linux/amd64 .
docker build --build-arg TASK_STATE=false -t gsedu-rxbm:latest --platform linux/amd64 .
echo Docker�����������...
:: ���������ϴ�����˽�о���ֿ�
echo ��ʼ�ϴ�����...
docker tag gsedu-rxbm:%VERSION% 10.185.8.57:5000/gsedu-rxbm:%VERSION%
docker tag gsedu-rxbm:latest 10.185.8.57:5000/gsedu-rxbm:latest
docker push 10.185.8.57:5000/gsedu-rxbm:%VERSION%
docker push 10.185.8.57:5000/gsedu-rxbm:latest
echo �ϴ��������...
:: ���Ĳ�������
echo ��ʼ����...
call mvn clean
docker rmi 10.185.8.57:5000/gsedu-rxbm:%VERSION%
docker rmi gsedu-rxbm:%VERSION%
docker rmi 10.185.8.57:5000/gsedu-rxbm:latest
docker rmi gsedu-rxbm:latest
echo �������...