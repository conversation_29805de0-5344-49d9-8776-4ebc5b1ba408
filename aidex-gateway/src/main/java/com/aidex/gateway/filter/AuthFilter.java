package com.aidex.gateway.filter;

import com.alibaba.fastjson2.JSON;
import com.aidex.common.constant.Constants;
import com.aidex.common.constant.HttpStatus;
import com.aidex.common.core.domain.AjaxResult;
import com.aidex.common.utils.StringUtils;
import com.aidex.gateway.config.properties.IgnoreWhiteProperties;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 网关鉴权
 * 
 * <AUTHOR>
 */
@Component
public class AuthFilter implements GlobalFilter, Ordered
{
    private static final Logger log = LoggerFactory.getLogger(AuthFilter.class);

    // 排除过滤的 uri 地址，nacos自行添加
    @Autowired
    private IgnoreWhiteProperties ignoreWhite;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain)
    {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpRequest.Builder mutate = request.mutate();

        String url = request.getURI().getPath();
        // 跳过不需要验证的路径
        if (StringUtils.matches(url, ignoreWhite.getWhites()))
        {
            return chain.filter(exchange);
        }
        String token = getToken(request);
        if (StringUtils.isEmpty(token))
        {
            return unauthorizedResponse(exchange, "令牌不能为空");
        }
        Claims claims = parseToken(token);
        if (claims == null)
        {
            return unauthorizedResponse(exchange, "令牌已过期或验证不正确！");
        }
        String userkey = getUserKey(claims);
        String userid = getUserId(claims);
        String username = getUsername(claims);
        if (StringUtils.isEmpty(userkey) || StringUtils.isEmpty(userid) || StringUtils.isEmpty(username))
        {
            return unauthorizedResponse(exchange, "令牌验证失败");
        }

        // 设置用户信息到请求
        addHeader(mutate, Constants.CURRENT_ID, userid);
        addHeader(mutate, Constants.CURRENT_USERNAME, username);
        // 内部请求来源参数清除
        removeHeader(mutate, Constants.FROM_SOURCE);
        return chain.filter(exchange.mutate().request(mutate.build()).build());
    }

    private void addHeader(ServerHttpRequest.Builder mutate, String name, Object value)
    {
        if (value == null)
        {
            return;
        }
        String valueStr = value.toString();
        String valueEncode = StringUtils.isNotEmpty(valueStr) ? valueStr : "";
        mutate.header(name, valueEncode);
    }

    private void removeHeader(ServerHttpRequest.Builder mutate, String name)
    {
        mutate.headers(httpHeaders -> httpHeaders.remove(name)).build();
    }

    private Mono<Void> unauthorizedResponse(ServerWebExchange exchange, String msg)
    {
        log.error("[鉴权异常处理]请求路径:{}", exchange.getRequest().getPath());
        ServerHttpResponse response = exchange.getResponse();
        response.getHeaders().set("Content-Type", "application/json; charset=UTF-8");
        AjaxResult result = AjaxResult.error(HttpStatus.UNAUTHORIZED, msg);
        DataBuffer dataBuffer = response.bufferFactory().wrap(JSON.toJSONString(result).getBytes());
        return response.writeWith(Mono.just(dataBuffer));
    }

    /**
     * 获取缓存key
     */
    private String getUserKey(Claims claims)
    {
        return getValue(claims, Constants.USER_KEY);
    }

    /**
     * 获取用户名
     */
    private String getUsername(Claims claims)
    {
        return getValue(claims, Constants.DETAILS_USERNAME);
    }

    /**
     * 获取用户ID
     */
    private String getUserId(Claims claims)
    {
        return getValue(claims, Constants.DETAILS_USER_ID);
    }

    /**
     * 获取请求token
     */
    private String getToken(ServerHttpRequest request)
    {
        String token = request.getHeaders().getFirst(Constants.TOKEN);
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(Constants.TOKEN_PREFIX))
        {
            token = token.replaceFirst(Constants.TOKEN_PREFIX, StringUtils.EMPTY);
        }
        return token;
    }

    /**
     * 根据身份信息获取键值
     */
    private String getValue(Claims claims, String key)
    {
        return StringUtils.convert(claims.get(key));
    }

    /**
     * 解析token
     */
    private Claims parseToken(String token)
    {
        try
        {
            return Jwts.parser().setSigningKey(Constants.SECRET).parseClaimsJws(token).getBody();
        }
        catch (Exception e)
        {
            log.error("解析token异常", e);
            return null;
        }
    }

    @Override
    public int getOrder()
    {
        return -200;
    }
}
