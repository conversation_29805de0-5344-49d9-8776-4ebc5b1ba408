# Tomcat
server:
  port: 8080

# Spring
spring:
  application:
    # 应用名称
    name: aidex-gateway
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
    gateway:
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      routes:
        # 认证中心
        - id: aidex-auth
          uri: lb://aidex-auth
          predicates:
            - Path=/auth/**
          filters:
            - StripPrefix=1
        # 系统模块
        - id: aidex-system
          uri: lb://aidex-system
          predicates:
            - Path=/system/**
          filters:
            - StripPrefix=1
        # 业务模块
        - id: aidex-business
          uri: lb://aidex-business
          predicates:
            - Path=/business/**
          filters:
            - StripPrefix=1
        # CMS模块
        - id: aidex-cms
          uri: lb://aidex-cms
          predicates:
            - Path=/cms/**
          filters:
            - StripPrefix=1
        # 移动端模块
        - id: aidex-mobile
          uri: lb://aidex-mobile
          predicates:
            - Path=/mobile/**
          filters:
            - StripPrefix=1
        # 前台模块
        - id: aidex-front
          uri: lb://aidex-front
          predicates:
            - Path=/front/**
          filters:
            - StripPrefix=1

# 安全配置
security:
  # 验证码
  captcha:
    enabled: true
    type: math
  # 防止XSS攻击
  xss:
    enabled: true
    excludeUrls:
      - /system/notice
  # 不校验白名单
  ignore:
    whites:
      - /auth/logout
      - /auth/login
      - /auth/register
      - /*/v2/api-docs
      - /csrf
