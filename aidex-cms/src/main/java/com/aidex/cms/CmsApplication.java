package com.aidex.cms;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * CMS服务启动程序
 * 
 * <AUTHOR>
 */
@EnableDiscoveryClient
@SpringBootApplication
public class CmsApplication
{
    private static final Logger logger = LoggerFactory.getLogger(CmsApplication.class);

    public static void main(String[] args)
    {
        SpringApplication.run(CmsApplication.class, args);
        logger.info("(♥◠‿◠)ﾉﾞ  CMS服务启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
