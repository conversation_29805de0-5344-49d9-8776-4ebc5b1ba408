version: '3.8'

services:
  # Nacos 服务注册与配置中心
  nacos:
    image: nacos/nacos-server:v2.4.3
    container_name: nacos-server
    environment:
      - MODE=standalone
      - SPRING_DATASOURCE_PLATFORM=mysql
      - MYSQL_SERVICE_HOST=mysql
      - MYSQL_SERVICE_DB_NAME=nacos_config
      - MYSQL_SERVICE_PORT=3306
      - MYSQL_SERVICE_USER=root
      - MYSQL_SERVICE_PASSWORD=123456
      - MYSQL_SERVICE_DB_PARAM=characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
    ports:
      - "8848:8848"
      - "9848:9848"
    depends_on:
      - mysql
    restart: always
    networks:
      - aidex-network

  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: mysql-server
    environment:
      - MYSQL_ROOT_PASSWORD=123456
      - MYSQL_DATABASE=nacos_config
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
      - ./sql/nacos-mysql.sql:/docker-entrypoint-initdb.d/nacos-mysql.sql
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    networks:
      - aidex-network

  # Redis 缓存
  redis:
    image: redis:7.0-alpine
    container_name: redis-server
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes --requirepass "123456"
    restart: always
    networks:
      - aidex-network

  # Sentinel Dashboard
  sentinel:
    image: bladex/sentinel-dashboard:1.8.8
    container_name: sentinel-dashboard
    ports:
      - "8858:8858"
    environment:
      - JAVA_OPTS=-Dserver.port=8858 -Dcsp.sentinel.dashboard.server=localhost:8858 -Dproject.name=sentinel-dashboard
    restart: always
    networks:
      - aidex-network

  # Seata Server
  seata:
    image: seataio/seata-server:2.1.0
    container_name: seata-server
    ports:
      - "8091:8091"
      - "7091:7091"
    environment:
      - SEATA_PORT=8091
      - SEATA_CONFIG_NAME=file:/root/seata-config/registry
      - SEATA_IP=seata
    volumes:
      - ./seata-config:/root/seata-config
    depends_on:
      - nacos
      - mysql
    restart: always
    networks:
      - aidex-network

volumes:
  mysql-data:
  redis-data:

networks:
  aidex-network:
    driver: bridge
