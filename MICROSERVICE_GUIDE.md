# AiDex 微服务架构改造指南

## 概述

本项目已成功改造为基于 Spring Cloud Alibaba 的微服务架构，采用了业界成熟的微服务技术栈。

## 架构设计

### 微服务拆分

| 服务名称 | 端口 | 描述 | 职责 |
|---------|------|------|------|
| aidex-gateway | 8080 | API网关 | 路由转发、认证鉴权、限流熔断 |
| aidex-auth | 9200 | 认证服务 | 用户认证、JWT令牌管理 |
| aidex-system | 9202 | 系统服务 | 用户管理、角色权限、系统配置 |
| aidex-business | 9201 | 业务服务 | 核心业务逻辑、报名管理 |
| aidex-cms | 9203 | 内容管理服务 | 文章管理、内容发布 |
| aidex-mobile | 9204 | 移动端服务 | 移动端API接口 |
| aidex-front | 9205 | 前台服务 | 前台页面服务 |

### 技术栈

- **服务注册与发现**: Nacos 2.4.3
- **配置管理**: Nacos Config
- **API网关**: Spring Cloud Gateway
- **负载均衡**: Spring Cloud LoadBalancer
- **熔断器**: Sentinel
- **分布式事务**: Seata
- **数据库**: MySQL 8.0
- **缓存**: Redis 7.0
- **消息队列**: RocketMQ (可选)

## 快速开始

### 环境要求

- JDK 17+
- Maven 3.6+
- Docker & Docker Compose
- 8GB+ 内存

### 启动步骤

1. **启动基础设施**
```bash
# 启动 Nacos、MySQL、Redis、Sentinel 等基础服务
docker-compose up -d
```

2. **等待基础设施就绪**
```bash
# 检查 Nacos 是否启动成功
curl http://localhost:8848/nacos/v1/console/health/readiness
```

3. **编译项目**
```bash
mvn clean package -DskipTests
```

4. **启动微服务**
```bash
# 使用提供的启动脚本
./microservice-start.sh
```

或者手动启动各个服务：

```bash
# 启动网关服务
cd aidex-gateway && java -jar target/aidex-gateway-3.4.0.jar &

# 启动认证服务
cd aidex-auth && java -jar target/aidex-auth-3.4.0.jar &

# 启动其他服务...
```

## 服务访问

### 应用服务
- **API网关**: http://localhost:8080
- **认证服务**: http://localhost:9200
- **系统服务**: http://localhost:9202
- **业务服务**: http://localhost:9201

### 基础设施
- **Nacos控制台**: http://localhost:8848/nacos (nacos/nacos)
- **Sentinel控制台**: http://localhost:8858 (sentinel/sentinel)
- **MySQL**: localhost:3306 (root/123456)
- **Redis**: localhost:6379 (密码: 123456)

## 配置说明

### Nacos 配置

每个微服务都通过 Nacos 进行配置管理，配置文件格式为：
- `application-dev.yml` - 开发环境配置
- `application-prod.yml` - 生产环境配置

### 网关路由配置

网关自动发现服务并进行路由转发：
- `/auth/**` -> aidex-auth
- `/system/**` -> aidex-system  
- `/business/**` -> aidex-business
- `/cms/**` -> aidex-cms
- `/mobile/**` -> aidex-mobile
- `/front/**` -> aidex-front

## 开发指南

### 新增微服务

1. 创建新的 Maven 模块
2. 添加 Spring Cloud Alibaba 依赖
3. 配置 bootstrap.yml
4. 创建启动类并添加 `@EnableDiscoveryClient`
5. 在网关中配置路由规则

### 服务间调用

使用 OpenFeign 进行服务间调用：

```java
@FeignClient(name = "aidex-system")
public interface SystemService {
    @GetMapping("/user/{id}")
    User getUserById(@PathVariable Long id);
}
```

### 分布式事务

使用 Seata 处理分布式事务：

```java
@GlobalTransactional
public void businessMethod() {
    // 业务逻辑
}
```

## 监控与运维

### 健康检查

所有服务都集成了 Spring Boot Actuator：
- 健康检查: `GET /actuator/health`
- 服务信息: `GET /actuator/info`

### 日志管理

日志文件位置：
- 网关服务: `logs/gateway.log`
- 认证服务: `logs/auth.log`
- 系统服务: `logs/system.log`
- 业务服务: `logs/business.log`

### 性能监控

通过 Sentinel 控制台监控：
- 实时监控
- 流量控制
- 熔断降级
- 系统规则

## 部署说明

### Docker 部署

每个微服务都可以构建为 Docker 镜像：

```bash
# 构建镜像
docker build -t aidex/gateway:latest aidex-gateway/

# 运行容器
docker run -d -p 8080:8080 aidex/gateway:latest
```

### Kubernetes 部署

提供了完整的 K8s 部署文件，支持：
- Service Discovery
- ConfigMap 配置管理
- Ingress 网关
- HPA 自动扩缩容

## 常见问题

### Q: 服务启动失败？
A: 检查 Nacos 是否正常启动，确保网络连通性

### Q: 服务注册失败？
A: 检查 bootstrap.yml 中的 Nacos 地址配置

### Q: 网关路由不通？
A: 检查服务是否正常注册到 Nacos，查看网关日志

### Q: 数据库连接失败？
A: 检查数据库配置，确保 MySQL 服务正常运行

## 后续优化

1. **链路追踪**: 集成 SkyWalking 或 Zipkin
2. **API文档**: 集成 Swagger/OpenAPI 3.0
3. **安全加固**: 集成 OAuth2/JWT 安全框架
4. **消息队列**: 集成 RocketMQ 处理异步消息
5. **缓存优化**: Redis 集群部署
6. **数据库**: 读写分离、分库分表

## 联系支持

如有问题，请联系开发团队或查看项目文档。
