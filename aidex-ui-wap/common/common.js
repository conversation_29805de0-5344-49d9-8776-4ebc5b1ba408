/**
 * 通用js方法封装处理
 * Copyright (c) 2019 aidex
 */

export function replaceAll (text,stringToFind,stringToReplace) {
	   if ( stringToFind == stringToReplace) return this;
	          var temp = text;
	          var index = temp.indexOf(stringToFind);
	          while (index != -1) {
	              temp = temp.replace(stringToFind, stringToReplace);
	              index = temp.indexOf(stringToFind);
	          }
	return temp;
}

// 日期格式化
export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    } else if (typeof time === 'string') {
      time = time.replace(new RegExp(/-/gm), '/').replace('T', ' ').replace(new RegExp(/\.[\d]{3}/gm), '');
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

/**
 * 随机生成指定长度的字符串
 * @param length length
 * @returns {string} 结果
 */
export function generateRandomString(length = 128) {
	if (length === undefined || length === null || length <= 0) {
		length = 128;
	}
	// 定义可能的字符，包括大小写字母和数字
	const baseCharacters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
	// 对于中间字符，额外添加"/"和"+"，但不包括"="
	const middleCharacters = baseCharacters + '/+';
	let result = '';
	// 生成首字符，确保不是 "/", "+", "="
	result += baseCharacters.charAt(Math.floor(Math.random() * baseCharacters.length));
	// 生成中间字符，可以是除"="之外的任何字符
	for (let i = 1; i < length - 1; i++) {
		result += middleCharacters.charAt(Math.floor(Math.random() * middleCharacters.length));
	}
	// 确保最后一个字符是 "="
	result += '=';
	return result;
}

