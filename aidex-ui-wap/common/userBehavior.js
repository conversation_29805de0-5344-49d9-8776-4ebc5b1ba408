import store from '@/store';

const baseURL = 'https://www.gs.smartedu.cn/wfw';
const url = baseURL +'/gansuedu/addLog';
const appid = "29mc8uaqglfcclpz";
const userid = "M000000390";

/**
 * 封装用户行为请求
 * @param {string} dataName - 数据标识
 */
const addOperaLog = (dataName) => {
  addLog(dataName, '教育入学');
}

/**
 * 封装用户行为请求
 * @param {string} dataName - 数据标识
 */
const addElementaryLog = (dataName) => {
  addLog(dataName, '教育入学-小学入学报名');
}

/**
 * 封装用户行为请求
 * @param {string} dataName - 数据标识
 */
const addJuniorHighLog = (dataName) => {
  addLog(dataName, '教育入学-小升初报名');
}

/**
 * 封装行为请求
 * @param {string} dataName - 数据标识
 * @param {string} dataType - 数据类型
 */
const addLog = (dataName, dataType) => {
  sendBehaviorLog(dataName, dataType, userid, appid);
}

/**
 * 封装行为请求
 * @param {string} dataName - 数据标识
 * @param {string} dataType - 数据类型
 * @param {string} userid - 用户ID
 * @param {string} appid - 应用ID
 */
const sendBehaviorLog = (dataName, dataType, userid, appid) => {
  if (!dataName || !dataType || !userid || !appid) {
    return;
  }
  // 使用微任务队列异步执行
  Promise.resolve().then(() => {
    const xhr = new XMLHttpRequest();
    const params = {
      dataName: dataName,
      dataType: dataType,
      userid: userid,
      appid: appid,
    }
    const query = new URLSearchParams(params).toString();
    xhr.open('GET', `${url}?${query}`, true);
    xhr.timeout = 5000;
    xhr.send();
  }).catch(() => {})
}

export {
  addOperaLog
}
