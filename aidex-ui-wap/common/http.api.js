/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */
// 此处第二个参数vm，就是我们在页面使用的this，你可以通过vm获取vuex等操作
const install = (Vue, vm) => {
	
	// 参数配置对象
	const config = vm.vuex_config;
	
	// 将各个定义的接口名称，统一放进对象挂载到vm.$u.api(因为vm就是this，也即this.$u.api)下
	vm.$u.api = {
		// 基础服务
		base: {
			// 登录
			login: (params = {}) => vm.$u.post('/security/token', params),
			// 发送验证码
			sendCode: (params = {}) => vm.$u.post('/wap/mobile/login/sendCode', params),
			// 用户信息
			getUserInfo: (params = {}) => vm.$u.get('/wap/mobile/user/getUserInfo', params),
			// 登出
			logout: (params = {}) => vm.$u.get('/security/logout', params),
			// 字典数据
			dictData: (params = {}) => vm.$u.get('/system/dict/data/type/' + params.dictType),
			// 区划树级数据
			listAreaTree: (params = {}) => vm.$u.get('/wap/mobile/area/listTree', params),
			// 获取图形验证码
			getCodeImg: (params = {}) => vm.$u.get('/captchaImage', null),
		},
		
		// 业务接口
		business: {
			//获取报名列表
			getApplyList: (params = {}) => vm.$u.get('/wap/mobile/biz/getApplyList', params),
			//获取报名信息
			getApplyData: (params = {}) => vm.$u.get('/wap/mobile/biz/getApplyData', params),
			// 保存完善信息
			saveApplyData: (params = {}) => vm.$u.postJson('/wap/mobile/biz/saveApplyBasicData', params),
		}
	};
	
}

export default {
	install
}