// 这里的vm，就是我们在vue文件里面的this，所以我们能在这里获取vuex的变量，比如存放在里面的token
// 同时，我们也可以在此使用getApp().globalData，如果你把token放在getApp().globalData的话，也是可以使用的
// 是否显示重新登录
const isRelogin = { show: false };

const install = (Vue, vm) => {
	// 通用请求头设定
	const ajaxHeader = 'x-ajax';
	const sessionIdHeader = vm.vuex_constant.CACHE_AUTHORIZATION_KEY;
	const rememberMeHeader = 'x-remember';
	
	Vue.prototype.$u.http.setConfig({
		baseUrl: vm.vuex_config.baseUrl,
		showLoading: false,// 是否显示请求中的loading
		// 如果将此值设置为true，拦截回调中将会返回服务端返回的所有数据response，而不是response.data
		// 设置为true后，就需要在this.$u.http.interceptor.response进行多一次的判断，请打印查看具体值
		originalData: true, 
		// 设置自定义头部content-type
		header: {
			'content-type': 'application/x-www-form-urlencoded',
			'x-requested-with': 'XMLHttpRequest'
		}
	});
	// 请求拦截，配置Token等参数
	Vue.prototype.$u.http.interceptor.request = (config) => {
		if (!config.header){
			config.header = [];
		}
		config.header["source"] = "app";
		// 默认指定返回 JSON 数据
		if (!config.header[ajaxHeader]){
			config.header[ajaxHeader] = 'json';
		}
		
		// 设定传递 Token 认证参数 aidex
		if (!config.header[sessionIdHeader] && vm.vuex_token){
			config.header[sessionIdHeader] = vm.vuex_constant.CACHE_TOKEN_PREFIX_KEY + vm.vuex_token;
		}
		
		return config; 
	}
	// 响应拦截，判断状态码是否通过
	Vue.prototype.$u.http.interceptor.response = (res) => {
		// 如果把originalData设置为了true，这里得到将会是服务器返回的所有的原始数据
		// 判断可能变成了res.statueCode，或者res.data.code之类的，请打印查看结果
		let data = res.data;
		if (!(data)){
			vm.$u.toast('未连接到服务器')
			return false;
		}
		let code = data.code;
		let repData = data.data || {};
		if (typeof data === 'object' && !(data instanceof Array)){
			if (data.token){
				vm.$u.vuex(vm.vuex_constant.CACHE_ACCESS_TOKEN, data.token);
				if (data.user){
					vm.$u.vuex(vm.vuex_constant.CACHE_USER_INFO, data.user);
				}
			}
			if (code === 401 || repData.result === 'login'){
				vm.$u.vuex(vm.vuex_constant.CACHE_ACCESS_TOKEN, '');
				vm.$u.vuex(vm.vuex_constant.CACHE_USER_INFO, {});
				if (!isRelogin.show) {
					isRelogin.show = true;
					uni.showModal({
						title: '系统提示',
						content: '登录状态已过期，请重新登录!',
						showCancel: false,
						confirmText: '重新登录',
						success: function (res) {
							setTimeout(() => {
								uni.reLaunch({
									url: '/pages/login/index'
								});
							}, 500);
						},
						complete: function () {
							isRelogin.show = false;
						}
					});
				}
				return false;
			}
		}
		
		return data;
	}
	
	// 封装 get text 请求
	vm.$u.getText = (url, data = {}, header = {}) => {
		return vm.$u.http.request({
			dataType: 'text',
			method: 'GET',
			url,
			header,
			data
		})
	}
	
	// 封装 post json 请求
	vm.$u.postJson = (url, data = {}, header = {}) => {
		header['content-type'] = 'application/json';
		return vm.$u.http.request({
			url,
			method: 'POST',
			header,
			data
		})
	}
	
}

export default {
	install
}