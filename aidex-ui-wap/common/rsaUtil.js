import JSEncrypt from '@/common/jsencrypt.min.js';

const DEFAULT_PUBLIC_KEY = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDQ3hFhYXtYGDX1bjsNXD4T29FxQXhJFA3VV8HdX8oE3ba9wekKW+nq2lTPsrlS/1YDr4qBNA2mugdA2PDDF1HQpMxZXsAOyMCY81lrPc+SYmwT+H96BDb4QKdOVly0Vd75ZOJsU7nABEkuXSAnQNgCN4Zy7ewyPoaXpg+tLSOpHQIDAQAB';
const DEFAULT_PRIVATE_KEY = '';

/**
 * RSA非对称加密
 * @param data 需要加密的数据
 * @param publicKey 公钥
 */
export function rsaEncrypt(data, publicKey) {
  // 公钥
  const PUBLIC_KEY = publicKey || DEFAULT_PUBLIC_KEY;
  // 使用公钥加密
  const encrypt = new JSEncrypt();
  encrypt.setPublicKey(PUBLIC_KEY);
  return encrypt.encrypt(data);
}

/**
 * RSA 解密
 * @param data 需要解密的数据
 * @param privateKey 私钥
 */
export function rsaDecrypt(data, privateKey) {
  //私钥
  const PRIVATE_KEY = privateKey || DEFAULT_PRIVATE_KEY;
  const decrypt = new JSEncrypt();
  decrypt.setPrivateKey(PRIVATE_KEY);
  return decrypt.decrypt(data);
}
