/**
 * 验证工具类
 */

/**
 * 检测是否为身份证号
 * @param {*} input 任意类型的输入
 * @returns {Boolean} 结果
 */
export const isIdCard = function (input) {
  const reg = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])\d{3}[0-9Xx]$/
  return reg.test(input);
};

/**
 * 检测是否为移动电话号码段
 * @param {*} input 任意类型的输入
 * @returns {Boolean} 结果
 */
export const isMobilePhone = function (input) {
  const reg = /^1\d{10}$|^(0\d{2,3}-?|\(0\d{2,3}\))?[1-9]\d{4,7}(-\d{1,8})?$/
  return reg.test(input);
};