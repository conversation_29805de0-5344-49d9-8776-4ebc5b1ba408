/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */
const config = {
	// 请求路径
	baseUrl: '',
	// 认证方式 0:system; 1:oauth
	authMethod: '0',
	// 认证地址
	authUrl: '',
	// 系统部署环境
	buildType: 'gsedu',
}

// 设置后台接口服务的基础地址
if (process.env.NODE_ENV === 'development') {
	config.baseUrl = '/api';
} else {
	let url = '';
	if (config.buildType === 'prod') {
		url = '';
	} else if (config.buildType === 'gsedu') {
		url = 'https://ruxue.smartedu.gsedu.cn/api';
		config.authMethod = '1';
		config.authUrl = '';
	}
	config.baseUrl = url;
}

export default config;