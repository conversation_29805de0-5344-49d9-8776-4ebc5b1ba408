/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */
const constantConfig = {
	// 身份认证KEY
	CACHE_AUTHORIZATION_KEY: "Mobile-Token",
	// 身份认证令牌前缀KEY
	CACHE_TOKEN_PREFIX_KEY: "Bearer ",
	// 浏览器存储KEY
	CACHE_STORAGE_KEY: "lifeData",
	// TOKEN缓存KEY
	CACHE_ACCESS_TOKEN: "vuex_token",
	// 用户基本信息缓存KEY
	CACHE_USER_INFO: "vuex_user",
	// 记住缓存KEY
	CACHE_REMEMBER: "vuex_remember",
	// 本地缓存KEY
	CACHE_LOCALE: "vuex_locale",
	// 浏览器标识缓存KEY
	CACHE_IS_AGENT: "vuex_isAgent"
}
	
export default constantConfig;