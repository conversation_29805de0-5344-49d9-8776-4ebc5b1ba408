/**
 * 字典js方法封装处理
 * Copyright (c) 2019 aidex
 */

// 根据字典类型查询字典数据信息
export async function getDicts(dictType) {
	var dictList = [];
	let params = {
		dictType: dictType
	}
	await this.$u.api.base.dictData(params).then(res => {
		const {code, data} = res;
		if(code === 200) {
			if(data && data.length > 0) {
				var arr = [];
				for (var i = 0; i < data.length; i++) {
					var item = data[i];
					arr.push({
						value: item.dictValue,
						label: item.dictLabel,
						tagType: item.listClass
					})
				}
				dictList = arr;
			}
		}
	})
	return dictList;
}

// 回显数据字典
export function selectDictLabel(datas, value) {
  if (value === undefined) {
    return "";
  }
  var actions = [];
  Object.keys(datas).some((key) => {
    if (datas[key].value == ('' + value)) {
      actions.push(datas[key].label);
      return true;
    }
  })
  if (actions.length === 0) {
    actions.push(value);
  }
  return actions.join('');
}

// 回显数据字典（字符串数组）
export function selectDictLabels(datas, value, separator) {
  if (value === undefined) {
    return "";
  }
  var actions = [];
  var currentSeparator = undefined === separator ? "," : separator;
  var temp = value.split(currentSeparator);
  Object.keys(value.split(currentSeparator)).some((val) => {
    var match = false;
    Object.keys(datas).some((key) => {
      if (datas[key].value == ('' + temp[val])) {
        actions.push(datas[key].label + currentSeparator);
        match = true;
      }
    })
    if (!match) {
      actions.push(temp[val] + currentSeparator);
    }
  })
  return actions.join('').substring(0, actions.join('').length - 1);
}

// 获取数据字典标签类型
export function selectDictTagType(datas, value) {
	var action = "info";
	if (value === undefined) {
	  return action;
	}
	Object.keys(datas).some((key) => {
	  if (datas[key].value == ('' + value)) {
		var tagType = datas[key].tagType;
		if(tagType == "danger") {
			action = "error";
		}else{
			action = tagType;
		}
	    return true;
	  }
	})
	return action;
}