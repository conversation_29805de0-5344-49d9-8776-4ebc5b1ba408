<template>
	<!-- <view class="container">
		<!-- 页面上半部分 -->
		<!-- <view class="top-section sticky">
			<view class="title">报名申请</view>
		</view> -->
		<!-- 页面下半部分 -->
		<!-- <view class="bottom-section"> -->
			<!-- <view v-if="list && list.length > 0"> -->
				<!-- <scroll-view scroll-y="true" style="height: 100%;"> -->
					<!-- <u-card v-for="(item, index) in list" :key="index" :title="item.name" 
					:sub-title="" 
					:sub-title-color="">
						<view slot="body">
							<view>报名学校：{{ item.schoolName }}</view>
							<view>生源类型：{{ item.sourceTypeName }}</view>
							<view>报名时间：{{ item.applyTime }}</view>
							<view>报名状态：<span :style="{color: addclass(item.status)}">{{ selectDictLabel(statusOptions, item.status) }}</span></view>
						</view>
						<view slot="foot">
							<view class="foot_cls">
								<u-icon name="eye-fill" size="34" color="#2979ff" label-color="#2979ff" label="查看" @click="handleView(item)"></u-icon>
							</view>
						</view>
					</u-card>
					<view style="padding-top: 20rpx; padding-bottom: 20rpx;">
						<u-loadmore :status="status" @loadmore="handleLoadmore" />
					</view> -->
				<!-- </scroll-view> -->
			<!-- </view>
			<view v-else style="padding-top:45%;">
				<u-empty text="暂无数据" mode="favor"></u-empty>
			</view> -->
		<!-- </view> -->
	<!-- </view> -->
	
	<view>
		<view class="wrap">
			<view class="u-tabs-box">
				<u-tabs-swiper activeColor="#f29100" ref="tabs" :list="list" :current="current" @change="change" :is-scroll="false" swiperWidth="750"></u-tabs-swiper>
			</view>
			<swiper class="swiper-box" :current="swiperCurrent" @transition="transition" @animationfinish="animationfinish">
				<swiper-item class="swiper-item">
					<scroll-view scroll-y style="height: 100%;width: 100%;" @scrolltolower="reachBottom">
						<view class="page-box">
							<view class="order" v-for="(res, index) in orderList[0]" :key="res.id">
								<view class="top">
									<view class="left">
										<u-icon name="home" :size="30" color="rgb(94,94,94)"></u-icon>
										<view class="store">{{ res.store }}</view>
										<u-icon name="arrow-right" color="rgb(203,203,203)" :size="26"></u-icon>
									</view>
									<view class="right">{{ res.deal }}</view>
								</view>
								<view class="item" v-for="(item, index) in res.goodsList" :key="index">
									<view class="left"><image :src="item.goodsUrl" mode="aspectFill"></image></view>
									<view class="content">
										<view class="title u-line-2">{{ item.title }}</view>
										<view class="type">{{ item.type }}</view>
										<view class="delivery-time">发货时间 {{ item.deliveryTime }}</view>
									</view>
									<view class="right">
										<view class="price">
											￥{{ priceInt(item.price) }}
											<text class="decimal">.{{ priceDecimal(item.price) }}</text>
										</view>
										<view class="number">x{{ item.number }}</view>
									</view>
								</view>
								<view class="total">
									共{{ totalNum(res.goodsList) }}件商品 合计:
									<text class="total-price">
										￥{{ priceInt(totalPrice(res.goodsList)) }}.
										<text class="decimal">{{ priceDecimal(totalPrice(res.goodsList)) }}</text>
									</text>
								</view>
								<view class="bottom">
									<view class="more"><u-icon name="more-dot-fill" color="rgb(203,203,203)"></u-icon></view>
									<view class="logistics btn">查看物流</view>
									<view class="exchange btn">卖了换钱</view>
									<view class="evaluate btn">评价</view>
								</view>
							</view>
							<u-loadmore :status="loadStatus[0]" bgColor="#f2f2f2"></u-loadmore>
						</view>
					</scroll-view>
				</swiper-item>
				<swiper-item class="swiper-item">
					<scroll-view scroll-y style="height: 100%;width: 100%;" @scrolltolower="reachBottom">
						<view class="page-box">
							<view class="order" v-for="(res, index) in  orderList[1]" :key="res.id">
								<view class="top">
									<view class="left">
										<u-icon name="home" :size="30" color="rgb(94,94,94)"></u-icon>
										<view class="store">{{ res.store }}</view>
										<u-icon name="arrow-right" color="rgb(203,203,203)" :size="26"></u-icon>
									</view>
									<view class="right">{{ res.deal }}</view>
								</view>
								<view class="item" v-for="(item, index) in res.goodsList" :key="index">
									<view class="left"><image :src="item.goodsUrl" mode="aspectFill"></image></view>
									<view class="content">
										<view class="title u-line-2">{{ item.title }}</view>
										<view class="type">{{ item.type }}</view>
										<view class="delivery-time">发货时间 {{ item.deliveryTime }}</view>
									</view>
									<view class="right">
										<view class="price">
											￥{{ priceInt(item.price) }}
											<text class="decimal">.{{ priceDecimal(item.price) }}</text>
										</view>
										<view class="number">x{{ item.number }}</view>
									</view>
								</view>
								<view class="total">
									共{{ totalNum(res.goodsList) }}件商品 合计:
									<text class="total-price">
										￥{{ priceInt(totalPrice(res.goodsList)) }}.
										<text class="decimal">{{ priceDecimal(totalPrice(res.goodsList)) }}</text>
									</text>
								</view>
								<view class="bottom">
									<view class="more"><u-icon name="more-dot-fill" color="rgb(203,203,203)"></u-icon></view>
									<view class="logistics btn">查看物流</view>
									<view class="exchange btn">卖了换钱</view>
									<view class="evaluate btn">评价</view>
								</view>
							</view>
							<u-loadmore :status="loadStatus[1]" bgColor="#f2f2f2"></u-loadmore>
						</view>
					</scroll-view>
				</swiper-item>
				<swiper-item class="swiper-item">
					<scroll-view scroll-y style="height: 100%;width: 100%;">
						<view class="page-box">
							<view>
								<view class="centre">
									<image src="https://cdn.uviewui.com/uview/template/taobao-order.png" mode=""></image>
									<view class="explain">
										您还没有相关的订单
										<view class="tips">可以去看看有那些想买的</view>
									</view>
									<view class="btn">随便逛逛</view>
								</view>
							</view>
						</view>
					</scroll-view>
				</swiper-item>
				<swiper-item class="swiper-item">
					<scroll-view scroll-y style="height: 100%;width: 100%;" @scrolltolower="reachBottom">
						<view class="page-box">
							<view class="order" v-for="(res, index) in  orderList[3]" :key="res.id">
								<view class="top">
									<view class="left">
										<u-icon name="home" :size="30" color="rgb(94,94,94)"></u-icon>
										<view class="store">{{ res.store }}</view>
										<u-icon name="arrow-right" color="rgb(203,203,203)" :size="26"></u-icon>
									</view>
									<view class="right">{{ res.deal }}</view>
								</view>
								<view class="item" v-for="(item, index) in res.goodsList" :key="index">
									<view class="left"><image :src="item.goodsUrl" mode="aspectFill"></image></view>
									<view class="content">
										<view class="title u-line-2">{{ item.title }}</view>
										<view class="type">{{ item.type }}</view>
										<view class="delivery-time">发货时间 {{ item.deliveryTime }}</view>
									</view>
									<view class="right">
										<view class="price">
											￥{{ priceInt(item.price) }}
											<text class="decimal">.{{ priceDecimal(item.price) }}</text>
										</view>
										<view class="number">x{{ item.number }}</view>
									</view>
								</view>
								<view class="total">
									共{{ totalNum(res.goodsList) }}件商品 合计:
									<text class="total-price">
										￥{{ priceInt(totalPrice(res.goodsList)) }}.
										<text class="decimal">{{ priceDecimal(totalPrice(res.goodsList)) }}</text>
									</text>
								</view>
								<view class="bottom">
									<view class="more"><u-icon name="more-dot-fill" color="rgb(203,203,203)"></u-icon></view>
									<view class="logistics btn">查看物流</view>
									<view class="exchange btn">卖了换钱</view>
									<view class="evaluate btn">评价</view>
								</view>
							</view>
							<u-loadmore :status="loadStatus[3]" bgColor="#f2f2f2"></u-loadmore>
						</view>
					</scroll-view>
				</swiper-item>
			</swiper>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [],
				queryParams: {
					pageNum: 1,
					pageSize: 10
				},
				total: 0,
				status: 'loading',
				statusOptions: []
			};
		},
		onLoad() {
			this.getDicts('biz_apply_student_status').then(response => {
				this.statusOptions = response
			})
			this.loadData();
		},
		onReachBottom() {
			this.handleLoadmore();
		},
		methods: {
			handleView(row) {
				const that = this;
				const id = row.id || ''
				uni.navigateTo({
					url: '/pages/business/adjust_form?operate=view&id=' + id
				})
			},
			handleAdd() {
				const that = this;
				uni.navigateTo({
					url:'/pages/business/adjust_form?operate=add',
					events: {
						reloadList: function(res){
							that.reset()
							that.loadData();
						}
					}
				})
			},
			loadData() {
				this.$u.api.business.getApplyList().then(res => {
					const {code, data} = res;
					if (code === 200) {
						this.list = this.list.concat(data.list || []);
						this.total = data.total || 0;
					}
					this.checkLoadMoreStatus();
				})
			},
			handleLoadmore() {
				if (this.checkLoadMoreStatus()) {
					this.status = 'loading'
					this.queryParams.pageNum++;
					this.loadData();
				}
			},
			checkLoadMoreStatus() {
				if ((this.queryParams.pageNum * this.queryParams.pageSize) < this.total) {
					this.status = 'loadmore';
					return true;
				} else {
					this.status = 'nomore';
					return false;
				}
			},
			// 页面数据重置
			reset () {
				// 数据集合
				this.list = []
				// 页数
				this.queryParams.pageNum = 1
				this.status = "loading"
			},
			addclass(i){
			  switch (i){
				case('0'):
					return '#333';
				case('1'):
					return '#333';
				case('2'):
					return '#BF2626';
				case('4'):
					return '#BF2626';
				case('6'):
					return '#BF2626';
				case('3'):
					return '#1C65EF';
				case('5'):
					return '#1C65EF';
				case('9'):
					return '#32A32D';
				default:
					return '#333';
			  }
			},
		}
	};
</script>

<style scoped lang="scss">
	// .container {
	// 	display: flex;
	// 	flex-direction: column;
	// 	height: 100%;
	// }

	// .top-section {
	// 	display: flex;
	// 	justify-content: space-between;
	// 	align-items: center;
	// 	padding: 10px;
	// 	border-bottom: 1px solid #ccc;

	// 	.title {
	// 		flex-grow: 1;
	// 		border-left: 5px solid rgb(32, 160, 255);
	// 		padding-left: 5px;
	// 	}

	// 	.spacer {
	// 		flex-grow: 2;
	// 	}
	// }

	// .sticky {
	// 	position: sticky;
	// 	top: 0;
	// 	z-index: 100;
	// 	background-color: #fff;
	// }

	// // .bottom-section {
	// // 	flex: 1;
	// // 	position: relative;
	// // }

	// .load-more, .no-more, .no-data {
	// 	text-align: center;
	// 	padding: 10px;
	// }
	
	// .foot_cls {
	// 	display: flex; 
	// 	justify-content: flex-end;
	// 	align-items: center;
	// }
	
	
/* #ifndef H5 */
page {
	height: 100%;
	background-color: #f2f2f2;
}
/* #endif */
	
.order {
	width: 710rpx;
	background-color: #ffffff;
	margin: 20rpx auto;
	border-radius: 20rpx;
	box-sizing: border-box;
	padding: 20rpx;
	font-size: 28rpx;
	.top {
		display: flex;
		justify-content: space-between;
		.left {
			display: flex;
			align-items: center;
			.store {
				margin: 0 10rpx;
				font-size: 32rpx;
				font-weight: bold;
			}
		}
		.right {
			color: $u-type-info-dark;
		}
	}
	.item {
		display: flex;
		margin: 20rpx 0 0;
		.left {
			margin-right: 20rpx;
			image {
				width: 200rpx;
				height: 200rpx;
				border-radius: 10rpx;
			}
		}
		.content {
			.title {
				font-size: 28rpx;
				line-height: 50rpx;
			}
			.type {
				margin: 10rpx 0;
				font-size: 24rpx;
				color: $u-tips-color;
			}
			.delivery-time {
				color: #e5d001;
				font-size: 24rpx;
			}
		}
		.right {
			margin-left: 10rpx;
			padding-top: 20rpx;
			text-align: right;
			.decimal {
				font-size: 24rpx;
				margin-top: 4rpx;
			}
			.number {
				color: $u-tips-color;
				font-size: 24rpx;
			}
		}
	}
	.total {
		margin-top: 20rpx;
		text-align: right;
		font-size: 24rpx;
		.total-price {
			font-size: 32rpx;
		}
	}
	.bottom {
		display: flex;
		margin-top: 40rpx;
		padding: 0 10rpx;
		justify-content: space-between;
		align-items: center;
		.btn {
			line-height: 52rpx;
			width: 160rpx;
			border-radius: 26rpx;
			border: 2rpx solid $u-border-color;
			font-size: 26rpx;
			text-align: center;
			color: $u-type-info-dark;
		}
		.evaluate {
			color: $u-type-warning-dark;
			border-color: $u-type-warning-dark;
		}
	}
}
.centre {
	text-align: center;
	margin: 200rpx auto;
	font-size: 32rpx;
	image {
		width: 164rpx;
		height: 164rpx;
		border-radius: 50%;
		margin-bottom: 20rpx;
	}
	.tips {
		font-size: 24rpx;
		color: #999999;
		margin-top: 20rpx;
	}
	.btn {
		margin: 80rpx auto;
		width: 200rpx;
		border-radius: 32rpx;
		line-height: 64rpx;
		color: #ffffff;
		font-size: 26rpx;
		background: linear-gradient(270deg, rgba(249, 116, 90, 1) 0%, rgba(255, 158, 1, 1) 100%);
	}
}
.wrap {
	display: flex;
	flex-direction: column;
	height: calc(100vh - var(--window-top));
	width: 100%;
}
.swiper-box {
	flex: 1;
}
.swiper-item {
	height: 100%;
}
</style>