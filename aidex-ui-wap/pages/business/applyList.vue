<template>
	<view>
		<view class="wrap">
			<view v-if="list && list.length > 0">
				<scroll-view scroll-y style="height: 100%;width: 100%;" @scrolltolower="reachBottom">
					<view class="page-box">
						<view class="order" v-for="(item, index) in list" :key="item.id">
							<view class="top">
								<view class="left">
									<u-icon name="account" :size="30" color="rgb(94,94,94)"></u-icon>
									<view class="store">{{ item.name || '无' }}</view>
								</view>
								<view class="right"><span :class="item.basicDataState === '1' ? 'success' : 'error'">{{ item.basicDataState === '1' ? '已完善' : '未完善' }}</span></view>
							</view>
							<view class="item">
								<view class="content">
									<view class="content-box">
										<view class="type">身份证号：</view>
										<view class="title u-line-2">{{ item.idCard || '-/-' }}</view>
									</view>
									<view class="content-box">
										<view class="type">报名学校：</view>
										<view class="title u-line-2">{{ item.schoolName || '-/-' }}</view>
									</view>
									<view class="content-box">
										<view class="type">生源类型：</view>
										<view class="title u-line-2">{{ item.sourceTypeName || '-/-' }}</view>
									</view>
									<view class="content-box">
										<view class="type">报名时间：</view>
										<view class="title u-line-2">{{ parseTime(item.applyTime, '{y}年{m}月{d}日') || '-/-' }}</view>
									</view>
									<view class="content-box">
										<view class="type">报名状态：</view>
										<view class="title u-line-2"><span :style="{color: addclass(item.status)}">{{ selectDictLabel(statusOptions, item.status) }}</span></view>
									</view>
								</view>
							</view>
							<view class="bottom">
								<view class="evaluate btn" v-if="item.status === '9' && item.basicDataState !== '1'" @click="handleForm(item)">信息采集</view>
								<!-- <view class="logistics btn">查看</view> -->
							</view>
						</view>
						<u-loadmore :status="loadStatus" bgColor="#f2f2f2"></u-loadmore>
					</view>
					
				</scroll-view>
			</view>
			<view v-else style="padding-top:55%;">
				<u-empty text="暂无数据" mode="favor"></u-empty>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			list: [],
			queryParams: {
				pageNum: 1,
				pageSize: 10
			},
			total: 0,
			loadStatus: 'loadmore',
			statusOptions: []
		};
	},
	onLoad() {
		this.getDicts('biz_apply_student_status').then(response => {
			this.statusOptions = response
		})
		this.loadData();
	},
	computed: {},
	methods: {
		handleForm(item) {
			const dataId = item.id || ''
			const that = this;
			uni.navigateTo({
				url:'/pages/business/applyForm?id=' + dataId,
				events: {
					reloadList: function(res){
						that.reset()
						that.loadData();
					}
				}
			})
		},
		reachBottom() {
			if (this.checkLoadMoreStatus()) {
				this.loadStatus = 'loading'
				this.queryParams.pageNum++;
				this.loadData();
			}
		},
		// 页面数据重置
		reset () {
			// 数据集合
			this.list = []
			// 页数
			this.queryParams.pageNum = 1
			this.loadStatus = "loadmore"
		},
		loadData() {
			this.$u.api.business.getApplyList().then(res => {
				const {code, data} = res;
				if (code === 200) {
					this.list = this.list.concat(data.list || []);
					this.total = data.total || 0;
				}
				this.checkLoadMoreStatus();
			})
		},
		checkLoadMoreStatus() {
			if ((this.queryParams.pageNum * this.queryParams.pageSize) < this.total) {
				this.loadStatus = 'loadmore';
				return true;
			} else {
				this.loadStatus = 'nomore';
				return false;
			}
		},
		addclass(i){
		  switch (i){
			case('0'):
				return '#333';
			case('1'):
				return '#333';
			case('2'):
				return '#BF2626';
			case('4'):
				return '#BF2626';
			case('6'):
				return '#BF2626';
			case('3'):
				return '#1C65EF';
			case('5'):
				return '#1C65EF';
			case('9'):
				return '#32A32D';
			default:
				return '#333';
		  }
		},
	}
};
</script>

<style lang="scss" scoped>
/* #ifndef H5 */
page {
	height: 100%;
	background-color: #f2f2f2;
}
/* #endif */
.wrap {
	display: flex;
	flex-direction: column;
	height: calc(100vh - var(--window-top));
	width: 100%;
}
.order {
	width: 710rpx;
	background-color: #ffffff;
	margin: 20rpx auto;
	border-radius: 20rpx;
	box-sizing: border-box;
	padding: 20rpx;
	font-size: 28rpx;
	.top {
		display: flex;
		justify-content: space-between;
		.left {
			display: flex;
			align-items: center;
			.store {
				margin: 0 10rpx;
				font-size: 32rpx;
				font-weight: bold;
			}
		}
		.right {
			.success {
				color: $u-type-success-dark;
			}
			.error {
				color: $u-type-error-dark;
			}
		}
	}
	.item {
		display: flex;
		margin: 20rpx 0 0;
		.left {
			margin-right: 20rpx;
			image {
				width: 200rpx;
				height: 200rpx;
				border-radius: 10rpx;
			}
		}
		.content {
			.content-box {
				display: flex;
				align-items: baseline;
				margin: 10rpx 0;
				.title {
					font-size: 30rpx;
					line-height: 50rpx;
					word-break: break-all;
					padding-left: 10rpx;
					flex-grow: 1;
				}
				.type {
					font-size: 30rpx;
					color: $u-tips-color;
					width: 150rpx; 
					flex-shrink: 0; 
					text-align: right; 
					word-break: break-all; 
				}
			}
			
		}
		.right {
			margin-left: 10rpx;
			padding-top: 20rpx;
			text-align: right;
			.decimal {
				font-size: 24rpx;
				margin-top: 4rpx;
			}
			.number {
				color: $u-tips-color;
				font-size: 24rpx;
			}
		}
	}
	.total {
		margin-top: 20rpx;
		text-align: right;
		font-size: 24rpx;
		.total-price {
			font-size: 32rpx;
		}
	}
	.bottom {
		display: flex;
		margin-top: 40rpx;
		padding: 0 10rpx;
		justify-content: flex-end;
		align-items: center;
		.btn {
			line-height: 52rpx;
			width: 160rpx;
			border-radius: 26rpx;
			border: 2rpx solid $u-border-color;
			font-size: 26rpx;
			text-align: center;
			color: $u-type-info-dark;
			margin-left: 20rpx;
		}
		.logistics {
			color: $u-type-primary-dark;
			border-color: $u-type-primary-dark;
		}
		.evaluate {
			color: $u-type-warning-dark;
			border-color: $u-type-warning-dark;
		}
	}
}
.centre {
	text-align: center;
	margin: 200rpx auto;
	font-size: 32rpx;
	image {
		width: 164rpx;
		height: 164rpx;
		border-radius: 50%;
		margin-bottom: 20rpx;
	}
	.tips {
		font-size: 24rpx;
		color: #999999;
		margin-top: 20rpx;
	}
	.btn {
		margin: 80rpx auto;
		width: 200rpx;
		border-radius: 32rpx;
		line-height: 64rpx;
		color: #ffffff;
		font-size: 26rpx;
		background: linear-gradient(270deg, rgba(249, 116, 90, 1) 0%, rgba(255, 158, 1, 1) 100%);
	}
}
</style>
