<template>
	<view class="wrap">
		<u-card :head-style="{padding: '20rpx 0 20rpx 10rpx'}" :body-style="{padding: '20rpx'}">
			<view slot="head">
				<view class="title">报名信息</view>
			</view>
			<view class="" slot="body">
				<view class="u-body-item-title u-line-2 con-box">
					<view class="label-text">姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名：</view>
					<view class="label-text2">{{studentData.name || '-/-'}}</view>
				</view>
				<view class="u-body-item-title u-line-2 con-box">
					<view class="label-text">性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别：</view>
					<view class="label-text2">{{ selectDictLabel(sexOptions, studentData.gender) || '-/-' }}</view>
				</view>
				<view class="u-body-item-title u-line-2 con-box">
					<view class="label-text">出生日期：</view>
					<view class="label-text2">{{ parseTime(studentData.birthday, '{y}年{m}月{d}日') || '-/-' }}</view>
				</view>
				<view class="u-body-item-title u-line-2 con-box">
					<view class="label-text">民&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;族：</view>
					<view class="label-text2">{{ selectDictLabel(nationOptions, studentData.nation) || '-/-' }}</view>
				</view>
				<view class="u-body-item-title u-line-2 con-box">
					<view class="label-text">身份证号：</view>
					<view class="label-text2">{{studentData.idCard || '-/-'}}</view>
				</view>
				<view class="u-body-item-title u-line-2 con-box">
					<view class="label-text">报名学校：</view>
					<view class="label-text2">{{studentData.schoolName || '-/-'}}</view>
				</view>
				<view class="u-body-item-title u-line-2 con-box">
					<view class="label-text">居住地址：</view>
					<view class="label-text2">{{studentData.residentialAddress || '-/-'}}</view>
				</view>
			</view>
		</u-card>
		<template> 
			<u-card :head-style="{padding: '20rpx 0 20rpx 10rpx'}" :body-style="{padding: '20rpx'}">
				<view slot="head">
					<view class="title">学籍信息</view>
				</view>
				<view class="" slot="body">
					<u-form ref="uForm" :model="form" label-width="200rpx" :rules="rules">
						<u-form-item label="出生地区:" prop="birthplaceAreaCodeName" required>
							<u-input v-model="form.birthplaceAreaCodeName" type="select" @click="areaSelect('birthplaceArea')" placeholder="请选择" />
						</u-form-item>
						<u-form-item label="籍贯:" prop="nativePlace" required>
							<u-input v-model.trim="form.nativePlace" placeholder="请输入" :maxlength="20" />
						</u-form-item>
						<u-form-item label="国籍/地区:" prop="countryName" required>
							<u-input v-model="form.countryName" type="select" @click="selectShow1 = true" placeholder="请选择" />
						</u-form-item>
						<u-form-item label="港澳台侨外:" prop="countrymenAbroad" required>
							<u-radio-group v-model="form.countrymenAbroad" class="custom-radio-cls">
								<u-radio v-for="(item, index) in yesOrNoOptions" :key="index" :name="item.value">
									{{ item.label }}
								</u-radio>
							</u-radio-group>
						</u-form-item>
						<u-form-item label="健康状况:" prop="healthConditionName" required>
							<u-input v-model="form.healthConditionName" type="select" @click="selectShow4 = true" placeholder="请选择" />
						</u-form-item>
						<u-form-item label="政治面貌:" prop="politicsStatusName" required>
							<u-input v-model="form.politicsStatusName" type="select" @click="selectShow2 = true" placeholder="请选择" />
						</u-form-item>
						<u-form-item label="户口性质:" prop="registeredResidenceCategoryName" required>
							<u-input v-model="form.registeredResidenceCategoryName" type="select" @click="selectShow6 = true" placeholder="请选择" />
						</u-form-item>
						<u-form-item label="入学方式:" prop="entryWayName" required>
							<u-input v-model="form.entryWayName" type="select" @click="selectShow7 = true" placeholder="请选择" />
						</u-form-item>
						<u-form-item label="就读方式:" prop="attendWayName" required>
							<u-input v-model="form.attendWayName" type="select" @click="selectShow8 = true" placeholder="请选择" />
						</u-form-item>
						<u-form-item label="邮政编码:" prop="postalCode" required>
							<u-input v-model.trim="form.postalCode" placeholder="请输入" :maxlength="10" />
						</u-form-item>
						<u-form-item label="是否独生子女:" prop="onlyChild" required>
							<u-radio-group v-model="form.onlyChild" class="custom-radio-cls">
								<u-radio v-for="(item, index) in yesOrNoOptions" :key="index" :name="item.value">
									{{ item.label }}
								</u-radio>
							</u-radio-group>
						</u-form-item>
						<u-form-item label="受过学前教育:" prop="preschoolEducation" required>
							<u-radio-group v-model="form.preschoolEducation" class="custom-radio-cls">
								<u-radio v-for="(item, index) in yesOrNoOptions" :key="index" :name="item.value">
									{{ item.label }}
								</u-radio>
							</u-radio-group>
						</u-form-item>
						<u-form-item label="是否留守儿童:" prop="leftBehindChildren" required>
							<u-radio-group v-model="form.leftBehindChildren" class="custom-radio-cls">
								<u-radio v-for="(item, index) in yesOrNoOptions" :key="index" :name="item.value">
									{{ item.label }}
								</u-radio>
							</u-radio-group>
						</u-form-item>
						<u-form-item label="需要申请资助:" prop="applyFunding" required>
							<u-radio-group v-model="form.applyFunding" class="custom-radio-cls">
								<u-radio v-for="(item, index) in yesOrNoOptions" :key="index" :name="item.value">
									{{ item.label }}
								</u-radio>
							</u-radio-group>
						</u-form-item>
						<u-form-item label="是否享受一补:" prop="enjoyGrants" required>
							<u-radio-group v-model="form.enjoyGrants" class="custom-radio-cls">
								<u-radio v-for="(item, index) in yesOrNoOptions" :key="index" :name="item.value">
									{{ item.label }}
								</u-radio>
							</u-radio-group>
						</u-form-item>
						<u-form-item label="是否孤儿:" prop="orphan" required>
							<u-radio-group v-model="form.orphan" class="custom-radio-cls">
								<u-radio v-for="(item, index) in yesOrNoOptions" :key="index" :name="item.value">
									{{ item.label }}
								</u-radio>
							</u-radio-group>
						</u-form-item>
						<u-form-item label="是否烈士或优抚子女:" prop="priorityRaising" required>
							<u-radio-group v-model="form.priorityRaising" class="custom-radio-cls">
								<u-radio v-for="(item, index) in yesOrNoOptions" :key="index" :name="item.value">
									{{ item.label }}
								</u-radio>
							</u-radio-group>
						</u-form-item>
						<u-form-item label="是否务工随迁子女:" prop="workAccompany" required>
							<u-radio-group v-model="form.workAccompany" class="custom-radio-cls">
								<u-radio v-for="(item, index) in yesOrNoOptions" :key="index" :name="item.value">
									{{ item.label }}
								</u-radio>
							</u-radio-group>
						</u-form-item>
						<u-form-item label="上下学距离:" prop="schoolDistance">
							<u-input v-model.trim="form.schoolDistance" placeholder="请输入" :maxlength="10" />
						</u-form-item>
						<u-form-item label="上下学方式:" prop="schoolWayName">
							<u-input v-model="form.schoolWayName" type="select" @click="selectShow9 = true" placeholder="请选择" />
						</u-form-item>
						<u-form-item label="是否需要乘坐校车:" prop="schoolBusTake">
							<u-radio-group v-model="form.schoolBusTake" class="custom-radio-cls">
								<u-radio v-for="(item, index) in yesOrNoOptions" :key="index" :name="item.value">
									{{ item.label }}
								</u-radio>
							</u-radio-group>
						</u-form-item>
						<u-form-item label="曾用名:" prop="formerName">
							<u-input v-model.trim="form.formerName" placeholder="请输入" :maxlength="10" />
						</u-form-item>
						<u-form-item label="血型:" prop="bloodTypeName">
							<u-input v-model="form.bloodTypeName" type="select" @click="selectShow3 = true" placeholder="请选择" />
						</u-form-item>
						<u-form-item label="特长:" prop="strongPoint">
							<u-input v-model.trim="form.strongPoint" placeholder="请输入" :maxlength="50" />
						</u-form-item>
						<u-form-item label="电子信箱:" prop="email">
							<u-input v-model.trim="form.email" placeholder="请输入" :maxlength="30" />
						</u-form-item>
						<u-form-item label="残疾类型:" prop="disabilityTypeName">
							<u-input v-model="form.disabilityTypeName" type="select" @click="selectShow5 = true" placeholder="请选择" />
						</u-form-item>
						<u-form-item label="是否由政府购买学位:" prop="governmentBuyDegree">
							<u-radio-group v-model="form.governmentBuyDegree" class="custom-radio-cls">
								<u-radio v-for="(item, index) in yesOrNoOptions" :key="index" :name="item.value">
									{{ item.label }}
								</u-radio>
							</u-radio-group>
						</u-form-item>
						<u-form-item label="随班就读:" prop="studyFollowClassName">
							<u-input v-model="form.studyFollowClassName" type="select" @click="selectShow10 = true" placeholder="请选择" />
						</u-form-item>
					</u-form>
				</view>
				<view class="" slot="foot">
					<view class="dialog-footer">
						<u-button type="primary" @click="submit()" :loading="loading" :disabled="loading">提交</u-button>
						<u-button type="default" @click="resetForm()" :loading="loading" :disabled="loading">重置</u-button>
					</view>
				</view>
			</u-card>
			<view>
				<u-select v-if="selectShow1" v-model="selectShow1" :list="countryOptions" @confirm="handleSelectedConfirm1"></u-select>
				<u-select v-if="selectShow2" v-model="selectShow2" :list="politicsStatusOptions" @confirm="handleSelectedConfirm2"></u-select>
				<u-select v-if="selectShow3" v-model="selectShow3" :list="bloodTypeOptions" @confirm="handleSelectedConfirm3"></u-select>
				<u-select v-if="selectShow4" v-model="selectShow4" :list="healthConditionOptions" @confirm="handleSelectedConfirm4"></u-select>
				<u-select v-if="selectShow5" v-model="selectShow5" :list="disabilityTypeOptions" @confirm="handleSelectedConfirm5"></u-select>
				<u-select v-if="selectShow6" v-model="selectShow6" :list="registeredResidenceCategoryOptions" @confirm="handleSelectedConfirm6"></u-select>
				<u-select v-if="selectShow7" v-model="selectShow7" :list="entryWayOptions" @confirm="handleSelectedConfirm7"></u-select>
				<u-select v-if="selectShow8" v-model="selectShow8" :list="attendWayOptions" @confirm="handleSelectedConfirm8"></u-select>
				<u-select v-if="selectShow9" v-model="selectShow9" :list="schoolWayOptions" @confirm="handleSelectedConfirm9"></u-select>
				<u-select v-if="selectShow10" v-model="selectShow10" :list="studyFollowClassOptions" @confirm="handleSelectedConfirm10"></u-select>
				<!-- 行政区划选择 -->
				<peng-tree :range="areaTreeOptions" ref="areaSelect" 
							idKey="id" nameKey="label" allKey="parentIds" 
							:multiple="false" :selectParent="false"
							title="请选择"
							@confirm="handleAreaChange"></peng-tree>
			</view>
		</template>
	</view>
</template>

<script>
	export default {
		data(){
			return{
				loading: false,
				id: '',
				sexOptions: [],
				nationOptions: [],
				yesOrNoOptions: [],
				countryOptions: [],
				politicsStatusOptions: [],
				bloodTypeOptions: [],
				healthConditionOptions: [],
				disabilityTypeOptions: [],
				registeredResidenceCategoryOptions: [],
				entryWayOptions: [],
				attendWayOptions: [],
				schoolWayOptions: [],
				studyFollowClassOptions: [],
				areaTreeOptions: [],
				studentData: {},
				form: {
					country: null,
					countryName: '',
					nativePlace: null,
					politicsStatus: null,
					politicsStatusName: '',
					postalCode: null,
					email: null,
					formerName: null,
					bloodType: null,
					bloodTypeName: '',
					healthCondition: null,
					healthConditionName: '',
					countrymenAbroad: null,
					disabilityType: null,
					disabilityTypeName: '',
					strongPoint: null,
					birthplaceAreaCode: null,
					birthplaceAreaCodeName: null,
					registeredResidenceCategory: null,
					registeredResidenceCategoryName: '',
					entryWay: null,
					entryWayName: '',
					attendWay: null,
					attendWayName: '',
					onlyChild: null,
					preschoolEducation: null,
					leftBehindChildren: null,
					applyFunding: null,
					enjoyGrants: null,
					orphan: null,
					priorityRaising: null,
					workAccompany: null,
					schoolDistance: null,
					schoolWay: null,
					schoolWayName: '',
					schoolBusTake: null,
					governmentBuyDegree: null,
					studyFollowClass: null,
					studyFollowClassName: '',
				},
				rules: {
					countryName: [ { required: true, message: '不能为空', trigger: 'blur'} ],
					nativePlace: [ { required: true, message: '不能为空', trigger: 'blur'} ],
					politicsStatusName: [ { required: true, message: '不能为空', trigger: 'blur' } ],
					postalCode: [ { required: true, message: '不能为空', trigger: 'blur' } ],
					email: [ { required: false, message: '不能为空', trigger: 'blur' } ],
					formerName: [ { required: false, message: '不能为空', trigger: 'blur' } ],
					bloodTypeName: [ { required: false, message: '不能为空', trigger: 'blur' } ],
					healthConditionName: [ { required: true, message: '不能为空', trigger: 'blur' } ],
					countrymenAbroad: [ { required: true, message: '不能为空', trigger: 'blur' } ],
					disabilityTypeName: [ { required: false, message: '不能为空', trigger: 'blur' } ],
					birthplaceAreaCodeName: [ { required: true, message: '不能为空', trigger: 'blur' } ],
					registeredResidenceCategoryName: [ { required: true, message: '不能为空', trigger: 'blur' } ],
					entryWayName: [ { required: true, message: '不能为空', trigger: 'blur' } ],
					attendWayName: [ { required: true, message: '不能为空', trigger: 'blur' } ],
					onlyChild: [ { required: true, message: '不能为空', trigger: 'blur' } ],
					preschoolEducation: [ { required: true, message: '不能为空', trigger: 'blur' } ],
					leftBehindChildren: [ { required: true, message: '不能为空', trigger: 'blur' } ],
					applyFunding: [ { required: true, message: '不能为空', trigger: 'blur' } ],
					enjoyGrants: [ { required: true, message: '不能为空', trigger: 'blur' } ],
					orphan: [ { required: true, message: '不能为空', trigger: 'blur' } ],
					priorityRaising: [ { required: true, message: '不能为空', trigger: 'blur' } ],
					workAccompany: [ { required: true, message: '不能为空', trigger: 'blur' } ],
					schoolDistance: [ { required: false, message: '不能为空', trigger: 'blur' } ],
					schoolWayName: [ { required: false, message: '不能为空', trigger: 'blur' } ],
					schoolBusTake: [ { required: false, message: '不能为空', trigger: 'blur' } ],
					governmentBuyDegree: [ { required: false, message: '不能为空', trigger: 'blur' } ],
					studyFollowClassName: [ { required: false, message: '不能为空', trigger: 'blur' } ],
				},
				selectShow1: false,
				selectShow2: false,
				selectShow3: false,
				selectShow4: false,
				selectShow5: false,
				selectShow6: false,
				selectShow7: false,
				selectShow8: false,
				selectShow9: false,
				selectShow10: false,
				areaTreeCurrentType: ''
				
			}
		},
		onLoad(event){
			//获取字典数据
			this.getDicts('sys_user_sex').then(response => {
				this.sexOptions = response;
			});
			this.getDicts('sys_nation').then(response => {
				this.nationOptions = response;
			});
			this.getDicts('sys_yes_no').then(response => {
				this.yesOrNoOptions = response
			});
			this.getDicts('sys_country').then(response => {
				this.countryOptions = response
			});
			this.getDicts('sys_politics_status').then(response => {
				this.politicsStatusOptions = response
			});
			this.getDicts('sys_blood_type').then(response => {
				this.bloodTypeOptions = response
			});
			this.getDicts('sys_health_condition').then(response => {
				this.healthConditionOptions = response
			});
			this.getDicts('sys_disability_type').then(response => {
				this.disabilityTypeOptions = response
			});
			this.getDicts('sys_registered_residence_category').then(response => {
				this.registeredResidenceCategoryOptions = response
			});
			this.getDicts('sys_entry_way').then(response => {
				this.entryWayOptions = response
			});
			this.getDicts('sys_attend_way').then(response => {
				this.attendWayOptions = response
			});
			this.getDicts('sys_school_way').then(response => {
				this.schoolWayOptions = response
			});
			this.getDicts('sys_study_follow_class').then(response => {
				this.studyFollowClassOptions = response
			});
			this.getAreaTree();
			this.reset();
			if(event) {
				if (event.id){
					this.id = event.id;
					this.getData();
				}
			}
		},
		// 必须要在onReady生命周期，因为onLoad生命周期组件可能尚未创建完毕
		onReady() {
			if(this.$refs.uForm) {
				this.$refs.uForm.setRules(this.rules);
			}
		},
		methods: {
			// 所属区域多选
			areaSelect(type) {
				this.areaTreeCurrentType = type;
			    //打开选择器
			    this.$refs.areaSelect._show();
			},
			// 所属区域选择
			handleAreaChange(e) {
				let id = null, value = null, label = null;
				if (e && e.length > 0) {
					id = e[0].id;
					value = e[0].value;
					label = e[0].name;
				}
			    if (this.areaTreeCurrentType === 'birthplaceArea') {
					this.form.birthplaceAreaCode = id;
					this.form.birthplaceAreaCodeName = label;
				}
				// 将树级选择标识置空
				this.areaTreeCurrentType = '';
			},
			getAreaTree() {
				this.$u.api.base.listAreaTree({id: '0', level: 3}).then(response => {
					const data = response.data || [];
					// 找到id为'620000'的数据项的索引
					const index = data.findIndex(item => item.id === '620000');
			
					// 如果找到了这样的数据项，并且它不在第一个位置
					if (index > 0) {
						// 将该元素移动到数组的第一个位置
						const [item] = data.splice(index, 1); // 移除目标元素
						data.unshift(item); // 将目标元素插入到数组的开始位置
					}
					this.areaTreeOptions = data;
				})
			},
			//取调配信息
			getData(){
				this.$u.api.business.getApplyData({id: this.id}).then(res => {
					const {code, data, msg} = res;
					if(code === 200) {
						this.form = data || {};
						this.studentData = data || {};
					}
				})
			},
			//提交
			submit(){
				this.$refs.uForm.validate(valid => {
					if (valid) {
						// 保存调配信息
						const saveForm = JSON.parse(JSON.stringify(this.form));
						this.loading = true
						this.$u.api.business.saveApplyData(saveForm).then(response => {
							const { code, msg } = response;
							if (code === 200) {
								this.$u.toast('操作成功');
								this.handleCallback()
								uni.navigateBack()
								this.reset();
							} else {
								let message = msg || '操作失败';
								this.$u.toast(message);
								this.loading = false;
							}
						}).catch(() => {
							this.loading = false;
						});
					} else {
						this.$u.toast('您填写的信息有误，请根据提示修正。');
					}
				});
			},
			handleCallback(){
			    // #ifdef APP-NVUE
			    const eventChannel = this.$scope.eventChannel; // 兼容APP-NVUE
			    // #endif
			    // #ifndef APP-NVUE
			    const eventChannel = this.getOpenerEventChannel();
			    // #endif
			    eventChannel.emit('reloadList');
			},
			resetForm() {
				this.form = this.$options.data().form;
				this.loading = false;
				if(this.$refs.uForm) {
					this.$refs.uForm.resetFields();
				}
				this.areaTreeCurrentType = '';
			},
			reset(){
				this.studentData = {};
				this.resetForm();
			},
			handleSelectedConfirm1(val){
				var value = null;
				var label = '';
				if(val && val.length > 0) {
					value = val[0].value;
					label = val[0].label;
				}
				this.form.country = value;
				this.form.countryName = label;
			},
			handleSelectedConfirm2(val){
				var value = null;
				var label = '';
				if(val && val.length > 0) {
					value = val[0].value;
					label = val[0].label;
				}
				this.form.politicsStatus = value;
				this.form.politicsStatusName = label;
			},
			handleSelectedConfirm3(val){
				var value = null;
				var label = '';
				if(val && val.length > 0) {
					value = val[0].value;
					label = val[0].label;
				}
				this.form.bloodType = value;
				this.form.bloodTypeName = label;
			},
			handleSelectedConfirm4(val){
				var value = null;
				var label = '';
				if(val && val.length > 0) {
					value = val[0].value;
					label = val[0].label;
				}
				this.form.healthCondition = value;
				this.form.healthConditionName = label;
			},
			handleSelectedConfirm5(val){
				var value = null;
				var label = '';
				if(val && val.length > 0) {
					value = val[0].value;
					label = val[0].label;
				}
				this.form.disabilityType = value;
				this.form.disabilityTypeName = label;
			},
			handleSelectedConfirm6(val){
				var value = null;
				var label = '';
				if(val && val.length > 0) {
					value = val[0].value;
					label = val[0].label;
				}
				this.form.registeredResidenceCategory = value;
				this.form.registeredResidenceCategoryName = label;
			},
			handleSelectedConfirm7(val){
				var value = null;
				var label = '';
				if(val && val.length > 0) {
					value = val[0].value;
					label = val[0].label;
				}
				this.form.entryWay = value;
				this.form.entryWayName = label;
			},
			handleSelectedConfirm8(val){
				var value = null;
				var label = '';
				if(val && val.length > 0) {
					value = val[0].value;
					label = val[0].label;
				}
				this.form.attendWay = value;
				this.form.attendWayName = label;
			},
			handleSelectedConfirm9(val){
				var value = null;
				var label = '';
				if(val && val.length > 0) {
					value = val[0].value;
					label = val[0].label;
				}
				this.form.schoolWay = value;
				this.form.schoolWayName = label;
			},
			handleSelectedConfirm10(val){
				var value = null;
				var label = '';
				if(val && val.length > 0) {
					value = val[0].value;
					label = val[0].label;
				}
				this.form.studyFollowClass = value;
				this.form.studyFollowClassName = label;
			},
		}
	}
</script>

<style scoped lang="scss">
	.wrap {
		padding: 0 0 10px 0;
	}
	
	.dialog-footer {
		padding: 10rpx 20rpx;
		
		button {
			margin-bottom: 20rpx;
		}
	} 
	
	.title {
		flex-grow: 1;
		border-left: 5px solid rgb(32, 160, 255);
		padding-left: 5px;
	}
	.con-box {
		margin: 10rpx 0;
		display: flex;
		align-items: baseline;
		
		.label-text {
			width: 25%;
		}
		
		.label-text2 {
			width: 85%;
			text-align: left;
		}
	}
	.custom-radio-cls {
		padding-top: 6rpx;
	}
</style>