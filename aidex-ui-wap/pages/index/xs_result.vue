<template>
	<view class="bgBox">
		<view class="passBox" :class="{'pass': stuApplyQuery.applyStatus == '1', 'nopass': stuApplyQuery.applyStatus == '2' }">
			<image src="../../static/pass.svg" v-if="stuApplyQuery.applyStatus == '1'"></image>
			<image src="../../static/nopass.svg" v-if="stuApplyQuery.applyStatus == '2'"></image>
			<text>{{ stuApplyQuery.applyStatusText || defalutValue }}</text>
		</view>
		<view class="mainBox">
			<view class="cardBox">
				<view class="title">报名信息</view>
				<view class="cardCon">
					<view class="cardList">
						<text>学生姓名</text>
						<text>{{ stuApplyQuery.name || defalutValue }}</text>
					</view>
					<view class="cardList">
						<text>学生身份证号</text>
						<text>{{ stuApplyQuery.idCard || defalutValue }}</text>
					</view>
					<view class="cardList">
						<text>报名学校</text>
						<text>{{ stuApplyQuery.schoolName || defalutValue }}</text>
					</view>
					<view class="cardList">
						<text>监护人电话</text>
						<text>{{ stuApplyQuery.createdUserPhone || defalutValue }}</text>
					</view>
					<view class="cardList">
						<text>报名时间</text>
						<text>{{ stuApplyQuery.applyTime || defalutValue }}</text>
					</view>
				</view>
			</view>
			<view class="cardBox" v-if="stuApplyQuery.applyStatusText === '已录取'">
				<view class="title">录取信息</view>
				<view class="cardCon">
					<view class="cardList">
						<text>录取学校</text>
						<text>{{ stuApplyQuery.endSchoolName || defalutValue }}</text>
					</view>
				</view>
			</view>
			<view class="cardBox"  v-if="stuApplyQuery.auditOpinion">
				<view class="title">审核意见</view>
				<view class="cardCon">
					<view class="cardP">{{ stuApplyQuery.auditOpinion || defalutValue }}</view>
				</view>
			</view>
			<view class="billBox" v-if="billShow">
					<image src="../../static/bill.jpg"></image>
					<text class="billL"></text>
					<text class="billR" @click="handleClick()">×</text>
			</view>
			
			<!-- 政策提示信息 -->
			<view class="cardBox tip-card">
				<view class="title tip-title">
					<text class="tip-icon">💡</text>
					温馨提示
				</view>
				<view class="cardCon tip-content">
					<view class="cardP">
						如有疑问请点击
						<text class="policy-link" @click="openPolicyPage">政策汇总</text>
						了解招生政策或查看学校联系电话进行咨询
					</view>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	var _this;
	export default {
		data() {
			return {
				billShow: false,
				// 报名详情
				stuApplyQuery: {},
				// 默认缺省值
				defalutValue: '-/-'
			}
		},
		onLoad(e) {
			this.handleReset();
			const obj = uni.getStorageSync('applyCacheData');
			if (obj) {
				// 对查询参数进行解码
				this.stuApplyQuery = obj;
			}
		},
		onUnload() {
			uni.removeStorageSync('applyCacheData');
		},
		methods: {
			handleReset() {
				this.billShow = false;
				this.stuApplyQuery = {};
			},
			handleClick() {
				this.billShow = false;
			},
			openPolicyPage() {
				// 打开政策汇总页面
				window.location.href = "https://ruxue.smartedu.gsedu.cn/explain_ysx";
			}
		}
	}
</script>

<style lang="scss" scoped>
.bgBox{
	position: fixed;
	inset: 0;
	background: url(../../static/login_bg.jpg) no-repeat center 50%;
	background-size: cover;
	.passBox{
		display: flex;
		justify-content: center;
		align-items: center;
		flex-flow: column;
		padding: 0 0 60rpx 0;
		gap: 20rpx;
		position: fixed;
		top: 80rpx;
		left: 50%;
		transform: translateX(-50%);
		image{
			width: 160rpx;
			height: 160rpx;
		}
		text{
			font-size: 36rpx;
			font-weight: bold;
		}
		&.pass{
			text{
				color: #2BB968;
			}
		}
		&.nopass{
			text{
				color: #EE2233;
			}
		}
		
	}
	.mainBox{
		position: fixed;
		top: 360rpx;
		left: 0;
		right: 0;
		bottom:0;
		overflow-y: auto;
		overflow-x: hidden;
		padding: 0rpx 0 0rpx 0;
		.cardBox{
			width: calc(100% - 100rpx);
			background: #d9e5ff;
			border-radius: 12rpx;
			transform: translateX(-50%);
			box-shadow: 0 12rpx 28rpx rgba(50, 97, 204, .4);
			padding: 0 0 16rpx 0;
			margin: 32rpx 0 32rpx 50%;
			.title{
				width: 100%;
				height: auto;
				padding: 16rpx 32rpx;
				box-sizing: border-box;
				font-size: 32rpx;
				color: #3570F6;
				font-weight: bold;
			}
			.cardCon{
				width: calc(100% - 32rpx);
				height: auto;
				background: #ffffff;
				margin-left: 16rpx;
				border-radius: 8rpx;
				display: flex;
				flex-flow: column;
				padding: 12rpx 0;
				.cardList{
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 12rpx 28rpx;
					text{
						font-size: 28rpx;
						&:first-child{
							color: #999;
							width: 110px;
							flex-shrink: 0;
							padding-right: 12px;
						}
					}
				}
				.cardP{
					padding: 12rpx 28rpx;
					font-size: 28rpx;
					line-height: 40rpx;
					.policy-link {
						color: #3570F6;
						text-decoration: underline;
						font-weight: bold;
					}
				}
			}
			// 温馨提示特殊样式
			&.tip-card {
				background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%);
				border: 2rpx solid #FF9800;
				box-shadow: 0 8rpx 24rpx rgba(255, 152, 0, .3);
				.tip-title {
					color: #E65100;
					display: flex;
					align-items: center;
					.tip-icon {
						margin-right: 8rpx;
						font-size: 28rpx;
					}
				}
				.tip-content {
					background: #FFFEF7;
					border: 1rpx solid #FFB74D;
				}
			}
		}
		.billBox{
				width: calc(100% - 100rpx);
				height: 230rpx;
				overflow: hidden;
				border: 10rpx solid #ffffff;
				box-sizing: border-box;
				border-radius: 12rpx;
				transform: translateX(-50%);
				margin: 64rpx 0 32rpx 50%;
				position: relative;
				image{
					position: absolute;
					z-index: 1;
					inset: 0;
					width: 100%;
					height: 210rpx;
				}
				.billL{
					position: absolute;
					z-index: 1;
					left: 0;
					top: 0;
					background: rgba(0, 0, 0, .15);
					color: #ffffff;
					font-size: 24rpx;
					padding: 8rpx 16rpx;
				}
				.billR{
					position: absolute;
					z-index: 1;
					right: 0;
					top: 0;
					width: 40rpx;
					height: 30rpx;
					overflow: hidden;
					background: rgba(0, 0, 0, .15);
					color: #ffffff;
					font-size: 32rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					padding: 0 0 10rpx 0;
				}
		}
	}
}
</style>
