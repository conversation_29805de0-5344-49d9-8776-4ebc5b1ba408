<template>
	<view class="bgBox">
		<view class="logo">
			<image src="../../static/logo.svg"></image>
		</view>
		<view class="inputBox">
			<input v-model="form.studentIdCard" class="inputTxt" value="" placeholder="请输入报名时填写的儿童身份证号" :always-embed="true" :adjust-position="true" cursor-spacing="30" />
			<input v-model="form.guardianIdCard" class="inputTxt" value="" placeholder="请输入报名时填写的监护人身份证号" :always-embed="true" :adjust-position="true" cursor-spacing="30" />
			<input v-model="form.guardianPhone" class="inputTxt" value="" placeholder="请输入报名时填写的监护人手机号" :always-embed="true" :adjust-position="true" cursor-spacing="30" />
			<view class="yanzhengBox">
				<input class="inputTxtYanzheng" v-model="form.code" value="" placeholder="请输入验证码" :always-embed="true" :adjust-position="true" cursor-spacing="30" />
				<img class="captchaBox" :src="codeUrl" @click="getCode" v-if="codeUrl">
			</view>
			<view class="captchaMessage">
				<text class="captchaText">验证码为图片中算式的计算结果,输入计算结果即可,如果验证码错误点击验证码图片可以刷新验证码</text>
			</view>
			<view @click="submit()" class="inputBtn">查询</view>
			<view @click="reset()" class="inputBtn resetBtn">重置</view>
		</view>
	</view>
</template>

<script>
	var _this;
	import { isIdCard, isMobilePhone } from '@/common/validate_utils.js';
	import { rsaEncrypt } from '@/common/rsaUtil.js';
	export default {
		data() {
			return {
				// 数据绑定
				form: {
					// 学生身份证号
					studentIdCard: '',
					// 监护人身份证号
					guardianIdCard: '',
					// 监护人手机号
					guardianPhone: '',
					// 验证码
					code: '',
					// 验证码标识
					uuid: ''
				},
				baseUrl: '',
				disabled: false,
				codeUrl: '',
			}
		},
		onLoad() {
			this.baseUrl = this.vuex_config.baseUrl;
			this.getCode();
		},
		onShow() {
			this.handleOperLog();
		},
		methods: {
			submit() {
				this.handleOperLog();
				const that = this;
				if(this.disabled) { return; }
				if (this.form.studentIdCard.length === 0) {
					this.$u.toast('请输入儿童身份证号');
					return;
				}
				if (!isIdCard(this.form.studentIdCard)) {
					this.$u.toast('请正确填写儿童身份证号');
					return;
				}
				if (this.form.guardianIdCard.length === 0) {
					this.$u.toast('请输入监护人身份证号');
					return;
				}
				if (!isIdCard(this.form.guardianIdCard)) {
					this.$u.toast('请正确填写监护人身份证号');
					return;
				}
				if (this.form.guardianPhone.length === 0) {
					this.$u.toast('请输入监护人手机号');
					return;
				}
				if (!isMobilePhone(this.form.guardianPhone)) {
					this.$u.toast('请正确填写监护人手机号');
					return;
				}
				if (this.form.code.length === 0) {
					this.$u.toast('请输入验证码');
					return;
				}

				const saveForm = JSON.parse(JSON.stringify(this.form));
				saveForm.studentIdCard = rsaEncrypt(saveForm.studentIdCard);
				saveForm.guardianIdCard = rsaEncrypt(saveForm.guardianIdCard);
				saveForm.guardianPhone = rsaEncrypt(saveForm.guardianPhone);

				this.disabled = true;
				uni.showLoading({
					title: '正在查询中...',
					mask: true
				});
				let apiUrl = '/mobile/api/queryApply'
				uni.request({
				    url: this.baseUrl + apiUrl,
				    method: 'GET',
				    data: saveForm,
				    success: (res) => {
						const { code, msg, data } = res.data;
						if (code === 200) {
							if (data && data.length > 0) {
								uni.setStorageSync('applyCacheData', data[0]);
								uni.navigateTo({
									url: '/pages/index/xs_result'
								})
							} else {
								uni.showModal({
									content: '暂未查询到报名信息!',
									showCancel: false,
									maskClick: false,
									confirmText: '知道了',
									success: function (res) {},
									complete: function () {}
								});
							}
						} else {
							let message = '查询失败，请稍后重试！';
							this.getCode();
							uni.showModal({
								content: msg || message,
								showCancel: false,
								maskClick: false,
								confirmText: '知道了',
								success: function (res) {},
								complete: function () {}
							});
						}
				    },
					fail() {
						this.$u.toast('网络异常，请稍后重试！');
					},
					complete() {
						uni.hideLoading();
						setTimeout(() => {
							that.disabled = false;
						}, 1000);
					}
				});
			},
			reset() {
				this.handleOperLog();
				const initData = this.$options.data();
				if (initData) {
					this.form = initData.form;
				}
			},
			getCode() {
				this.handleOperLog();
				this.$u.api.base.getCodeImg().then(res => {
					const {code, img, uuid} = res;
					if (code === 200) {
						this.codeUrl = 'data:image/gif;base64,' + img
						this.form.uuid = uuid
					}
				})
				this.form.code = ''
			},
			handleOperLog() {
				this.$addOperaLog('小学报名-报名查询')
			}
		}
	}
</script>

<style lang="scss" scoped>
.bgBox{
	position: fixed;
	inset: 0;
	background: url(../../static/login_bg.jpg) no-repeat center 50%;
	background-size: cover;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-flow: column;
	.logo{
		padding: 0 0 80rpx 0;
		image{
			width: 80vw;
			height: 300rpx;
		}
	}
	.inputBox{
		width: 100%;
		height: auto;
    max-height: calc(100% - 340rpx);
    overflow-y: auto;
		display: flex;
		flex-flow: column;
		justify-content: flex-start;
		align-items: center;
		gap: 40rpx;
		padding: 0 0 70rpx 0;
		.inputTxt{
			display: block;
			width: 60%;
			height: 50rpx;
			line-height: 50rpx;
			background: #ffffff;
			border: 6rpx solid #3570F680;
			border-radius: 1000px;
			padding: 20rpx 42rpx;
			font-size: 28rpx;
		}
		.inputBtn{
			display: block;
			width: 60%;
			height: 50rpx;
			line-height: 50rpx;
			background: #3570F6;
			border-radius: 1000px;
			padding: 20rpx 42rpx;
			font-size: 28rpx;
			text-align: center;
			color: #ffffff;
		}
		.resetBtn{
			background: #ffffff !important;
			color: #3570F6 !important;
			border: 1px solid #3570F6 !important;
		}
		.yanzhengBox{
			width: 60%;
			height: 50rpx;
			line-height: 50rpx;
			background: #ffffff;
			border: 6rpx solid #3570F680;
			border-radius: 1000px;
			padding: 20rpx 42rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.inputTxtYanzheng{
				font-size: 28rpx;
			}
			.captchaBox {
			    display: block;
			    // width: 60%;
			    height: 78rpx;
			}
			text{
				font-size: 28rpx;
				color: #3570F6;
			}
		}
		.captchaMessage{
			display: flex;
			align-items: center;
			justify-content: flex-start;
			width: 60%;
			padding: 12rpx 20rpx;
			background: rgba(255, 255, 255, 0.9);
			border: 1rpx solid rgba(53, 112, 246, 0.3);
			border-radius: 16rpx;
			backdrop-filter: blur(10rpx);
			box-shadow: 0 2rpx 8rpx rgba(53, 112, 246, 0.1);
			margin-top: -15rpx;
			.captchaIcon{
				font-size: 28rpx;
				margin-right: 8rpx;
				opacity: 0.8;
			}
			.captchaText{
				color: #3570F6;
				font-size: 30rpx;
				font-weight: 400;
				line-height: 1.3;
				text-align: left;
				opacity: 0.85;
			}
		}
	}
}
</style>
