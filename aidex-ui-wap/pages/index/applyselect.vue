<template>
	<view class="bgBox">
		<view class="logo">
			<image src="../../static/logo.svg"></image>
		</view>
		<view class="inputBox">
			<view class="textBtn" @click="handleToXS()"><image src="../../static/text1.svg"></image></view>
			<view class="textBtn"@click="handleToXSC()"><image src="../../static/text2.svg"></image></view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {}
		},
		onLoad() {},
		onShow() {
			this.$addOperaLog('报名查询')
		},
		methods: {
			handleToXS() {
				uni.navigateTo({
				  url: '/pages/index/xs_applyselect'
				});
			},
			handleToXSC() {
        uni.navigateTo({
          url: '/pages/index/cz_applyselect'
        });
			}
		}
	}
</script>

<style lang="scss" scoped>
.bgBox{
	position: fixed;
	inset: 0;
	background: url(../../static/login_bg.jpg) no-repeat center 50%;
	background-size: cover;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-flow: column;
	.logo{
		padding:0 0 160rpx 0;
		image{
			width: 80vw;
			height: 300rpx;
		}
	}
	.inputBox{
		width: 100%;
		height: auto;
		display: flex;
		flex-flow: column;
		justify-content: center;
		align-items: center;
		gap: 80rpx;
		padding:0 0 160rpx 0;
    .textBtn{
      width: calc(100% - 320rpx);
      height: auto;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgba(255,255,255,.8);
      padding: 60rpx 100rpx;
      border-radius: 30rpx;
      image{
        width: 100%;
        height: 90rpx;
        object-fit: contain;
      }
      &:nth-child(1){
        box-shadow: 0 20rpx 50rpx rgba(11,133,178,.14);
      }
      &:nth-child(2){
        box-shadow: 0 20rpx 50rpx rgba(68,111,238,.14);
      }
    }

	}
}
</style>
