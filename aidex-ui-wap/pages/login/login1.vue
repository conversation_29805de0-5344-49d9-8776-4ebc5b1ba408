<template>
	<view class="bgBox">
		<view class="logo">
			<image src="../../static/logo.svg"></image>
		</view>
		<view class="inputBox">
			<input  class="inputTxt" value="" placeholder="请输入报名时填写的监护人手机号" />
			<view class="yanzhengBox">
				<input  class="inputTxtYanzheng" value="" placeholder="验证码" />
				<text>获取验证码</text>
			</view>
			<view class="inputBtn">查询</view>
		</view>
	</view>
</template>

<script>
	var _this;
	export default {
		data() {
			return {
				
			}
		},
		onLoad() {
			
		},
		methods: {
			
		}
	}
</script>

<style lang="scss" scoped>
.bgBox{
	position: fixed;
	inset: 0;
	background: url(../../static/login_bg.jpg) no-repeat center 50%;
	background-size: cover;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-flow: column;
	.logo{
		padding:0 0 160rpx 0;
		image{
			width: 80vw;
			height: 300rpx;
		}
	}
	.inputBox{
		width: 100%;
		height: auto;
		display: flex;
		flex-flow: column;
		justify-content: center;
		align-items: center;
		gap: 40rpx;
		padding:0 0 160rpx 0;
		.inputTxt{
			display: block;
			width: 60%;
			height: 50rpx;
			line-height: 50rpx;
			background: #ffffff;
			border: 6rpx solid #3570F680;
			border-radius: 1000px;
			padding:20rpx 42rpx;
			font-size: 28rpx;
		}
		.inputBtn{
			display: block;
			width: 60%;
			height: 50rpx;
			line-height: 50rpx;
			background: #3570F6;
			border-radius: 1000px;
			padding:20rpx 42rpx;
			font-size: 28rpx;
			text-align: center;
			color: #ffffff;
		}
		.yanzhengBox{
			width: 60%;
			height: 50rpx;
			line-height: 50rpx;
			background: #ffffff;
			border: 6rpx solid #3570F680;
			border-radius: 1000px;
			padding:20rpx 42rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.inputTxtYanzheng{
				font-size: 28rpx;
			}
			text{
				font-size: 28rpx;
				color: #3570F6;
			}
		}
	}
}
</style>
