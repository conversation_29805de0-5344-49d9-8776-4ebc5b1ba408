<template>
	<view class="wrap">
		<view>{{ tipsTitle }}</view>
	</view>
</template>
<script>
	/**
	 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
	 */
	export default {
		data() {
			return {
				tipsTitle: '正在登录跳转中，请稍后...',
				// 认证方式
				authMethod: null,
				indexUrl: '/pages/business/applyList'
			};
		},
		onLoad(event) {
			uni.redirectTo({
				url: '/pages/index/applyselect'
			})
		},
		methods: {
			handleLoginLoad(event) {
				const token = this.vuex_token;
				this.authMethod = this.vuex_config.authMethod;
				if (token) {
					uni.reLaunch({
						url: this.indexUrl
					});
				} else if (event && event.token) {
					this.handleToken(event.token)
					this.loginSuccess()
				} else if (event && event.message) {
					this.tipsTitle = event.message
				} else {
					if(this.authMethod === '1') {
						const params = event && event.params
						let queryParams = {};
						if (params) {
							queryParams = JSON.parse(params);
						}
						const code = queryParams.code;
						const state = queryParams.state;
						const authType = queryParams.authType;
						if (code && state) {
							this.gseduOauth(code, state, authType)
						} else {
							// 统一身份认证
							window.location.href = this.vuex_config.authUrl;
						}
					} else {
						// 登录跳转
						this.tipsTitle = '请刷新页面重试！'
					}
				}
			},
			submit(loginType) {
				
			},
			async handleToken(token) {
				this.$u.vuex(this.vuex_constant.CACHE_ACCESS_TOKEN, token);
				await this.$u.api.base.getUserInfo().then(res => {
					const {code, user} = res;
					if (code === 200) {
						this.$u.vuex(this.vuex_constant.CACHE_USER_INFO, user);
					}
				})
			},
			loginSuccess() {
				const that = this
				that.tipsTitle = '登录认证成功！'
				setTimeout(() => {
					uni.reLaunch({
						url: that.indexUrl
					});
				}, 3 * 1000)
			},
			gseduOauth(code, state, authType) {
				const that = this
				const loginForm = {
					username: code,
					password: that.generateRandomString(),
					state: state,
					authorize_channel: authType
				}
				that.$u.api.base.login(loginForm).then((res) => {
					const {code, data, message} = res;
					if (code === 200 && data) { 
						const token = data || null
						that.handleToken(token)
						that.loginSuccess()
					} else {
						that.tipsTitle = message || '登录认证失败，请稍后重试！'
					}
				}).catch(err => {
					that.tipsTitle = '登录认证失败！'
				})
			}
		}
	};
</script>
<style lang="scss">
</style>
