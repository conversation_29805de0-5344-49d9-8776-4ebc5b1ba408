{
    "name" : "luyan",
    "appid" : "__UNI__F088D7B",
    "description" : "",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    "app-plus" : {
        /* 5+App特有相关 */
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "nvueStyleCompiler" : "uni-app",
        "modules" : {},
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            "ios" : {},
            /* ios打包配置 */
            "sdkConfigs" : {}
        }
    },
    /* SDK配置 */
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        /* 小程序特有相关 */
        "appid" : "wx9c560dde8e7c2dc5",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true
    },
    "vueVersion" : "2",
    "h5" : {
        "title" : "甘肃省教育入学一件事报名查询",
        "router" : {
            "base" : "/wap/"
        },
        "devServer" : {
            "port" : 1090,
            "https" : false,
            "disableHostCheck" : true,
            "proxy" : {
                "/api" : {
                    "target" : "http://127.0.0.1:8080",
                    "changeOrigin" : true,
                    "secure" : true,
                    "pathRewrite" : {
                        "^/api" : ""
                    }
                }
            }
        },
        "template" : "index.html"
    }
}
