<template>
	<view class="container">
		<view class="top">
			<view class="ind">序号</view>
			<view class="name">项目名称</view>
			<view class="score">总分</view>
			<view class="ranking">名次</view>
		</view>
		<view class="content" v-for="(item, ind) in list">
			<view class="ind"><view class="circle">{{ ind + 1 }}</view></view>
			<view class="name">{{ item.projectName }}</view>
			<view class="score">{{ item.score || '--' }}</view>
			<view class="ranking" v-if="ind === 0 && item.score != null">
				<u-icon :name="frist"  width="52" height="45"></u-icon>
			</view>
			<view class="ranking" v-else-if="ind === 1 && item.score != null">
				<u-icon :name="second"  width="52" height="45"></u-icon>
			</view>
			<view class="ranking" v-else-if="ind === 2 && item.score != null">
				<u-icon :name="third"  width="52" height="45"></u-icon>
			</view>
		</view>
		<view class="title">
			<u-icon name="info-circle-fill" size='28' color='#bbb'></u-icon>
			<view style="margin-left: 10rpx;">  -- 表示尚有评委未提交对该项目的评分</view>
		</view>
	</view>	
</template>

<script>
	export default {
		props: {
			list: {
				type: Array,
				default() {
					return []
				}
			}
		},
		data() {
			return {
				frist: require('@/static/1.png'),
				second: require('@/static/2.png'),
				third: require('@/static/3.png'),
				other: require('@/static/other.png'),
			}
		},
		methods: {
			navTo(url) {
				uni.navigateTo({
					url
				})
			}
		}
	}	
</script>

<style lang="scss" scoped>
	.top{
		display: flex;
		width: 100%;
		background: #f5f5f5;
		color: #bbb;
		font-size: 24rpx;
		height: 70rpx;
		align-items: center;
	}
	.circle{
		width: 40rpx;
		height: 40rpx;
		line-height: 40rpx;
		border-radius: 50%;
		background: #f5f5f5;
		font-size: 24rpx;
		text-align: center;
		color: #bbb;
	}
	.content{
		display: flex;
		width: 100%;
		color: #333;
		height: 80rpx;
		align-items: center;
		font-size: 28rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}
	.ind{
		width: 15%;
		display: flex;
		justify-content: center;
	}
	.name{
		width: 55%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	.score{
		width: 15%;
		text-align: center;
	}
	.ranking{
		width: 15%;
		text-align: center;
	}
	.title{
		margin-top: 50rpx;
		font-size: 24rpx;
		color: #bbb;
		display: flex;
		align-items: center;
		justify-content: center;	
	}
</style>