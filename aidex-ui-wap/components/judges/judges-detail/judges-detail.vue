<template>
	<view class="container" v-if="roadshow">
		<u-image width="100%" height="400rpx" border-radius="10rpx" :src="roadshow.picUrl" :lazy-load="true" />
		<view class="con">
			<view class="con_item" style="margin-top: 20rpx;">
				<view class="title">{{roadshow.name}}</view>
			</view>
			<view class="con_item bor" style="margin-top: 100rpx;">
				<view class="left">
					<view class="name">主办单位</view>
				</view>
				<view class="right">
					<view class="status">{{roadshow.unit}}</view>
				</view>
			</view>
			<view class="con_item bor">
				<view class="left">
					<view class="name">路演地点</view>
				</view>
				<view class="right">
					<view class="status">{{roadshow.address}}</view>
				</view>
			</view>
			<view class="con_item bor">
				<view class="left">
					<view class="name">路演时间</view>
				</view>
				<view class="right">
					<view class="status">{{roadshow.startTime}}</view>
					<view class="status">至</view>
					<view class="status" style="margin-left: 7px;">{{roadshow.stopTime}}</view>
				</view>
			</view>
			<view class="con_item bor">
				<view class="left">
					<view class="name">补充说明</view>
				</view>
				<view class="right">
					<view class="status" v-if="roadshow.remark">{{roadshow.remark}}</view>
					<view class="status" v-if="!roadshow.remark">暂无</view>
				</view>
			</view>
		</view>
	</view>	
</template>

<script>
	export default {
		props: {
			roadshow: {
				type: Object,
				default() {
					return null
				}
			}
		},
		data() {
			return {
				addImg: '',
				url: 'https://ts1.cn.mm.bing.net/th/id/R-C.********************************?rik=Z3Hew18zFaF%2bLQ&riu=http%3a%2f%2fwww.pp3.cn%2fuploads%2f20120418lw%2f13.jpg&ehk=Es5ZGH90h%2foCghvlIwdKfUiqpO05gLSgOEBU2i0Mwok%3d&risl=&pid=ImgRaw&r=0',
				name: ''
			}
		},
		methods: {
			navTo(url) {
				uni.navigateTo({
					url
				})
			}
		}
	}	
</script>

<style lang="scss" scoped>
	.con{
		padding: 0 20rpx;
		background: #fff;
		.con_item{
			display: flex;
			min-height: 80rpx;
			align-items: center;
			justify-content: space-between;
			.left{
				width: 30%;
				display: flex;
				align-items: center;
				padding-left: 20rpx;
				.name{
					font-size: 26rpx;
					margin-left: 10rpx;
				}
			}
			.right{
				width: 80%;
				display: flex;
				align-items: center;
				.status{
					font-size: 26rpx;
					color: #bbb;
					margin-right: 10rpx;
				}
			}
		.con_item:last-child{
			border-bottom: 0;
		}
		}
		.bor{
			margin-top: 40rpx;
			border: 1rpx solid #e0e0e0;
		}
	}
</style>