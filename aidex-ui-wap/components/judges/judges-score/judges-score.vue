<template>
	<view class="container">
		<view class="top">
			<view class="ind">序号</view>
			<view class="name">项目名称</view>
			<view class="score">得分</view>
		</view>
		<view class="content" v-for="(item, ind) in list">
			<view class="ind">
				<view class="point_blank" />
				<view class="circle">{{ ind + 1 }}</view>
			</view>
			<view class="name">{{ item.project_name }}</view>
			<view class="score">{{ item.score || '--'}} </view>
		</view>
		<view v-if="project" class="content" @click="navTo('/pages/judges/score', project)">
			<view class="ind">
				<view class="point"/>
				<view class="point_blank" />
				<view class="circle">*</view>
			</view>
			<view class="name">{{ project.projectName }}</view>
			<view class="score">-- </view>
			<view class="ranking">
				<u-icon name="arrow-right" color="#BBB" size="28"></u-icon>
			</view>
		</view>
		<view v-if="project" class="title">
			<view class="point" />
			<view>表示你尚未提交该项目的评分，请尽快提交</view>
		</view>
		
		<view class="btn">
			<view style="width: 90%;">
				<u-button type="success" :ripple="true" ripple-bg-color="#909399" @click="loginOut">退出登录</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			roadshowId: {
				type: String,
				default() {
					return ""
				}
			},
			list: {
				type: Array,
				default() {
					return []
				}
			},
			project: {
				type: Object,
				default() {
					return null
				}
			}
		},
		data() {
			return {
				frist: require('@/static/1.png'),
				second: require('@/static/2.png'),
				third: require('@/static/3.png'),
				other: require('@/static/other.png'),
			}
		},
		methods: {
			navTo(url, obj) {
				var detail = JSON.stringify(obj);
				console.log(detail);
				uni.navigateTo({
					url: `${url}?detail=${detail}`
				})
			},
			
			loginOut(){
				uni.showModal({
					title: '提示',
					content: '您确定要退出登录吗？',
					success: function (res) {
						if (res.confirm) {
							uni.removeStorageSync('loginUser');
							//返回首页
							uni.reLaunch({
								url: '/pages/index/index'
							});
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			}
		}
	}	
</script>

<style lang="scss" scoped>
	.top{
		display: flex;
		width: 100%;
		background: #f5f5f5;
		color: #bbb;
		font-size: 24rpx;
		height: 70rpx;
		align-items: center;
	}
	.circle{
		width: 40rpx;
		height: 40rpx;
		line-height: 40rpx;
		border-radius: 50%;
		background: #f5f5f5;
		font-size: 24rpx;
		text-align: center;
		color: #bbb;
	}
	.content{
		display: flex;
		width: 100%;
		color: #333;
		height: 80rpx;
		align-items: center;
		font-size: 28rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}
	.ind{
		width: 20%;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.name{
		width: 60%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	.score{
		width: 10%;
		text-align: center;
	}
	.ranking{
		width: 10%;
		text-align: center;
	}
	.point{
		width: 8rpx;
		height: 8rpx;
		background: red;
		border-radius: 50%;
		margin-right: 8rpx;
	}
	.point_blank{
		width: 8rpx;
		height: 8rpx;
		margin-right: 8rpx;
	}
	.title{
		margin-top: 50rpx;
		font-size: 24rpx;
		color: #bbb;
		display: flex;
		align-items: center;
		justify-content: center;	
	}
	.btn {
		position: fixed;
		width: 100%;
		height: 100rpx;
		background: #fff;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>