// #ifndef VUE3
import Vue from 'vue'
import App from './App'

Vue.config.productionTip = false

App.mpType = 'app'
// 引入全局 uView 框架
import uView from '@/uni_modules/uview-ui';
Vue.use(uView);

// 全局存储 vuex 的封装
import store from '@/store';

// 引入 uView 提供的对 vuex 的简写法文件
let vuexStore = require('@/store/$u.mixin.js');
Vue.mixin(vuexStore);

const app = new Vue({
	store,
    ...App
})

// http拦截器，将此部分放在new Vue()和app.$mount()之间，才能App.vue中正常使用
import httpInterceptor from '@/common/http.interceptor.js';
Vue.use(httpInterceptor, app);

// http 接口 API 抽离，免于写 url 或者一些固定的参数
import httpApi from '@/common/http.api.js';
Vue.use(httpApi, app);

import { getDicts, selectDictLabel, selectDictLabels, selectDictTagType } from '@/common/dict.js'
import { replaceAll, parseTime, generateRandomString } from '@/common/common.js'
import { addOperaLog } from '@/common/userBehavior.js'
Vue.prototype.getDicts = getDicts
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictTagType = selectDictTagType
Vue.prototype.replaceAll = replaceAll
Vue.prototype.parseTime = parseTime
Vue.prototype.generateRandomString = generateRandomString
Vue.prototype.$addOperaLog = addOperaLog

app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import App from './App.vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif