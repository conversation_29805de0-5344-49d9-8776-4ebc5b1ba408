# 开发环境共享配置
# 数据源配置
spring:
  datasource:
    dynamic:
      # 主库数据源
      primary:
        driverClassName: com.mysql.cj.jdbc.Driver
        url: ************************************************************************************************************************************************************************************************************
        username: root
        password: 123456
    hikari:
      # 最小连接池数量
      minimum-idle: 10
      # 最大连接池数量
      maximum-pool-size: 200
      # 连接允许在池中闲置的最长时间，单位是毫秒
      idle-timeout: 600000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      max-lifetime: 900000
      # 等待连接池分配连接的最大时长，单位是毫秒
      connection-timeout: 30000
      # 配置检测连接是否有效
      connection-test-query: SELECT 1 FROM DUAL
      # 验证连接有效性的超时时间，单位为毫秒
      validation-timeout: 10000

  # Redis配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password: 123456
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.aidex.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice,/business/bizSourceTypeExplain
  # 匹配链接
  urlPatterns: /*

# 日志配置
logging:
  level:
    com.aidex: debug
    org.springframework: warn
    root: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
  file:
    max-size: 50MB
    max-history: 30

# Spring Boot Actuator
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
