#!/bin/sh

VERSION=$(date "+%Y%m%d%H%M%S")

# 第一步：构建项目
echo '开始构建项目...'
mvn clean package
echo '项目构建完成...'

# 第二步：构建镜像
echo '开始构建Docker镜像...'
cd ./aidex-admin
docker build --build-arg TASK_STATE=false -t gsedu-rxbm:${VERSION} --platform linux/amd64 .
docker build --build-arg TASK_STATE=false -t gsedu-rxbm:latest --platform linux/amd64 .
echo 'Docker构建镜像完成...'

# 第三步：上传镜像到私有镜像仓库
echo '开始上传镜像...'
docker tag gsedu-rxbm:${VERSION} 10.185.8.57:5000/gsedu-rxbm:${VERSION}
docker tag gsedu-rxbm:latest 10.185.8.57:5000/gsedu-rxbm:latest

docker push 10.185.8.57:5000/gsedu-rxbm:${VERSION}
docker push 10.185.8.57:5000/gsedu-rxbm:latest
echo '上传镜像完成...'

# 第四步：清理
echo '开始清理...'
mvn clean
docker rmi 10.185.8.57:5000/gsedu-rxbm:${VERSION}
docker rmi gsedu-rxbm:${VERSION}

docker rmi 10.185.8.57:5000/gsedu-rxbm:latest
docker rmi gsedu-rxbm:latest
echo '清理完成...'