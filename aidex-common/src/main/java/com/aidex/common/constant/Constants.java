package com.aidex.common.constant;

import io.jsonwebtoken.Claims;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants {
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 注册
     */
    public static final String REGISTER = "Register";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 2;

    /**
     * 令牌
     */
    public static final String TOKEN = "token";

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = "login_user_key";

    /**
     * 用户ID
     */
    public static final String JWT_USERID = "userid";

    /**
     * 用户名称
     */
    public static final String JWT_USERNAME = Claims.SUBJECT;

    /**
     * 用户头像
     */
    public static final String JWT_AVATAR = "avatar";

    /**
     * 创建时间
     */
    public static final String JWT_CREATED = "created";

    /**
     * 用户权限
     */
    public static final String JWT_AUTHORITIES = "authorities";

    /**
     * JWT密钥
     */
    public static final String SECRET = "abcdefghijklmnopqrstuvwxyz";

    /**
     * 用户标识
     */
    public static final String USER_KEY = "user_key";

    /**
     * 用户ID字段
     */
    public static final String DETAILS_USER_ID = "user_id";

    /**
     * 用户名字段
     */
    public static final String DETAILS_USERNAME = "username";

    /**
     * 授权信息字段
     */
    public static final String AUTHORIZATION_HEADER = "authorization";

    /**
     * 请求来源
     */
    public static final String FROM_SOURCE = "from-source";

    /**
     * 内部请求
     */
    public static final String INNER = "inner";

    /**
     * 用户标识
     */
    public static final String CURRENT_ID = "current_id";

    /**
     * 用户名称
     */
    public static final String CURRENT_USERNAME = "current_username";

    /**
     * 部门ID
     */
    public static final String CURRENT_DEPT_ID = "current_dept_id";

    /**
     * 空字符串
     */
    public static final String EMPTY = "";

    /**
     * RMI 远程方法调用
     */
    public static final String LOOKUP_RMI = "rmi:";

    /**
     * LDAP 远程方法调用
     */
    public static final String LOOKUP_LDAP = "ldap:";

    /**
     * LDAPS 远程方法调用
     */
    public static final String LOOKUP_LDAPS = "ldaps:";

    /**
     * 定时任务白名单配置（仅允许访问的包名，如其他需要可以自行添加）
     */
    public static final String[] JOB_WHITELIST_STR = { "com.aidex" };

    /**
     * 定时任务违规的字符
     */
    public static final String[] JOB_ERROR_STR = { "java.net.URL", "javax.naming.InitialContext", "org.yaml.snakeyaml",
            "org.springframework", "org.apache", "com.aidex.common.utils.file" };

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    /**
     * 默认为空消息
     */
    public static final String DEFAULT_NULL_MESSAGE = "暂无数据";
    /**
     * 默认成功消息
     */
    public static final String DEFAULT_SUCCESS_MESSAGE = "操作成功";
    /**
     * 默认失败消息
     */
    public static final String DEFAULT_FAILURE_MESSAGE = "操作失败";

    /**
     * 树的ID串分隔符
     */
    public static final String TREE_ROOT = "0";

    /**
     * 树的ID串分隔符
     */
    public static final String TREE_IDS_SPLIT_CHART = "/";

    /**
     * 分隔符
     */
    public static final String COMMON_SPLIT_CHART = ",";

    /**
     * 树的ID串分隔符
     */
    public static final String TREE_LEAF_Y = "y";

    /**
     * 树的ID串分隔符
     */
    public static final String TREE_LEAF_N = "n";

    /**
     * 日志操作类型
     */
    public enum OpType {
        login, insert, delete, update, select, logout
    }

    /**
     * 登录用户编号 redis key
     */
    public static final String LOGIN_USERID_KEY = "login_userid:";

    /**
     * 注册
     */
    public static final String ATTACH_SAVE_TYPE_DISK = "Disk";

    /**
     * 网站标题名称
     */
    public static final String SYSTEM_TITLE_KEY = "sys.index.title";

    /**
     * 网站显示LOGO
     */
    public static final String SYSTEM_LOGO_KEY = "sys.index.logo";

    /**
     * 网站登录框背景图
     */
    public static final String SYSTEM_LOGIN_BOX_BG_KEY = "sys.login.box.bg";

    /**
     * 网站登录背景图
     */
    public static final String SYSTEM_LOGIN_CONTAINER_BG_KEY = "sys.login.container.bg";

    /**
     * 网站登录短信验证码功能
     */
    public static final String SYSTEM_LOGIN_SMS_OFF_KEY = "sys.login.sms.onOff";

    /**
     * 网站登录短信验证码长度
     */
    public static final String SYSTEM_LOGIN_SMS_LENGTH_KEY = "sys.login.sms.length";

    /**
     * 网站登录短信验证码时长
     */
    public static final String SYSTEM_LOGIN_SMS_EXPIRE_KEY = "sys.login.sms.expire";

    /**
     * 审核锁定状态过期时长(单位：分钟)
     */
    public static final String SYSTEM_AUDIT_LOCK_EXPIRE_KEY = "sys.audit.lock.expire";

    /**
     * 前置短信验证码功能
     */
    public static final String SYSTEM_FRONT_SMS_OFF_KEY = "sys.front.sms.onOff";

    /**
     * 前置登录短信验证码功能
     */
    public static final String SYSTEM_FRONT_LOGIN_SMS_OFF_KEY = "sys.front.login.sms.onOff";

    /**
     * 短信发送重试最大次数
     */
    public static final String SYSTEM_SMS_RETRY_MAX_COUNT_KEY = "sys.sms.retry.max.count";

    /**
     * 初始化密码
     */
    public static final String SYSTEM_USER_INIT_PASSWORD_KEY = "sys.user.initPassword";

    /** 参数定义 */
    public static final String PARAMS_TREE_LEAF = "treeLeaf";
    public static final String PARAMS_TREE_LEVEL = "treeLevel";
    public static final String PARAMS_TREE_PATH = "treePath";
    public static final String PARAMS_SUBTITLE = "subtitle";
    public static final String PARAMS_AREA_PIN_YIN = "areaPinyin";
    public static final String PARAMS_DEPT_PIN_YIN = "deptPinyin";
    public static final String PARAMS_DEPT_TYPE = "deptType";
    public static final String PARAMS_DEPT_CLASSIFY = "deptClassify";
    public static final String PARAMS_DEPT_NATURE = "deptNature";

}
