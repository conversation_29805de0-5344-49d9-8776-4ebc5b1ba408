package com.aidex.auth;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 认证授权中心
 * 
 * <AUTHOR>
 */
@EnableDiscoveryClient
@SpringBootApplication
public class AuthApplication
{
    private static final Logger logger = LoggerFactory.getLogger(AuthApplication.class);

    public static void main(String[] args)
    {
        SpringApplication.run(AuthApplication.class, args);
        logger.info("(♥◠‿◠)ﾉﾞ  认证授权中心启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
