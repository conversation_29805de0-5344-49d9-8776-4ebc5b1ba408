package com.aidex.auth.controller;

import com.aidex.common.core.domain.AjaxResult;
import com.aidex.common.core.domain.model.LoginUser;
import com.aidex.common.core.domain.model.LoginBody;
import com.aidex.common.utils.StringUtils;
import com.aidex.framework.web.service.TokenService;
import com.aidex.framework.web.service.SysLoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

/**
 * token 控制
 * 
 * <AUTHOR>
 */
@RestController
public class TokenController
{
    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysLoginService sysLoginService;

    /**
     * 登录方法
     *
     * @param username 用户名
     * @param password 密码
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    @PostMapping("login")
    public AjaxResult login(String username, String password, String code, String uuid)
    {
        // 创建登录对象
        LoginBody loginBody = new LoginBody();
        loginBody.setUsername(username);
        loginBody.setPassword(password);
        loginBody.setCode(code);
        loginBody.setUuid(uuid);

        // 生成令牌
        String token = sysLoginService.login(loginBody);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("token", token);
        return ajax;
    }

    /**
     * 退出方法
     */
    @DeleteMapping("logout")
    public AjaxResult logout(HttpServletRequest request)
    {
        String token = tokenService.getToken(request);
        if (StringUtils.isNotEmpty(token))
        {
            String username = tokenService.getUsernameFromToken(token);
            // 删除用户缓存记录
            tokenService.delLoginUser(token);
            // 记录用户退出日志 - 这里可以后续添加日志记录逻辑
            // TODO: 添加退出日志记录
        }
        return AjaxResult.success();
    }

    /**
     * 刷新令牌
     */
    @PostMapping("refresh")
    public AjaxResult refresh(HttpServletRequest request)
    {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser))
        {
            // 刷新令牌有效期
            tokenService.refreshToken(loginUser);
            return AjaxResult.success();
        }
        return AjaxResult.success();
    }

    /**
     * 注册方法
     *
     * @param username 用户名
     * @param password 密码
     * @return 结果
     */
    @PostMapping("register")
    public AjaxResult register(String username, String password)
    {
        // 用户注册 - 这里可以后续添加注册逻辑
        // TODO: 添加用户注册逻辑
        return AjaxResult.success("注册功能待实现");
    }
}
