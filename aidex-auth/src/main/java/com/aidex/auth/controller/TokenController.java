package com.aidex.auth.controller;

import com.aidex.common.core.domain.AjaxResult;
import com.aidex.common.core.domain.model.LoginUser;
import com.aidex.common.utils.StringUtils;
import com.aidex.framework.web.service.TokenService;
import com.aidex.system.service.ISysLoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * token 控制
 * 
 * <AUTHOR>
 */
@RestController
public class TokenController
{
    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysLoginService sysLoginService;

    /**
     * 登录方法
     * 
     * @param username 用户名
     * @param password 密码
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    @PostMapping("login")
    public AjaxResult login(String username, String password, String code, String uuid)
    {
        // 生成令牌
        String token = sysLoginService.login(username, password, code, uuid);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("token", token);
        return ajax;
    }

    /**
     * 退出方法
     */
    @DeleteMapping("logout")
    public AjaxResult logout(HttpServletRequest request)
    {
        String token = tokenService.getToken(request);
        if (StringUtils.isNotEmpty(token))
        {
            String username = tokenService.getUsernameFromToken(token);
            // 删除用户缓存记录
            tokenService.delLoginUser(token);
            // 记录用户退出日志
            sysLoginService.recordLogininfor(username, "退出", "退出成功");
        }
        return AjaxResult.success();
    }

    /**
     * 刷新令牌
     */
    @PostMapping("refresh")
    public AjaxResult refresh(HttpServletRequest request)
    {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser))
        {
            // 刷新令牌有效期
            tokenService.refreshToken(loginUser);
            return AjaxResult.success();
        }
        return AjaxResult.success();
    }

    /**
     * 注册方法
     * 
     * @param username 用户名
     * @param password 密码
     * @return 结果
     */
    @PostMapping("register")
    public AjaxResult register(String username, String password)
    {
        // 用户注册
        sysLoginService.register(username, password);
        return AjaxResult.success();
    }
}
