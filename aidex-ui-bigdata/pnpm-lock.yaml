lockfileVersion: '6.0'

dependencies:
  d3-geo:
    specifier: ^3.1.0
    version: registry.npmmirror.com/d3-geo@3.1.0
  gsap:
    specifier: ^3.12.2
    version: registry.npmmirror.com/gsap@3.12.2
  three:
    specifier: ^0.155.0
    version: registry.npmmirror.com/three@0.155.0
  three.interactive:
    specifier: ^1.7.0
    version: registry.npmmirror.com/three.interactive@1.7.0(three@0.155.0)
  vue:
    specifier: ^3.2.47
    version: registry.npmmirror.com/vue@3.2.47
  vue-router:
    specifier: ^4.2.1
    version: registry.npmmirror.com/vue-router@4.2.1(vue@3.2.47)

devDependencies:
  '@types/three':
    specifier: ^0.152.0
    version: registry.npmmirror.com/@types/three@0.152.0
  '@vitejs/plugin-vue':
    specifier: ^4.1.0
    version: registry.npmmirror.com/@vitejs/plugin-vue@4.1.0(vite@4.3.2)(vue@3.2.47)
  sass:
    specifier: ^1.62.1
    version: registry.npmmirror.com/sass@1.62.1
  vite:
    specifier: ^4.3.2
    version: registry.npmmirror.com/vite@4.3.2(sass@1.62.1)

packages:

  registry.npmmirror.com/@babel/helper-string-parser@7.22.5:
    resolution: {integrity: sha512-mM4COjgZox8U+JcXQwPijIZLElkgEpO5rsERVDJTc2qfCDfERyob6k5WegS14SX18IIjv+XD+GrqNumY5JRCDw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.22.5.tgz}
    name: '@babel/helper-string-parser'
    version: 7.22.5
    engines: {node: '>=6.9.0'}

  registry.npmmirror.com/@babel/helper-validator-identifier@7.22.5:
    resolution: {integrity: sha512-aJXu+6lErq8ltp+JhkJUfk1MTGyuA4v7f3pA+BJ5HLfNC6nAQ0Cpi9uOquUj8Hehg0aUiHzWQbOVJGao6ztBAQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.5.tgz}
    name: '@babel/helper-validator-identifier'
    version: 7.22.5
    engines: {node: '>=6.9.0'}

  registry.npmmirror.com/@babel/parser@7.22.7:
    resolution: {integrity: sha512-7NF8pOkHP5o2vpmGgNGcfAeCvOYhGLyA3Z4eBQkT1RJlWu47n63bCs93QfJ2hIAFCil7L5P2IWhs1oToVgrL0Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/parser/-/parser-7.22.7.tgz}
    name: '@babel/parser'
    version: 7.22.7
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': registry.npmmirror.com/@babel/types@7.22.5

  registry.npmmirror.com/@babel/types@7.22.5:
    resolution: {integrity: sha512-zo3MIHGOkPOfoRXitsgHLjEXmlDaD/5KU1Uzuc9GNiZPhSqVxVRtxuPaSBZDsYZ9qV88AjtMtWW7ww98loJ9KA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/types/-/types-7.22.5.tgz}
    name: '@babel/types'
    version: 7.22.5
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': registry.npmmirror.com/@babel/helper-string-parser@7.22.5
      '@babel/helper-validator-identifier': registry.npmmirror.com/@babel/helper-validator-identifier@7.22.5
      to-fast-properties: registry.npmmirror.com/to-fast-properties@2.0.0

  registry.npmmirror.com/@esbuild/android-arm64@0.17.19:
    resolution: {integrity: sha512-KBMWvEZooR7+kzY0BtbTQn0OAYY7CsiydT63pVEaPtVYF0hXbUaOyZog37DKxK7NF3XacBJOpYT4adIJh+avxA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/android-arm64/-/android-arm64-0.17.19.tgz}
    name: '@esbuild/android-arm64'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/android-arm@0.17.19:
    resolution: {integrity: sha512-rIKddzqhmav7MSmoFCmDIb6e2W57geRsM94gV2l38fzhXMwq7hZoClug9USI2pFRGL06f4IOPHHpFNOkWieR8A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/android-arm/-/android-arm-0.17.19.tgz}
    name: '@esbuild/android-arm'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/android-x64@0.17.19:
    resolution: {integrity: sha512-uUTTc4xGNDT7YSArp/zbtmbhO0uEEK9/ETW29Wk1thYUJBz3IVnvgEiEwEa9IeLyvnpKrWK64Utw2bgUmDveww==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/android-x64/-/android-x64-0.17.19.tgz}
    name: '@esbuild/android-x64'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/darwin-arm64@0.17.19:
    resolution: {integrity: sha512-80wEoCfF/hFKM6WE1FyBHc9SfUblloAWx6FJkFWTWiCoht9Mc0ARGEM47e67W9rI09YoUxJL68WHfDRYEAvOhg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/darwin-arm64/-/darwin-arm64-0.17.19.tgz}
    name: '@esbuild/darwin-arm64'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/darwin-x64@0.17.19:
    resolution: {integrity: sha512-IJM4JJsLhRYr9xdtLytPLSH9k/oxR3boaUIYiHkAawtwNOXKE8KoU8tMvryogdcT8AU+Bflmh81Xn6Q0vTZbQw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.19.tgz}
    name: '@esbuild/darwin-x64'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/freebsd-arm64@0.17.19:
    resolution: {integrity: sha512-pBwbc7DufluUeGdjSU5Si+P3SoMF5DQ/F/UmTSb8HXO80ZEAJmrykPyzo1IfNbAoaqw48YRpv8shwd1NoI0jcQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.19.tgz}
    name: '@esbuild/freebsd-arm64'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/freebsd-x64@0.17.19:
    resolution: {integrity: sha512-4lu+n8Wk0XlajEhbEffdy2xy53dpR06SlzvhGByyg36qJw6Kpfk7cp45DR/62aPH9mtJRmIyrXAS5UWBrJT6TQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/freebsd-x64/-/freebsd-x64-0.17.19.tgz}
    name: '@esbuild/freebsd-x64'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-arm64@0.17.19:
    resolution: {integrity: sha512-ct1Tg3WGwd3P+oZYqic+YZF4snNl2bsnMKRkb3ozHmnM0dGWuxcPTTntAF6bOP0Sp4x0PjSF+4uHQ1xvxfRKqg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-arm64/-/linux-arm64-0.17.19.tgz}
    name: '@esbuild/linux-arm64'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-arm@0.17.19:
    resolution: {integrity: sha512-cdmT3KxjlOQ/gZ2cjfrQOtmhG4HJs6hhvm3mWSRDPtZ/lP5oe8FWceS10JaSJC13GBd4eH/haHnqf7hhGNLerA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-arm/-/linux-arm-0.17.19.tgz}
    name: '@esbuild/linux-arm'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-ia32@0.17.19:
    resolution: {integrity: sha512-w4IRhSy1VbsNxHRQpeGCHEmibqdTUx61Vc38APcsRbuVgK0OPEnQ0YD39Brymn96mOx48Y2laBQGqgZ0j9w6SQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.19.tgz}
    name: '@esbuild/linux-ia32'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-loong64@0.17.19:
    resolution: {integrity: sha512-2iAngUbBPMq439a+z//gE+9WBldoMp1s5GWsUSgqHLzLJ9WoZLZhpwWuym0u0u/4XmZ3gpHmzV84PonE+9IIdQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-loong64/-/linux-loong64-0.17.19.tgz}
    name: '@esbuild/linux-loong64'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-mips64el@0.17.19:
    resolution: {integrity: sha512-LKJltc4LVdMKHsrFe4MGNPp0hqDFA1Wpt3jE1gEyM3nKUvOiO//9PheZZHfYRfYl6AwdTH4aTcXSqBerX0ml4A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-mips64el/-/linux-mips64el-0.17.19.tgz}
    name: '@esbuild/linux-mips64el'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-ppc64@0.17.19:
    resolution: {integrity: sha512-/c/DGybs95WXNS8y3Ti/ytqETiW7EU44MEKuCAcpPto3YjQbyK3IQVKfF6nbghD7EcLUGl0NbiL5Rt5DMhn5tg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.19.tgz}
    name: '@esbuild/linux-ppc64'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-riscv64@0.17.19:
    resolution: {integrity: sha512-FC3nUAWhvFoutlhAkgHf8f5HwFWUL6bYdvLc/TTuxKlvLi3+pPzdZiFKSWz/PF30TB1K19SuCxDTI5KcqASJqA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-riscv64/-/linux-riscv64-0.17.19.tgz}
    name: '@esbuild/linux-riscv64'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-s390x@0.17.19:
    resolution: {integrity: sha512-IbFsFbxMWLuKEbH+7sTkKzL6NJmG2vRyy6K7JJo55w+8xDk7RElYn6xvXtDW8HCfoKBFK69f3pgBJSUSQPr+4Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-s390x/-/linux-s390x-0.17.19.tgz}
    name: '@esbuild/linux-s390x'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-x64@0.17.19:
    resolution: {integrity: sha512-68ngA9lg2H6zkZcyp22tsVt38mlhWde8l3eJLWkyLrp4HwMUr3c1s/M2t7+kHIhvMjglIBrFpncX1SzMckomGw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-x64/-/linux-x64-0.17.19.tgz}
    name: '@esbuild/linux-x64'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/netbsd-x64@0.17.19:
    resolution: {integrity: sha512-CwFq42rXCR8TYIjIfpXCbRX0rp1jo6cPIUPSaWwzbVI4aOfX96OXY8M6KNmtPcg7QjYeDmN+DD0Wp3LaBOLf4Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/netbsd-x64/-/netbsd-x64-0.17.19.tgz}
    name: '@esbuild/netbsd-x64'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/openbsd-x64@0.17.19:
    resolution: {integrity: sha512-cnq5brJYrSZ2CF6c35eCmviIN3k3RczmHz8eYaVlNasVqsNY+JKohZU5MKmaOI+KkllCdzOKKdPs762VCPC20g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/openbsd-x64/-/openbsd-x64-0.17.19.tgz}
    name: '@esbuild/openbsd-x64'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/sunos-x64@0.17.19:
    resolution: {integrity: sha512-vCRT7yP3zX+bKWFeP/zdS6SqdWB8OIpaRq/mbXQxTGHnIxspRtigpkUcDMlSCOejlHowLqII7K2JKevwyRP2rg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/sunos-x64/-/sunos-x64-0.17.19.tgz}
    name: '@esbuild/sunos-x64'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/win32-arm64@0.17.19:
    resolution: {integrity: sha512-yYx+8jwowUstVdorcMdNlzklLYhPxjniHWFKgRqH7IFlUEa0Umu3KuYplf1HUZZ422e3NU9F4LGb+4O0Kdcaag==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/win32-arm64/-/win32-arm64-0.17.19.tgz}
    name: '@esbuild/win32-arm64'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/win32-ia32@0.17.19:
    resolution: {integrity: sha512-eggDKanJszUtCdlVs0RB+h35wNlb5v4TWEkq4vZcmVt5u/HiDZrTXe2bWFQUez3RgNHwx/x4sk5++4NSSicKkw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/win32-ia32/-/win32-ia32-0.17.19.tgz}
    name: '@esbuild/win32-ia32'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/win32-x64@0.17.19:
    resolution: {integrity: sha512-lAhycmKnVOuRYNtRtatQR1LPQf2oYCkRGkSFnseDAKPl8lu5SOsK/e1sXe5a0Pc5kHIHe6P2I/ilntNv2xf3cA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/win32-x64/-/win32-x64-0.17.19.tgz}
    name: '@esbuild/win32-x64'
    version: 0.17.19
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@tweenjs/tween.js@18.6.4:
    resolution: {integrity: sha512-lB9lMjuqjtuJrx7/kOkqQBtllspPIN+96OvTCeJ2j5FEzinoAXTdAMFnDAQT1KVPRlnYfBrqxtqP66vDM40xxQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@tweenjs/tween.js/-/tween.js-18.6.4.tgz}
    name: '@tweenjs/tween.js'
    version: 18.6.4
    dev: true

  registry.npmmirror.com/@types/stats.js@0.17.0:
    resolution: {integrity: sha512-9w+a7bR8PeB0dCT/HBULU2fMqf6BAzvKbxFboYhmDtDkKPiyXYbjoe2auwsXlEFI7CFNMF1dCv3dFH5Poy9R1w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/stats.js/-/stats.js-0.17.0.tgz}
    name: '@types/stats.js'
    version: 0.17.0
    dev: true

  registry.npmmirror.com/@types/three@0.152.0:
    resolution: {integrity: sha512-9QdaV5bfZEqeQi0xkXLdnoJt7lgYZbppdBAgJSWRicdtZoCYJ34nS2QkdeuzXt+UXExofk4OWqMzdX71HeDOVg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/three/-/three-0.152.0.tgz}
    name: '@types/three'
    version: 0.152.0
    dependencies:
      '@tweenjs/tween.js': registry.npmmirror.com/@tweenjs/tween.js@18.6.4
      '@types/stats.js': registry.npmmirror.com/@types/stats.js@0.17.0
      '@types/webxr': registry.npmmirror.com/@types/webxr@0.5.2
      fflate: registry.npmmirror.com/fflate@0.6.10
      lil-gui: registry.npmmirror.com/lil-gui@0.17.0
    dev: true

  registry.npmmirror.com/@types/webxr@0.5.2:
    resolution: {integrity: sha512-szL74BnIcok9m7QwYtVmQ+EdIKwbjPANudfuvDrAF8Cljg9MKUlIoc1w5tjj9PMpeSH3U1Xnx//czQybJ0EfSw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/webxr/-/webxr-0.5.2.tgz}
    name: '@types/webxr'
    version: 0.5.2
    dev: true

  registry.npmmirror.com/@vitejs/plugin-vue@4.1.0(vite@4.3.2)(vue@3.2.47):
    resolution: {integrity: sha512-++9JOAFdcXI3lyer9UKUV4rfoQ3T1RN8yDqoCLar86s0xQct5yblxAE+yWgRnU5/0FOlVCpTZpYSBV/bGWrSrQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vitejs/plugin-vue/-/plugin-vue-4.1.0.tgz}
    id: registry.npmmirror.com/@vitejs/plugin-vue/4.1.0
    name: '@vitejs/plugin-vue'
    version: 4.1.0
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      vite: ^4.0.0
      vue: ^3.2.25
    dependencies:
      vite: registry.npmmirror.com/vite@4.3.2(sass@1.62.1)
      vue: registry.npmmirror.com/vue@3.2.47
    dev: true

  registry.npmmirror.com/@vue/compiler-core@3.2.47:
    resolution: {integrity: sha512-p4D7FDnQb7+YJmO2iPEv0SQNeNzcbHdGByJDsT4lynf63AFkOTFN07HsiRSvjGo0QrxR/o3d0hUyNCUnBU2Tig==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/compiler-core/-/compiler-core-3.2.47.tgz}
    name: '@vue/compiler-core'
    version: 3.2.47
    dependencies:
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.22.7
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.2.47
      estree-walker: registry.npmmirror.com/estree-walker@2.0.2
      source-map: registry.npmmirror.com/source-map@0.6.1

  registry.npmmirror.com/@vue/compiler-dom@3.2.47:
    resolution: {integrity: sha512-dBBnEHEPoftUiS03a4ggEig74J2YBZ2UIeyfpcRM2tavgMWo4bsEfgCGsu+uJIL/vax9S+JztH8NmQerUo7shQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.2.47.tgz}
    name: '@vue/compiler-dom'
    version: 3.2.47
    dependencies:
      '@vue/compiler-core': registry.npmmirror.com/@vue/compiler-core@3.2.47
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.2.47

  registry.npmmirror.com/@vue/compiler-sfc@3.2.47:
    resolution: {integrity: sha512-rog05W+2IFfxjMcFw10tM9+f7i/+FFpZJJ5XHX72NP9eC2uRD+42M3pYcQqDXVYoj74kHMSEdQ/WmCjt8JFksQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.2.47.tgz}
    name: '@vue/compiler-sfc'
    version: 3.2.47
    dependencies:
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.22.7
      '@vue/compiler-core': registry.npmmirror.com/@vue/compiler-core@3.2.47
      '@vue/compiler-dom': registry.npmmirror.com/@vue/compiler-dom@3.2.47
      '@vue/compiler-ssr': registry.npmmirror.com/@vue/compiler-ssr@3.2.47
      '@vue/reactivity-transform': registry.npmmirror.com/@vue/reactivity-transform@3.2.47
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.2.47
      estree-walker: registry.npmmirror.com/estree-walker@2.0.2
      magic-string: registry.npmmirror.com/magic-string@0.25.9
      postcss: registry.npmmirror.com/postcss@8.4.25
      source-map: registry.npmmirror.com/source-map@0.6.1

  registry.npmmirror.com/@vue/compiler-ssr@3.2.47:
    resolution: {integrity: sha512-wVXC+gszhulcMD8wpxMsqSOpvDZ6xKXSVWkf50Guf/S+28hTAXPDYRTbLQ3EDkOP5Xz/+SY37YiwDquKbJOgZw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/compiler-ssr/-/compiler-ssr-3.2.47.tgz}
    name: '@vue/compiler-ssr'
    version: 3.2.47
    dependencies:
      '@vue/compiler-dom': registry.npmmirror.com/@vue/compiler-dom@3.2.47
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.2.47

  registry.npmmirror.com/@vue/devtools-api@6.5.0:
    resolution: {integrity: sha512-o9KfBeaBmCKl10usN4crU53fYtC1r7jJwdGKjPT24t348rHxgfpZ0xL3Xm/gLUYnc0oTp8LAmrxOeLyu6tbk2Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.5.0.tgz}
    name: '@vue/devtools-api'
    version: 6.5.0
    dev: false

  registry.npmmirror.com/@vue/reactivity-transform@3.2.47:
    resolution: {integrity: sha512-m8lGXw8rdnPVVIdIFhf0LeQ/ixyHkH5plYuS83yop5n7ggVJU+z5v0zecwEnX7fa7HNLBhh2qngJJkxpwEEmYA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/reactivity-transform/-/reactivity-transform-3.2.47.tgz}
    name: '@vue/reactivity-transform'
    version: 3.2.47
    dependencies:
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.22.7
      '@vue/compiler-core': registry.npmmirror.com/@vue/compiler-core@3.2.47
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.2.47
      estree-walker: registry.npmmirror.com/estree-walker@2.0.2
      magic-string: registry.npmmirror.com/magic-string@0.25.9

  registry.npmmirror.com/@vue/reactivity@3.2.47:
    resolution: {integrity: sha512-7khqQ/75oyyg+N/e+iwV6lpy1f5wq759NdlS1fpAhFXa8VeAIKGgk2E/C4VF59lx5b+Ezs5fpp/5WsRYXQiKxQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/reactivity/-/reactivity-3.2.47.tgz}
    name: '@vue/reactivity'
    version: 3.2.47
    dependencies:
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.2.47

  registry.npmmirror.com/@vue/runtime-core@3.2.47:
    resolution: {integrity: sha512-RZxbLQIRB/K0ev0K9FXhNbBzT32H9iRtYbaXb0ZIz2usLms/D55dJR2t6cIEUn6vyhS3ALNvNthI+Q95C+NOpA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/runtime-core/-/runtime-core-3.2.47.tgz}
    name: '@vue/runtime-core'
    version: 3.2.47
    dependencies:
      '@vue/reactivity': registry.npmmirror.com/@vue/reactivity@3.2.47
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.2.47

  registry.npmmirror.com/@vue/runtime-dom@3.2.47:
    resolution: {integrity: sha512-ArXrFTjS6TsDei4qwNvgrdmHtD930KgSKGhS5M+j8QxXrDJYLqYw4RRcDy1bz1m1wMmb6j+zGLifdVHtkXA7gA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/runtime-dom/-/runtime-dom-3.2.47.tgz}
    name: '@vue/runtime-dom'
    version: 3.2.47
    dependencies:
      '@vue/runtime-core': registry.npmmirror.com/@vue/runtime-core@3.2.47
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.2.47
      csstype: registry.npmmirror.com/csstype@2.6.21

  registry.npmmirror.com/@vue/server-renderer@3.2.47(vue@3.2.47):
    resolution: {integrity: sha512-dN9gc1i8EvmP9RCzvneONXsKfBRgqFeFZLurmHOveL7oH6HiFXJw5OGu294n1nHc/HMgTy6LulU/tv5/A7f/LA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/server-renderer/-/server-renderer-3.2.47.tgz}
    id: registry.npmmirror.com/@vue/server-renderer/3.2.47
    name: '@vue/server-renderer'
    version: 3.2.47
    peerDependencies:
      vue: 3.2.47
    dependencies:
      '@vue/compiler-ssr': registry.npmmirror.com/@vue/compiler-ssr@3.2.47
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.2.47
      vue: registry.npmmirror.com/vue@3.2.47

  registry.npmmirror.com/@vue/shared@3.2.47:
    resolution: {integrity: sha512-BHGyyGN3Q97EZx0taMQ+OLNuZcW3d37ZEVmEAyeoA9ERdGvm9Irc/0Fua8SNyOtV1w6BS4q25wbMzJujO9HIfQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/shared/-/shared-3.2.47.tgz}
    name: '@vue/shared'
    version: 3.2.47

  registry.npmmirror.com/anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/anymatch/-/anymatch-3.1.3.tgz}
    name: anymatch
    version: 3.1.3
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: registry.npmmirror.com/normalize-path@3.0.0
      picomatch: registry.npmmirror.com/picomatch@2.3.1
    dev: true

  registry.npmmirror.com/binary-extensions@2.2.0:
    resolution: {integrity: sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/binary-extensions/-/binary-extensions-2.2.0.tgz}
    name: binary-extensions
    version: 2.2.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/braces@3.0.2:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/braces/-/braces-3.0.2.tgz}
    name: braces
    version: 3.0.2
    engines: {node: '>=8'}
    dependencies:
      fill-range: registry.npmmirror.com/fill-range@7.0.1
    dev: true

  registry.npmmirror.com/chokidar@3.5.3:
    resolution: {integrity: sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz}
    name: chokidar
    version: 3.5.3
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: registry.npmmirror.com/anymatch@3.1.3
      braces: registry.npmmirror.com/braces@3.0.2
      glob-parent: registry.npmmirror.com/glob-parent@5.1.2
      is-binary-path: registry.npmmirror.com/is-binary-path@2.1.0
      is-glob: registry.npmmirror.com/is-glob@4.0.3
      normalize-path: registry.npmmirror.com/normalize-path@3.0.0
      readdirp: registry.npmmirror.com/readdirp@3.6.0
    optionalDependencies:
      fsevents: registry.npmmirror.com/fsevents@2.3.2
    dev: true

  registry.npmmirror.com/csstype@2.6.21:
    resolution: {integrity: sha512-Z1PhmomIfypOpoMjRQB70jfvy/wxT50qW08YXO5lMIJkrdq4yOTR+AW7FqutScmB9NkLwxo+jU+kZLbofZZq/w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/csstype/-/csstype-2.6.21.tgz}
    name: csstype
    version: 2.6.21

  registry.npmmirror.com/d3-array@3.2.4:
    resolution: {integrity: sha512-tdQAmyA18i4J7wprpYq8ClcxZy3SC31QMeByyCFyRt7BVHdREQZ5lpzoe5mFEYZUWe+oq8HBvk9JjpibyEV4Jg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/d3-array/-/d3-array-3.2.4.tgz}
    name: d3-array
    version: 3.2.4
    engines: {node: '>=12'}
    dependencies:
      internmap: registry.npmmirror.com/internmap@2.0.3
    dev: false

  registry.npmmirror.com/d3-geo@3.1.0:
    resolution: {integrity: sha512-JEo5HxXDdDYXCaWdwLRt79y7giK8SbhZJbFWXqbRTolCHFI5jRqteLzCsq51NKbUoX0PjBVSohxrx+NoOUujYA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/d3-geo/-/d3-geo-3.1.0.tgz}
    name: d3-geo
    version: 3.1.0
    engines: {node: '>=12'}
    dependencies:
      d3-array: registry.npmmirror.com/d3-array@3.2.4
    dev: false

  registry.npmmirror.com/esbuild@0.17.19:
    resolution: {integrity: sha512-XQ0jAPFkK/u3LcVRcvVHQcTIqD6E2H1fvZMA5dQPSOWb3suUbWbfbRf94pjc0bNzRYLfIrDRQXr7X+LHIm5oHw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild/-/esbuild-0.17.19.tgz}
    name: esbuild
    version: 0.17.19
    engines: {node: '>=12'}
    hasBin: true
    requiresBuild: true
    optionalDependencies:
      '@esbuild/android-arm': registry.npmmirror.com/@esbuild/android-arm@0.17.19
      '@esbuild/android-arm64': registry.npmmirror.com/@esbuild/android-arm64@0.17.19
      '@esbuild/android-x64': registry.npmmirror.com/@esbuild/android-x64@0.17.19
      '@esbuild/darwin-arm64': registry.npmmirror.com/@esbuild/darwin-arm64@0.17.19
      '@esbuild/darwin-x64': registry.npmmirror.com/@esbuild/darwin-x64@0.17.19
      '@esbuild/freebsd-arm64': registry.npmmirror.com/@esbuild/freebsd-arm64@0.17.19
      '@esbuild/freebsd-x64': registry.npmmirror.com/@esbuild/freebsd-x64@0.17.19
      '@esbuild/linux-arm': registry.npmmirror.com/@esbuild/linux-arm@0.17.19
      '@esbuild/linux-arm64': registry.npmmirror.com/@esbuild/linux-arm64@0.17.19
      '@esbuild/linux-ia32': registry.npmmirror.com/@esbuild/linux-ia32@0.17.19
      '@esbuild/linux-loong64': registry.npmmirror.com/@esbuild/linux-loong64@0.17.19
      '@esbuild/linux-mips64el': registry.npmmirror.com/@esbuild/linux-mips64el@0.17.19
      '@esbuild/linux-ppc64': registry.npmmirror.com/@esbuild/linux-ppc64@0.17.19
      '@esbuild/linux-riscv64': registry.npmmirror.com/@esbuild/linux-riscv64@0.17.19
      '@esbuild/linux-s390x': registry.npmmirror.com/@esbuild/linux-s390x@0.17.19
      '@esbuild/linux-x64': registry.npmmirror.com/@esbuild/linux-x64@0.17.19
      '@esbuild/netbsd-x64': registry.npmmirror.com/@esbuild/netbsd-x64@0.17.19
      '@esbuild/openbsd-x64': registry.npmmirror.com/@esbuild/openbsd-x64@0.17.19
      '@esbuild/sunos-x64': registry.npmmirror.com/@esbuild/sunos-x64@0.17.19
      '@esbuild/win32-arm64': registry.npmmirror.com/@esbuild/win32-arm64@0.17.19
      '@esbuild/win32-ia32': registry.npmmirror.com/@esbuild/win32-ia32@0.17.19
      '@esbuild/win32-x64': registry.npmmirror.com/@esbuild/win32-x64@0.17.19
    dev: true

  registry.npmmirror.com/estree-walker@2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz}
    name: estree-walker
    version: 2.0.2

  registry.npmmirror.com/fflate@0.6.10:
    resolution: {integrity: sha512-IQrh3lEPM93wVCEczc9SaAOvkmcoQn/G8Bo1e8ZPlY3X3bnAxWaBdvTdvM1hP62iZp0BXWDy4vTAy4fF0+Dlpg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fflate/-/fflate-0.6.10.tgz}
    name: fflate
    version: 0.6.10
    dev: true

  registry.npmmirror.com/fill-range@7.0.1:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fill-range/-/fill-range-7.0.1.tgz}
    name: fill-range
    version: 7.0.1
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: registry.npmmirror.com/to-regex-range@5.0.1
    dev: true

  registry.npmmirror.com/fsevents@2.3.2:
    resolution: {integrity: sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fsevents/-/fsevents-2.3.2.tgz}
    name: fsevents
    version: 2.3.2
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz}
    name: glob-parent
    version: 5.1.2
    engines: {node: '>= 6'}
    dependencies:
      is-glob: registry.npmmirror.com/is-glob@4.0.3
    dev: true

  registry.npmmirror.com/gsap@3.12.2:
    resolution: {integrity: sha512-EkYnpG8qHgYBFAwsgsGEqvT1WUidX0tt/ijepx7z8EUJHElykg91RvW1XbkT59T0gZzzszOpjQv7SE41XuIXyQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/gsap/-/gsap-3.12.2.tgz}
    name: gsap
    version: 3.12.2
    dev: false

  registry.npmmirror.com/immutable@4.3.1:
    resolution: {integrity: sha512-lj9cnmB/kVS0QHsJnYKD1uo3o39nrbKxszjnqS9Fr6NB7bZzW45U6WSGBPKXDL/CvDKqDNPA4r3DoDQ8GTxo2A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/immutable/-/immutable-4.3.1.tgz}
    name: immutable
    version: 4.3.1
    dev: true

  registry.npmmirror.com/internmap@2.0.3:
    resolution: {integrity: sha512-5Hh7Y1wQbvY5ooGgPbDaL5iYLAPzMTUrjMulskHLH6wnv/A+1q5rgEaiuqEjB+oxGXIVZs1FF+R/KPN3ZSQYYg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/internmap/-/internmap-2.0.3.tgz}
    name: internmap
    version: 2.0.3
    engines: {node: '>=12'}
    dev: false

  registry.npmmirror.com/is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-binary-path/-/is-binary-path-2.1.0.tgz}
    name: is-binary-path
    version: 2.1.0
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: registry.npmmirror.com/binary-extensions@2.2.0
    dev: true

  registry.npmmirror.com/is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz}
    name: is-extglob
    version: 2.1.1
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz}
    name: is-glob
    version: 4.0.3
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: registry.npmmirror.com/is-extglob@2.1.1
    dev: true

  registry.npmmirror.com/is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz}
    name: is-number
    version: 7.0.0
    engines: {node: '>=0.12.0'}
    dev: true

  registry.npmmirror.com/lil-gui@0.17.0:
    resolution: {integrity: sha512-MVBHmgY+uEbmJNApAaPbtvNh1RCAeMnKym82SBjtp5rODTYKWtM+MXHCifLe2H2Ti1HuBGBtK/5SyG4ShQ3pUQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lil-gui/-/lil-gui-0.17.0.tgz}
    name: lil-gui
    version: 0.17.0
    dev: true

  registry.npmmirror.com/magic-string@0.25.9:
    resolution: {integrity: sha512-RmF0AsMzgt25qzqqLc1+MbHmhdx0ojF2Fvs4XnOqz2ZOBXzzkEwc/dJQZCYHAn7v1jbVOjAZfK8msRn4BxO4VQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/magic-string/-/magic-string-0.25.9.tgz}
    name: magic-string
    version: 0.25.9
    dependencies:
      sourcemap-codec: registry.npmmirror.com/sourcemap-codec@1.4.8

  registry.npmmirror.com/nanoid@3.3.6:
    resolution: {integrity: sha512-BGcqMMJuToF7i1rt+2PWSNVnWIkGCU78jBG3RxO/bZlnZPK2Cmi2QaffxGO/2RvWi9sL+FAiRiXMgsyxQ1DIDA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/nanoid/-/nanoid-3.3.6.tgz}
    name: nanoid
    version: 3.3.6
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  registry.npmmirror.com/normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz}
    name: normalize-path
    version: 3.0.0
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/picocolors@1.0.0:
    resolution: {integrity: sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/picocolors/-/picocolors-1.0.0.tgz}
    name: picocolors
    version: 1.0.0

  registry.npmmirror.com/picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz}
    name: picomatch
    version: 2.3.1
    engines: {node: '>=8.6'}
    dev: true

  registry.npmmirror.com/postcss@8.4.25:
    resolution: {integrity: sha512-7taJ/8t2av0Z+sQEvNzCkpDynl0tX3uJMCODi6nT3PfASC7dYCWV9aQ+uiCf+KBD4SEFcu+GvJdGdwzQ6OSjCw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/postcss/-/postcss-8.4.25.tgz}
    name: postcss
    version: 8.4.25
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: registry.npmmirror.com/nanoid@3.3.6
      picocolors: registry.npmmirror.com/picocolors@1.0.0
      source-map-js: registry.npmmirror.com/source-map-js@1.0.2

  registry.npmmirror.com/readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz}
    name: readdirp
    version: 3.6.0
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: registry.npmmirror.com/picomatch@2.3.1
    dev: true

  registry.npmmirror.com/rollup@3.26.2:
    resolution: {integrity: sha512-6umBIGVz93er97pMgQO08LuH3m6PUb3jlDUUGFsNJB6VgTCUaDFpupf5JfU30529m/UKOgmiX+uY6Sx8cOYpLA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/rollup/-/rollup-3.26.2.tgz}
    name: rollup
    version: 3.26.2
    engines: {node: '>=14.18.0', npm: '>=8.0.0'}
    hasBin: true
    optionalDependencies:
      fsevents: registry.npmmirror.com/fsevents@2.3.2
    dev: true

  registry.npmmirror.com/sass@1.62.1:
    resolution: {integrity: sha512-NHpxIzN29MXvWiuswfc1W3I0N8SXBd8UR26WntmDlRYf0bSADnwnOjsyMZ3lMezSlArD33Vs3YFhp7dWvL770A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/sass/-/sass-1.62.1.tgz}
    name: sass
    version: 1.62.1
    engines: {node: '>=14.0.0'}
    hasBin: true
    dependencies:
      chokidar: registry.npmmirror.com/chokidar@3.5.3
      immutable: registry.npmmirror.com/immutable@4.3.1
      source-map-js: registry.npmmirror.com/source-map-js@1.0.2
    dev: true

  registry.npmmirror.com/source-map-js@1.0.2:
    resolution: {integrity: sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/source-map-js/-/source-map-js-1.0.2.tgz}
    name: source-map-js
    version: 1.0.2
    engines: {node: '>=0.10.0'}

  registry.npmmirror.com/source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz}
    name: source-map
    version: 0.6.1
    engines: {node: '>=0.10.0'}

  registry.npmmirror.com/sourcemap-codec@1.4.8:
    resolution: {integrity: sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz}
    name: sourcemap-codec
    version: 1.4.8
    deprecated: Please use @jridgewell/sourcemap-codec instead

  registry.npmmirror.com/three.interactive@1.7.0(three@0.155.0):
    resolution: {integrity: sha512-WuJijcJUT8cxiU3iP7FbqjN9EtU4xuVH/YR+jmEV4dG5ijNE8KeZcQtFRuK2dlJk4AGwCFte1GPUxAR7hYyo3g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/three.interactive/-/three.interactive-1.7.0.tgz}
    id: registry.npmmirror.com/three.interactive/1.7.0
    name: three.interactive
    version: 1.7.0
    peerDependencies:
      three: '>= 0.122.0'
    dependencies:
      three: registry.npmmirror.com/three@0.155.0
    dev: false

  registry.npmmirror.com/three@0.155.0:
    resolution: {integrity: sha512-sNgCYmDijnIqkD/bMfk+1pHg3YzsxW7V2ChpuP6HCQ8NiZr3RufsXQr8M3SSUMjW4hG+sUk7YbyuY0DncaDTJQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/three/-/three-0.155.0.tgz}
    name: three
    version: 0.155.0
    dev: false

  registry.npmmirror.com/to-fast-properties@2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/to-fast-properties/-/to-fast-properties-2.0.0.tgz}
    name: to-fast-properties
    version: 2.0.0
    engines: {node: '>=4'}

  registry.npmmirror.com/to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz}
    name: to-regex-range
    version: 5.0.1
    engines: {node: '>=8.0'}
    dependencies:
      is-number: registry.npmmirror.com/is-number@7.0.0
    dev: true

  registry.npmmirror.com/vite@4.3.2(sass@1.62.1):
    resolution: {integrity: sha512-9R53Mf+TBoXCYejcL+qFbZde+eZveQLDYd9XgULILLC1a5ZwPaqgmdVpL8/uvw2BM/1TzetWjglwm+3RO+xTyw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vite/-/vite-4.3.2.tgz}
    id: registry.npmmirror.com/vite/4.3.2
    name: vite
    version: 4.3.2
    engines: {node: ^14.18.0 || >=16.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': '>= 14'
      less: '*'
      sass: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      sass:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
    dependencies:
      esbuild: registry.npmmirror.com/esbuild@0.17.19
      postcss: registry.npmmirror.com/postcss@8.4.25
      rollup: registry.npmmirror.com/rollup@3.26.2
      sass: registry.npmmirror.com/sass@1.62.1
    optionalDependencies:
      fsevents: registry.npmmirror.com/fsevents@2.3.2
    dev: true

  registry.npmmirror.com/vue-router@4.2.1(vue@3.2.47):
    resolution: {integrity: sha512-nW28EeifEp8Abc5AfmAShy5ZKGsGzjcnZ3L1yc2DYUo+MqbBClrRP9yda3dIekM4I50/KnEwo1wkBLf7kHH5Cw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vue-router/-/vue-router-4.2.1.tgz}
    id: registry.npmmirror.com/vue-router/4.2.1
    name: vue-router
    version: 4.2.1
    peerDependencies:
      vue: ^3.2.0
    dependencies:
      '@vue/devtools-api': registry.npmmirror.com/@vue/devtools-api@6.5.0
      vue: registry.npmmirror.com/vue@3.2.47
    dev: false

  registry.npmmirror.com/vue@3.2.47:
    resolution: {integrity: sha512-60188y/9Dc9WVrAZeUVSDxRQOZ+z+y5nO2ts9jWXSTkMvayiWxCWOWtBQoYjLeccfXkiiPZWAHcV+WTPhkqJHQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vue/-/vue-3.2.47.tgz}
    name: vue
    version: 3.2.47
    dependencies:
      '@vue/compiler-dom': registry.npmmirror.com/@vue/compiler-dom@3.2.47
      '@vue/compiler-sfc': registry.npmmirror.com/@vue/compiler-sfc@3.2.47
      '@vue/runtime-dom': registry.npmmirror.com/@vue/runtime-dom@3.2.47
      '@vue/server-renderer': registry.npmmirror.com/@vue/server-renderer@3.2.47(vue@3.2.47)
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.2.47
