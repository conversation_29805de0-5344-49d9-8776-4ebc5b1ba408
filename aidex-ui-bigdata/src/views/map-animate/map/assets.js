import { Resource } from "@/mini3d"
import { FileLoader } from "three"
import side from "@/assets/texture/side2.png"
import topNormal from "@/assets/texture/top_surface_normal_map2.jpg"
import rotationBorder1 from "@/assets/texture/rotationBorder1.png"
import rotationBorder2 from "@/assets/texture/rotationBorder3.png"
import guangquan1 from "@/assets/texture/guangquan01.png"
import guangquan2 from "@/assets/texture/guangquan02.png"
import quan from "@/assets/texture/quan.png"
import huiguang from "@/assets/texture/huiguang.png"
import grid from "@/assets/texture/grid.png"
import gridBlack from "@/assets/texture/gridBlack.png"
import gaoguang1 from "@/assets/texture/gaoguang1.png"
import flyLine from "@/assets/texture/flyLine2.png"
import arrow from "@/assets/texture/arrow.png"
import pathLine from "@/assets/texture/pathLine2.png"
import pathLine2 from "@/assets/texture/pathLine4.png"
import point from "@/assets/texture/point1.png"

export class Assets {
  constructor(onLoadCallback = null) {
    this.onLoadCallback = onLoadCallback
    this.init()
  }
  init() {
    this.instance = new Resource()
    // 添加Fileloader
    this.instance.addLoader(FileLoader, "FileLoader")
    // 资源加载进度
    this.instance.on("onProgress", (path, itemsLoaded, itemsTotal) => {
      let progress = (itemsLoaded / itemsTotal) * 100
      let bfb = progress.toFixed(2) + "%!"
      // console.log(bfb, path, itemsLoaded, itemsTotal)
    })
    // 资源加载完成事件
    this.instance.on("onLoad", () => {
      // console.log("资源加载完成")
      this.onLoadCallback && this.onLoadCallback()
    })
    // 资源加载
    let base_url = import.meta.env.BASE_URL
    let assets = [
      { type: "Texture", name: "grid", path: grid },
      { type: "Texture", name: "pathLine", path: pathLine2 },
      { type: "Texture", name: "pathLine2", path: pathLine },
      { type: "Texture", name: "flyLine", path: flyLine },
      { type: "Texture", name: "arrow", path: arrow },
      { type: "Texture", name: "gridBlack", path: gridBlack },
      { type: "Texture", name: "quan", path: quan },
      { type: "Texture", name: "gaoguang1", path: gaoguang1 },
      { type: "Texture", name: "huiguang", path: huiguang },
      { type: "Texture", name: "rotationBorder1", path: rotationBorder1 },
      { type: "Texture", name: "rotationBorder2", path: rotationBorder2 },
      { type: "Texture", name: "guangquan1", path: guangquan1 },
      { type: "Texture", name: "guangquan2", path: guangquan2 },
      { type: "Texture", name: "side", path: side },
      { type: "Texture", name: "topNormal", path: topNormal },
      { type: "Texture", name: "point", path: point },
      {
        type: "File",
        name: "chinaStorke",
        path: base_url + "assets/json/甘肃省-轮廓.json",
      },
      {
        type: "File",
        name: "lzStorke",
        path: base_url + "assets/json/兰州市-轮廓.json",
      },
      {
        type: "File",
        name: "lzMap",
        path: base_url + "assets/json/兰州市.json",
      },
      {
        type: "File",
        name: "jygStorke",
        path: base_url + "assets/json/嘉峪关-轮廓.json",
      },
      {
        type: "File",
        name: "jygMap",
        path: base_url + "assets/json/嘉峪关市.json",
      },
      {
        type: "File",
        name: "jcStorke",
        path: base_url + "assets/json/金昌市-轮廓.json",
      },
      {
        type: "File",
        name: "jcMap",
        path: base_url + "assets/json/金昌市.json",
      },
      {
        type: "File",
        name: "byStorke",
        path: base_url + "assets/json/白银市-轮廓.json",
      },
      {
        type: "File",
        name: "byMap",
        path: base_url + "assets/json/白银市.json",
      },
      {
        type: "File",
        name: "tsStorke",
        path: base_url + "assets/json/天水市-轮廓.json",
      },
      {
        type: "File",
        name: "tsMap",
        path: base_url + "assets/json/天水市.json",
      },
      {
        type: "File",
        name: "wwStorke",
        path: base_url + "assets/json/武威市-轮廓.json",
      },
      {
        type: "File",
        name: "wwMap",
        path: base_url + "assets/json/武威市.json",
      },
      {
        type: "File",
        name: "zyStorke",
        path: base_url + "assets/json/张掖市-轮廓.json",
      },
      {
        type: "File",
        name: "zyMap",
        path: base_url + "assets/json/张掖市.json",
      },
      {
        type: "File",
        name: "plStorke",
        path: base_url + "assets/json/平凉市-轮廓.json",
      },
      {
        type: "File",
        name: "plMap",
        path: base_url + "assets/json/平凉市.json",
      },
      {
        type: "File",
        name: "jqStorke",
        path: base_url + "assets/json/酒泉市-轮廓.json",
      },
      {
        type: "File",
        name: "jqMap",
        path: base_url + "assets/json/酒泉市.json",
      },
      {
        type: "File",
        name: "qyStorke",
        path: base_url + "assets/json/庆阳市-轮廓.json",
      },
      {
        type: "File",
        name: "qyMap",
        path: base_url + "assets/json/庆阳市.json",
      },
      {
        type: "File",
        name: "dxStorke",
        path: base_url + "assets/json/定西市-轮廓.json",
      },
      {
        type: "File",
        name: "dxMap",
        path: base_url + "assets/json/定西市.json",
      },
      {
        type: "File",
        name: "lnStorke",
        path: base_url + "assets/json/陇南市-轮廓.json",
      },
      {
        type: "File",
        name: "lnMap",
        path: base_url + "assets/json/陇南市.json",
      },
      {
        type: "File",
        name: "lxStorke",
        path: base_url + "assets/json/临夏回族自治州-轮廓.json",
      },
      {
        type: "File",
        name: "lxMap",
        path: base_url + "assets/json/临夏回族自治州.json",
      },
      {
        type: "File",
        name: "gnStorke",
        path: base_url + "assets/json/甘南藏族自治州-轮廓.json",
      },
      {
        type: "File",
        name: "gnMap",
        path: base_url + "assets/json/甘南藏族自治州.json",
      },


      {
        type: "File",
        name: "china",
        path: base_url + "assets/json/甘肃省.json",
      },
      {
        type: "File",
        name: "transportPath",
        path: base_url + "assets/json/运输路径.json",
      },
    ]
    // 资源加载
    this.instance.loadAll(assets)
  }
}
