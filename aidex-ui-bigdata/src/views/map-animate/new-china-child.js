import {
  Group,
  RepeatWrapping,
  LineBasicMaterial,
  Vector3,
  SpriteMaterial,
  Sprite,
  Color,
  Mesh,
  MeshBasicMaterial,
  DoubleSide,
  AdditiveBlending,
  BoxGeometry,
  PlaneGeometry,
  SRGBColorSpace,
  MathUtils,
} from "three"
import { ExtrudeMap } from "./map/extrudeMap"
import { getBoundBox, emptyObject, GradientShader } from "@/mini3d"
import { geoMercator } from "d3-geo"
import { gsap } from "gsap"
// import scatterData from "./map/scatter"
function sortByValue(data) {
  console.log("0000000")
  console.log(data)
  data.sort((a, b) => b.value - a.value)
  return data
}
export class ChildMap {
  constructor(parent, options) {
    this.parent = parent
    this.time = parent.time
    this.instance = new Group()
    this.instance.rotateX(-Math.PI / 2)
    this.instance.position.set(0, 0.2, 0)

    let defaultOptions = {
      adcode: 10000,
      center: [0, 0],
      centroid: [0, 0],
      childrenNum: 0,
      parentBoxSize: [1, 1], // 上级地图的尺寸
      mapData: {},
      geoProjectionCenter: [0, 0],
      geoProjectionScale: 480,
      geoProjectionTranslate: [0, 0],
    }
    this.options = Object.assign({}, defaultOptions, options)
    // 是否点击了
    this.clicked = false
    // 缩放值
    this.scale = 1
    // 地图的box大小
    this.boundBox = {}
    this.allProvinceLabel = []
    this.allScatterLabel = []
    this.barLabelGroup = new Group()
    // 地图的区域数据
    this.areaData = []
    // 区域标签
    this.allAreaLabel = []
    // 区域标签组
    this.areaLabelGroup = new Group()
    // 区域点组
    this.areaPointGroup = new Group()
    // 光圈组
    this.gqGroup = new Group()
    this.instance.add(this.gqGroup)
    // 信息标签组
    this.allInfoLabel = []
    this.infoLabelGroup = new Group()
    this.instance.add(this.areaLabelGroup, this.areaPointGroup, this.barLabelGroup)
    // 事件元素
    this.eventElement = []
    this.pointEventElement = []
    this.init()
  }
  init() {
    console.log(this.options)
    this.createModel()
    if(this.options.childMapData && this.options.childMapData.length > 0){
      this.createBar(this.options.childMapData)
    }
    if (this.options.childrenNum == 0) {
      this.addLabel(this.options.childMapCountyData)
      this.addPointEvent()
    } else {
      this.addLabel()
      this.addEvent()
    }
  }
  createModel() {
    let { map } = this.createMap()
    this.setScale(map)
    map.setParent(this.instance)
  }
  // 创建省份
  createMap() {
    // 广东地图
    let mapJsonData = this.options.mapData
    let topNormal = this.parent.assets.instance.getResource("topNormal")
    topNormal.wrapS = topNormal.wrapT = RepeatWrapping

    // 地图线
    this.mapLineMaterial = new LineBasicMaterial({
      color: 0x2bc4dc,
      opacity: 0,
      transparent: true,
      fog: false,
    })
    let [top, side] = this.parent.createProvinceMaterial()
    let topMaterial = top.clone()
    topMaterial.opacity = 1
    let sideMaterial = side.clone()
    sideMaterial.opacity = 1
    let map = new ExtrudeMap(this.parent, {
      center: this.options.center,
      position: new Vector3(0, 0, 0.06),
      data: mapJsonData,
      depth: this.parent.depth,
      topFaceMaterial: topMaterial,
      sideMaterial: sideMaterial,
      lineMaterial: this.parent.mapLineMaterial,
      renderOrder: 9,
    })
    this.areaData = map.coordinates
    let { boxSize, box3 } = getBoundBox(map.mapGroup)
    map.mapGroup.children.map((group, index) => {
      group.children.map((mesh) => {
        if (mesh.type === "Mesh") {
          mesh.userData.type = "map"
          this.eventElement.push(mesh)
          this.parent.calcUv2(mesh.geometry, boxSize.x, boxSize.y, box3.min.x, box3.min.y)
        }
      })
    })

    return {
      map,
    }
  }

  addEvent() {
    let objectsHover = []

    const reset = (mesh) => {
      gsap.to(mesh.scale, {
        duration: 0.3,
        z: 1,
        onComplete: () => {
          mesh.traverse((obj) => {
            if (obj.isMesh) {
              obj.material[0].emissive.setHex(mesh.userData.materialEmissiveHex)
              obj.material[0].emissiveIntensity = 1
              obj.renderOrder = 9
            }
          })
        },
      })

      this.setLabelMove(mesh.userData.adcode, "down")
      this.setPointMove(mesh.userData.adcode, "down")
    }
    const move = (mesh) => {
      gsap.to(mesh.scale, {
        duration: 0.3,
        z: 1.5,
      })

      this.setLabelMove(mesh.userData.adcode)
      this.setPointMove(mesh.userData.adcode)

      mesh.traverse((obj) => {
        if (obj.isMesh) {
          obj.material[0].emissive.setHex(0x0b112d)
          obj.material[0].emissiveIntensity = 1.5
          obj.renderOrder = 21
        }
      })
    }

    // 循环添加事件
    this.eventElement.map((mesh) => {
      this.parent.interactionManager.add(mesh)
      mesh.addEventListener("mousedown", (event) => {
        if (this.clicked) return false
        this.clicked = true
        let userData = event.target.parent.userData
        this.parent.history.push(userData)
        this.parent.loadChildMap(userData)
      })
      mesh.addEventListener("mouseup", (ev) => {
        this.clicked = false
      })
      mesh.addEventListener("mouseover", (event) => {
        if (!objectsHover.includes(event.target.parent)) {
          objectsHover.push(event.target.parent)
        }

        document.body.style.cursor = "pointer"
        move(event.target.parent)
      })
      mesh.addEventListener("mouseout", (event) => {
        objectsHover = objectsHover.filter((n) => n.userData.name !== event.target.parent.userData.name)
        if (objectsHover.length > 0) {
          const mesh = objectsHover[objectsHover.length - 1]
        }
        reset(event.target.parent)
        document.body.style.cursor = "default"
      })
    })
  }
  // 添加标点事件
  addPointEvent() {
    let objectsHover = []

    this.pointEventElement.map((mesh) => {
      this.parent.interactionManager.add(mesh)
      mesh.addEventListener("mousedown", (event) => {
        if (this.clicked) return false
        this.clicked = true
        let userData = event.target.userData

        this.allInfoLabel.map((label, index) => {
          label.hide()
          if (userData.index === index) {
            label.show()
            window.schoolPop(userData)
          }
        })
      })
      mesh.addEventListener("mouseup", (ev) => {
        this.clicked = false
      })
      mesh.addEventListener("mouseover", (event) => {
        if (!objectsHover.includes(event.target.parent)) {
          objectsHover.push(event.target.parent)
        }

        document.body.style.cursor = "pointer"
        let sprite = event.target
        sprite.material = this.pointHoverMaterial.clone()
      })
      mesh.addEventListener("mouseout", (event) => {
        objectsHover = objectsHover.filter((n) => n.userData.name !== event.target.parent.userData.name)
        if (objectsHover.length > 0) {
          const mesh = objectsHover[objectsHover.length - 1]
        }

        document.body.style.cursor = "default"
        let sprite = event.target
        sprite.material = this.pointDefaultMaterial.clone()
      })
    })
  }
  // 设置标签移动
  setLabelMove(adcode, type = "up") {
    ;[...this.allAreaLabel].map((label) => {
      if (label.userData.adcode === adcode) {
        gsap.to(label.position, {
          duration: 0.3,
          z: type === "up" ? label.userData.position[2] + 3 / this.scale : label.userData.position[2],
        })
      }
    })
  }
  // 设置点移动
  setPointMove(adcode, type = "up") {
    this.areaPointGroup.children.map((point) => {
      if (point.userData.adcode === adcode) {
        gsap.to(point.position, {
          duration: 0.3,
          z: type === "up" ? point.userData.position[2] + 3 / this.scale : point.userData.position[2],
        })
      }
    })
  }
  addLabel(childMapCountyData) {
    let areaData = null
    if (childMapCountyData != undefined) {
      areaData = childMapCountyData
    } else {
      areaData = this.areaData
    }
    // 贴图
    const texture = this.parent.assets.instance.getResource("point")

    const material = new SpriteMaterial({
      map: texture,
      color: 0xffffff,
      transparent: true,
      depthTest: false,
    })
    this.pointDefaultMaterial = material
    this.pointHoverMaterial = material.clone()
    this.pointHoverMaterial.color = new Color(0x00ffff)
    const sprite = new Sprite(material)
    sprite.renderOrder = 23
    areaData.map((item, index) => {
      let [x, y] = this.geoProjection(item.centroid)
      // 名称
      let nameLabel = this.labelNameStyle(item, index, new Vector3(x, -y, 0))
      this.allAreaLabel.push(nameLabel)
      // 信息
      let infoLabel = this.infoLabel(item, index, new Vector3(x, -y, 0))
      this.allInfoLabel.push(infoLabel)

      //点
      let areaPoint = sprite.clone()
      sprite.material = material.clone()
      areaPoint.position.set(x, -y, 0)
      areaPoint.userData.adcode = item.adcode

      areaPoint.userData.type = "point"
      areaPoint.userData.name = item.name
      areaPoint.userData.position = [x, -y, 0]
      areaPoint.userData.index = index
      this.areaPointGroup.add(areaPoint)
    })
    this.setNameScale()
    this.setInfoScale()
    this.setPointScale()
  }
  infoLabel(data, index, position) {
    let label3d = this.parent.label3d
    let label = label3d.create("", "info-point", true)

    label.init(
      ` <div class="info-point-wrap">
        <div class="info-point-wrap-inner">
          <div class="info-point-line">
            <div class="line"></div>
            <div class="line"></div>
            <div class="line"></div>
          </div>
          <div class="info-point-content">
            <div class="content-item"><span class="label">名称</span><span class="value">${data.name}</span></div>
            <div class="content-item"><span class="label">PM2.5</span><span class="value">100ug/m²</span></div>
            <div class="content-item"><span class="label">等级</span><span class="value">良好</span></div>
          </div>
        </div>
      </div>
    `,
      position
    )
    label3d.setLabelStyle(label, 0.06 / this.scale, "x")
    label.setParent(this.infoLabelGroup)
    label.hide()
    return label
  }
  // 城市标签
  labelNameStyle(data, index, position) {
    let label3d = this.parent.label3d
    let label = label3d.create("", "area-name-label", true)
    label.init(`<div class="area-name-label-wrap">${data.name}</div>`, position)
    label3d.setLabelStyle(label, 0.08 / this.scale, "x")
    label.setParent(this.areaLabelGroup)
    label.userData.adcode = data.adcode
    label.userData.position = [position.x, position.y, position.z]
    return label
  }

  calculateScale(parentBoxSize, boxSize) {
    let xScale = parentBoxSize[0] / boxSize[0]
    let yScale = parentBoxSize[1] / boxSize[1]
    let scale = Math.min(xScale, yScale)

    return scale
  }

  setScale(map) {
    let { parentBoxSize } = this.options
    let boundBox = getBoundBox(map.mapGroup)

    let scale = this.calculateScale(parentBoxSize, [boundBox.boxSize.x, boundBox.boxSize.y])
    // 子地图缩放到主地图大小
    map.mapGroup.scale.set(scale, scale, 1)
    let boundBox1 = getBoundBox(map.mapGroup)
    // 放大后，中心坐标有偏移，偏移了多少，就反向移动多少
    map.mapGroup.position.x = -boundBox1.center.x
    map.mapGroup.position.y = -boundBox1.center.y
    this.scale = scale
    this.boundBox = boundBox1
  }

  setNameScale() {
    this.areaLabelGroup.scale.set(this.scale, this.scale, this.scale)

    this.areaLabelGroup.position.x = -this.boundBox.center.x
    this.areaLabelGroup.position.y = -this.boundBox.center.y
    this.allAreaLabel.map((label) => {
      let z = (this.parent.depth + 0.4) / this.scale
      label.position.z = z

      label.position.y -= 1.5 / this.scale
      label.userData.position = [label.position.x, label.position.y, label.position.z]
    })
  }

  setPointScale() {
    this.areaPointGroup.scale.set(this.scale, this.scale, this.scale)

    this.areaPointGroup.position.x = -this.boundBox.center.x
    this.areaPointGroup.position.y = -this.boundBox.center.y
    this.areaPointGroup.children.map((point) => {
      let z = (this.parent.depth + 1.4) / this.scale
      point.position.z = z
      point.userData.position[2] = z

      point.scale.set(5 / this.scale, 5 / this.scale, 5 / this.scale)

      point.userData.position = [point.position.x, point.position.y, point.position.z]

      this.pointEventElement.push(point)
    })
  }

  setInfoScale() {
    this.infoLabelGroup.scale.set(this.scale, this.scale, this.scale)

    this.infoLabelGroup.position.x = -this.boundBox.center.x
    this.infoLabelGroup.position.y = -this.boundBox.center.y
    this.infoLabelGroup.children.map((point) => {
      let z = (this.parent.depth + 10) / this.scale
      point.position.z = z

      point.scale.set(0.06 / this.scale, 0.06 / this.scale, 0.06 / this.scale)
    })
  }
  setBarScale() {
    this.barLabelGroup.scale.set(this.scale, this.scale, this.scale)
    this.barLabelGroup.position.x = -this.boundBox.center.x
    this.barLabelGroup.position.y = -this.boundBox.center.y

    this.barGroup.scale.set(this.scale, this.scale, this.scale)
    this.barGroup.position.x = -this.boundBox.center.x
    this.barGroup.position.y = -this.boundBox.center.y
    this.barGroup.children.map((bar, i) => {
      let z = (this.parent.depth + 0.4) / this.scale
      bar.position.z = z
      bar.scale.set(1 / this.scale, 1 / this.scale, 1 / this.scale)
      this.barLabelGroup.children[i].scale.set(0.05 / this.scale, 0.05 / this.scale, 0.05 / this.scale)
      this.allbarLabel[i].position.z = z + bar.userData.geoHeight / this.scale
    })

    this.gqGroup.scale.set(this.scale, this.scale, this.scale)
    this.gqGroup.position.x = -this.boundBox.center.x
    this.gqGroup.position.y = -this.boundBox.center.y

    this.gqGroup.children.map((gq) => {
      let z = (this.parent.depth + 0.4) / this.scale
      gq.position.z = z

      gq.scale.set(2 / this.scale, 2 / this.scale, 2 / this.scale)
    })
  }
  // 创建柱状图
  createBar(mapData) {
    let self = this
    let label3d = this.parent.label3d
    // let data = this.areaData.map((item) => {
    //   item.value = MathUtils.randInt(20, 100)
    //   return item
    // })
    let data = sortByValue(mapData) //.filter((item, index) => index < 15);
    const barGroup = new Group()
    barGroup.name = "barGroup"
    this.barGroup = barGroup
    const factor = 7
    const height = 4 * factor
    const max = data[0].value

    this.allBar = []
    this.allBarMaterial = []
    this.allGuangquan = []
    this.allbarLabel = []
    this.allProvinceNameLabel = []
    const colors = ['#ffffff', '#FAEEA8', '#FF8748', '#FF4848', '#7650AA'];
    let colorIndex = 0
    data.map((item, index) => {
      // 网格
      let geoHeight = height * (item.value / max)
      // 材质
      const thresholds = [0, 5000, 15000, 20000, 5000000];
      for (let i = 0; i < thresholds.length; i++) {
        if (item.luqu < thresholds[i]) {
          colorIndex = i;
          break;
        }
      }

      let material = new MeshBasicMaterial({
        color: colors[colorIndex],
        transparent: true,
        opacity: 0,
        depthTest: false,
        fog: false,
      });
      // 设置渐变材质

      new GradientShader(material, {
        uColor1: index < 3 ? 0xfbdf88 : 0x50bbfe,
        uColor2: index < 3 ? 0xfbdf88 : 0x50bbfe,
        size: geoHeight,
        dir: "y",
      })
      const geo = new BoxGeometry(0.05 * factor, 0.05 * factor, geoHeight)
      // 上移
      geo.translate(0, 0, geoHeight / 2)
      const mesh = new Mesh(geo, material)
      mesh.renderOrder = 22
      let areaBar = mesh
      let [x, y] = this.geoProjection(item.centroid)
      areaBar.position.set(x, -y, this.parent.depth + 0.46)
      areaBar.scale.set(1, 1, 1)
      areaBar.userData.name = item.name
      areaBar.userData.adcode = item.adcode
      areaBar.userData.geoHeight = geoHeight
      areaBar.userData.position = [x, -y, this.parent.depth + 0.46]

      let guangQuan = this.createQuan()
      guangQuan.position.set(x, -y, this.parent.depth + 0.46)
      guangQuan.userData.name = item.name
      guangQuan.userData.adcode = item.adcode
      guangQuan.userData.position = [x, -y, this.parent.depth + 0.46]
      this.gqGroup.add(guangQuan)
      let hg = this.createHUIGUANG(geoHeight, index < 3 ? 0xfffef4 : 0x77fbf5)
      areaBar.add(...hg)

      barGroup.add(areaBar)
      let barLabel = labelStyle04(item, index, new Vector3(x, -y, this.parent.depth + 0.9 + geoHeight))
      this.allbarLabel.push(barLabel)
    })
    this.instance.add(barGroup)
    this.setBarScale()
    // 人口标签
    function labelStyle04(data, index, position) {
      let label = label3d.create("", "provinces-label-style03", true)
      label.init(
        `<div class="provinces-label-style03">
          <div>
            <div class="number luqu" style="display: block"><span class="value">${data.luqu}</span><span class="unit">人</span><span class="title">招生指标</span></div>
            <div class="number luqu1" style="display: none"><span class="value">${data.luqu}</span><span class="unit">人</span><span class="title">录取人数</span></div>
            <div class="number luqu2" style="display: none"><span class="value">${data.value}</span><span class="unit">人</span><span class="title">线上报名人数</span></div>
            <div class="number luqu3" style="display: none"><span class="value">${data.luqu}</span><span class="unit">人</span><span class="title">代报人数</span></div>
            <div class="number luqu4" style="display: none"><span class="value">${data.value}</span><span class="unit">人</span><span class="title">调剂人数</span></div>
            <div class="number luqu5" style="display: none"><span class="value">${data.luqu}</span><span class="unit">人</span><span class="title">政策照顾人数</span></div>
            <div class="number defaultNum" style="display: block"><span class="value">${data.value}</span><span class="unit">人</span><span class="title">报名人数</span></div>
          </div>
        </div>`,
        position
      )
      label3d.setLabelStyle(label, 0.05, "x")
      label.setParent(self.barLabelGroup)
      label.userData.adcode = data.adcode
      label.userData.position = [position.x, position.y, position.z]

      return label
    }
  }
  createHUIGUANG(h, color) {
    let geometry = new PlaneGeometry(1.5, h)
    geometry.translate(0, h / 2, 0)
    const texture = this.parent.assets.instance.getResource("huiguang")
    texture.colorSpace = SRGBColorSpace
    texture.wrapS = RepeatWrapping
    texture.wrapT = RepeatWrapping
    let material = new MeshBasicMaterial({
      color: color,
      map: texture,
      transparent: true,
      opacity: 0.4,
      depthWrite: false,

      side: DoubleSide,
      blending: AdditiveBlending,
    })
    let mesh = new Mesh(geometry, material)
    mesh.renderOrder = 23
    mesh.rotateX(Math.PI / 2)
    let mesh2 = mesh.clone()
    let mesh3 = mesh.clone()
    mesh2.rotateY((Math.PI / 180) * 60)
    mesh3.rotateY((Math.PI / 180) * 120)
    return [mesh, mesh2, mesh3]
  }
  createQuan() {
    const guangquan1 = this.parent.assets.instance.getResource("guangquan1")
    const guangquan2 = this.parent.assets.instance.getResource("guangquan2")
    let geometry = new PlaneGeometry(2, 2)

    let material1 = new MeshBasicMaterial({
      color: 0xffffff,
      map: guangquan1,
      alphaMap: guangquan1,
      opacity: 1,
      transparent: true,
      depthTest: false,
      fog: false,
      blending: AdditiveBlending,
    })
    let material2 = new MeshBasicMaterial({
      color: 0xffffff,
      map: guangquan2,
      alphaMap: guangquan2,
      opacity: 1,
      transparent: true,
      depthTest: false,
      fog: false,
      blending: AdditiveBlending,
    })
    let mesh1 = new Mesh(geometry, material1)
    let mesh2 = new Mesh(geometry, material2)
    mesh1.renderOrder = 24
    mesh2.renderOrder = 24

    mesh2.position.z -= 0.001
    mesh1.scale.set(1, 1, 1)
    mesh2.scale.set(1, 1, 1)
    this.quanGroup = new Group()
    this.quanGroup.add(mesh1, mesh2)

    this.time.on("tick", (delta) => {
      mesh1.rotation.z += delta * 2
    })
    return this.quanGroup
  }
  // 创建散点图
  createScatter() {
    let self = this
    let label3d = this.parent.label3d
    this.scatterGroup = new Group()
    this.scatterLableGroup = new Group()
    this.instance.add(this.scatterGroup, this.scatterLableGroup)
    // 贴图
    const texture = this.parent.assets.instance.getResource("arrow")
    const material = new SpriteMaterial({
      map: texture,
      color: 0xffff00,
      transparent: true,
      depthTest: false,
    })

    let scatterAllData = sortByValue(scatterData)
    let max = scatterAllData[0].value
    scatterAllData.map((data, index) => {
      const sprite = new Sprite(material)
      sprite.renderOrder = 23
      let scale = (data.value / max) * 2
      console.log(scale)
      sprite.scale.set(scale, scale, scale)
      let [x, y] = this.geoProjection([data.lng, data.lat])
      sprite.position.set(x, -y, this.depth + 0.41)
      sprite.userData.adcode = data.adcode
      sprite.userData.position = [x, -y, this.depth + 0.41]
      this.scatterGroup.add(sprite)

      let label = labelScatterStyle(data, index, new Vector3(x, -y, this.depth + 0.41))
      this.scatterLableGroup.add(label)
      this.allScatterLabel.push(label)
    })

    // 散点标签
    function labelScatterStyle(data, index, position) {
      let label = label3d.create("", "scatter-name-label", true)
      label.init(
        `<div class="scatter-name-label"><div class="scatter-name-label-wrap">${data.name}</div></div>`,
        position
      )
      label3d.setLabelStyle(label, 0.005, "x")
      label.setParent(self.scatterLableGroup)
      label.userData.adcode = data.adcode
      label.userData.position = [position.x, position.y, position.z]
      label.element.addEventListener("click", () => {
        // alert(data.name)
      })
      return label
    }
    this.setScatterScale()
  }
  setScatterScale() {
    this.scatterLableGroup.scale.set(this.scale, this.scale, this.scale)
    this.scatterLableGroup.position.x = -this.boundBox.center.x
    this.scatterLableGroup.position.y = -this.boundBox.center.y
    this.scatterLableGroup.children.map((label, i) => {
      let z = (this.parent.depth + 0.4) / this.scale
      label.position.z = z

      label.position.y -= 1.5 / this.scale
      label.userData.position = [label.position.x, label.position.y, label.position.z]
      this.scatterLableGroup.children[i].scale.set(0.1 / this.scale, 0.1 / this.scale, 0.1 / this.scale)
    })
    this.scatterGroup.scale.set(this.scale, this.scale, this.scale)
    this.scatterGroup.position.x = -this.boundBox.center.x
    this.scatterGroup.position.y = -this.boundBox.center.y
    this.scatterGroup.children.map((label, i) => {
      let z = (this.parent.depth + 0.4) / this.scale
      label.position.z = z
      label.position.y -= 1.5 / this.scale
      label.userData.position = [label.position.x, label.position.y, label.position.z]
      this.scatterGroup.children[i].scale.set(
        this.scatterGroup.children[i].scale.x / this.scale,
        this.scatterGroup.children[i].scale.y / this.scale,
        this.scatterGroup.children[i].scale.z / this.scale
      )
    })
  }
  geoProjection = (args) => {
    let { geoProjectionScale, geoProjectionTranslate, center } = this.options
    return geoMercator().center(center).scale(geoProjectionScale).translate(geoProjectionTranslate)(args)
  }

  setParent(parent) {
    parent.add(this.instance)
  }

  destroy() {
    this.allAreaLabel = this.allAreaLabel || [];
    this.allInfoLabel = this.allInfoLabel || [];
    this.allbarLabel = this.allbarLabel || [];
    this.allScatterLabel = this.allScatterLabel || [];
    ;[...this.allAreaLabel, ...this.allInfoLabel, ...this.allbarLabel, ...this.allScatterLabel].map((label) => {
      label.remove()
    })
    this.removeElement(".area-name-label")
    this.removeElement(".info-point")
    this.removeElement(".provinces-label-style03")
    this.removeElement(".scatter-name-label")
      ;[...this.eventElement, ...this.pointEventElement].map((mesh) => {
        this.parent.interactionManager.remove(mesh)
      })

    emptyObject(this.instance)
  }
  removeElement(elementClassName) {
    var elements = document.querySelectorAll(elementClassName)
    for (let i = 0; i < elements.length; i++) {
      const element = elements[i]
      const parent = element.parentNode
      parent.removeChild(element)
    }
  }
}
