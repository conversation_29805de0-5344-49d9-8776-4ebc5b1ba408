<template>
  <div class="map-level">
    <canvas id="canvas"></canvas>
    <div class="return-btn" @click="goBack">返回上一级</div>
    <div class="map-btn-group" style="display: none">
      <div class="btn" :class="{ active: state.bar }" @click="setEffectToggle('bar')">柱状图</div>
      <div class="btn" :class="{ active: state.flyLine }" @click="setEffectToggle('flyLine')">飞线</div>
      <div class="btn" :class="{ active: state.scatter }" @click="setEffectToggle('scatter')">散点图</div>
      <div class="btn" :class="{ active: state.card }" @click="setEffectToggle('card')">标牌</div>
      <div class="btn" :class="{ active: state.particle }" @click="setEffectToggle('particle')">粒子特效</div>
      <div class="btn" :class="{ active: state.path }" @click="setEffectToggle('path')">路径轨迹</div>
      <div class="btn" :class="{ active: state.mirror }" @click="setEffectToggle('mirror')">倒影</div>
    </div>

    <div class="top" :class="{ show: state.layoutshow }"></div>
    <div class="layout left" :class="{ show: state.layoutshow }">
      <img src="../../assets/img/left.png" />
    </div>
    <div class="layout right" :class="{ show: state.layoutshow }">
      <img src="../../assets/img/right.png" />
    </div>

    <div class="btnbox" :class="{ show: state.datashow }">
      <img src="../../assets/img/btn1.png" />
      <img src="../../assets/img/btn2.png" />
      <img src="../../assets/img/btn3.png" />
      <img src="../../assets/img/btn4.png" />
      <img src="../../assets/img/btn5.png" />
      <img src="../../assets/img/btn6.png" />
    </div>

    <div class="cruise" @click="openCruise" v-if="startCruise">开始巡航</div>
    <div class="cruise" @click="closeCruise" v-else>结束巡航</div>

    <div class="atlasCity" id="mapDiv"></div>
  </div>
</template>

<script setup>
import { onMounted, ref, onBeforeUnmount, reactive, watch } from "vue"
import { World } from "./1"
import { SubWorld } from "./subMap"

let app = null
let newApp = null
let startCruise = ref(true)
let setTime = ref()
let isMapPage = ref(false)

const state = reactive({
  bar: true, // 柱状图
  flyLine: false, // 飞线
  scatter: false, // 散点图
  card: false, // 标牌
  particle: false, // 粒子
  mirror: false, // 倒影
  path: false, // 路径轨迹
  layoutshow: false,
  datashow: false
})
const setEffectToggle = (type) => {
  if (["bar", "flyLine", "scatter", "card", "path"].includes(type) && app && app.currentScene === "childScene") {
    return false
  }
  // 设置按钮状态
  state[type] = !state[type]
  if (type === "bar") {
    app.barGroup.visible = state[type]
    app.setLabelVisible("labelGroup", state[type])
  }
  if (type === "particle") {
    app.particles.enable = state[type]
    app.particles.instance.visible = state[type]
  }
  if (type === "flyLine") {
    app.flyLineGroup.visible = state[type]
    app.flyLineFocusGroup.visible = state[type]
  }
  if (type === "scatter") {
    app.scatterGroup.visible = state[type]
  }
  if (type === "card") {
    app.setLabelVisible("badgeGroup", state[type])
  }
  if (type === "mirror") {
    app.groundMirror.visible = state[type]
  }
  if (type === "path") {
    app.pathLineGroup.visible = state[type]
  }
}
// 设置按钮启用和禁用
const setEnable = (bool) => {
  state.bar = bool
  state.flyLine = bool
  state.scatter = bool
  state.card = bool
  state.path = bool
}
// 返回上一级
const goBack = () => {
  let mapContainer = document.querySelector("#mapDiv")
  if (mapContainer.style.zIndex == 1) {
    mapContainer.style.zIndex = -1
    app &&  app.goBack()
    return
  }
  app && app.goBack()
}

let mapInfo = reactive([])
onMounted(() => {
  init()
  // initLoad()
})

const init = () => {
  app = new World(document.getElementById("canvas"), {
    geoProjectionCenter: [101.623557, 37.558039],
    setEnable: setEnable,
  })
  mapInfo = app.mapData
  isMapPage = app.isMapPage

  setTimeout(() => {
    state.layoutshow = true
    setTimeout(() => {
      state.datashow = true
    }, 1000);
  }, 5000);
}


onBeforeUnmount(() => {
  app && app.destroy()
})


let setTimeArray = [];

const openCruise = () => {
  startCruise.value = false
  const mapInfo = app.mapData
  const newMapInfo = reactive([])
  const map = new Map()
  for (var i = 0; i < mapInfo.length; i++) {

    if (map.has(mapInfo[i].parent.userData)) {
      map.set(mapInfo[i].parent.userData, mapInfo[i])
    } else {
      map.set(mapInfo[i].parent.userData, mapInfo[i])
      newMapInfo.push(mapInfo[i])
    }
  }
  // for (let i = 0; i < newMapInfo.length; i++) {
  //   (function (j) {
  //     setTimeout(function timer() {
  //       app.loadChildMap(newMapInfo[i].parent.userData)
  //     }, 5000 * i);
  //   })(i);
  // }




  const provincesData = [
    {
      name: "城关",
      center: [103.83, 36.06],
      centroid: [103.83, 36.06],
      adcode: 620102,
      enName: "",
      value: 31231,
      luqu: 27631,
    }, {
      name: "永登县",
      center: [103.26, 36.74],
      centroid: [103.26, 36.74],
      adcode: 620105,
      enName: "",
      value: 27812,
      luqu: 27631,
    },
    {
      name: "榆中县",
      center: [104.11, 35.84],
      centroid: [104.11, 35.84],
      adcode: 620103,
      enName: "",
      value: 23417,
      luqu: 21685,
    },

  ]

  const dom = document.getElementsByClassName('map-level');
  const canvas = document.createElement('canvas');
  canvas.style.width = '100vw';
  canvas.style.height = '100vh';
  canvas.style.touchAction = 'none';
  canvas.setAttribute('data-engine', 'three.js r155');
  canvas.id = 'canvas';
  dom[0].appendChild(canvas);


  for (let i = 0; i < newMapInfo.length; i++) {
    (function (j) {
      setTime.value = setTimeout(function timer() {
        app && app.destroy()
        app = new SubWorld(document.getElementById("canvas"), {
          // geoProjectionCenter: newMapInfo[i].parent.userData,
          geoProjectionCenter: [101.623557, 37.558039],
          setEnable: setEnable,
          provincesData,
          mapCoordinate: newMapInfo[i].parent.userData
        })
      }, 10000 * i);
      setTimeArray.push(setTime.value)
    })(i);
  }



  // const mapInfo = app.mapData
  // const newMapInfo = reactive([])
  // const map = new Map()
  // for (var i = 0; i < mapInfo.length; i++) {

  //   if (map.has(mapInfo[i].parent.userData)) {
  //     map.set(mapInfo[i].parent.userData, mapInfo[i])
  //   } else {
  //     map.set(mapInfo[i].parent.userData, mapInfo[i])
  //     newMapInfo.push(mapInfo[i])
  //   }
  // }
  // for (let i = 0; i < newMapInfo.length; i++) {
  //   (function (j) {
  //     setTimeout(function timer() {
  //       app.loadChildMap(newMapInfo[i].parent.userData)
  //     }, 5000 * i);
  //   })(i);
  // }
}

const closeCruise = () => {
  startCruise.value = true
  setTimeArray.forEach(timer => {
    clearTimeout(timer);
  });
  app.setMainMapVisible(false)
  app && app.destroy()

  init()
}


// watch(() => app.isMapPage, (newValue, oldValue) => {
//     initLoad()
// })

const map = ref(null)
const zoom = ref(12)
let longitude = ref('');
let latitude = ref('');
const initLoad = () => {
  // 获取经纬度及名称
  longitude.value = 103.294696;
  latitude.value = 35.636193;
  const T = window.T
  map.value = new T.Map('mapDiv', {
    zoom: zoom.value,
    center: new T.LngLat(longitude.value, latitude.value)
  })

  // 添加地图类型控件
  const ctrl = new T.Control.MapType([{
    title: '地图',
    icon: 'https://api.tianditu.gov.cn/v4.0/image/map/maptype/vector.png',
    layer: window.TMAP_NORMAL_MAP
  },
  {
    title: '卫星',
    icon: 'https://api.tianditu.gov.cn/v4.0/image/map/maptype/satellite.png',
    layer: window.TMAP_SATELLITE_MAP
  }
  ]);
  map.value.addControl(ctrl)


  // addMarkers();
}

</script>

<style lang="scss">
.map-level {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;

  #canvas {
    width: 100%;
    height: 100%;
    background: #000;
  }
}


.top {
  position: absolute;
  top: -116px;
  left: 0;
  width: 100%;
  height: 243px;
  transition: ease-in-out all .5s;
  background: url("../../assets/img/top.png") no-repeat 0 0;
  background-size: 100% 100%;
  z-index: 9;
  opacity: 0;

  &.show {
    top: 0;
    opacity: 1;
  }
}

.layout {
  position: absolute;
  width: 370px;
  bottom: 30px;
  top: 120px;
  background: url("../../assets/img/lrbg.png") no-repeat 0 0;
  background-size: 100% 100%;
  backdrop-filter: blur(5px) brightness(110%);
  -webkit-backdrop-filter: blur(5px) brightness(110%);
  transition: ease-in-out all .5s;
  z-index: 9;
  display: flex;
  justify-content: center;
  align-items: center;

  &.left {
    left: -370px;
    opacity: 0;

    &.show {
      left: 30px;
      opacity: 1;
    }
  }

  &.right {
    right: -370px;
    opacity: 0;

    &.show {
      right: 30px;
      opacity: 1;
    }
  }

  img {
    width: calc(100% - 30px);
    height: calc(100% - 30px);
  }
}

.bottom {
  position: absolute;
  bottom: -63px;
  left: 0;
  width: 100%;
  height: 63px;
  transition: ease-in-out all .5s;
  background: url("../../assets/img/bottom.png") no-repeat 0 0;
  background-size: 100% 100%;
  z-index: 9;
  opacity: 0;

  &.show {
    bottom: 0;
    opacity: 1;
  }
}

.btnbox {
  position: absolute;
  right: 430px;
  width: 600px;
  top: 100px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  z-index: 9;
  opacity: 0;
  transition: ease-in-out all .5s;
  transform: scale(0.5);

  &.show {
    transform: scale(1);
    opacity: 1;
  }

  img {
    width: 30%;
  }
}

// 返回按钮
.return-btn {
  position: absolute;
  left: 50%;
  bottom: 80px;
  transform: translateX(-50%);
  padding: 5px 24px;
  color: #000000;
  border: 1px solid #2bc4dc;
  margin-bottom: 10px;
  font-size: 12px;
  text-align: center;
  opacity: 0.5;
  display: none;
  cursor: pointer;
  transition: all 0.3s;
  z-index: 99;
  background-color: #eee;

  &:hover {
    opacity: 1;
  }
}

// 右侧按钮组
.map-btn-group {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);

  .btn {
    padding: 5px 12px;
    color: #fff;
    border: 1px solid #2bc4dc;
    margin-bottom: 10px;
    font-size: 12px;
    text-align: center;
    opacity: 0.5;
    cursor: pointer;
    transition: all 0.3s;

    &.active {
      opacity: 1;
    }
  }
}

// 信息框
.info-point {
  background: rgba(0, 0, 0, 0.5);
  color: #a3dcde;
  font-size: 14px;
  width: 170px;
  height: 106px;
  padding: 16px 12px 0;
  margin-bottom: 30px;

  &-wrap {

    &:after,
    &:before {
      display: block;
      content: "";
      position: absolute;
      top: 0;
      width: 15px;
      height: 15px;
      border-top: 1px solid #4b87a6;
    }

    &:before {
      left: 0;
      border-left: 1px solid #4b87a6;
    }

    &:after {
      right: 0;
      border-right: 1px solid #4b87a6;
    }

    &-inner {

      &:after,
      &:before {
        display: block;
        content: "";
        position: absolute;
        bottom: 0;
        width: 15px;
        height: 15px;
        border-bottom: 1px solid #4b87a6;
      }

      &:before {
        left: 0;
        border-left: 1px solid #4b87a6;
      }

      &:after {
        right: 0;
        border-right: 1px solid #4b87a6;
      }
    }
  }

  &-line {
    position: absolute;
    top: 7px;
    right: 12px;
    display: flex;

    .line {
      width: 5px;
      height: 2px;
      margin-right: 5px;
      background: #17e5c3;
    }
  }

  &-content {
    .content-item {
      display: flex;
      height: 28px;
      line-height: 28px;
      background: rgba(35, 47, 58, 0.6);
      margin-bottom: 5px;

      .label {
        width: 60px;
        padding-left: 10px;
      }

      .value {
        color: #fff;
      }
    }
  }
}

// 标牌
.badges-label {
  z-index: 99999;

  &-outline {
    position: absolute;
  }

  &-wrap {
    position: relative;
    padding: 10px 10px;
    background: #0e1937;
    border: 1px solid #1e7491;
    font-size: 12px;
    font-weight: bold;
    color: #fff;
    // margin-bottom: 50px;
    bottom: 50px;
    z-index: 99999;

    span {
      color: #ffe70b;
    }

    &:after {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 10px;
      height: 10px;
      display: block;
      content: "";
      border-right: 2px solid #6cfffe;
      border-bottom: 2px solid #6cfffe;
    }

    &:before {
      position: absolute;
      left: 0;
      top: 0;
      width: 10px;
      height: 10px;
      display: block;
      content: "";
      border-left: 2px solid #6cfffe;
      border-top: 2px solid #6cfffe;
    }

    .icon {
      position: absolute;
      width: 27px;
      height: 20px;
      left: 50%;
      transform: translateX(-13px);
      bottom: -40px;
    }
  }
}

.area-name-label {
  &-wrap {
    color: #5fc6dc;
    opacity: 1;
    text-shadow: 1px 1px 0px #000;
  }
}

.provinces-name-label {

  //市州名称
  &-wrap {
    font-size: 20px;
    color: #5fc6dc;
    opacity: 0;
    text-shadow: 1px 1px 0px #000;
  }
}

.provinces-label-style02 {
  transform: translateX(50%);

  &-wrap {
    transform: translate(0%, 200%);
    opacity: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 80px;
    z-index: 2;
  }

  .number {
    color: #E0F1FF;
    font-size: 44px;
    font-weight: bold;
    margin-bottom: 10px;

    .unit {
      color: #fff;
      font-size: 22px;
      opacity: 0.5;
      padding-left: 5px;
      margin-top: 8px;
      font-weight: normal;
    }

    &.zhibiao {
      color: #C3FFC1;
      font-size: 44px;
      text-shadow: 0 0 5px rgba(21, 255, 138, .8);

      .title {
        color: #C3FFC1;
        font-size: 26px;
        padding-left: 15px;
        font-weight: normal;
      }

      .unit {
        color: #C3FFC1;
        font-size: 22px;
        opacity: 0.5;
        padding-left: 5px;
        margin-top: 8px;
        font-weight: normal;
      }
    }

    &.luqu {
      background: rgba(0, 0, 0, .5);
      padding: 5px 35px;
      border-radius: 1000px;
      color: #FFEFC1;
      font-size: 44px;
      text-shadow: 0 0 5px rgba(255, 112, 21, .8);
      zoom: 0.9;

      .title {
        color: #FFEFC1;
        font-size: 26px;
        padding-left: 15px;
        font-weight: normal;
      }

      .unit {
        color: #FFEFC1;
        font-size: 22px;
        opacity: 0.5;
        padding-left: 5px;
        margin-top: 8px;
        font-weight: normal;
      }
    }
  }

  .no {
    //排名数字
    display: flex;
    justify-content: center;
    align-items: center;
    color: #7efbf6;
    /* text-shadow: 0px 0px 4px 0px #7efbf6; */
    text-shadow: 0 0 5px #7efbf6;
    font-size: 26px;
    /* font-weight: 700; */
    width: auto;
    height: 40px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 1000px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    padding: 0 15px;
  }

  .yellow {
    .no {
      color: #fef99e !important;
      text-shadow: 0 0 5px #fef99e !important;
    }
  }
}

.fixed-loading {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 99;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.5);
}

.page-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 10px;
}

.page-loading {
  width: 30px;
  height: 30px;
  border: 2px solid #fff;
  border-top-color: transparent;
  border-radius: 100%;
  animation: loading infinite 0.75s linear;
}

@keyframes loading {
  from {
    transform: rotate(0);
  }

  to {
    transform: rotate(360deg);
  }
}

.cruise {
  position: absolute;
  right: 5in;
  top: 90%;
  font-size: 20px;
  color: #fff;
  background-color: #020202ad;
  z-index: 999;
  padding: 10px 30px;
  cursor: pointer;
}

.atlasCity {
  position: absolute;
  top: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
}
</style>
