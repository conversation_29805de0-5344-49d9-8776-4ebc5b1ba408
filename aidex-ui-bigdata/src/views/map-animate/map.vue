<template>
  <div class="map-level">


    <div class="map-con">
      <canvas id="canvas"></canvas>
    </div>


    <div id="leftBox">

    <div class="zhezhao" :class="{ boxhidn: zhezhao == false }">
      <div class="topMiddle">甘肃省教育入学一件事大数据看板</div>
      <video src="../../assets/video/video.mp4" muted autoplay loop="loop" />
      <div class="jiazaiTxt" v-if="tokenIsHave">
        <img v-if="zhezhao == true" src="../../assets/img/loading.gif" />
        <span v-if="zhezhao == true">数据加载中</span>
        <span v-if="zhezhao == false">加载完成</span>
      </div>
      <div class="jiazaiTxt" v-else>
        <span>无权访问</span>
      </div>
    </div>
    <div v-if="showGoBack" class="return-btn" @click="goBack">返回上一级</div>
    <div class="map-btn-group" style="display: none">
      <div class="btn" :class="{ active: state.bar }" @click="setEffectToggle('bar')">柱状图</div>
      <div class="btn" :class="{ active: state.flyLine }" @click="setEffectToggle('flyLine')">飞线</div>
      <div class="btn" :class="{ active: state.scatter }" @click="setEffectToggle('scatter')">散点图</div>
      <div class="btn" :class="{ active: state.card }" @click="setEffectToggle('card')">标牌</div>
      <div class="btn" :class="{ active: state.particle }" @click="setEffectToggle('particle')">粒子特效</div>
      <div class="btn" :class="{ active: state.path }" @click="setEffectToggle('path')">路径轨迹</div>
      <div class="btn" :class="{ active: state.path }" @click="setEffectToggle('path')">路径轨迹</div>
      <div class="btn" :class="{ active: state.mirror }" @click="setEffectToggle('mirror')">倒影</div>
    </div>
    <div class="top" :class="{ show: state.layoutshow }">
      <topLayout :top-title="topTitle" :area-name="areaName1 + areaName2 + areaName3"/>
    </div>

    <div class="layout1 botBox1" style="display: none" :class="{ show: state.layoutshow }" v-if="buttonShow">
      <div class="bottomBox active" :class="{ 'active': showData }">报名人数</div>
      <div class="bottomBox1" @click="zhaoshengClick('招生指标')" :class="{ 'active': showData[0] }">招生指标</div>
      <div class="bottomsg"><img src="../../assets/img/sg.png" /></div>
      <div class="bottomBox2" @click="zhaoshengClick('录取人数')" :class="{ 'active': showData[1] }">录取人数</div>
      <div class="bottomBox3" @click="zhaoshengClick('线上报名人数')" :class="{ 'active': showData[2] }">线上报名人数</div>
      <div class="bottomBox4" @click="zhaoshengClick('代报人数')" :class="{ 'active': showData[3] }">代报人数</div>
      <div class="bottomBox5" @click="zhaoshengClick('调剂人数')" :class="{ 'active': showData[4] }">调剂人数</div>
      <div class="bottomBox6" @click="zhaoshengClick('政策照顾人数')" :class="{ 'active': showData[5] }">政策照顾人数</div>
    </div>

    <div class="layout left" :class="{ show: state.layoutshow }">
      <leftLayout ref="childLeftRef" @showPop="popFrame2" @showPop1="popFrame3" :dataResult="dataResult"/>
    </div>
    <div class="layout right" :class="{ show: state.layoutshow }">
      <rightLayout ref="childRightRef" @showPop="popFrame" @showPop1="popFrame1"  :dataResult="dataResult"/>
    </div>

    <!--    <div class="btnbox" :class="{ show: state.datashow }">-->

    <!--    </div>-->

    <div class="cruise botBox2" :class="{ show: state.layoutshow }" @click="openCruise" v-if="startCruise">
<!--      <div>-->
<!--        <img src="../../assets/img/gs10.png" />-->
<!--      </div>-->
<!--      <div class="spanBox">市州巡航</div>-->
    </div>

    <div class="cruise botBox2" :class="{ show: state.layoutshow }" @click="closeCruise" v-else>
      <div>
        <img src="../../assets/img/gs10.png" />
      </div>
      <div class="spanBox">结束巡航</div>
    </div>

    <div class="atlasCity" id="mapDiv"></div>
<!--    &lt;!&ndash; 右边弹框 &ndash;&gt;-->
<!--    <div class="tkBox" :class="{ boxshow: showModalFlag }">-->
<!--      <div class="topBox">-->
<!--        <div><img src="../../assets/img/tkl.png" />热点学校</div>-->
<!--        <div class="xiaoshi" @click="guanbiClick">-->
<!--          <img src="../../assets/img/tkxs.png">-->
<!--        </div>-->
<!--      </div>-->
<!--      <div class="butBox">-->
<!--        <div class="box_big" v-for="(v, i) in dataValue" :key="i">-->
<!--          <div class="box_big_top">-->
<!--            <div class="xiaoxue">{{ v.schoolName }}</div>-->
<!--            <div class="shixian">{{ replaceText(v.deptName, '教育局', '')}}</div>-->
<!--          </div>-->
<!--          <div class="box_small1">-->
<!--            <div class="zbs"><span></span> 指标数<span class="zbssl">{{ v.applyNumberFirst }}</span></div>-->
<!--            <div class="lqs"><span></span> 录取数<span class="lqssl">{{ v.lqrs }}</span></div>-->
<!--            <div class="cbl" v-if="v.overStandardRate > 1"><span></span> 超标率<span class="cblsl">{{ roundedResult(v.overStandardRate) }} %</span></div>-->
<!--            <div class="cbl" v-else><span></span> 录取率<span class="cblsl">{{ roundedResult(v.overStandardRate) }} %</span></div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->
    <!--    左边弹框-->
    <div class="tkBox" :class="{ boxshow: showFlag }">
      <div class="topBox">
        <div><img src="../../assets/img/tkl.png" />初审办件时长排行</div>
        <div class="xiaoshi" @click="popLeftClick">
          <img src="../../assets/img/tkxs.png">
        </div>
      </div>
      <div class="butBox">
        <div class="butBoxtx">
          <p class="butBoxtxp">
            <span>名称</span>
            <span>办件数</span>
            <span>平均办件时长</span>
          </p>
        </div>
        <div class="butBoxtx1">
          <p v-for="(v, i) in tableData" :key="i">
            <span>{{ v.areaName }}</span>
            <span>{{ v.processesCount }} 件</span>
            <span>{{ roundedResult1(v.processesTime) }} 小时</span>
          </p>
        </div>
      </div>
    </div>

    <div class="tkBox" :class="{ boxshow: showFlag1 }">
      <div class="topBox">
        <div><img src="../../assets/img/tkl.png" />跑0次录取占比排行</div>
        <div class="xiaoshi" @click="popLeftClick1">
          <img src="../../assets/img/tkxs.png">
        </div>
      </div>
      <div class="butBox">
        <div class="butBoxtx">
          <p class="butBoxtxp">
            <span>名称</span>
            <span>总录取数</span>
            <span>跑0次数</span>
            <span>占比</span>
          </p>
        </div>
        <div class="butBoxtx1">
          <p v-for="(v, i) in tableData" :key="i">
            <span>{{ v.name }}</span>
            <span>{{ v.applyNum }} 人</span>
            <span>{{ v.skipNum }} 人</span>
            <span>{{ v.skipRate }} %</span>
          </p>
        </div>
      </div>
    </div>

    <!-- 学校弹框 -->
    <div class="tkBox1" :class="{ boxshow: showSchool }">
      <div class="topBox">
        <div><img src="../../assets/img/tkl.png" />{{ schoolName }}</div>
        <div class="xiaoshi" @click="schoolClick">
          <img src="../../assets/img/tkxs.png">
        </div>
      </div>
      <div class="butBox">
        <div class="schooleTop">
          <div class="topLeft">
            <!--              <img src="../../assets/img/tkPhoto.png">-->
            <div class="zszb">招生指标</div>
            <div class="jz">{{ schoolData.zszb }}</div>
          </div>
          <div class="topRight">
            <span class="txt">{{ schoolData.zsbl * 100 }}%</span>
            <div class="cont">
              <p :style="`width:${schoolData.zsbl * 100}%`"></p>
            </div>
          </div>
        </div>
        <div class="slBox">
          <div class="sbxBox">
            <div class="szBox">{{ schoolData.bmrs }}</div>
            <p>报名</p>
          </div>
          <div class="sbxBox">
            <div class="szBox">{{ schoolData.csrs }}</div>
            <p>初审</p>
          </div>
          <div class="sbxBox">
            <div class="szBox">{{ schoolData.xcshrs }}</div>
            <p>现场审核</p>
          </div>
          <div class="sbxBox">
            <div class="szBox1">{{ schoolData.lqrs }}</div>
            <p>已录取</p>
          </div>
        </div>
      </div>
    </div>
<!--    右边-->
    <div class="tkBox" :class="{ boxshow: showFlag2 }">
      <div class="topBox">
        <div><img src="../../assets/img/tkl.png" />初审一次通过率排行</div>
        <div class="xiaoshi" @click="popLeftClick2">
          <img src="../../assets/img/tkxs.png">
        </div>
      </div>
      <div class="butBox">
        <div class="butBoxtx">
          <p class="butBoxtxp">
            <span>名称</span>
            <span>初审数</span>
            <span>通过数</span>
            <span>通过率</span>
          </p>
        </div>
        <div class="butBoxtx1">
          <p v-for="(v, i) in tableData" :key="i">
            <span>{{ v.deptName }}</span>
            <span>{{ v.applyNum }} 人</span>
            <span>{{ v.applySuccessNum }} 人</span>
            <span>{{ v.successRate }} %</span>
          </p>
        </div>
      </div>
    </div>

    <div class="tkBox" :class="{ boxshow: showFlag3 }">
      <div class="topBox">
        <div><img src="../../assets/img/tkl.png" />初审二次驳回率排行</div>
        <div class="xiaoshi" @click="popLeftClick3">
          <img src="../../assets/img/tkxs.png">
        </div>
      </div>
      <div class="butBox">
        <div class="butBoxtx">
          <p class="butBoxtxp">
            <span>名称</span>
            <span>初审数</span>
            <span>驳回数</span>
            <span>驳回率</span>
          </p>
        </div>
        <div class="butBoxtx1">
          <p v-for="(v, i) in tableData" :key="i">
            <span>{{ v.deptName }}</span>
            <span>{{ v.applyNum }} 人</span>
            <span>{{ v.applyErrorNum }} 人</span>
            <span>{{ v.errorRate }}%</span>
          </p>
        </div>
      </div>
    </div>
    </div>
  </div>
</template>

<script setup>
import {onMounted, ref, onBeforeUnmount, reactive, watchEffect, watch} from "vue"
import { useRoute } from 'vue-router'
import { World } from "./map"
import { SubWorld } from "./subMap"
import leftLayout from '../../views/layout/left.vue'
import rightLayout from '../../views/layout/right.vue'
import topLayout from '../../views/layout/top.vue'
import provincesDataCity from "./map/provincesData"

import autofit from "autofit.js"

onMounted(() => {
  autofit.init({
    dh: 1080,
    dw: 1920,
    el: "#leftBox",
    resize: true
  }, false)
})
onBeforeUnmount (()=>{
  autofit.off()
})
// 是否显示地图下面的切换按钮
let buttonShow = true;

const topTitle = ref("甘肃省教育入学一件事大数据看板")

// 省
const areaName1 = "甘肃省";
// 市
let areaName2 = "";
// 县
let areaName3 = "";
// 是否重新加载地图
let isLoadMap = true

const BASE_URL = "https://ruxue.smartedu.gsedu.cn/api"
// const BASE_URL = "http://127.0.0.1:8080"

const childLeftRef = ref("")

const childRightRef = ref("")

const route = useRoute()

//是否显示返回上一级按钮
const showGoBack = ref(true)

const roundedResult =(inputValue)=>{
  return Math.round(inputValue * 100);
}

const tokenIsHave = ref(true)

//地图层级(点击层级标识: 0省级  1:市级  2:县区级)
let mapIndex = 0;
// 判断地图是向下点击还是返回上一级(true为向下，false为返回上一级)
let isUpOrDown = true;

// 第一次加载完了先存起来
let zbrsValue = 0; //指标人数
let lqrsValue = 0; // 录取人数
let bmrsValue = 0; // 报名人数

const roundedResult1 =(inputValue)=>{
  return (inputValue / 3600).toFixed(2);
}

let provincesDataCity1 = [];
let showData = reactive([
  false, false, false, false, false, false,
])
const text = ref(40)
const dataResult = ref({sdqx: 0,zszqx: 0, xxsl: 0, zzzbs:0, bmrs:0, lqrs: 0, pjbjsc : 0})

const handleList = (text) => {
  const rateN = 0.46 * parseFloat(text)
  rate.value = text
  ratePercentage.value = rateN
}

let ratePercentage = ref(20)

// 学校数据
let schoolData = {};

let dataValue = []
let tableData = []

let app = null
let newApp = null
let startCruise = ref(true)
let setTime = ref()
const showModalFlag = ref(false)
const showFlag = ref(false)
const showFlag1 = ref(false)
const showFlag2 = ref(false)
const showFlag3 = ref(false)
const showSchool = ref(false)

const zhezhao = ref(false)
// 右边弹框
const popFrame2 = async () => {
  tableData = dataResult.value.successRateList
  showFlag2.value = !showFlag2.value
  showModalFlag.value = false
  showSchool.value = false
  showFlag.value = false
  showFlag1.value = false
  showFlag3.value=false
}
const popFrame3 = async () => {
  tableData = dataResult.value.NoPassRateList
  showFlag3.value = !showFlag3.value
  showFlag.value = false
  showFlag1.value = false
  showModalFlag.value = false
  showSchool.value = false
  showFlag2.value=false
}

const guanbiClick = () => {
  showModalFlag.value = false
}

const auth_key = 'X-Auth-Token'
let token = ''

// 左边弹框
const popFrame = async () => {
  const response = await fetch(BASE_URL + '/front/data/getProcessingTimeList', {headers: {[auth_key]: token}});
  const data = await response.json();

  if (data.code === 200) {
    tableData = data.data.bizProcessingTimeListCs
    showFlag.value = !showFlag.value
    showModalFlag.value = false
    showSchool.value = false
    showFlag1.value=false
    showFlag2.value=false
    showFlag3.value=false
  }
}
const popFrame1 = async () => {
    // const response = await fetch(BASE_URL + '/front/data/getProcessingTimeList');
    // const data = await response.json();

    // if (data.code === 200) {
    // tableData = data.data.bizProcessingTimeListMs
    tableData = dataResult.value.skipZeroAuditList
    showFlag1.value = !showFlag1.value
    showModalFlag.value = false
    showSchool.value = false
    showFlag2.value=false
    showFlag.value=false
    showFlag3.value=false
  // }
}
const popLeftClick = () => {
  showFlag.value = false
}
const popLeftClick1 = () => {
  showFlag1.value = false
}

const popLeftClick2 = () => {
  showFlag2.value = false
}
const popLeftClick3 = () => {
  showFlag3.value = false
}
let schoolName = ref('')
// 学校弹框
const schoolPop = async (userData) => {
  const response = await fetch(BASE_URL + '/front/data/getSchoolData?schoolId=' + userData.adcode, {headers: {[auth_key]: token}});
  console.log("=============")
  console.log(response)
  const data = await response.json();
  console.log(data);

  if(data.code === 200){
    schoolData = data.data;
  }
  schoolName.value = userData.name
  showSchool.value = !showSchool.value
  showModalFlag.value = false
  showFlag.value = false
  showFlag1.value = false
}

const schoolClick = () => {
  showSchool.value = false
}

const state = reactive({
  bar: true, // 柱状图
  flyLine: false, // 飞线
  scatter: false, // 散点图
  card: false, // 标牌
  particle: false, // 粒子
  mirror: false, // 倒影
  path: false, // 路径轨迹
  layoutshow: false,
  // datashow: false
})
const setEffectToggle = (type) => {
  if (["bar", "flyLine", "scatter", "card", "path"].includes(type) && app && app.currentScene === "childScene") {
    return false
  }
  // 设置按钮状态
  state[type] = !state[type]
  if (type === "bar") {
    app.barGroup.visible = state[type]
    app.setLabelVisible("labelGroup", state[type])
  }
  if (type === "particle") {
    app.particles.enable = state[type]
    app.particles.instance.visible = state[type]
  }
  if (type === "flyLine") {
    app.flyLineGroup.visible = state[type]
    app.flyLineFocusGroup.visible = state[type]
  }
  if (type === "scatter") {
    app.scatterGroup.visible = state[type]
  }
  if (type === "card") {
    app.setLabelVisible("badgeGroup", state[type])
  }
  if (type === "mirror") {
    app.groundMirror.visible = state[type]
  }
  if (type === "path") {
    app.pathLineGroup.visible = state[type]
  }
}
// 设置按钮启用和禁用
const setEnable = (bool) => {
  state.bar = bool
  state.flyLine = bool
  state.scatter = bool
  state.card = bool
  state.path = bool
}
// 返回上一级
const goBack = () => {
  isUpOrDown = false
  if(mapIndex === 2){
    mapIndex = 1;
    areaName3 = ""
    loadData();
  } else if(mapIndex === 1){
    mapIndex = 0;
    areaName2 = ""
    loadData();
  } else {
    mapIndex = 0;
    loadData();
  }
  let mapContainer = document.querySelector("#mapDiv")
  if (mapContainer.style.zIndex == 1) {
    mapContainer.style.zIndex = -1
    app && app.goBack()
    return
  }
  app && app.goBack()
}
const zhaoshengClick = (ele) => {
  let userDats = document.getElementsByClassName('number')

  for (let i = 0; i < userDats.length; i++) {
    const dataElement = userDats[i].children[2].innerHTML

    if(showData[i] == true && dataElement != '报名人数'){
      userDats[i].style.display = 'none'
      showData[i] = false
    }else if(showData[i] != true){
      if (dataElement == ele || dataElement == '报名人数') {
        userDats[i].style.display = 'block'
        showData[i] = true
      } else {
        userDats[i].style.display = 'none'
        showData[i] = false
      }
    }



  }
}
let mapInfo = reactive([])
onMounted(() => {
  init()
  window.schoolPop = schoolPop
  window.zhaoshengClick = zhaoshengClick
})


let areaCode = "";

const init = () => {
  // 判断是否传过来token
  if(route.query && route.query.token && route.query.token != ''){
    token = route.query.token
  }
  loadData()
}

const getArea = async (areaCode) => {
  const response = await fetch(BASE_URL + '/front/data/getArea?areaCode=' + areaCode, {headers: {[auth_key]: token}});
  return await response.json();
}

// 区县一级数据
let countyData = {adcode: '', name:'', center:[], centroid:[], childrenNum: 0, materialEmissiveHex: 0, index: 0};

const checkToken = async (token) => {
  const response = await fetch(BASE_URL + '/front/data/validateToken?token=' + token);
  const data = await response.json();
  if(data.code === 200){
    setTimeout(() => {
      if(areaCode && areaCode != ''){
        let areaData = getArea(areaCode);
        areaData.then((result) => {
          console.log(result);
          if(result && result.code == 200){
            let resultData = result.data;

            topTitle.value = resultData.areaName + "教育入学一件事大数据看板";

            countyData.adcode = resultData.areaCode;
            countyData.name = resultData.areaName;
            countyData.center[0] = resultData.longitude;
            countyData.center[1] = resultData.latitude;

            countyData.centroid[0] = resultData.centroidLongitude;
            countyData.centroid[1] = resultData.centroidLatitude;
            loadData();
          }
        });
      }else{
        loadData();
      }
    }, 1000);
    // if(data.data.isToken){
    //   setTimeout(() => {
    //     if(areaCode && areaCode != ''){
    //       let areaData = getArea(areaCode);
    //       alert(areaData);
    //     }else{
    //       loadData();
    //     }
    //   }, 1000);
    // }else{
    //   tokenIsHave.value = false;
    // }
  }
}

// 加载数据
const loadData = async () => {
  const response = await fetch(BASE_URL + '/front/data/getData', {headers: {[auth_key]: token}});
  const data = await response.json();
  if(data && data.data && data.data.data && data.data.data.length > 0){
    let listData = data.data.data[0];
    let allCount = 0;
    if(listData.data && listData.data.length > 0){
      let list = listData.data;

      // 指标人数合计
      let zbrs = 0;
      // 报名人数合计
      let bmrs = 0;
      // 录取人数合计
      let lqrs = 0;
      for (let i = 0; i < list.length; i++) {
        const element = list[i];
        zbrs = zbrs + element.stipulate;
        bmrs = bmrs + element.total;
        lqrs = lqrs + element.verifyTotal;

        let provincesDataItem = {};
        // 坐标
        let coordinates = [];
        let centroid = [];
        if(element.longitude){
          coordinates.push(element.longitude);
        }
        if(element.latitude){
          coordinates.push(element.latitude);
        }
        if(element.centroidLongitude){
          centroid.push(element.centroidLongitude);
        }
        if(element.centroidLatitude){
          centroid.push(element.centroidLatitude);
        }
        provincesDataItem.center = coordinates;
        provincesDataItem.centroid = centroid;
        provincesDataItem.adcode = element.areaCode;
        // 地区名称
        provincesDataItem.name = element.name;
        // 招生指标
        provincesDataItem.luqu = element.stipulate;
        // 报名人数
        provincesDataItem.value = element.total;
        // 录取人数
        provincesDataItem.verifyTotal = element.verifyTotal;
        // 线上报名人数
        provincesDataItem.lineApplyTotal = element.lineApplyTotal;
        // 代报报名人数
        provincesDataItem.otherApplyTotal = element.otherApplyTotal;
        // 优抚政策人数
        provincesDataItem.policyApplyTotal = element.policyApplyTotal;
        // 调配人数
        provincesDataItem.adjustTotal = element.adjustTotal;
        provincesDataCity1.push(provincesDataItem);
      }
      allCount = lqrs;
      zbrsValue = zbrs;
      lqrsValue = lqrs;
      bmrsValue = bmrs;
      data.data.zzzbs = zbrs;
      data.data.bmrs = bmrs;
      data.data.lqrs = lqrs;
      zhezhao.value = false;
      if (isLoadMap) {
        app = new World(document.getElementById("canvas"), {
          geoProjectionCenter: [101.623557, 37.558039],
          setEnable: setEnable,
        }, provincesDataCity1, mapClickCallback, mapClickCallbackTime, areaCode)
        mapInfo = app.mapData
        isLoadMap = false
      }
      setTimeout(() => {
        if(areaCode && areaCode != ""){
          app.loadChildMap(countyData);
        }
        state.layoutshow = true
        // setTimeout(() => {
        //   state.datashow = true
        // }, 1000);
      }, 5000);
    }
    childLeftRef.value.createdChart(data.data);
    childRightRef.value.createdChart(data.data);
    // childRightRef.value.createTagPieChart(data.data);
    childRightRef.value.createKexinlv(data.data);
  }
  dataResult.value = data.data
}

// 加载省级两边数据
const getProvincesData = async () => {
  const response = await fetch(BASE_URL + '/front/data/getProvincesData', {headers: {[auth_key]: token}});
  console.log("=============")
  console.log(response)
  const data = await response.json();
  console.log(data);
  if(data && data.data){
    data.data.zzzbs = zbrsValue;
    data.data.bmrs = bmrsValue;
    data.data.lqrs = lqrsValue;
    zhezhao.value = false;
    childLeftRef.value.createdChart(data.data);
    childRightRef.value.createdChart(data.data);
    // childRightRef.value.createTagPieChart(data.data);
    childRightRef.value.createKexinlv(data.data);
  }
  dataResult.value = data.data
}

// 加载子地图数据
const loadChildrenData = async (areaCode) => {
  const response = await fetch(BASE_URL + '/front/data/getData?areaCode=' + areaCode, {headers: {[auth_key]: token}});
  console.log("=============")
  console.log(response)
  const data = await response.json();
  console.log(data);
  dataResult.value = data.data
  if(data && data.data && data.data.data && data.data.data.length > 0){
    let listData = data.data.data[0];
    if(listData.data && listData.data.length > 0){
      let list = listData.data;
      // 指标人数合计
      let zbrs = 0;
      // 报名人数合计
      let bmrs = 0;
      // 录取人数合计
      let lqrs = 0;
      for (let i = 0; i < list.length; i++) {
        const element = list[i];
        zbrs = zbrs + element.stipulate;
        bmrs = bmrs + element.total;
        lqrs = lqrs + element.verifyTotal;
      }
      data.data.zzzbs = zbrs;
      data.data.bmrs = bmrs;
      data.data.lqrs = lqrs;
      zhezhao.value = false;
    }
  }
  return data;
}

onBeforeUnmount(() => {
  app && app.destroy()
})

// 加载子地图回调，用户加载相应子地图数据
const mapClickCallback = async (mapData) => {
  if(mapIndex == 0){
    mapIndex = 1;
    areaName2 = mapData.name;
  }else if(mapIndex == 1 && isUpOrDown){
    mapIndex = 2;
    areaName3 = mapData.name
  }
  isUpOrDown = true
  console.log(mapData)
  let areaCode = mapData.adcode + "";
  let ending = "00";
  if(areaCode && areaCode.substring(areaCode.length - ending.length) === ending){
    areaCode = areaCode.substring(0, areaCode.length - 2)
  }
  // 加载子地图数据
  return await loadChildrenData(areaCode);
}

const mapClickCallbackTime = async (data) => {
  console.log(data)
  if(data.code == 200){
    if(data.data && data.data){
      childLeftRef.value.createdChart(data.data);
      childRightRef.value.createKexinlv(data.data);
    }
  }
}

let setTimeArray = [];
const anotherFunction = () => {
  // 在anotherFunction函数中调用openCruise函数
  openCruise();
};
const openCruise = () => {
  // showData = [true, false, false, false, false, false]
  zhaoshengClick('招生指标')
  startCruise.value = false
  const mapInfo = app.mapData
  const newMapInfo = reactive([])
  const map = new Map()
  for (var i = 0; i < mapInfo.length; i++) {

    if (map.has(mapInfo[i].parent.userData)) {
      map.set(mapInfo[i].parent.userData, mapInfo[i])
    } else {
      map.set(mapInfo[i].parent.userData, mapInfo[i])
      newMapInfo.push(mapInfo[i])
    }
  }
  // for (let i = 0; i < newMapInfo.length; i++) {
  //   (function (j) {
  //     setTimeout(function timer() {
  //       app.loadChildMap(newMapInfo[i].parent.userData)
  //     }, 5000 * i);
  //   })(i);
  // }

  const provincesData = [
    {
      name: "嘉峪关市",
      center: [98.23, 39.80],
      centroid: [98.23, 39.80],
      adcode: 620102,
      enName: "",
      value: 31231,
      luqu: 27631,
    },
    {
      name: "榆中县",
      center: [104.11, 35.84],
      centroid: [104.11, 35.84],
      adcode: 620103,
      enName: "",
      value: 23417,
      luqu: 21685,
    },

  ]

  const dom = document.getElementsByClassName('map-level');
  const canvas = document.createElement('canvas');
  canvas.style.width = '100vw';
  canvas.style.height = '100vh';
  canvas.style.touchAction = 'none';
  canvas.setAttribute('data-engine', 'three.js r155');
  canvas.id = 'canvas';
  dom[0].appendChild(canvas);

  function startLoop(index, one) {
    let time = 6000
    if (one != undefined) {
      time = 0
    }
    setTimeArray.forEach(timer => {
      clearTimeout(timer);
    });
    if (index >= newMapInfo.length) {
      index = 0;
    }
    setTime.value = setTimeout(function () {
      app && app.destroy();
      app = new SubWorld(document.getElementById("canvas"), {
        geoProjectionCenter: newMapInfo[index].parent.userData.center.slice(0, 2),
        setEnable: setEnable,
        provincesData,
        mapCoordinate: newMapInfo[index].parent.userData
      });
      startLoop(index + 1); // 递归调用，实现循环
    }, time);

    setTimeArray.push(setTime.value);
  }
  startLoop(0, 0); // 开始第一次循环
}

const closeCruise = () => {

  startCruise.value = true
  setTimeArray.forEach(timer => {
    clearTimeout(timer);
  });
  app.setMainMapVisible(false)
  app && app.destroy()

  init()
}

const replaceText=(text, placeholder, replacement)=>{
  return text.replace(placeholder, replacement);
}
</script>

<style lang="scss">
.zhezhao{
  position: fixed;
  inset: 0;
  background: rgba(255,255,255,.3);
  z-index: 9999999;
  transition: ease-in-out all 1.5s;
  video{
    position: absolute; /* 绝对定位，可以覆盖整个容器 */
    top: 0;
    left: 0;
    width: 100%; /* 视频宽度匹配容器宽度 */
    height: 100%; /* 视频高度匹配容器高度 */
    object-fit: cover; /* 确保视频覆盖整个容器，可能被裁剪 */
    z-index: 1;
  }
  .topAreaMiddle{
    color: red;
    float: left;

  }
  .topMiddle{
    position: absolute;
    width: 100%;
    height: auto;
    left: 0;
    top: 50%;
    transform: translateY(calc(-50% - 20px));
    white-space: nowrap;
    font-size: 70px;
    font-family: ckt;
    background: -webkit-linear-gradient(#fff, #fff,#65B4F1,#65B4F1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    display: flex;
    justify-content: center;
    z-index: 99;
  }
  .jiazaiTxt{
    position: absolute;
    width: auto;
    height: auto;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(calc(-50% + 220px));
    z-index: 99;
    background: rgba(0,0,0,1);
    font-size: 22px;
    padding:15px 55px;
    border-radius: 1000px;
    border: 2px solid #000000;
    color: rgba(255,255,255,1);
    display: flex;
    flex-flow: column;
    justify-content: center;
    align-items: center;
    opacity: .7;
    img{
      display: block;
      width: 100px;
      height: 30px;
      object-fit: cover;
    }
  }
  &.boxhidn{
    transform: scale(20);
    opacity: 0;
    pointer-events: none;
  }
}


::-webkit-scrollbar {
  //display: none;
}

.box_big {
  .box_big_top {
    width: 100%;
    display: flex;
    align-items: center;
    font-size: 16px;
  }

  .xiaoxue {
    color: #ffffff;
  }

  .shixian {
    color: #94DBEF;
    padding: 5px 30px;
    font-size: 14px;
  }

  margin: 1% 3%;
  display: flex;
  flex-flow: column;
  border: 1px solid #214e89;
  border-radius: 10px;
  padding: 10px 15px 15px 15px;

  .box_small1 {
    border-top: 1px solid #758795;
    padding-top: 10px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    color: #e2f0fd;
    align-items: center;

    .zbs {
      font-size: 14px;
      display: flex;
      align-items: center;

      span:first-child {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #83DBDC;
        display: block;
        margin-right: 5px;
      }

      img {
        width: 10px;
        height: 10px;
      }

      .zbssl {
        color: #83dbdc;
        margin-left: 5px;
        font-size: 14px;
      }
    }

    .lqs {
      font-size: 14px;
      display: flex;
      align-items: center;

      span:first-child {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #FE6F6F;
        display: block;
        margin-right: 5px;
      }

      .lqssl {
        color: #FE6F6F;
        margin-left: 5px;
        font-size: 14px;
      }
    }

    .cbl {
      font-size: 14px;
      display: flex;
      align-items: center;

      span:first-child {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #F6FF86;
        display: block;
        margin-right: 5px;
      }

      .cblsl {
        color: #F6FF86;
        margin-left: 5px;
        font-size: 14px;
      }
    }
  }
}

//弹框
.tkBox {
  width: 600px;
  height: 400px;
  position: absolute;
  top: 50%;
  left: 50%;
  color: #ffffff;
  display: flex;
  flex-flow: column;
  font-size: 20px;
  background: rgba(0, 14, 26, 0.7);
  border: 1px solid #2D5F84;
  backdrop-filter: blur(10px) brightness(110%);
  -webkit-backdrop-filter: blur(10px) brightness(110%);
  transform: translateX(-50%);
  z-index: -1;
  opacity: 0;
  transition: ease-in-out all 0.3s;
  cursor: pointer;

  .topBox {
    width: 100%;
    height: 44px;
    background: url('../../assets/img/tkbg.png') no-repeat center;
    background-size: 100% 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    div:first-child {
      display: flex;
      align-items: center;
      font-size: 20px;

      img {
        width: 40px;
        height: 40px;
      }
    }

    .xiaoshi {
      margin-right: 10px;
      cursor: pointer;

      img {
        width: 26px;
        height: 25px;
      }
    }
  }

  .butBox {
    height: 330px;
    display: flex;
    flex-flow: column;
    margin: 10px;
    overflow-y: auto;

    .butBoxtx {
      width: 100%;

      p {
        width: 100%;
        font-size: 14px;
        line-height: 45px;
        border-bottom: 1px dashed rgba(255, 255, 255, .1);
        display: flex;
        justify-content: space-between;

        span {
          display: inline-block;
          width: 25%;
          text-align: center;

          &:nth-child(1) {
            width: calc(60% - 30px);
            text-align: left;
            padding: 0 15px;
          }

          &:nth-child(2) {
            width: 20%;
            text-align: left;
            color: #ffefc1;
          }

          &:nth-child(3) {
            width: calc(20% - 30px);
            text-align: right;
            padding: 0 15px;
            color: #c1ffde;
          }

        }
      }
    }
    .butBoxtx1 {
      position: absolute;
      top: 100px;
      bottom: 10px;
      left: 10px;
      right: 0px;
      width: auto;
      overflow-y: auto;
      padding:0 10px 0 0;

      p {
        width: 100%;
        font-size: 14px;
        line-height: 45px;
        border-bottom: 1px dashed rgba(255, 255, 255, .1);
        display: flex;
        justify-content: space-between;

        span {
          display: inline-block;
          width: 25%;
          text-align: center;

          &:nth-child(1) {
            width: calc(60% - 30px);
            text-align: left;
            padding: 0 15px;
          }

          &:nth-child(2) {
            width: 20%;
            text-align: left;
            color: #ffefc1;
          }

          &:nth-child(3) {
            width: calc(20% - 30px);
            text-align: right;
            padding: 0 15px;
            color: #c1ffde;
          }

        }
      }
    }

    .butBoxtxp {
      background: rgba(0, 0, 0, 0.5);
      border-bottom: none;
      font-size: 16px;
      color: #9ED5FF!important;
      position: absolute;
      left: 10px;
      right: 10px;
      width: auto!important;
      span{
        color: #9ED5FF!important;
      }
    }
  }

  &.boxshow {
    transform: scaleX(1) translateX(-50%) translateY(-50%);
    opacity: 1;
    z-index: 10;
    pointer-events:auto;
  }
}

.tkBox1 {
  width: 600px;
  height: 300px;
  position: absolute;
  top: 50%;
  left: 50%;
  color: #ffffff;
  display: flex;
  flex-flow: column;
  font-size: 20px;
  background: rgba(0, 14, 26, 0.7);
  border: 1px solid #2D5F84;
  backdrop-filter: blur(10px) brightness(110%);
  -webkit-backdrop-filter: blur(10px) brightness(110%);
  transform: translateX(-50%);
  z-index: -1;
  opacity: 0;
  transition: ease-in-out all 0.3s;
  cursor: pointer;

  .topBox {
    width: 100%;
    height: 44px;
    background: url('../../assets/img/tkbg.png') no-repeat center;
    background-size: 100% 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    div:first-child {
      display: flex;
      align-items: center;
      font-size: 20px;

      img {
        width: 40px;
        height: 40px;
      }
    }

    .xiaoshi {
      margin-right: 10px;
      cursor: pointer;

      img {
        width: 26px;
        height: 25px;
      }
    }
  }

  .butBox {
    width: 600px;
    height: 330px;
    display: flex;
    flex-flow: column;
    margin: 10px;
    overflow-y: auto;

    .schooleTop {
      width: 600px;
      display: flex;
      align-items: center;

      .topLeft {
        width: 300px;
        height: 120px;
        background: url('../../assets/img/tkPhoto.png') no-repeat center;
        background-size: 100% 100%;
        position: relative;
        transform: translate(40px, -10px);

        .zszb {
          position: absolute;
          left: 120px;
          top: 32px;
          font-size: 21px;
          font-family: ckt;

        }

        .jz {
          position: absolute;
          left: 120px;
          top: 70px;
          font-size: 32px;
          font-family: PangMenZhengDaoBiaoTiTi-1;
          color: #FFEFC1;
        }
      }

      .topRight {
        width: 260px;
        display: flex;
        flex-direction: column;
        position: relative;

        p {
          width: 260px; // 进度条长度
          height: 8px;
        }

        /* 进度条位置调整 */
        .cont {
          width: 300px;
          background: rgba(255, 255, 255, 0.3);
          position: absolute;
          right: 0px;
          top: 7px
        }

        .cont p {
          background: linear-gradient(224deg, #1870D5 0%, #102B52 100%);
        }

        /* 进度提示数字展示 */
        .txt {
          position: absolute;
          bottom: -4px;
          right: 0px;
          font-size: 20px;
          font-family: PangMenZhengDaoBiaoTiTi-1;
        }
      }
    }

    .slBox {
      width: 100%;
      display: flex;
      justify-content: space-around;
      margin-top: 20px;

      .sbxBox {
        position: relative;
        width: 100px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        color: #E0F1FF;
      }

      .sbxBox1 {
        position: relative;
        width: 100px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        color: #C1FFDE;
      }

      p {
        font-size: 16px;
        font-family: ckt;
      }

      .szBox {
        font-size: 26px;
        font-family: PangMenZhengDaoBiaoTiTi-1;
        color: #E0F1FF;
        background: url('@/assets/img/cmm.png')no-repeat center;
        background-size: 100% 100%;
      }

      .szBox1 {
        font-size: 26px;
        font-family: PangMenZhengDaoBiaoTiTi-1;
        color: #C1FFDE;
        background: url('@/assets/img/cmm.png')no-repeat center;
        background-size: 100% 100%;
      }

    }
  }

  &.boxshow {
    transform: scaleX(1) translateX(-50%) translateY(-50%);
    opacity: 1;
    z-index: 10;
    pointer-events:auto;
  }
}


.active {
  color: #ffffff !important;
  border: 1px solid #9ED5FF !important;
  box-shadow: inset 0 0 10px #007FFF;
  background: #163B67;
  font-size: 14px;
}

.map-level {
  position: relative;
  width: 100%;
  height: 100%;

  .map-con{
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 2;
  }


  #leftBox{
    position: absolute;
    inset: 0;
    z-index: 3;
    pointer-events:none;
  }

}

.bottomBox {
  position: absolute;
  bottom: 15px;
  left: 420px;
  z-index: 10;
  color: #8795a4;
  padding: 7px 12px;
  display: flex;
  width: 60px;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  background: rgba(24, 49, 78, 0.7);
  border: 1px solid #2D5F84;
  cursor: pointer;
}

.bottomBox1 {
  position: absolute;
  bottom: 15px;
  left: 526px;
  z-index: 10;
  color: #8795a4;
  padding: 7px 12px;
  display: flex;
  align-items: center;
  width: 60px;
  justify-content: center;
  font-size: 14px;
  background: rgba(24, 49, 78, 0.7);
  border: 1px solid #2D5F84;
  cursor: pointer;
}

.bottomsg {
  position: absolute;
  bottom: 16px;
  left: 515px;
  z-index: 10;

  img {
    width: 2px;
    height: 30px;
  }

}

.bottomBox2 {
  width: 60px;
  position: absolute;
  bottom: 15px;
  left: 626px;
  z-index: 10;
  color: #8795a4;
  padding: 7px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  background: rgba(24, 49, 78, 0.7);
  border: 1px solid #2D5F84;
  cursor: pointer;
}

.bottomBox3 {
  width: 100px;
  position: absolute;
  bottom: 15px;
  left: 722px;
  z-index: 10;
  color: #8795a4;
  padding: 7px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  background: rgba(24, 49, 78, 0.7);
  border: 1px solid #2D5F84;
  cursor: pointer;
}

.bottomBox4 {
  position: absolute;
  bottom: 15px;
  left: 860px;
  z-index: 10;
  width: 60px;
  color: #8795a4;
  padding: 7px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  background: rgba(24, 49, 78, 0.7);
  border: 1px solid #2D5F84;
  cursor: pointer;
}

.bottomBox5 {
  position: absolute;
  bottom: 15px;
  left: 955px;
  z-index: 10;
  color: #8795a4;
  width: 60px;
  padding: 7px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  background: rgba(24, 49, 78, 0.7);
  border: 1px solid #2D5F84;
  cursor: pointer;
}

.bottomBox6 {
  width: 100px;
  position: absolute;
  bottom: 15px;
  left: 1050px;
  z-index: 10;
  color: #8795a4;
  padding: 7px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  background: rgba(24, 49, 78, 0.7);
  border: 1px solid #2D5F84;
  cursor: pointer;
}

.top {
  position: absolute;
  top: -116px;
  left: 0;
  width: 100%;
  height: 243px;
  transition: ease-in-out all .5s;
  background: url("../../assets/img/tops.png") no-repeat 0 0;
  background-size: 100% 100%;
  z-index: 9;
  opacity: 0;
  display: flex;
  pointer-events:auto;

  &.show {
    top: 0;
    opacity: 1;
  }
}

.layout1 {
  position: absolute;
  bottom: 15px;
  left: 0;
  display: flex;
  transition: ease-in-out all .5s;

  &.botBox1 {
    bottom: -15px;
    opacity: 0;

    &.show {
      bottom: 15px;
      opacity: 1;
    }
  }
}

.layout {
  position: absolute;
  width: 370px;
  bottom: 30px;
  top: 100px;
  background: url("../../assets/img/lrbg.png") no-repeat 0 0;
  background-size: 100% 100%;
  backdrop-filter: blur(5px) brightness(110%);
  -webkit-backdrop-filter: blur(5px) brightness(110%);
  transition: ease-in-out all .5s;
  z-index: 9;
  display: flex;
  justify-content: center;
  align-items: center;

  &.left {
    left: -370px;
    opacity: 0;
    pointer-events:auto;

    &.show {
      left: 30px;
      opacity: 1;
    }
  }

  &.right {
    right: -370px;
    opacity: 0;
    pointer-events:auto;

    &.show {
      right: 30px;
      opacity: 1;
    }
  }

  img {
    width: calc(100% - 30px);
    height: calc(100% - 30px);
  }
}

.bottom {
  position: absolute;
  bottom: -63px;
  left: 0;
  width: 100%;
  height: 63px;
  transition: ease-in-out all .5s;
  background: url("../../assets/img/bottom.png") no-repeat 0 0;
  background-size: 100% 100%;
  z-index: 9;
  opacity: 0;

  &.show {
    bottom: 0;
    opacity: 1;
  }
}

.btnbox {
  position: absolute;
  right: 430px;
  width: 600px;
  top: 100px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  z-index: 9;
  opacity: 0;
  transition: ease-in-out all .5s;
  transform: scale(0.5);

  &.show {
    transform: scale(1);
    opacity: 1;
  }

  img {
    width: 30%;
  }
}

// 返回按钮
.return-btn {
  position: absolute;
  left: 50%;
  bottom: 80px;
  transform: translateX(-50%);
  padding: 10px 25px;
  border-radius: 1000px;
  color: #d0e1ff;
  border: 1px solid #d0e1ff55;
  margin-bottom: 10px;
  font-size: 16px;
  text-align: center;
  display: none;
  cursor: pointer;
  transition: all 0.2s;
  z-index: 99;
  pointer-events:auto;
  background: rgba(0,90,255,.4);
  box-shadow: 0 8px 24px rgba(0,90,255,.6);

  &:hover {
    background: rgba(0,90,255,.8);
    transform: translateX(-50%) translateY(5px);
    box-shadow: 0 2px 5px rgba(0,90,255,1);
  }
}

// 右侧按钮组
.map-btn-group {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);

  .btn {
    padding: 5px 12px;
    color: #fff;
    border: 1px solid #2bc4dc;
    margin-bottom: 10px;
    font-size: 12px;
    text-align: center;
    opacity: 0.5;
    cursor: pointer;
    transition: all 0.3s;

    &.active {
      opacity: 1;
    }
  }
}

// 信息框
.info-point {
  background: rgba(0, 0, 0, 0.5);
  color: #a3dcde;
  font-size: 14px;
  width: 170px;
  height: 106px;
  padding: 16px 12px 0;
  margin-bottom: 30px;

  &-wrap {

    &:after,
    &:before {
      display: block;
      content: "";
      position: absolute;
      top: 0;
      width: 15px;
      height: 15px;
      border-top: 1px solid #4b87a6;
    }

    &:before {
      left: 0;
      border-left: 1px solid #4b87a6;
    }

    &:after {
      right: 0;
      border-right: 1px solid #4b87a6;
    }

    &-inner {

      &:after,
      &:before {
        display: block;
        content: "";
        position: absolute;
        bottom: 0;
        width: 15px;
        height: 15px;
        border-bottom: 1px solid #4b87a6;
      }

      &:before {
        left: 0;
        border-left: 1px solid #4b87a6;
      }

      &:after {
        right: 0;
        border-right: 1px solid #4b87a6;
      }
    }
  }

  &-line {
    position: absolute;
    top: 7px;
    right: 12px;
    display: flex;

    .line {
      width: 5px;
      height: 2px;
      margin-right: 5px;
      background: #17e5c3;
    }
  }

  &-content {
    .content-item {
      display: flex;
      height: 28px;
      line-height: 28px;
      background: rgba(35, 47, 58, 0.6);
      margin-bottom: 5px;

      .label {
        width: 60px;
        padding-left: 10px;
      }

      .value {
        color: #fff;
      }
    }
  }
}

// 标牌
.badges-label {
  z-index: 99999;

  &-outline {
    position: absolute;
  }

  &-wrap {
    position: relative;
    padding: 10px 10px;
    background: #0e1937;
    border: 1px solid #1e7491;
    font-size: 12px;
    font-weight: bold;
    color: #fff;
    // margin-bottom: 50px;
    bottom: 50px;
    z-index: 99999;

    span {
      color: #ffe70b;
    }

    &:after {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 10px;
      height: 10px;
      display: block;
      content: "";
      border-right: 2px solid #6cfffe;
      border-bottom: 2px solid #6cfffe;
    }

    &:before {
      position: absolute;
      left: 0;
      top: 0;
      width: 10px;
      height: 10px;
      display: block;
      content: "";
      border-left: 2px solid #6cfffe;
      border-top: 2px solid #6cfffe;
    }

    .icon {
      position: absolute;
      width: 27px;
      height: 20px;
      left: 50%;
      transform: translateX(-13px);
      bottom: -40px;
    }
  }
}

.area-name-label {
  &-wrap {
    color: #5fc6dc;
    opacity: 1;
    text-shadow: 1px 1px 0px #000;
  }
}

.provinces-name-label {

  //市州名称
  &-wrap {
    font-size: 20px;
    color: #5fc6dc;
    opacity: 0;
    text-shadow: 1px 1px 0px #000;
  }
}

.provinces-label-style02,
.provinces-label-style03 {
  transform: translate(0,-28%);
  &-wrap {
    transform: translate(0%, 100%);
    opacity: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 80px;
    z-index: 2;
  }

  .numberBox{
    width: 600px;
    height:auto;
    background: rgba(0, 14, 26, 0.9);
    border: 1px solid #2D5F84;
    .numberTitle{
      width: 100%;
      height: 80px;
      background: url('../../assets/img/tkbg.png') no-repeat center;
      background-size: 100% 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      span{
        color: #ffffff;
        font-size: 36px;
        font-family: ckt;
      }
      .cityname{
        opacity: 0;
      }
      .cityname1{
        opacity: 1;
        position: absolute;
        width:auto;
        white-space: nowrap;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%,);
      }
    }
    .numberCon{
      width: 100%;
      height: auto;
      padding:15px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      gap:20px;
      box-sizing: border-box;
      p{
        width: calc(33% - 15px);
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        .title{
          width: 100%;
          font-size: 28px;
          text-align: center;
        }
        .value{
          font-size: 40px;
          font-family: PangMenZhengDao;
          margin: 0 15px;
        }
        .unit{
          font-size: 22px;
          color: rgba(255,255,255,.3);
        }
        &:nth-child(1){
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          background: rgba(255,255,255,.15);
          border: 1px solid #E0F1FF77;
          padding:15px 0;
          border-radius: 12px;
          .title{
            width: auto;
            color: #E0F1FF;
          }
          .value{
            color: #E0F1FF;
          }
          .unit{
            color: #E0F1FF;
          }
        }
        &:nth-child(2){
          .title{
            color: #C3FFC1;
          }
          .value{
            color: #C3FFC1;
            text-shadow: 0 0 5px rgba(21, 255, 138, .8);
          }
          .unit{
            color: #C3FFC1;
          }
        }
        &:nth-child(3){
          .title{
            color: #FFEFC1;
          }
          .value{
            color: #FFEFC1;
            text-shadow: 0 0 5px rgba(255, 112, 21, .8);
          }
          .unit{
            color: #FFEFC1;
          }
        }
        &:nth-child(4){
          .title{
            color: #9EFFFC;
          }
          .value{
            color: #9EFFFC;
            text-shadow: 0 0 5px rgba(255, 112, 21, .8);
          }
          .unit{
            color: #9EFFFC;
          }
        }
        &:nth-child(5){
          .title{
            color: #FFBF9E;
          }
          .value{
            color: #FFBF9E;
            text-shadow: 0 0 5px rgba(255, 112, 21, .8);
          }
          .unit{
            color: #FFBF9E;
          }
        }
        &:nth-child(6){
          .title{
            color: #69E8FF;
          }
          .value{
            color: #69E8FF;
            text-shadow: 0 0 5px rgba(255, 112, 21, .8);
          }
          .unit{
            color: #69E8FF;
          }
        }
        &:nth-child(7){
          .title{
            color: #FF9EC2;
          }
          .value{
            color: #FF9EC2;
            text-shadow: 0 0 5px rgba(255, 112, 21, .8);
          }
          .unit{
            color: #FF9EC2;
          }
        }
      }
    }
  }

  .defaultNum {
    padding: 15px 35px;
    background: rgba(0, 0, 0, .9);
    border-radius: 1000px 1000px 1000px 0;
    position: relative;
    .cityname{
      position: absolute;
      background: #000000;
      left: 0;
      bottom: 0;
      transform: translateX(calc(-100% + 20px));
      background: rgba(0, 0, 0, .9);
      border-radius: 1000px 0 20px 1000px;
      padding: 15px 35px;
      white-space: nowrap;
      font-size: 32px;
      font-weight: normal;
    }
    .cityname{
      opacity: 0;
      position: absolute;
      background: #000000;
      left: 0;
      bottom: 0;
      transform: translateX(calc(-100% + 20px));
      background: rgba(0, 0, 0, .9);
      border-radius: 1000px 0 20px 1000px;
      padding: 15px 35px;
      white-space: nowrap;
      font-size: 32px;
      font-weight: normal;
    }
    .cityname1{
      position: absolute;
      background: #000000;
      left: 0;
      bottom: 0;
      transform: translateX(calc(-100% + 20px));
      background: rgba(0, 0, 0, .9);
      border-radius: 1000px 0 20px 1000px;
      padding: 15px 35px;
      white-space: nowrap;
      font-size: 32px;
      font-weight: normal;
    }
  }

  .number {
    color: #E0F1FF;
    font-size: 44px;
    font-weight: bold;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    text-shadow: 0 0 5px rgba(21, 154, 255, .8);

    .title {
      color: #fff;
      font-size: 26px;
      padding-left: 15px;
      font-weight: normal;
    }

    .unit {
      color: #fff;
      font-size: 22px;
      opacity: 0.5;
      padding-left: 5px;
      margin-top: 8px;
      font-weight: normal;
    }

    &.zhibiao {
      color: #C3FFC1;
      font-size: 44px;
      text-shadow: 0 0 5px rgba(21, 255, 138, .8);

      .title {
        color: #C3FFC1;
        font-size: 26px;
        padding-left: 15px;
        font-weight: normal;
      }

      .unit {
        color: #C3FFC1;
        font-size: 22px;
        opacity: 0.5;
        padding-left: 5px;
        margin-top: 8px;
        font-weight: normal;
      }
    }

    &.luqu {
      background: rgba(0, 0, 0, .9);
      padding: 5px 35px;
      border-radius: 1000px;
      color: #FFEFC1;
      font-size: 44px;
      text-shadow: 0 0 5px rgba(255, 112, 21, .8);
      zoom: 0.9;

      .title {
        color: #FFEFC1;
        font-size: 26px;
        padding-left: 15px;
        font-weight: normal;
      }

      .unit {
        color: #FFEFC1;
        font-size: 22px;
        opacity: 0.5;
        padding-left: 5px;
        margin-top: 8px;
        font-weight: normal;
      }
    }

    &.luqu1 {
      background: rgba(0, 0, 0, .9);
      padding: 5px 35px;
      border-radius: 1000px;
      color: #9EFFFC;
      font-size: 44px;
      text-shadow: 0 0 5px rgba(255, 112, 21, .8);
      zoom: 0.9;

      .title {
        color: #9EFFFC;
        font-size: 26px;
        padding-left: 15px;
        font-weight: normal;
      }

      .unit {
        color: #9EFFFC;
        font-size: 22px;
        opacity: 0.5;
        padding-left: 5px;
        margin-top: 8px;
        font-weight: normal;
      }
    }

    &.luqu2 {
      background: rgba(0, 0, 0, .9);
      padding: 5px 35px;
      border-radius: 1000px;
      color: #A4FF9E;
      font-size: 44px;
      text-shadow: 0 0 5px rgba(255, 112, 21, .8);
      zoom: 0.9;

      .title {
        color: #A4FF9E;
        font-size: 26px;
        padding-left: 15px;
        font-weight: normal;
      }

      .unit {
        color: #A4FF9E;
        font-size: 22px;
        opacity: 0.5;
        padding-left: 5px;
        margin-top: 8px;
        font-weight: normal;
      }
    }

    &.luqu3 {
      background: rgba(0, 0, 0, .9);
      padding: 5px 35px;
      border-radius: 1000px;
      color: #F9FF9E;
      font-size: 44px;
      text-shadow: 0 0 5px rgba(255, 112, 21, .8);
      zoom: 0.9;

      .title {
        color: #F9FF9E;
        font-size: 26px;
        padding-left: 15px;
        font-weight: normal;
      }

      .unit {
        color: #F9FF9E;
        font-size: 22px;
        opacity: 0.5;
        padding-left: 5px;
        margin-top: 8px;
        font-weight: normal;
      }
    }

    &.luqu4 {
      background: rgba(0, 0, 0, .9);
      padding: 5px 35px;
      border-radius: 1000px;
      color: #FFBF9E;
      font-size: 44px;
      text-shadow: 0 0 5px rgba(255, 112, 21, .8);
      zoom: 0.9;

      .title {
        color: #FFBF9E;
        font-size: 26px;
        padding-left: 15px;
        font-weight: normal;
      }

      .unit {
        color: #FFBF9E;
        font-size: 22px;
        opacity: 0.5;
        padding-left: 5px;
        margin-top: 8px;
        font-weight: normal;
      }
    }

    &.luqu5 {
      background: rgba(0, 0, 0, .9);
      padding: 5px 35px;
      border-radius: 1000px;
      color: #FF9EC2;
      font-size: 44px;
      text-shadow: 0 0 5px rgba(255, 112, 21, .8);
      zoom: 0.9;

      .title {
        color: #FF9EC2;
        font-size: 26px;
        padding-left: 15px;
        font-weight: normal;
      }

      .unit {
        color: #FF9EC2;
        font-size: 22px;
        opacity: 0.5;
        padding-left: 5px;
        margin-top: 8px;
        font-weight: normal;
      }
    }
  }

  .no {
    //排名数字
    display: flex;
    justify-content: center;
    align-items: center;
    color: #7efbf6;
    /* text-shadow: 0px 0px 4px 0px #7efbf6; */
    text-shadow: 0 0 5px #7efbf6;
    font-size: 26px;
    /* font-weight: 700; */
    width: auto;
    height: 40px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 1000px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    padding: 0 15px;
  }

  .yellow {
    .no {
      color: #fef99e !important;
      text-shadow: 0 0 5px #fef99e !important;
    }
  }
}

.fixed-loading {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 99;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.5);
}

.page-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 10px;
}

.page-loading {
  width: 30px;
  height: 30px;
  border: 2px solid #fff;
  border-top-color: transparent;
  border-radius: 100%;
  animation: loading infinite 0.75s linear;
}

@keyframes loading {
  from {
    transform: rotate(0);
  }

  to {
    transform: rotate(360deg);
  }
}

.cruise {
  position: absolute;
  right: 4.5in;
  height: 100px;
  //top: 90%;
  //bottom: 10px;
  font-size: 20px;
  color: #fff;
  z-index: 99;
  //padding: 10px 30px;
  cursor: pointer;
  display: flex;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  width: 70px;
  transition: ease-in-out all .5s;

  &.botBox2 {
    bottom: -15px;
    opacity: 0;

    &.show {
      opacity: 1;
      bottom: 15px;
    }
  }

  .spanBox {
    height: 30px;
    width: 70px;
    display: flex;
    justify-content: center;
    font-size: 14px;
    margin-top: -10px;
  }

  img {
    width: 70px;
    height: 70px;
  }
}


.atlasCity {
  position: absolute;
  top: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
}

</style>
