<template>
  <div class="bigBox">
      <div class="topLeft">
        <img class="logoB" src="../../assets/top_logo.svg" />
        <span><img src="../../assets/site.svg" />{{ areaName }}</span>
      </div>
      <div class="topMiddle">
        {{ topTitle }}
      </div>
      <div class="topRight">
        <div class="dateTime">{{formatDate(date)}}</div>
        <div class="weekTime">{{currentWeek}}</div>
        <div class="weekTime">{{currentTime}}</div>
      </div>
  </div>
</template>
<script setup>
import {onMounted, ref, onUnmounted, defineProps} from 'vue';
const currentTime = ref(new Date().toLocaleTimeString());

const props = defineProps({
  topTitle : "甘肃省教育入学一件事大数据看板",
  areaName : ""
})

const updateTime = () => {
  currentTime.value = new Date().toLocaleTimeString();
};
const date = ref(new Date());
function formatDate(date) {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
}
onMounted(()=>{
  updateTime();
  const intervalId = setInterval(updateTime, 1000);

  // 清理定时器
  onUnmounted(() => {
    clearInterval(intervalId);
  });
})

let weekDay = new Date().getDay()
let weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
let currentWeek= weeks[weekDay]
</script>
<style scoped lang="scss">
.bigBox{
  width: 100%;
  height: 77px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .topLeft{
    display: flex;
    width: 700px;
    justify-content: flex-start;
    align-items: center;
    img.logoB{
      height: 36px;
      margin-left: 20px;
      opacity: .7;
    }
    span{
      display: flex;
      justify-content: center;
      align-items: center;
      color: rgba(255,255,255,1);
      font-size: 18px;
      opacity: .7;
      margin-left: 30px;
      img{
        height: 18px;
        margin-right: 10px;
      }
    }
  }
  .topMiddle{
    text-shadow: 0px 1px 30px #65B4F1 ;
    font-size: 30px;
    font-family: ckt;
    background: -webkit-linear-gradient(#fff, #fff,#65B4F1,#65B4F1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    width: calc(100% - 1400px);
    display: flex;
    justify-content: center;

  }
  .topRight{
    width: 700px;
    height: 77px;
    display: flex;
    font-family: PangMenZhengDaoBiaoTiTi-1;
    color: #ffffff;
    font-size: 24px;
    align-items: center;
    justify-content: flex-end;
    opacity: .7;
    .dateTime{
      margin:0 30px ;
    }
    .weekTime{
      margin-right: 30px;
    }
  }
}
</style>
