<template>
  <div class="leftBox">
      <div class="rkqk">
        <div class="title"><span>招生数据总览</span></div>
        <div class="cardBox">
          <img src="../../assets/icon/icon1.png" alt="">
          <div>
            <p><a>{{ dataResult.zszqx }}</a><span>个</span></p>
            <p>招生中区县</p>
          </div>
        </div>
        <div class="cardBox" style="width:calc(50% - 5px)">
          <img src="../../assets/icon/icon3.png" alt="">
          <div>
            <p><a>{{ dataResult.zzzbs }}</a><span>人</span></p>
            <p>招生指标</p>
          </div>
        </div>
        <div class="cardBox">
          <img src="../../assets/icon/icon4.png" alt="">
          <div>
            <p><a>{{ dataResult.bmrs }}</a><span>人  </span>/  <a class="sz1">{{ dataResult.lqrs }}</a><span>人</span></p>
            <p>报名人数 / 录取人数</p>
          </div>
        </div>
      </div>

    <div class="lqqk">
      <div class="title"><span>录取数据总览</span></div>
      <div class="dlSum">
        <img src="../../assets/img/bz (5).svg" alt=""> <span>自主填报</span><span>{{ dataResult.xsbm }}</span><span>人</span>
      </div>
      <div class="edubox">
        <div class="schoolCon">
          <a>{{ dataResult.db }}</a>
          <span>批量代填</span>
        </div>
        <div class="schoolCon">
          <a>{{ dataResult.tj }}</a>
          <span>在线调剂</span>
        </div>
        <div class="schoolCon">
          <a>{{ dataResult.zg }}</a>
          <span>政策照顾</span>
        </div>
      </div>
      <div class="title_sub"><span>录取学生性别比例</span></div>
      <div class="sexbox">
        <div>
          <p><span>{{ dataResult.nan }}</span></p>
          <p>男性（{{ dataResult.nanPercent }}%）</p>
        </div>
        <div>
          <p><span>{{ dataResult.nv }}</span></p>
          <p>女性（{{ dataResult.nvPercent }}%）</p>
        </div>
      </div>
    </div>

      <div class="bigBox" @mouseover="stopInterval" @mouseout="continueInterval">
        <div class="tabTitle">
          <span :class="{ now : tagShow == 0 }" @click="tagShow = 0">一次初审通过率排行</span>
          <span :class="{ now : tagShow == 1 }" @click="tagShow = 1">二次初审驳回率排行</span>
        </div>
        <div class="schoolCharts1" :class="{ chartsShow1 : tagShow == 0 }" ref="schoolChart1"></div>
        <div class="schoolCharts2" :class="{ chartsShow1 : tagShow == 1 }" ref="schoolChart2"></div>
        <a v-if="tagShow == 0" @click="capClick" class="more">········查看全部一次初审通过········</a>
        <a v-if="tagShow == 1" @click="capClick1" class="more">········查看全部二次初审驳回········</a>
      </div>
  </div>
</template>
<script setup>

import {defineEmits, onMounted, ref, defineProps,onUnmounted} from 'vue';
import * as echarts from 'echarts';
const schoolChart1 = ref(null);
const schoolChart2 = ref(null);
let echartBarOption=null
let echartBarOption1=null

const tagShow=ref(0)
const tz = ref([])



const x = () => {
  if(tagShow.value == 0){
    tagShow.value = 1
  }else if(tagShow.value == 1){
    tagShow.value = 0
  }
}

let intervalId;
onMounted(() => {
  intervalId = setInterval(x, 8000);
  window.addEventListener('resize', handleResize);
})

const stopInterval = () => {
  clearInterval(intervalId);
};
const continueInterval = () => {
  intervalId = setInterval(x, 8000);
};
// 弹框
const emit = defineEmits(['showPop'])
const capClick=()=>{
  emit('showPop')
}
const capClick1=()=>{
  emit('showPop1')
}

const roundedResult =(inputValue)=>{
  return Math.round(inputValue * 100);
}

const props = defineProps({
  dataResult: {sdqx: 0, zszqx: 0, xxsl: 0, zzzbs:0, bmrs:0, lqrs: 0, plc: 0, pyc: 0, bjs: 0, pjbjsc : 0}
})
const handleResize = () => {
  if (echartBarOption) {
    echartBarOption.resize();
  }
  if (echartBarOption1) {
    echartBarOption1.resize();
  }
};
const createdChart = (data) => {
  echartBarOption = echarts.init(schoolChart1.value);
  echartBarOption1 = echarts.init(schoolChart2.value);

  let dataX1=[];
  let dataY1=[];

  if(data && data.successRateList){
    var successRateList = data.successRateList;

    let index = 20;
    if(successRateList.length < 20){
      index = successRateList.length;
    }

    for (let i = 0; i < index; i++) {
      dataX1.push(successRateList[i].successRate)
      dataY1.push(successRateList[i].deptName)
    }
  }

  echartBarOption.value = {
    title: {
      text: "",
      textStyle: {
        color: "#6dc3ff",
        fontSize: 14,
        fontWeight: 'bold',
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      top: 0,
      right: 50,
      left: 100,
      bottom: 0
    },
    xAxis: {
      type: 'value',
      splitLine: { show: false },// 不显示网格线
      axisTick: { show: false },// 不显示坐标轴刻度线
      axisLine: { show: false },// 不显示坐标轴线
      axisLabel: {
        show:false
      },
    },
    yAxis: [
      {
        type: 'category',
        name: '',
        data: dataY1,
        axisLabel: {
          color: '#fff',
          fontSize: 12,
        },
        plitLine: { show: false },// 不显示网格线
        axisTick: { show: false },// 不显示坐标轴刻度线
        axisLine: { show: false },// 不显示坐标轴线
        inverse: true,
      },
    ],
    series: [
      {
        data: dataX1,
        type: 'bar',
        barWidth: '10px',
        stack: 'Total',
        label: {
          show: true,
          position: 'right',
          color:'#85DDDD'
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: '#85DDDD' },
            { offset: 1, color: '#85DDDD00' }
          ])
        },
      }
    ],
  };
  const chartEcharts = echarts.init(schoolChart1.value);
  chartEcharts.setOption(echartBarOption.value);

  let dataX2=[];
  let dataY2=[];

  if(data && data.NoPassRateList){
    var NoPassRateList = data.NoPassRateList;

    let index = 20;
    if(NoPassRateList.length < 20){
      index = NoPassRateList.length;
    }

    for (let i = 0; i < index; i++) {
      dataX2.push(NoPassRateList[i].errorRate)
      dataY2.push(NoPassRateList[i].deptName)
    }
  }

  echartBarOption1.value= {
    title: {
      text: "",
      textStyle: {
        color: "#6dc3ff",
        fontSize: 14,
        fontWeight: 'bold',
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      top: 0,
      right: 50,
      left: 100,
      bottom: 0
    },
    xAxis: {
      type: 'value',
      splitLine: { show: false },// 不显示网格线
      axisTick: { show: false },// 不显示坐标轴刻度线
      axisLine: { show: false },// 不显示坐标轴线
      axisLabel: {
        show:false
      },
    },
    yAxis: [
      {
        type: 'category',
        name: '',
        data: dataY2,
        axisLabel: {
          color: '#fff',
          fontSize: 12,
        },
        plitLine: { show: false },// 不显示网格线
        axisTick: { show: false },// 不显示坐标轴刻度线
        axisLine: { show: false },// 不显示坐标轴线
        inverse: true,
      },
    ],
    series: [
      {
        data: dataX2,
        type: 'bar',
        barWidth: '10px',
        stack: 'Total',
        label: {
          show: true,
          position: 'right',
          color:'#85DDDD'
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: '#85DDDD' },
            { offset: 1, color: '#85DDDD00' }
          ])
        },
      }
    ],
  };
  const chartEcharts1 = echarts.init(schoolChart2.value);
  chartEcharts1.setOption(echartBarOption1.value);
}

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  echartBarOption && echartBarOption.dispose(); // 清理图表实例
  echartBarOption1 && echartBarOption1.dispose();
});
defineExpose({
  createdChart
})

</script>
<style scoped lang="scss">
$font-size1:22px;
$font-size2:20px;
$font-sizeB:24px;

.lqqk{
  width: 90%;
  margin: 10px 5% 0 5%;
  position: absolute;
  top:200px;
  overflow: hidden;
  .dlSum{
    width: 100%;
    height:auto;
    display: flex;
    align-items: center;
    margin:5px 0;
    font-size: 16px;
    font-family: ckt;
    font-weight: 600;
    color: #fff;
    img{
      width: 40px;
      height: 30px;
    }
    span{
      display: inline;
      font-family: PangMenZhengDaoBiaoTiTi-1;
      font-size:$font-sizeB;
      color: #ffefc1;
      text-shadow:0 0 3px hsla(52, 100%, 50%, 0.973);
      font-weight: 500;
      background: none;
      &:nth-child(2){
        color: #ffffff;
        text-shadow:none;
        font-weight: normal;
        font-size: 20px;
      }
      &:nth-child(3){
        padding:0 10px;
      }
    }
    span:last-child{
      display: inline;
      font-size: 15px;
      color: #5a7587;
      margin-bottom: -5px;
      text-shadow:none;
      font-family: Source Han Sans CN;
    }
  }
  .edubox{
    display: flex;
    justify-content: space-around;
    width: 100%;
    .schoolCon{
      display: flex;
      flex-flow: column;
      align-items: center;
      a{
        font-family: PangMenZhengDaoBiaoTiTi-1;
        font-size: $font-size2;
        &::after{
          content:"人";
          font-size: 12px;
          font-family: normal;
          margin-left: 5px;
          color: #5a7587!important;
          text-shadow: none;
        }
      }
      &:nth-child(1){
        a{
          color: #E0F1FF;
          text-shadow: 0 0 10px #0099e6;
        }
      }
      &:nth-child(2){
        a{
          color: #b4ebb4;
          text-shadow:0 0 10px #00ff00;
        }
      }
      &:nth-child(3){
        a{
          color: #dfb8bb;
          text-shadow:0 0 5px #ff6f6f;
        }
      }
      span{
        display: flex;
        flex-flow: column;
        justify-content: space-between;
        align-items: center;
        font-size: 13px;
        width:80px;
        height: 28px;
        margin-top: 10px;
        color: #ffffff;
        background: rgba(60,158,255,1);  /* fallback for old browsers */
        background: -webkit-linear-gradient(to right,rgba(60,158,255,0),rgba(60,158,255,.5), rgba(60,158,255,0));
        background: linear-gradient(to right,rgba(60,158,255,0),rgba(60,158,255,.5), rgba(60,158,255,0));
        &::before,&::after{
          content: "";
          width: 100%;
          height: 1px;
          background: rgba(60,158,255,1);  /* fallback for old browsers */
          background: -webkit-linear-gradient(to right,rgba(60,158,255,0),rgba(60,158,255,1), rgba(60,158,255,0));
          background: linear-gradient(to right,rgba(60,158,255,0),rgba(60,158,255,1), rgba(60,158,255,0));
        }
      }
    }
  }
  .sexbox{
    display: flex;
    width: 100%;
    margin: 10px 0 0 0;
    div{
      width: 100%;
      color: #b2cfdd;
      display: flex;
      flex-direction: column;
      text-align: center;
      font-family: ckt;
      font-size: 14px;
      text-shadow: 0 0 10px rgba(38,131,213,.64);
      span{
        color: #4bccfa;
        font-family: PangMenZhengDaoBiaoTiTi-1;
        font-size: $font-size1;
        text-shadow:0 0 10px #4bccfa;
      }
      &:nth-child(2){
        color: #dfb8bb;
        text-shadow: 0 0 10px rgba(255,90,90,.64);
        span{
          color: #ff6f6f;
          text-shadow:0 0 1px #ff6f6f;
        }
      }
    }
  }
}



.bigBox{
  width: 90%;
  margin: 0 5%;
  position: absolute;
  top:490px;
  bottom:15px;
  overflow: hidden;
  .tabTitle{
    width: 100%;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    span{
      width: auto;
      height: 36px;
      box-shadow: 0 0 15px rgba(41,136,255,.3) inset;
      color: rgba(255,255,255,.6);
      display: flex;
      justify-content: center;
      align-items: center;
      padding:0 15px;
      white-space: nowrap;
      cursor: pointer;
      &.now{
        box-shadow: 0 0 25px rgba(41,136,255,.8) inset;
        color: rgba(255,255,255,1);
      }
    }
  }
  a.more{
    position: absolute;
    bottom:0px;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(255,255,255,.3);
    cursor: pointer;
    width: 100%;
    &:hover{
      color: rgba(255,255,255,.8);
    }
  }
}
.schoolCharts1{
  position: absolute;
  inset: 0;
  top: 40px;
  bottom:20px;
  width: auto;
  height: auto;
  transition: ease-in-out all .2s;
  transform: translateX(-100%);
  &.chartsShow1{
    transform: translateX(0);
  }
}
.schoolCharts2{
  position: absolute;
  inset: 0;
  top: 40px;
  width: auto;
  height: auto;
  bottom:20px;
  transition: ease-in-out all .2s;
  transform: translateX(100%);
  &.chartsShow1{
    transform: translateX(0);
  }
}
::-webkit-scrollbar{
  display: none;
}
.leftBox{
  position: fixed;
  top:0;
  bottom: 0;
  z-index: 4;
  width: 380px;
  height: auto;
  z-index: 4;
  display: flex;
  justify-content: flex-start;
  flex-flow:column ;
  .title{
    width: 100%;
    margin: 10px 0 0 0;
    height: 33px;
    background: url("../../assets/img/tit_bg.png") no-repeat 0 0;
    background-size: 100% auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding:0 0 6px 0;
    span{
      //width: 100%;
      margin-left: 50px;
      font-size: 18px;
      font-family: ckt;
      color: #ffffff;
      padding: 0 20px 0 0;
      background: url("../../assets/img/bz (3).svg") no-repeat right 50%;
      background-size: auto 50%;
    }
    img{
      width: 15px;
      height: 15px;
      margin-left: 15px;
    }
    a{
      color: #dff8ff;
      font-size: 14px;
      margin-right: 10px;
    }
  }

  .title_sub{
    margin: 5px 0 0px 0;
    padding:12px 0 16px 50px;
    background: url("../../assets/img/title_bg.png") no-repeat;
    background-size: 100% 100%;
    span{
      color: #fff;
      font-family: ckt;
      font-size: 15px;
    }
  }
  .title_sub{
    //width: 100%;
    padding:10px 0 12px 50px;
    background: url("../../assets/img/title_bg.png")no-repeat;
    background-size: 100% 100%;
    margin:15px 0 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    div:first-child{
      color: #63a8b0;
      font-size: 15px;
      span{
        color: #fff;
        font-family: ckt;
        font-size: 15px;
      }
    }
    div:last-child{
      color: #dff8ff;
      font-size: 14px;
      transition: ease-in-out all .2s;
    }
  }



    .rkqk{
      width: 90%;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      margin: 10px 5% 0 5%;
      .cardBox{
        min-width: calc(50% - 5px);
        display: flex;
        margin: 10px 0 0 0;
        img{
          width: auto;
          height: 60px;
          margin-right: 10px;
          margin-left: 5px;
        }
        div{
          display: flex;
          flex-flow: column;
          justify-content: center;
          p:nth-child(1){
            font-size: 16px;
            color: rgba(255,255,255,.5);
            a{
              font-family: PangMenZhengDaoBiaoTiTi-1;
              font-size: $font-size1;
              color: #E0F1FF;
              text-shadow: 0 0 15px #E0F1FF;
              margin-right: 2px;
            }
            span{
              font-size: 12px;
              margin-left: 5px;
            }
          }
          p:nth-child(2){
            font-size: 16px;
            color: rgba(255,255,255,1);
          }
        }
      }
    }
    .rkqk1{
      width: 100%;
      display: flex;
      //justify-content: space-around;
      flex-wrap: wrap;
      padding:10px;
      margin: 0 5%;
      .cardBox{
        min-width: 44%;
        display: flex;
        margin: 10px 0 0 0;
        align-items: flex-start;
        div:first-child{
          width: 60px;
          background: url("../../assets/img/bz (10).svg");
          background-size: cover;
          background-position: center center;
          background-repeat: no-repeat;
          margin-right: 10px;
          img{
            width: 36px;
            height: 38px;
            padding:0 0 32px 12px;
          }
        }

        div{
          display: flex;
          flex-flow: column;
          justify-content: center;
          p:nth-child(1){
            font-size: 13px;
            color: rgba(255,255,255,.7);
            span{
              font-size: 12px;
              color: rgba(255,255,255,.5);
            }
          }
          p:nth-child(2){
            font-size: 12px;

            color: rgba(255,255,255,.5);
            span{
              color: #E0F1FF;
              text-shadow: 0 0 10px #0099e6;
              font-family: PangMenZhengDaoBiaoTiTi-1;
              font-size: 24px;
            }
            .xg{
              font-size: 20px;
            }
            .sz1{
              color: #ffefc1;
              font-family: PangMenZhengDaoBiaoTiTi-1;
              font-size: 24px;
              font-weight: normal;
              margin-right: 5px;
            }
            .sz2{
              font-size: 24px;
              color: #c1ffde;
              font-family: PangMenZhengDaoBiaoTiTi-1;
              text-shadow: 0 0 15px #c1ffde;
              font-weight: normal;
              margin-right: 5px;
            }
          }
        }
      }
    }
}

.sz1{
  color: #ffefc1!important;
  text-shadow: 0 0 15px rgba(255,218,113,1)!important;
}
.sz2{
  color: #c1ffde!important;
  font-size: 16px!important;
  text-shadow: 0 0 15px #c1ffde!important;
}
.more{
  font-size: 14px!important;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
</style>
