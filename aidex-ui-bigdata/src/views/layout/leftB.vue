<template>
  <div class="leftBox">
      <div class="rkqk">
        <div class="title"><span>招生数据总览</span></div>
        <div class="cardBox">
          <img src="../../assets/icon/icon1.png" alt="">
          <div>
            <p><a>{{ dataResult.zszqx }}</a><span>个</span></p>
            <p>招生中区县</p>
          </div>
        </div>
        <div class="cardBox" style="width:calc(50% - 5px)">
          <img src="../../assets/icon/icon3.png" alt="">
          <div>
            <p><a>{{ dataResult.zzzbs }}</a><span>人</span></p>
            <p>招生指标</p>
          </div>
        </div>
        <div class="cardBox">
          <img src="../../assets/icon/icon4.png" alt="">
          <div>
            <p><a>{{ dataResult.bmrs }}</a><span>人  </span>/  <a class="sz1">{{ dataResult.lqrs }}</a><span>人</span></p>
            <p>报名人数 / 录取人数</p>
          </div>
        </div>
        <div class="cardBox">
          <img src="../../assets/icon/icon5.png" alt="">
          <div>
            <p><a>{{ dataResult.bjs }}</a><span>件  </span>/  <a class="sz1">{{ dataResult.bjs - dataResult.bmrs }}</a><span>件  </span><a class="sz2">{{ ((dataResult.bjs - dataResult.bmrs)/dataResult.bjs*100).toFixed(2)}}</a><span>%</span></p>
            <p>总办件量 / 初审驳回量及占比</p>
          </div>
        </div>
        <div class="cardBox">
          <img src="../../assets/icon/icon7.png" alt="">
          <div>
            <p><a>{{ dataResult.lqrs }}</a><span>件  </span>/  <a class="sz1">{{ dataResult.plc }}</a><span>件  </span><a class="sz2">{{ (dataResult.plc/dataResult.lqrs*100).toFixed(2)}}</a><span>%</span></p>
            <p>录取人数 / 跑0次录取人数及占比</p>
          </div>
        </div>

        <div class="cardBox">
          <img src="../../assets/icon/icon6.png" alt="">
          <div>
            <p><a>{{ (dataResult.pjbjsc / 3600).toFixed(2) }}</a><span>小时  </span>/  <a class="sz1">{{ dataResult.bjs }}</a><span>件</span></p>
            <p>平均初审时长 / 办件总数</p>
          </div>
        </div>


        <div class="cardBox">
          <img src="../../assets/icon/icon8.png" alt="">
          <div>
            <p><a>{{ dataResult.applyFirstTrial }}</a><span>人  </span>/  <a class="sz1">{{ dataResult.applyFirstTrialSuccess }}</a><span>人</span><a class="sz2">{{ dataResult.applyFirstTrialSuccessRate }}</a><span>%</span></p>
            <p>初审数量 / 一次初审通过量及占比</p>
          </div>
        </div>

        <div class="cardBox">
          <img src="../../assets/icon/icon9.png" alt="">
          <div>
            <p><a>{{ dataResult.applyFirstTrial }}</a><span>人  </span>/  <a class="sz1">{{ dataResult.applyFirstTrialError }}</a><span>人</span><a class="sz2">{{ dataResult.applyFirstTrialErrorRate }}</a><span>%</span></p>
            <p>初审数量 / 二次初审驳回量及占比</p>
          </div>
        </div>

      </div>
      <div class="bigBox">
<!--        <div class="title"><span>区县办件时长TOP10</span><a @click="capClick" class="more">查看全部</a></div>-->
        <div class="tabTitle">
          <span :class="{ now : tagShow == 0 }" @click="tagShow = 0">初审办件时长排行</span>
          <span :class="{ now : tagShow == 1 }" @click="tagShow = 1">现场审核办件时长排行</span>
        </div>
        <div class="schoolCharts1" :class="{ chartsShow1 : tagShow == 0 }" ref="schoolChart1"></div>
        <div class="schoolCharts2" :class="{ chartsShow1 : tagShow == 1 }" ref="schoolChart2"></div>
        <a v-if="tagShow == 0" @click="capClick" class="more">········查看全部初审办件········</a>
        <a v-if="tagShow == 1" @click="capClick1" class="more">········查看全部现场审核········</a>
      </div>
  </div>
</template>
<script setup>

import {defineEmits, onMounted, ref, defineProps,onUnmounted} from 'vue';
import * as echarts from 'echarts';
const schoolChart1 = ref(null);
const schoolChart2 = ref(null);
let echartBarOption=null
let echartBarOption1=null

const tagShow=ref(0)
const tz = ref([])



const x = () => {
  if(tagShow.value == 0){
    tagShow.value = 1
  }else if(tagShow.value == 1){
    tagShow.value = 0
  }
}

let intervalId;
onMounted(() => {
  intervalId = setInterval(x, 5000);
  createdChart();
  window.addEventListener('resize', handleResize);
})

// 弹框
const emit = defineEmits(['showPop'])
const capClick=()=>{
  emit('showPop')
}
const capClick1=()=>{
  emit('showPop1')
}

const roundedResult =(inputValue)=>{
  return Math.round(inputValue * 100);
}

const props = defineProps({
  dataResult: {sdqx: 0, zszqx: 0, xxsl: 0, zzzbs:0, bmrs:0, lqrs: 0, plc: 0, pyc: 0, bjs: 0, pjbjsc : 0}
})
const handleResize = () => {
  if (echartBarOption) {
    echartBarOption.resize();
  }
  if (echartBarOption1) {
    echartBarOption1.resize();
  }
};
const createdChart = (data) => {
  echartBarOption = echarts.init(schoolChart1.value);
  echartBarOption1 = echarts.init(schoolChart2.value);

  let dataX=[];
  let dataY=[];
  if(data && data.bizProcessingTimeListCs){
    var bizProcessingTimeList = data.bizProcessingTimeListCs;
    for (let i = (bizProcessingTimeList.length - 1); i >= 0; i--) {
      dataX.push((bizProcessingTimeList[i].processesTime/ 3600).toFixed(2))
      dataY.push(bizProcessingTimeList[i].areaName)
    }
    // dataX.sort((a, b) =>  b-a);
  }

  echartBarOption.value = {
    title: {
      text: "",
      textStyle: {
        color: "#6dc3ff",
        fontSize: 14,
        fontWeight: 'bold',
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      top: 0,
      right: 50,
      left: 100,
      bottom: 0
    },
    xAxis: {
      type: 'value',
      splitLine: { show: false },// 不显示网格线
      axisTick: { show: false },// 不显示坐标轴刻度线
      axisLine: { show: false },// 不显示坐标轴线
      axisLabel: {
        show:false
      },
    },
    yAxis: [
      {
        type: 'category',
        name: '',
        data: dataY,
        axisLabel: {
          color: '#fff',
          fontSize: 12,
        },
        plitLine: { show: false },// 不显示网格线
        axisTick: { show: false },// 不显示坐标轴刻度线
        axisLine: { show: false },// 不显示坐标轴线
      },
    ],
    series: [
      {
        data: dataX,
        type: 'bar',
        barWidth: '10px',
        stack: 'Total',
        label: {
          show: true,
          position: 'right',
          color:'#85DDDD'
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: '#85DDDD' },
            { offset: 1, color: '#85DDDD00' }
          ])
        },
      }
    ],
  };
  const chartEcharts = echarts.init(schoolChart1.value);
  chartEcharts.setOption(echartBarOption.value);

  let dataMsX=[];
  let dataMsY=[];
  if(data && data.bizProcessingTimeListMs){
    var bizProcessingTimeListMs = data.bizProcessingTimeListMs;
    for (let i = (bizProcessingTimeListMs.length - 1); i >= 0; i--) {
      dataMsX.push((bizProcessingTimeListMs[i].processesTime/ 3600).toFixed(2))
      dataMsY.push(bizProcessingTimeListMs[i].areaName)
    }
    // dataX.sort((a, b) =>  b-a);
  }

  echartBarOption1.value= {
    title: {
      text: "",
      textStyle: {
        color: "#6dc3ff",
        fontSize: 14,
        fontWeight: 'bold',
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      top: 0,
      right: 50,
      left: 100,
      bottom: 0
    },
    xAxis: {
      type: 'value',
      splitLine: { show: false },// 不显示网格线
      axisTick: { show: false },// 不显示坐标轴刻度线
      axisLine: { show: false },// 不显示坐标轴线
      axisLabel: {
        show:false
      },
    },
    yAxis: [
      {
        type: 'category',
        name: '',
        data: dataMsY,
        axisLabel: {
          color: '#fff',
          fontSize: 12,
        },
        plitLine: { show: false },// 不显示网格线
        axisTick: { show: false },// 不显示坐标轴刻度线
        axisLine: { show: false },// 不显示坐标轴线
      },
    ],
    series: [
      {
        data: dataMsX,
        type: 'bar',
        barWidth: '10px',
        stack: 'Total',
        label: {
          show: true,
          position: 'right',
          color:'#85DDDD'
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: '#85DDDD' },
            { offset: 1, color: '#85DDDD00' }
          ])
        },
      }
    ],
  };
  const chartEcharts1 = echarts.init(schoolChart2.value);
  chartEcharts1.setOption(echartBarOption1.value);
}

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  echartBarOption && echartBarOption.dispose(); // 清理图表实例
  echartBarOption1 && echartBarOption1.dispose();
});
defineExpose({
  createdChart
})

</script>
<style scoped lang="scss">
.bigBox{
  width: 90%;
  margin: 0 5%;
  position: absolute;
  top:560px;
  bottom:15px;
  overflow: hidden;
  .tabTitle{
    width: 100%;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    span{
      width: auto;
      height: 36px;
      box-shadow: 0 0 15px rgba(41,136,255,.3) inset;
      color: rgba(255,255,255,.6);
      display: flex;
      justify-content: center;
      align-items: center;
      padding:0 15px;
      white-space: nowrap;
      cursor: pointer;
      &.now{
        box-shadow: 0 0 25px rgba(41,136,255,.8) inset;
        color: rgba(255,255,255,1);
      }
    }
  }
  a.more{
    position: absolute;
    bottom:0px;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(255,255,255,.3);
    cursor: pointer;
    width: 100%;
    &:hover{
      color: rgba(255,255,255,.8);
    }
  }
}
.schoolCharts1{
  position: absolute;
  inset: 0;
  top: 40px;
  bottom:20px;
  width: auto;
  height: auto;
  transition: ease-in-out all .2s;
  transform: translateX(-100%);
  &.chartsShow1{
    transform: translateX(0);
  }
}
.schoolCharts2{
  position: absolute;
  inset: 0;
  top: 40px;
  width: auto;
  height: auto;
  bottom:20px;
  transition: ease-in-out all .2s;
  transform: translateX(100%);
  &.chartsShow1{
    transform: translateX(0);
  }
}
::-webkit-scrollbar{
  display: none;
}
.leftBox{
  position: fixed;
  top:0;
  bottom: 0;
  z-index: 4;
  width: 380px;
  height: auto;
  z-index: 4;
  display: flex;
  justify-content: flex-start;
  flex-flow:column ;
  .title{
    width: 100%;
    margin: 10px 0 0 0;
    height: 33px;
    background: url("../../assets/img/tit_bg.png") no-repeat 0 0;
    background-size: 100% auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding:0 0 6px 0;
    span{
      //width: 100%;
      margin-left: 50px;
      font-size: 18px;
      font-family: ckt;
      color: #ffffff;
      padding: 0 20px 0 0;
      background: url("../../assets/img/bz (3).svg") no-repeat right 50%;
      background-size: auto 50%;
    }
    img{
      width: 15px;
      height: 15px;
      margin-left: 15px;
    }
    a{
      color: #dff8ff;
      font-size: 14px;
      margin-right: 10px;
    }
  }
  .title_sub{
    //width: 100%;
    padding:10px 0 12px 50px;
    background: url("../../assets/img/title_bg.png")no-repeat;
    background-size: 100% 100%;
    margin:15px 7% 0 3%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    div:first-child{
      color: #63a8b0;
      font-size: 15px;
      span{
        color: #fff;
        font-family: ckt;
        font-size: 15px;
      }
    }
    div:last-child{
      color: #dff8ff;
      font-size: 14px;
      transition: ease-in-out all .2s;
    }
  }

    .rkqk{
      width: 90%;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      margin: 10px 5% 0 5%;
      .cardBox{
        min-width: calc(50% - 5px);
        display: flex;
        margin: 10px 0 0 0;
        img{
          width: auto;
          height: 60px;
          margin-right: 10px;
          margin-left: 5px;
        }
        div{
          display: flex;
          flex-flow: column;
          justify-content: center;
          p:nth-child(1){
            font-size: 12px;
            color: rgba(255,255,255,.5);
            a{
              font-family: PangMenZhengDaoBiaoTiTi-1;
              font-size: 22px;
              color: #E0F1FF;
              text-shadow: 0 0 15px #E0F1FF;
              margin-right: 2px;
            }
            span{
              font-size: 12px;
              margin-left: 5px;
            }
          }
          p:nth-child(2){
            font-size: 12px;
            color: rgba(255,255,255,1);
          }
        }
      }
    }
    .rkqk1{
      width: 100%;
      display: flex;
      //justify-content: space-around;
      flex-wrap: wrap;
      padding:10px;
      margin: 0 5%;
      .cardBox{
        min-width: 44%;
        display: flex;
        margin: 10px 0 0 0;
        align-items: flex-start;
        div:first-child{
          width: 60px;
          background: url("../../assets/img/bz (10).svg");
          background-size: cover;
          background-position: center center;
          background-repeat: no-repeat;
          margin-right: 10px;
          img{
            width: 36px;
            height: 38px;
            padding:0 0 32px 12px;
          }
        }

        div{
          display: flex;
          flex-flow: column;
          justify-content: center;
          p:nth-child(1){
            font-size: 13px;
            color: rgba(255,255,255,.7);
            span{
              font-size: 12px;
              color: rgba(255,255,255,.5);
            }
          }
          p:nth-child(2){
            font-size: 12px;

            color: rgba(255,255,255,.5);
            span{
              color: #E0F1FF;
              text-shadow: 0 0 10px #0099e6;
              font-family: PangMenZhengDaoBiaoTiTi-1;
              font-size: 24px;
            }
            .xg{
              font-size: 20px;
            }
            .sz1{
              color: #ffefc1;
              font-family: PangMenZhengDaoBiaoTiTi-1;
              font-size: 24px;
              font-weight: normal;
              margin-right: 5px;
            }
            .sz2{
              font-size: 24px;
              color: #c1ffde;
              font-family: PangMenZhengDaoBiaoTiTi-1;
              text-shadow: 0 0 15px #c1ffde;
              font-weight: normal;
              margin-right: 5px;
            }
          }
        }
      }
    }
}

.sz1{
  color: #ffefc1!important;
  text-shadow: 0 0 15px rgba(255,218,113,1)!important;
}
.sz2{
  color: #c1ffde!important;
  font-size: 16px!important;
  text-shadow: 0 0 15px #c1ffde!important;
}
.more{
  font-size: 14px!important;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
</style>
