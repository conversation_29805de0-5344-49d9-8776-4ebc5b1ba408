<template>
  <div class="rightBox">
    <div class="rightTop">
        <div class="title"><span>办件数据总览</span></div>
        <div class="cardBox">
          <img src="../../assets/icon/icon5.png" alt="">
          <div>
            <p><a>{{ dataResult.bjs }}</a><span>件  </span>/  <a class="sz1">{{ (dataResult.pjbjsc / 3600).toFixed(2) }}</a><span>小时  </span></p>
            <p>总办件量 / 平均初审时长</p>
          </div>
        </div>
        <div class="cardBox">
          <img src="../../assets/icon/icon7.png" alt="">
          <div>
            <p><a>{{ dataResult.lqrs }}</a><span>人  </span>/  <a class="sz1">{{ dataResult.plc }}</a><span>人  </span><a class="sz2">{{ dataResult.lqrs != null && dataResult.lqrs !== 0 ? (dataResult.plc / dataResult.lqrs * 100).toFixed(2) : 0 }}</a><span>%</span></p>
            <p>录取人数 / 跑0次录取人数及占比</p>
          </div>
        </div>

      <div class="cardBoxSub">
        <div>
          <p><a class="sz1">{{ kexinlvData.fckx.count }}</a><span>件  </span><a class="sz2">{{ kexinlvData.fckx.bz }}</a><span>%</span></p>
          <p>房产可信数及占比</p>
        </div>
      </div>

      <div class="cardBoxSub">
        <div>
          <p><a class="sz1">{{ kexinlvData.hjkx.count }}</a><span>件  </span><a class="sz2">{{ kexinlvData.hjkx.bz }}</a><span>%</span></p>
          <p>户籍可信数及占比</p>
        </div>
      </div>

      <div class="cardBoxSub end">
        <div>
          <p><a class="sz1">{{ kexinlvData.syskx.count }}</a><span>件  </span><a class="sz2">{{ kexinlvData.syskx.bz }}</a><span>%</span></p>
          <p>三要素可信数及占比</p>
        </div>
      </div>

        <div class="cardBox">
          <img src="../../assets/icon/icon8.png" alt="">
          <div>
            <p><a>{{ dataResult.applyFirstTrial }}</a><span>人  </span>/  <a class="sz1">{{ dataResult.applyFirstTrialSuccess }}</a><span>人</span><a class="sz2">{{ dataResult.applyFirstTrialSuccessRate }}</a><span>%</span></p>
            <p>初审数量 / 一次初审通过量及占比</p>
          </div>
        </div>

        <div class="cardBox">
          <img src="../../assets/icon/icon9.png" alt="">
          <div>
            <p><a>{{ dataResult.applyFirstTrial }}</a><span>人  </span>/  <a class="sz1">{{ dataResult.applyFirstTrialError }}</a><span>人</span><a class="sz2">{{ dataResult.applyFirstTrialErrorRate }}</a><span>%</span></p>
            <p>初审数量 / 二次初审驳回量及占比</p>
          </div>
        </div>

<!--        <div class="cardBox">-->
<!--          <img src="../../assets/icon/icon6.png" alt="">-->
<!--          <div>-->
<!--            <p><a>{{ (dataResult.pjbjsc / 3600).toFixed(2) }}</a><span>小时  </span>/  <a class="sz1">{{ dataResult.bjs }}</a><span>件</span></p>-->
<!--            <p>平均初审时长 / 办件总数</p>-->
<!--          </div>-->
<!--        </div>-->
    </div>

<!--    <div class="rightMiddle">-->
<!--      <div class="title"><span>数据可信率</span></div>-->
<!--      <div class="kjkey" ref="kjkeyCharts"></div>-->
<!--    </div>-->

    <div class="rightBottom" @mouseover="stopInterval" @mouseout="continueInterval">
      <div class="tabTitle">
        <span :class="{ now : tagShow == 0 }" @click="tagShow = 0">初审办件时长排行</span>
        <span :class="{ now : tagShow == 1 }" @click="tagShow = 1">跑0次录取占比排行</span>
      </div>
      <div class="schoolCharts1" :class="{ chartsShow1 : tagShow == 0 }" ref="rightChart1">1</div>
      <div class="schoolCharts2" :class="{ chartsShow1 : tagShow == 1 }" ref="rightChart2">2</div>
      <a v-if="tagShow == 0" @click="capClick2" class="more">········查看全部初审办件········</a>
      <a v-if="tagShow == 1" @click="capClick3" class="more">········查看全部跑0次占比········</a>
    </div>

<!--    <div class="title1"> <div><span>热点学校</span><img src="../../assets/img/bz (3).svg" /></div> <div @click="ckClick">查看全部</div> </div>-->
<!--    <vue3-seamless-scroll :list="dataResult.overStandardRateList" :hover="true"  class="scroll" :step="0.5">-->
<!--    <div class="bottom_Box">-->
<!--      <div class="box_big" v-for="(v,i) in dataResult.overStandardRateList" :key="i">-->
<!--        <div class="xiaoxue">{{v.schoolName}}</div>-->
<!--        <div class="shixian">{{ replaceText(v.deptName, '教育局', '')}}</div>-->
<!--        <div class="box_small">-->
<!--          <div class="zbs"><span></span> 指标数<span class="zbssl">{{v.applyNumberFirst}} 人</span></div>-->
<!--          <div class="lqs"><span></span> 录取数<span class="lqssl">{{v.lqrs}} 人</span></div>-->
<!--          <div class="cbl" v-if="v.overStandardRate > 1"><span></span> 超标率<span class="cblsl">{{ roundedResult(v.overStandardRate) }} %</span></div>-->
<!--          <div class="cbl" v-else><span></span> 录取率<span class="cblsl">{{ roundedResult(v.overStandardRate) }} %</span></div>-->
<!--        </div>-->
<!--      </div>-->

<!--    </div>-->
<!--    </vue3-seamless-scroll>-->

  </div>
</template>
<script setup>
import {onMounted, ref, defineEmits, defineProps, onUnmounted} from 'vue';
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import * as echarts from 'echarts';

const rightChart1 = ref(null);
const rightChart2 = ref(null);
let zdecharts=null
let zdecharts1=null

const kjkeyCharts = ref(null);
let kjkeyChartsOption=null;
// 弹框
const emit = defineEmits(['showPop'])
const capClick2=()=>{
  emit('showPop')
}
const capClick3=()=>{
  emit('showPop1')
}

const tagShow=ref(0)
const props = defineProps({
  dataResult: {xsbm: 0,db: 0, tj: 0, zg:0, nan:0, nv: 0, nanPercent:0, nvPercent: 0, overStandardRateList:[]}
})

const roundedResult =(inputValue)=>{
  return Math.round(inputValue * 100);
}

const x = () => {
  if(tagShow.value == 0){
    tagShow.value = 1
  }else if(tagShow.value == 1){
    tagShow.value = 0
  }
}
const handleResize = () => {
  if (zdecharts) {
    zdecharts.resize();
  }
  if (zdecharts1) {
    zdecharts1.resize();
  }
  if (kjkeyChartsOption) {
    kjkeyChartsOption.resize();
  }
};
// 初审
const createdChart = (data) => {
  zdecharts = echarts.init(rightChart1.value);
  zdecharts1 = echarts.init(rightChart2.value);

  let dataX=[];
  let dataY=[];
  if(data && data.bizProcessingTimeListCs){
    var bizProcessingTimeList = data.bizProcessingTimeListCs;
    for (let i = (bizProcessingTimeList.length - 1); i >= 0; i--) {
      dataX.push((bizProcessingTimeList[i].processesTime/ 3600).toFixed(2))
      dataY.push(bizProcessingTimeList[i].areaName)
    }
    // dataX.sort((a, b) =>  b-a);
  }

  zdecharts.value = {
    title: {
      text: "",
      textStyle: {
        color: "#6dc3ff",
        fontSize: 14,
        fontWeight: 'bold',
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      top: 0,
      right: 50,
      left: 100,
      bottom: 0
    },
    xAxis: {
      type: 'value',
      splitLine: { show: false },// 不显示网格线
      axisTick: { show: false },// 不显示坐标轴刻度线
      axisLine: { show: false },// 不显示坐标轴线
      axisLabel: {
        show:false
      },
    },
    yAxis: [
      {
        type: 'category',
        name: '',
        data: dataY,
        axisLabel: {
          color: '#fff',
          fontSize: 12,
        },
        plitLine: { show: false },// 不显示网格线
        axisTick: { show: false },// 不显示坐标轴刻度线
        axisLine: { show: false },// 不显示坐标轴线
      },
    ],
    series: [
      {
        data: dataX,
        type: 'bar',
        barWidth: '10px',
        stack: 'Total',
        label: {
          show: true,
          position: 'right',
          color:'#3497FF'
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: '#3497FF' },
            { offset: 1, color: '#3497FF00' }
          ])
        },
      }
    ],
  };
  // 产值
  const chartEcharts = echarts.init(rightChart1.value);
  chartEcharts.setOption(zdecharts.value);

  let dataMsX=[];
  let dataMsY=[];
  if(data && data.skipZeroAuditList){
    var skipZeroAuditList = data.skipZeroAuditList;

    let index = 21;
    if(skipZeroAuditList.length < 21){
      index = skipZeroAuditList.length;
    }

    for (let i = (index - 1); i >= 0; i--) {
      dataMsX.push(skipZeroAuditList[i].skipRate)
      dataMsY.push(skipZeroAuditList[i].name)
    }
    // dataX.sort((a, b) =>  b-a);
  }
  zdecharts1.value = {
    title: {
      text: "",
      textStyle: {
        color: "#6dc3ff",
        fontSize: 14,
        fontWeight: 'bold',
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      top: 0,
      right: 50,
      left: 100,
      bottom: 0
    },
    xAxis: {
      type: 'value',
      splitLine: { show: false },// 不显示网格线
      axisTick: { show: false },// 不显示坐标轴刻度线
      axisLine: { show: false },// 不显示坐标轴线
      axisLabel: {
        show:false
      },
    },
    yAxis: [
      {
        type: 'category',
        name: '',
        data: dataMsY,
        axisLabel: {
          color: '#fff',
          fontSize: 12,
        },
        plitLine: { show: false },// 不显示网格线
        axisTick: { show: false },// 不显示坐标轴刻度线
        axisLine: { show: false },// 不显示坐标轴线
      },
    ],
    series: [
      {
        data: dataMsX,
        type: 'bar',
        barWidth: '10px',
        stack: 'Total',
        label: {
          show: true,
          position: 'right',
          color:'#3497FF'
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: '#3497FF' },
            { offset: 1, color: '#3497FF00' }
          ])
        },
      }
    ],
  };

  const chartEcharts1 = echarts.init(rightChart2.value);
  chartEcharts1.setOption(zdecharts1.value);
}

let kexinlvData = {fckx:{name:'', count:0, bz: 0}, hjkx:{name:'', count:0, bz: 0}, syskx:{name:'', count:0, bz: 0}};
const createKexinlv = (data) => {
  if(data && data.hotTags){
    var hotTags = data.hotTags;

    // 算出总数
    let allCount = data.lqrs;
    // for (let i = 0; i < hotTags.length; i++) {
    //   allCount = allCount + hotTags[i].clickCount;
    // }
    for (let i = 0; i < hotTags.length; i++) {
      let bzValue = allCount != null && allCount !== 0 ? (Math.round((hotTags[i].clickCount / allCount) * 10000) / 100).toFixed(2) : 0;
      if(hotTags[i].tagName === "房产可信率"){
        kexinlvData.fckx = {name: hotTags[i].tagName, count: hotTags[i].clickCount, bz: bzValue};
      }else if(hotTags[i].tagName === "户籍可信率"){
        kexinlvData.hjkx = {name: hotTags[i].tagName, count: hotTags[i].clickCount, bz: bzValue};
      }else if(hotTags[i].tagName === "三要素可信率"){
        kexinlvData.syskx = {name: hotTags[i].tagName, count: hotTags[i].clickCount, bz: bzValue};
      }
    }
  }
}

const createTagPieChart = (data) => {


  kjkeyChartsOption = echarts.init(kjkeyCharts.value);

  let alldata =[]

  if(data && data.hotTags){
    var hotTags = data.hotTags;
    for (let i = 0; i < hotTags.length; i++) {
      let dataItem = {value: hotTags[i].clickCount, name: hotTags[i].tagName};
      alldata.push(dataItem);
    }
  }

  kjkeyChartsOption.value = {
    title:{
      show:false,
    },
    tooltip: {
      trigger: 'item',
      formatter: function (params) {
        var res = params.data.name + ' : ' + params.data.value + '(' + params.percent + '%)';
        return res;
      }
    },
    legend: {
      orient: 'horizontal',
      icon: 'circle',
      width:'100%',
      bottom: 0,
      left:'center',
      itemGap:0,
      textStyle: {//图例文字颜色
        color: ['#73DDFF', '#73ACFF', '#FDD56A', '#FD866A', '#9E87FF','#FDB36A'],
        fontSize:12,
      },
      formatter: function(name) {
        var data = alldata;
        var total = 0;
        var tarValue;

        for (var i = 0; i < data.length; i++) {
          total += data[i].value;
          if (data[i].name == name) {
            tarValue = data[i].value;
          }
        }
        var v = tarValue;
        var p = ((tarValue / total) * 100).toFixed(2);
        return `${name}  ${v} (${p}%) `;
      },
    },
    series: [
      {
        name: '',
        type: 'pie',
        center: ['50%', '35%'],
        radius: ['40%', '60%'],
        avoidLabelOverlap: false,
        color: ['#73DDFF', '#73ACFF', '#FDD56A', '#FD866A', '#9E87FF','#FDB36A'],
        itemStyle: {
          borderRadius: 10,
          borderColor: '#000000',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: false,
            fontSize: 40,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: alldata
      }
    ]
  };

  const chartEcharts = echarts.init(kjkeyCharts.value);
  chartEcharts.setOption(kjkeyChartsOption.value);
}

defineExpose({
  createdChart,
  createTagPieChart,
  createKexinlv,
})
let intervalId;

onMounted(() => {
    intervalId = setInterval(x, 8000);
    window.addEventListener('resize', handleResize);
})
const stopInterval = () => {
  clearInterval(intervalId);
};
const continueInterval = () => {
  intervalId = setInterval(x, 8000);
};
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  zdecharts && zdecharts.dispose(); // 清理图表实例
  zdecharts1 && zdecharts1.dispose(); // 清理图表实例
  kjkeyChartsOption && kjkeyChartsOption.dispose(); // 清理图表实例
});
const dataValue = [
  {
    title:'榆中县文成小学',
    text:'兰州市榆中县',
    zbs:440,
    lqs:460,
    cbl:'30%'
  },
  {
    title:'榆中县文成小学',
    text:'兰州市榆中县',
    zbs:440,
    lqs:460,
    cbl:'30%'
  },
  {
    title:'榆中县文成小学',
    text:'兰州市榆中县',
    zbs:440,
    lqs:460,
    cbl:'30%'
  },
  {
    title:'榆中县文成小学',
    text:'兰州市榆中县',
    zbs:440,
    lqs:460,
    cbl:'30%'
  },
  {
    title:'榆中县文成小学',
    text:'兰州市榆中县',
    zbs:440,
    lqs:460,
    cbl:'30%'
  },
  {
    title:'榆中县文成小学',
    text:'兰州市榆中县',
    zbs:440,
    lqs:460,
    cbl:'30%'
  },
  {
    title:'榆中县文成小学',
    text:'兰州市榆中县',
    zbs:440,
    lqs:460,
    cbl:'30%'
  },
  {
    title:'榆中县文成小学',
    text:'兰州市榆中县',
    zbs:440,
    lqs:460,
    cbl:'30%'
  }
]

const replaceText=(text, placeholder, replacement)=>{
  return text.replace(placeholder, replacement);
}
</script>
<style scoped lang="scss">
$font-size1:20px;
$font-size2:20px;

.scroll{
  margin-top: 10px;
  width: 100%;
  //max-height: 220px;
  overflow: hidden;
  margin-top: 20px;
  height: calc(100vh - 490px);
  cursor: pointer;
}
.rightBox{
  position: fixed;
  inset: 0;
  width: auto;
  height: auto;
  z-index: 4;
  .rightBottom{
    position: absolute;
    left: 5%;
    right: 5%;
    bottom:10px;
    height: calc(100% - 530px);
    overflow: hidden;
    .tabTitle{
      width: 100%;
      height: 36px;
      display: flex;
      justify-content: center;
      align-items: center;
      span{
        width: auto;
        height: 36px;
        box-shadow: 0 0 15px rgba(41,136,255,.3) inset;
        color: rgba(255,255,255,.6);
        display: flex;
        justify-content: center;
        align-items: center;
        padding:0 15px;
        white-space: nowrap;
        cursor: pointer;
        &.now{
          box-shadow: 0 0 25px rgba(41,136,255,.8) inset;
          color: rgba(255,255,255,1);
        }
      }
    }
    .schoolCharts1{
      position: absolute;
      inset: 0;
      top: 40px;
      bottom:20px;
      width: auto;
      height: auto;
      transition: ease-in-out all .2s;
      transform: translateX(-100%);
      &.chartsShow1{
        transform: translateX(0);
      }
    }
    .schoolCharts2{
      position: absolute;
      inset: 0;
      top: 40px;
      width: auto;
      height: auto;
      bottom:20px;
      transition: ease-in-out all .2s;
      transform: translateX(100%);
      &.chartsShow1{
        transform: translateX(0);
      }
    }
    a.more{
      position: absolute;
      bottom:0px;
      left: 50%;
      transform: translateX(-50%);
      color: rgba(255,255,255,.3);
      cursor: pointer;
      width: 100%;
      font-size: 14px!important;
      display: flex;
      justify-content: center;
      align-items: center;
      &:hover{
        color: rgba(255,255,255,.8);
      }
    }
  }
  .rightMiddle{
    position: absolute;
    top: 415px;
    left: 5%;
    right: 5%;
    height: calc((100% - 430px) * 0.4);
    .kjkey{
      position: absolute;
      inset: 0;
      top: 50px;
    }
  }
  .rightTop{
    width: 90%;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin: 10px 5% 0 5%;
    .cardBox{
      min-width: calc(50% - 5px);
      display: flex;
      margin: 10px 0 0 0;
      img{
        width: auto;
        height: 60px;
        margin-right: 10px;
        margin-left: 5px;
      }
      div{
        display: flex;
        flex-flow: column;
        justify-content: center;
        p:nth-child(1){
          font-size: 16px;
          color: rgba(255,255,255,.5);
          a{
            font-family: PangMenZhengDaoBiaoTiTi-1;
            font-size: $font-size1;
            color: #E0F1FF;
            text-shadow: 0 0 15px #E0F1FF;
            margin-right: 2px;
          }
          span{
            font-size: 12px;
            margin-left: 5px;
          }
        }
        p:nth-child(2){
          font-size: 16px;
          color: rgba(255,255,255,1);
        }
      }
    }
    .cardBoxSub{
      min-width: 100%;
      display: flex;
      margin: 10px 0 0 0;
      padding:0 0 0 70px;
      position: relative;
      &::before{
        content:'';
        position: absolute;
        left: 25px;
        top: 5px;
        width: 10px;
        height: 10px;
        border: 2px solid rgba(255,255,255,.7);
        background: #2f64c3;
        border-radius: 1000px;
        z-index: 2;
      }
      &::after{
        content:'';
        position: absolute;
        left: 32px;
        top: -10px;
        bottom:0;
        width: 1px;
        height: auto;
        border-left: 1px dotted #95BBFF;
        z-index: 1;
      }
      &.end{
        &::after{
          top: -10px!important;
          bottom:40px!important;
        }
      }
      img{
        width: auto;
        height: 60px;
        margin-right: 10px;
        margin-left: 5px;
      }
      div{
        display: flex;
        flex-flow: column;
        justify-content: center;
        p:nth-child(1){
          font-size: 16px;
          color: rgba(255,255,255,.5);
          a{
            font-family: PangMenZhengDaoBiaoTiTi-1;
            font-size: $font-size1;
            color: #E0F1FF;
            text-shadow: 0 0 15px #E0F1FF;
            margin-right: 2px;
          }
          span{
            font-size: 12px;
            margin-left: 5px;
          }
          .sz2{
            color: #c1ffde!important;
            font-size: $font-size2!important;
            text-shadow: 0 0 15px #c1ffde!important;
          }
        }
        p:nth-child(2){
          font-size: 16px;
          color: rgba(255,255,255,1);
        }
      }
    }
  }
  .title{
    width: 100%;
    margin: 10px 0 0 0;
    height: 33px;
    background: url("../../assets/img/tit_bg.png") no-repeat 0 0;
    background-size: 100% auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding:0 0 6px 0;
    span{
      //width: 100%;
      margin-left: 50px;
      font-size: 18px;
      font-family: ckt;
      color: #ffffff;
      padding: 0 20px 0 0;
      background: url("../../assets/img/bz (3).svg") no-repeat right 50%;
      background-size: auto 50%;
    }
    img{
      width: 15px;
      height: 15px;
      margin-left: 15px;
    }
    a{
      color: #dff8ff;
      font-size: 14px;
      margin-right: 10px;
    }
  }
  .title1{
    cursor: pointer;
    width: 90%;
    margin: 10px 5% -12px 5%;
    height: 33px;
    background: url("../../assets/img/tit_bg.png") no-repeat 0 0;
    background-size: 100% auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding:0 0 6px 0;
    div:first-child{
      span{
        color: #ffffff;
        margin-left: 50px;
        font-size: 18px;
        font-family: ckt;
      }
      img{
        width: 15px;
        height: 15px;
        margin-left: 15px;
      }
    }
    div:last-child{
      color: #dff8ff;
      font-size: 14px;
      margin-right: 5%;
    }
  }
  .title_sub{
    width: 100%;
    margin: 5px 0 -20px 0;
    padding:12px 0 16px 50px;
    background: url("../../assets/img/title_bg.png")no-repeat;
    background-size: 100% 100%;
    span{
      color: #fff;
      font-family: ckt;
      font-size: 15px;
    }
  }

  }


::-webkit-scrollbar{
  display: none;
}
.bottom_Box{
  width: 94%;
  display: flex;
  flex-flow: column;
  overflow-y: auto;
  margin: 20px 3% 0 3%;
  font-size: 16px;
  .xiaoxue {
    color: #ffffff;
  }

  .shixian{
    color: #94DBEF;
    border-bottom: 1px solid #758795;
    padding:5px 0 10px 0;
    font-size: 14px;
  }
  .box_big{
    margin: 1% 3%;
    display: flex;
    flex-flow: column;
    border: 1px solid #214e89;
    border-radius: 10px;
    padding: 15px;
    .box_small{
      margin-top: 10px;
      width: 100%;
      display: flex;
      justify-content: space-between;
      color: #e2f0fd;
      align-items: center;
      .zbs{
        font-size: 12px;
        display: flex;
        align-items: center;
        span:first-child{
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #83DBDC;
          display: block;
          margin-right: 5px;
        }
        img{
          width: 10px;
          height: 10px;
        }
        .zbssl{
          color:#83dbdc ;
          margin-left: 5px;
          font-size: 12px;
        }
      }
      .lqs{
        font-size: 12px;
        display: flex;
        align-items: center;
        span:first-child{
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #FE6F6F;
          display: block;
          margin-right: 5px;
        }
        .lqssl{
          color: #FE6F6F;
          margin-left: 5px;
          font-size: 12px;
        }
      }
      .cbl{
        font-size: 12px;
        display: flex;
        align-items: center;
        span:first-child{
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #F6FF86;
          display: block;
          margin-right: 5px;
        }
        .cblsl{
          color: #F6FF86;
          margin-left: 5px;
          font-size: 12px;
        }
      }
    }
  }
}


.sz1{
  color: #ffefc1!important;
  text-shadow: 0 0 15px rgba(255,218,113,1)!important;
}
.sz2{
  color: #c1ffde!important;
  font-size: $font-size2!important;
  text-shadow: 0 0 15px #c1ffde!important;
}
</style>
