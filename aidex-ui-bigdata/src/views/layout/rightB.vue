<template>
  <div class="rightBox">
    <div class="rightTop">
      <div class="title"><span>录取情况</span></div>
      <div class="dlSum">
        <img src="../../assets/img/bz (5).svg" alt=""> <span>自主填报</span><span>{{ dataResult.xsbm }}</span><span>人</span>
      </div>
      <div class="edubox">
        <div class="schoolCon">
          <a>{{ dataResult.db }}</a>
          <span>批量代填</span>
        </div>
        <div class="schoolCon">
          <a>{{ dataResult.tj }}</a>
          <span>在线调剂</span>
        </div>
        <div class="schoolCon">
          <a>{{ dataResult.zg }}</a>
          <span>政策照顾</span>
        </div>
      </div>
      <div class="title_sub"><span>录取学生性别比例</span></div>
      <div class="sexbox">
        <div>
          <p><span>{{ dataResult.nan }}</span></p>
          <p>男性（{{ dataResult.nanPercent }}%）</p>
        </div>
        <div>
          <p><span>{{ dataResult.nv }}</span></p>
          <p>女性（{{ dataResult.nvPercent }}%）</p>
        </div>
      </div>
    </div>

    <div class="rightMiddle">
      <div class="title"><span>驳回意见占比</span></div>
      <div class="kjkey" ref="kjkeyCharts"></div>
    </div>

    <div class="rightBottom">
      <div class="tabTitle">
        <span :class="{ now : tagShow == 0 }" @click="tagShow = 0">一次初审通过率排行</span>
        <span :class="{ now : tagShow == 1 }" @click="tagShow = 1">二次初审驳回率排行</span>
      </div>
      <div class="schoolCharts1" :class="{ chartsShow1 : tagShow == 0 }" ref="rightChart1">1</div>
      <div class="schoolCharts2" :class="{ chartsShow1 : tagShow == 1 }" ref="rightChart2">2</div>
      <a v-if="tagShow == 0" @click="capClick2" class="more">········查看全部一次初审通过········</a>
      <a v-if="tagShow == 1" @click="capClick3" class="more">········查看全部二次初审驳回········</a>
    </div>

<!--    <div class="title1"> <div><span>热点学校</span><img src="../../assets/img/bz (3).svg" /></div> <div @click="ckClick">查看全部</div> </div>-->
<!--    <vue3-seamless-scroll :list="dataResult.overStandardRateList" :hover="true"  class="scroll" :step="0.5">-->
<!--    <div class="bottom_Box">-->
<!--      <div class="box_big" v-for="(v,i) in dataResult.overStandardRateList" :key="i">-->
<!--        <div class="xiaoxue">{{v.schoolName}}</div>-->
<!--        <div class="shixian">{{ replaceText(v.deptName, '教育局', '')}}</div>-->
<!--        <div class="box_small">-->
<!--          <div class="zbs"><span></span> 指标数<span class="zbssl">{{v.applyNumberFirst}} 人</span></div>-->
<!--          <div class="lqs"><span></span> 录取数<span class="lqssl">{{v.lqrs}} 人</span></div>-->
<!--          <div class="cbl" v-if="v.overStandardRate > 1"><span></span> 超标率<span class="cblsl">{{ roundedResult(v.overStandardRate) }} %</span></div>-->
<!--          <div class="cbl" v-else><span></span> 录取率<span class="cblsl">{{ roundedResult(v.overStandardRate) }} %</span></div>-->
<!--        </div>-->
<!--      </div>-->

<!--    </div>-->
<!--    </vue3-seamless-scroll>-->

  </div>
</template>
<script setup>
import {onMounted, ref, defineEmits, defineProps, onUnmounted} from 'vue';
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import * as echarts from 'echarts';

const rightChart1 = ref(null);
const rightChart2 = ref(null);
let zdecharts=null
let zdecharts1=null

const kjkeyCharts = ref(null);
const kjkeyChartsOption=ref({})
// 弹框
const emit = defineEmits(['showPop'])
const capClick2=()=>{
  emit('showPop')
}
const capClick3=()=>{
  emit('showPop1')
}

const tagShow=ref(0)
const props = defineProps({
  dataResult: {xsbm: 0,db: 0, tj: 0, zg:0, nan:0, nv: 0, nanPercent:0, nvPercent: 0, overStandardRateList:[]}
})

const roundedResult =(inputValue)=>{
  return Math.round(inputValue * 100);
}

const x = () => {
  if(tagShow.value == 0){
    tagShow.value = 1
  }else if(tagShow.value == 1){
    tagShow.value = 0
  }
}
const handleResize = () => {
  if (zdecharts) {
    zdecharts.resize();
  }
  if (zdecharts1) {
    zdecharts1.resize();
  }
};
// 初审
const createdChart = (data) => {
  zdecharts = echarts.init(rightChart1.value);
  zdecharts1 = echarts.init(rightChart2.value);


  let dataX1=[];
  let dataY1=[];

  if(data && data.successRateList){
    var successRateList = data.successRateList;

    for (let i = 0; i < successRateList.length; i++) {
      dataX1.push(successRateList[i].successRate)
      dataY1.push(successRateList[i].deptName)
    }
  }

  zdecharts.value = {
    title: {
      text: "",
      textStyle: {
        color: "#6dc3ff",
        fontSize: 14,
        fontWeight: 'bold',
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      top: 0,
      right: 50,
      left: 100,
      bottom: 0
    },
    xAxis: {
      type: 'value',
      splitLine: { show: false },// 不显示网格线
      axisTick: { show: false },// 不显示坐标轴刻度线
      axisLine: { show: false },// 不显示坐标轴线
      axisLabel: {
        show:false
      },
    },
    yAxis: [
      {
        type: 'category',
        name: '',
        data: dataY1,
        axisLabel: {
          color: '#fff',
          fontSize: 12,
        },
        plitLine: { show: false },// 不显示网格线
        axisTick: { show: false },// 不显示坐标轴刻度线
        axisLine: { show: false },// 不显示坐标轴线
        inverse: true,
      },
    ],
    series: [
      {
        data: dataX1,
        type: 'bar',
        barWidth: '10px',
        stack: 'Total',
        label: {
          show: true,
          position: 'right',
          color:'#3497FF'
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: '#3497FF' },
            { offset: 1, color: '#3497FF00' }
          ])
        },
      }
    ],
  };
  // 产值
  const chartEcharts = echarts.init(rightChart1.value);
  chartEcharts.setOption(zdecharts.value);

  let dataX2=[];
  let dataY2=[];

  if(data && data.NoPassRateList){
    var NoPassRateList = data.NoPassRateList;
    for (let i = 0; i < NoPassRateList.length; i++) {
      dataX2.push(NoPassRateList[i].errorRate)
      dataY2.push(NoPassRateList[i].deptName)
    }
  }
  zdecharts1.value = {
    title: {
      text: "",
      textStyle: {
        color: "#6dc3ff",
        fontSize: 14,
        fontWeight: 'bold',
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      top: 0,
      right: 50,
      left: 100,
      bottom: 0
    },
    xAxis: {
      type: 'value',
      splitLine: { show: false },// 不显示网格线
      axisTick: { show: false },// 不显示坐标轴刻度线
      axisLine: { show: false },// 不显示坐标轴线
      axisLabel: {
        show:false
      },
    },
    yAxis: [
      {
        type: 'category',
        name: '',
        data: dataY2,
        axisLabel: {
          color: '#fff',
          fontSize: 12,
        },
        plitLine: { show: false },// 不显示网格线
        axisTick: { show: false },// 不显示坐标轴刻度线
        axisLine: { show: false },// 不显示坐标轴线
        inverse: true,
      },
    ],
    series: [
      {
        data: dataX2,
        type: 'bar',
        barWidth: '10px',
        stack: 'Total',
        label: {
          show: true,
          position: 'right',
          color:'#3497FF'
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: '#3497FF' },
            { offset: 1, color: '#3497FF00' }
          ])
        },
      }
    ],
  };

  const chartEcharts1 = echarts.init(rightChart2.value);
  chartEcharts1.setOption(zdecharts1.value);
}

const createTagPieChart = (data) => {
  let alldata =[]

  if(data && data.hotTags){
    var hotTags = data.hotTags;
    for (let i = 0; i < hotTags.length; i++) {
      let dataItem = {value: hotTags[i].clickCount, name: hotTags[i].tagName};
      alldata.push(dataItem);
    }
  }

  kjkeyChartsOption.value = {
    title:{
      show:false,
    },
    tooltip: {
      trigger: 'item',
      formatter: function (params) {
        var res = params.data.name + ' : ' + params.data.value + '(' + params.percent + '%)';
        return res;
      }
    },
    legend: {
      orient: 'horizontal',
      icon: 'circle',
      width:'100%',
      bottom: 0,
      left:'center',
      itemGap:0,
      textStyle: {//图例文字颜色
        color: ['#73DDFF', '#73ACFF', '#FDD56A', '#FD866A', '#9E87FF','#FDB36A'],
        fontSize:12,
      },
      formatter: function(name) {
        var data = alldata;
        var total = 0;
        var tarValue;

        for (var i = 0; i < data.length; i++) {
          total += data[i].value;
          if (data[i].name == name) {
            tarValue = data[i].value;
          }
        }
        var v = tarValue;
        var p = ((tarValue / total) * 100).toFixed(2);
        return `${name}  ${v} (${p}%) `;
      },
    },
    series: [
      {
        name: '',
        type: 'pie',
        center: ['50%', '35%'],
        radius: ['40%', '60%'],
        avoidLabelOverlap: false,
        color: ['#73DDFF', '#73ACFF', '#FDD56A', '#FD866A', '#9E87FF','#FDB36A'],
        itemStyle: {
          borderRadius: 10,
          borderColor: '#000000',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: false,
            fontSize: 40,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: alldata
      }
    ]
  };

  const chartEcharts = echarts.init(kjkeyCharts.value);
  chartEcharts.setOption(kjkeyChartsOption.value);
}

defineExpose({
  createdChart,
  createTagPieChart,
})
let intervalId;

onMounted(() => {
    intervalId = setInterval(x, 5000);
    window.addEventListener('resize', handleResize);
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  zdecharts && zdecharts.dispose(); // 清理图表实例
  zdecharts1 && zdecharts1.dispose(); // 清理图表实例
});
const dataValue = [
  {
    title:'榆中县文成小学',
    text:'兰州市榆中县',
    zbs:440,
    lqs:460,
    cbl:'30%'
  },
  {
    title:'榆中县文成小学',
    text:'兰州市榆中县',
    zbs:440,
    lqs:460,
    cbl:'30%'
  },
  {
    title:'榆中县文成小学',
    text:'兰州市榆中县',
    zbs:440,
    lqs:460,
    cbl:'30%'
  },
  {
    title:'榆中县文成小学',
    text:'兰州市榆中县',
    zbs:440,
    lqs:460,
    cbl:'30%'
  },
  {
    title:'榆中县文成小学',
    text:'兰州市榆中县',
    zbs:440,
    lqs:460,
    cbl:'30%'
  },
  {
    title:'榆中县文成小学',
    text:'兰州市榆中县',
    zbs:440,
    lqs:460,
    cbl:'30%'
  },
  {
    title:'榆中县文成小学',
    text:'兰州市榆中县',
    zbs:440,
    lqs:460,
    cbl:'30%'
  },
  {
    title:'榆中县文成小学',
    text:'兰州市榆中县',
    zbs:440,
    lqs:460,
    cbl:'30%'
  }
]

const replaceText=(text, placeholder, replacement)=>{
  return text.replace(placeholder, replacement);
}
</script>
<style scoped lang="scss">

.scroll{
  margin-top: 10px;
  width: 100%;
  //max-height: 220px;
  overflow: hidden;
  margin-top: 20px;
  height: calc(100vh - 490px);
  cursor: pointer;
}
.rightBox{
  position: fixed;
  inset: 0;
  width: auto;
  height: auto;
  z-index: 4;
  .rightBottom{
    position: absolute;
    left: 5%;
    right: 5%;
    bottom:10px;
    height: calc((100% - 280px) * 0.6);
    overflow: hidden;
    .tabTitle{
      width: 100%;
      height: 36px;
      display: flex;
      justify-content: center;
      align-items: center;
      span{
        width: auto;
        height: 36px;
        box-shadow: 0 0 15px rgba(41,136,255,.3) inset;
        color: rgba(255,255,255,.6);
        display: flex;
        justify-content: center;
        align-items: center;
        padding:0 15px;
        white-space: nowrap;
        cursor: pointer;
        &.now{
          box-shadow: 0 0 25px rgba(41,136,255,.8) inset;
          color: rgba(255,255,255,1);
        }
      }
    }
    .schoolCharts1{
      position: absolute;
      inset: 0;
      top: 40px;
      bottom:20px;
      width: auto;
      height: auto;
      transition: ease-in-out all .2s;
      transform: translateX(-100%);
      &.chartsShow1{
        transform: translateX(0);
      }
    }
    .schoolCharts2{
      position: absolute;
      inset: 0;
      top: 40px;
      width: auto;
      height: auto;
      bottom:20px;
      transition: ease-in-out all .2s;
      transform: translateX(100%);
      &.chartsShow1{
        transform: translateX(0);
      }
    }
    a.more{
      position: absolute;
      bottom:0px;
      left: 50%;
      transform: translateX(-50%);
      color: rgba(255,255,255,.3);
      cursor: pointer;
      width: 100%;
      font-size: 14px!important;
      display: flex;
      justify-content: center;
      align-items: center;
      &:hover{
        color: rgba(255,255,255,.8);
      }
    }
  }
  .rightMiddle{
    position: absolute;
    top: 262px;
    left: 5%;
    right: 5%;
    height: calc((100% - 280px) * 0.4);
    .kjkey{
      position: absolute;
      inset: 0;
      top: 50px;
    }
  }
  .rightTop{
    position: absolute;
    top: 10px;
    left: 5%;
    right: 5%;
    height: 252px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  .title{
    width: 100%;
    margin: 10px 0 0 0;
    height: 33px;
    background: url("../../assets/img/tit_bg.png") no-repeat 0 0;
    background-size: 100% auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding:0 0 6px 0;
    span{
      //width: 100%;
      margin-left: 50px;
      font-size: 18px;
      font-family: ckt;
      color: #ffffff;
      padding: 0 20px 0 0;
      background: url("../../assets/img/bz (3).svg") no-repeat right 50%;
      background-size: auto 50%;
    }
    img{
      width: 15px;
      height: 15px;
      margin-left: 15px;
    }
    a{
      color: #dff8ff;
      font-size: 14px;
      margin-right: 10px;
    }
  }
  .title1{
    cursor: pointer;
    width: 90%;
    margin: 10px 5% -12px 5%;
    height: 33px;
    background: url("../../assets/img/tit_bg.png") no-repeat 0 0;
    background-size: 100% auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding:0 0 6px 0;
    div:first-child{
      span{
        color: #ffffff;
        margin-left: 50px;
        font-size: 18px;
        font-family: ckt;
      }
      img{
        width: 15px;
        height: 15px;
        margin-left: 15px;
      }
    }
    div:last-child{
      color: #dff8ff;
      font-size: 14px;
      margin-right: 5%;
    }
  }
  .title_sub{
    width: 100%;
    margin: 5px 0 -20px 0;
    padding:12px 0 16px 50px;
    background: url("../../assets/img/title_bg.png")no-repeat;
    background-size: 100% 100%;
    span{
      color: #fff;
      font-family: ckt;
      font-size: 15px;
    }
  }
    .sexbox{
      display: flex;
      width: 100%;
      margin: 10px 0 0 0;
      div{
        width: 100%;
        color: #b2cfdd;
        display: flex;
        flex-direction: column;
        text-align: center;
        font-family: ckt;
        font-size: 14px;
        text-shadow: 0 0 10px rgba(38,131,213,.64);
        span{
          color: #4bccfa;
          font-family: PangMenZhengDaoBiaoTiTi-1;
          font-size: 22px;
          text-shadow:0 0 10px #4bccfa;
        }
        &:nth-child(2){
          color: #dfb8bb;
          text-shadow: 0 0 10px rgba(255,90,90,.64);
          span{
            color: #ff6f6f;
            text-shadow:0 0 1px #ff6f6f;
          }
        }
      }
    }
  }
    .dlSum{
      width: 100%;
      height:auto;
      display: flex;
      align-items: center;
      margin:5px 0;
      font-size: 16px;
      font-family: ckt;
      font-weight: 600;
      color: #fff;
      img{
        width: 40px;
        height: 30px;
      }
      span{
        display: inline;
        font-family: PangMenZhengDaoBiaoTiTi-1;
        font-size:26px;
        color: #ffefc1;
        text-shadow:0 0 3px hsla(52, 100%, 50%, 0.973);
        font-weight: 500;
        background: none;
        &:nth-child(2){
          color: #ffffff;
          text-shadow:none;
          font-weight: normal;
          font-size: 20px;
        }
        &:nth-child(3){
          padding:0 10px;
        }
      }
      span:last-child{
        display: inline;
        font-size: 15px;
        color: #5a7587;
        margin-bottom: -5px;
        text-shadow:none;
        font-family: Source Han Sans CN;
      }
    }
.edubox{
  display: flex;
  justify-content: space-around;
  width: 100%;
  .schoolCon{
    display: flex;
    flex-flow: column;
    align-items: center;
    a{
      font-family: PangMenZhengDaoBiaoTiTi-1;
      font-size: 22px;
      &::after{
        content:"人";
        font-size: 12px;
        font-family: normal;
        margin-left: 5px;
        color: #5a7587!important;
        text-shadow: none;
      }
    }
    &:nth-child(1){
      a{
        color: #E0F1FF;
        text-shadow: 0 0 10px #0099e6;
      }
    }
    &:nth-child(2){
      a{
        color: #b4ebb4;
        text-shadow:0 0 10px #00ff00;
      }
    }
    &:nth-child(3){
      a{
        color: #dfb8bb;
        text-shadow:0 0 5px #ff6f6f;
      }
    }
    span{
      display: flex;
      flex-flow: column;
      justify-content: space-between;
      align-items: center;
      font-size: 13px;
      width:80px;
      height: 28px;
      margin-top: 10px;
      color: #ffffff;
      background: rgba(60,158,255,1);  /* fallback for old browsers */
      background: -webkit-linear-gradient(to right,rgba(60,158,255,0),rgba(60,158,255,.5), rgba(60,158,255,0));
      background: linear-gradient(to right,rgba(60,158,255,0),rgba(60,158,255,.5), rgba(60,158,255,0));
      &::before,&::after{
        content: "";
        width: 100%;
        height: 1px;
        background: rgba(60,158,255,1);  /* fallback for old browsers */
        background: -webkit-linear-gradient(to right,rgba(60,158,255,0),rgba(60,158,255,1), rgba(60,158,255,0));
        background: linear-gradient(to right,rgba(60,158,255,0),rgba(60,158,255,1), rgba(60,158,255,0));
      }
    }
  }
}
::-webkit-scrollbar{
  display: none;
}
.bottom_Box{
  width: 94%;
  display: flex;
  flex-flow: column;
  overflow-y: auto;
  margin: 20px 3% 0 3%;
  font-size: 16px;
  .xiaoxue {
    color: #ffffff;
  }

  .shixian{
    color: #94DBEF;
    border-bottom: 1px solid #758795;
    padding:5px 0 10px 0;
    font-size: 14px;
  }
  .box_big{
    margin: 1% 3%;
    display: flex;
    flex-flow: column;
    border: 1px solid #214e89;
    border-radius: 10px;
    padding: 15px;
    .box_small{
      margin-top: 10px;
      width: 100%;
      display: flex;
      justify-content: space-between;
      color: #e2f0fd;
      align-items: center;
      .zbs{
        font-size: 12px;
        display: flex;
        align-items: center;
        span:first-child{
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #83DBDC;
          display: block;
          margin-right: 5px;
        }
        img{
          width: 10px;
          height: 10px;
        }
        .zbssl{
          color:#83dbdc ;
          margin-left: 5px;
          font-size: 12px;
        }
      }
      .lqs{
        font-size: 12px;
        display: flex;
        align-items: center;
        span:first-child{
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #FE6F6F;
          display: block;
          margin-right: 5px;
        }
        .lqssl{
          color: #FE6F6F;
          margin-left: 5px;
          font-size: 12px;
        }
      }
      .cbl{
        font-size: 12px;
        display: flex;
        align-items: center;
        span:first-child{
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #F6FF86;
          display: block;
          margin-right: 5px;
        }
        .cblsl{
          color: #F6FF86;
          margin-left: 5px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
