package com.aidex.front;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 前台服务启动程序
 * 
 * <AUTHOR>
 */
@EnableDiscoveryClient
@SpringBootApplication
public class FrontApplication
{
    private static final Logger logger = LoggerFactory.getLogger(FrontApplication.class);

    public static void main(String[] args)
    {
        SpringApplication.run(FrontApplication.class, args);
        logger.info("(♥◠‿◠)ﾉﾞ  前台服务启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
