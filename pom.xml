<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

    <groupId>com.aidex</groupId>
    <artifactId>edu-enrollment</artifactId>
    <version>3.4.0</version>

    <name>edu-enrollment</name>
    <url>http://www.usoftgs.com</url>
    <description>应用服务</description>

    <properties>
        <aidex.version>3.4.0</aidex.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>
        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <druid.version>1.2.14</druid.version>
        <bitwalker.version>1.21</bitwalker.version>
		<kaptcha.version>2.3.2</kaptcha.version>
        <mybatis-spring-boot.version>3.0.4</mybatis-spring-boot.version>
        <pagehelper.boot.version>2.1.0</pagehelper.boot.version>
        <fastjson.version>2.0.16</fastjson.version>
        <oshi.version>6.3.0</oshi.version>
        <commons.io.version>2.11.0</commons.io.version>
        <commons.fileupload.version>1.4</commons.fileupload.version>
        <commons.collections.version>3.2.2</commons.collections.version>
        <poi.version>4.1.2</poi.version>
        <velocity.version>2.3</velocity.version>
        <jwt.version>0.9.1</jwt.version>
        <!-- add 2021-01-28 -->
        <lombok.version>1.18.36</lombok.version>
        <org.mapstruct.version>1.4.1.Final</org.mapstruct.version>
        <pinyin4j.version>2.5.0</pinyin4j.version>
        <xstream-version>1.3.1</xstream-version>
        <com.squareup.retrofit2.version>2.4.0</com.squareup.retrofit2.version>
        <flowable.version>7.1.0</flowable.version>
        <commons.httpclient.version>3.1</commons.httpclient.version>
        <hutool.version>5.8.8</hutool.version>
        <bcprov-jdk15to18.version>1.71</bcprov-jdk15to18.version>
        <minio.version>8.3.9</minio.version>
        <okhttp.version>4.10.0</okhttp.version>
        <okio.version>2.7.0</okio.version>
        <aliyun-sdk-oss.version>3.10.2</aliyun-sdk-oss.version>
        <cos_api.version>5.6.108</cos_api.version>
        <aliyun-java-sdk-core.version>4.5.16</aliyun-java-sdk-core.version>
        <tencentcloud-sdk-java.version>3.1.546</tencentcloud-sdk-java.version>
        <springboot.version>3.4.1</springboot.version>
        <usoft.sdk.version>1.0.15</usoft.sdk.version>

        <!-- Spring Cloud Alibaba 版本 -->
        <spring-cloud.version>2023.0.3</spring-cloud.version>
        <spring-cloud-alibaba.version>2023.0.1.2</spring-cloud-alibaba.version>
        <nacos.version>2.4.3</nacos.version>
        <sentinel.version>1.8.8</sentinel.version>
        <seata.version>2.1.0</seata.version>
        <rocketmq.version>5.3.1</rocketmq.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- Spring Cloud 依赖管理 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Cloud Alibaba 依赖管理 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>com.usoft.springboot</groupId>
                <artifactId>usoft-bom-springboot3</artifactId>
                <version>${usoft.sdk.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- freemarker -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-freemarker</artifactId>
                <version>${springboot.version}</version>
            </dependency>

            <!--数据库连接池 -->
            <dependency>
                <groupId>com.usoft.springboot</groupId>
                <artifactId>usoft-starter-datasource-springboot3</artifactId>
                <version>${usoft.sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.15</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.17.0</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <!-- SpringBoot集成mybatis框架 -->
            <dependency>
                <groupId>com.usoft.springboot</groupId>
                <artifactId>usoft-starter-mybatis-springboot3</artifactId>
                <version>${usoft.sdk.version}</version>
            </dependency>

            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

            <!--  引入lombok 2020-01-28  -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <!--  引入mapstruct 2020-01-28  -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <!--  引入pinyin4j 2020-01-28  -->
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>
            <!--  引入xstream 2021-05-25  -->
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>${xstream-version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
                <scope>compile</scope>
            </dependency>

            <!--io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!--文件上传工具类 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- collections工具类 -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons.collections.version}</version>
            </dependency>

            <!--Token生成与解析-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!--验证码 -->
            <dependency>
                <groupId>com.github.penggle</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- HTTP 协议的客户端编程工具包 -->
            <dependency>
                <groupId>commons-httpclient</groupId>
                <artifactId>commons-httpclient</artifactId>
                <version>${commons.httpclient.version}</version>
            </dependency>

            <!-- hutool -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- 定时任务-->
            <dependency>
                <groupId>com.aidex</groupId>
                <artifactId>aidex-quartz</artifactId>
                <version>${aidex.version}</version>
            </dependency>

            <!-- 代码生成-->
            <dependency>
                <groupId>com.aidex</groupId>
                <artifactId>aidex-generator</artifactId>
                <version>${aidex.version}</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>com.aidex</groupId>
                <artifactId>aidex-framework</artifactId>
                <version>${aidex.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.aidex</groupId>
                <artifactId>aidex-system</artifactId>
                <version>${aidex.version}</version>
            </dependency>

            <!-- 通用工具-->
            <dependency>
                <groupId>com.aidex</groupId>
                <artifactId>aidex-common</artifactId>
                <version>${aidex.version}</version>
            </dependency>

            <!-- flowable -->
            <dependency>
                <groupId>com.aidex</groupId>
                <artifactId>aidex-flowable</artifactId>
                <version>${aidex.version}</version>
            </dependency>

            <!-- CMS -->
            <dependency>
                <groupId>com.aidex</groupId>
                <artifactId>aidex-cms</artifactId>
                <version>${aidex.version}</version>
            </dependency>

            <!--阿里短信服务 -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${aliyun-java-sdk-core.version}</version>
            </dependency>

            <!-- 腾讯云短信组件 -->
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java</artifactId>
                <version>${tencentcloud-sdk-java.version}</version>
            </dependency>

            <!-- flowable工作流 -->
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-process</artifactId>
                <version>${flowable.version}</version>
                <exclusions><!-- 需要排除flowable的mybatis依赖，不然会跟mybatis-plus冲突 -->
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-engine</artifactId>
                <version>${flowable.version}</version>
                <scope>compile</scope>
            </dependency>

            <!-- 加密组件 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk15to18</artifactId>
                <version>${bcprov-jdk15to18.version}</version>
            </dependency>

            <!-- MinIO 对象存储 -->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
                <exclusions>
                    <!--已包含bcprov-jdk15to18-->
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcprov-jdk15on</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>${okio.version}</version>
            </dependency>
            <!-- 阿里云对象存储 -->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-sdk-oss.version}</version>
            </dependency>
            <!-- 腾讯云对象存储 -->
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${cos_api.version}</version>
            </dependency>

            <!--  IKAnalyzer分词器 -->
            <dependency>
                <groupId>org.wltea.analyzer</groupId>
                <artifactId>IKAnalyzer</artifactId>
                <version>3.2.8</version>
            </dependency>

            <!-- Apache Commons -->
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>

            <!-- 业务模块 -->
            <dependency>
                <groupId>com.aidex</groupId>
                <artifactId>aidex-business</artifactId>
                <version>${aidex.version}</version>
            </dependency>

            <!-- 小程序模块 -->
            <dependency>
                <groupId>com.aidex</groupId>
                <artifactId>aidex-mobile</artifactId>
                <version>${aidex.version}</version>
            </dependency>

            <!-- 前置模块 -->
            <dependency>
                <groupId>com.aidex</groupId>
                <artifactId>aidex-front</artifactId>
                <version>${aidex.version}</version>
            </dependency>

            <!-- magic-api -->
<!--            <dependency>-->
<!--                <groupId>org.ssssssss</groupId>-->
<!--                <artifactId>magic-api-spring-boot-starter</artifactId>-->
<!--                <version>2.1.1</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>org.jeecgframework.jimureport</groupId>-->
<!--                <artifactId>jimureport-spring-boot-starter</artifactId>-->
<!--                <version>1.7.6</version>-->
<!--            </dependency>-->

            <!-- 教育厅加密平台 -->
            <dependency>
                <groupId>cn.tna</groupId>
                <artifactId>tna-ist</artifactId>
                <version>3.9</version>
            </dependency>

            <!-- Mysql驱动包 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>8.0.29</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <!-- 基础模块 -->
        <module>aidex-common</module>
        <module>aidex-framework</module>

        <!-- 微服务模块 -->
        <module>aidex-gateway</module>
        <module>aidex-auth</module>
        <module>aidex-system</module>
        <module>aidex-business</module>
        <module>aidex-cms</module>
        <module>aidex-mobile</module>
        <module>aidex-front</module>

        <!-- 工具模块 -->
        <module>aidex-quartz</module>
        <module>aidex-generator</module>
        <module>aidex-flowable</module>

        <!-- 原有模块保留 -->
        <module>aidex-admin</module>
        <module>aidex-controller</module>
        <module>aidex-web</module>
    </modules>
    <packaging>pom</packaging>


    <dependencies>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.9.1.2184</version>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.vm</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>
