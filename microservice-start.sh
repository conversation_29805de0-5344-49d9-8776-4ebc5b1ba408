#!/bin/bash

# 微服务启动脚本
# 作者: AiDex Team
# 说明: 用于启动Spring Cloud Alibaba微服务

echo "=========================================="
echo "    AiDex 微服务架构启动脚本"
echo "=========================================="

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请先安装JDK 17+"
    exit 1
fi

# 检查Maven环境
if ! command -v mvn &> /dev/null; then
    echo "错误: 未找到Maven环境，请先安装Maven"
    exit 1
fi

# 检查Docker环境
if ! command -v docker &> /dev/null; then
    echo "错误: 未找到Docker环境，请先安装Docker"
    exit 1
fi

# 启动基础设施
echo "1. 启动基础设施服务..."
docker-compose up -d

echo "等待基础设施启动完成..."
sleep 30

# 检查Nacos是否启动成功
echo "2. 检查Nacos服务状态..."
for i in {1..10}; do
    if curl -f http://localhost:8848/nacos/v1/console/health/readiness; then
        echo "Nacos服务启动成功!"
        break
    else
        echo "等待Nacos启动... ($i/10)"
        sleep 10
    fi
    if [ $i -eq 10 ]; then
        echo "错误: Nacos启动失败，请检查日志"
        exit 1
    fi
done

# 编译项目
echo "3. 编译项目..."
mvn clean package -DskipTests

# 启动微服务
echo "4. 启动微服务..."

# 启动网关服务
echo "启动网关服务..."
cd aidex-gateway
nohup java -jar target/aidex-gateway-3.4.0.jar --spring.profiles.active=dev > ../logs/gateway.log 2>&1 &
cd ..
sleep 10

# 启动认证服务
echo "启动认证服务..."
cd aidex-auth
nohup java -jar target/aidex-auth-3.4.0.jar --spring.profiles.active=dev > ../logs/auth.log 2>&1 &
cd ..
sleep 10

# 启动系统服务
echo "启动系统服务..."
cd aidex-system
nohup java -jar target/aidex-system-3.4.0.jar --spring.profiles.active=dev > ../logs/system.log 2>&1 &
cd ..
sleep 10

# 启动业务服务
echo "启动业务服务..."
cd aidex-business
nohup java -jar target/aidex-business-3.4.0.jar --spring.profiles.active=dev > ../logs/business.log 2>&1 &
cd ..
sleep 10

# 启动CMS服务
echo "启动CMS服务..."
cd aidex-cms
nohup java -jar target/aidex-cms-3.4.0.jar --spring.profiles.active=dev > ../logs/cms.log 2>&1 &
cd ..
sleep 10

# 启动移动端服务
echo "启动移动端服务..."
cd aidex-mobile
nohup java -jar target/aidex-mobile-3.4.0.jar --spring.profiles.active=dev > ../logs/mobile.log 2>&1 &
cd ..
sleep 10

# 启动前台服务
echo "启动前台服务..."
cd aidex-front
nohup java -jar target/aidex-front-3.4.0.jar --spring.profiles.active=dev > ../logs/front.log 2>&1 &
cd ..

echo "=========================================="
echo "微服务启动完成!"
echo "=========================================="
echo "服务访问地址:"
echo "- 网关服务: http://localhost:8080"
echo "- 认证服务: http://localhost:9200"
echo "- 系统服务: http://localhost:9202"
echo "- 业务服务: http://localhost:9201"
echo "- CMS服务: http://localhost:9203"
echo "- 移动端服务: http://localhost:9204"
echo "- 前台服务: http://localhost:9205"
echo ""
echo "基础设施:"
echo "- Nacos控制台: http://localhost:8848/nacos (nacos/nacos)"
echo "- Sentinel控制台: http://localhost:8858 (sentinel/sentinel)"
echo "- MySQL: localhost:3306 (root/123456)"
echo "- Redis: localhost:6379 (123456)"
echo "=========================================="
