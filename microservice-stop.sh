#!/bin/bash

# 微服务停止脚本
# 作者: AiDex Team
# 说明: 用于停止Spring Cloud Alibaba微服务

echo "=========================================="
echo "    AiDex 微服务架构停止脚本"
echo "=========================================="

# 停止微服务
echo "1. 停止微服务..."

# 查找并停止Java进程
echo "停止网关服务..."
pkill -f "aidex-gateway"

echo "停止认证服务..."
pkill -f "aidex-auth"

echo "停止系统服务..."
pkill -f "aidex-system"

echo "停止业务服务..."
pkill -f "aidex-business"

echo "停止CMS服务..."
pkill -f "aidex-cms"

echo "停止移动端服务..."
pkill -f "aidex-mobile"

echo "停止前台服务..."
pkill -f "aidex-front"

# 等待进程完全停止
echo "等待服务停止..."
sleep 5

# 停止基础设施
echo "2. 停止基础设施服务..."
docker-compose down

echo "=========================================="
echo "所有服务已停止!"
echo "=========================================="

# 显示剩余的Java进程
echo "检查剩余进程:"
ps aux | grep java | grep -v grep || echo "没有发现Java进程"
