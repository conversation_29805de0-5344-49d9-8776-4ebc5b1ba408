#
# 编译时(node 端)可用的：process.env.*
# 运行时(客户端)可用的：process.env.NODE_ENV、process.env.BASE_URL、process.env.VUE_APP_*
#
# BASE_URL 需要尾斜杠，接口前缀不需要尾斜杠
# BASE_URL 也可以生成为相对路径，详见：https://cli.vuejs.org/zh/config/#publicpath
#
VUE_APP_TITLE = "中小学入学招生综合服务系统"
NODE_ENV = production
VUE_APP_ENV = prod
VUE_APP_BASE_API = http://116.63.136.116:9074/api
VUE_APP_ENABLE_DOCS = false
# 接口服务 (给 devServer.proxy 使用)
DEV_PROXY_TARGET_API = http://127.0.0.1:8080
VUE_APP_RESOURCE_PREFIX = /profile
