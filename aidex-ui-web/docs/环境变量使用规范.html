<!DOCTYPE html><html><head>
      <title>&#x73AF;&#x5883;&#x53D8;&#x91CF;&#x4F7F;&#x7528;&#x89C4;&#x8303;</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">

      <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.13.0/dist/katex.min.css">









      <style>
      /**
 * prism.js Github theme based on GitHub's theme.
 * <AUTHOR>
 */
code[class*="language-"],
pre[class*="language-"] {
  color: #333;
  background: none;
  font-family: Consolas, "Liberation Mono", Menlo, Courier, monospace;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.4;

  -moz-tab-size: 8;
  -o-tab-size: 8;
  tab-size: 8;

  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}

/* Code blocks */
pre[class*="language-"] {
  padding: .8em;
  overflow: auto;
  /* border: 1px solid #ddd; */
  border-radius: 3px;
  /* background: #fff; */
  background: #f5f5f5;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
  padding: .1em;
  border-radius: .3em;
  white-space: normal;
  background: #f5f5f5;
}

.token.comment,
.token.blockquote {
  color: #969896;
}

.token.cdata {
  color: #183691;
}

.token.doctype,
.token.punctuation,
.token.variable,
.token.macro.property {
  color: #333;
}

.token.operator,
.token.important,
.token.keyword,
.token.rule,
.token.builtin {
  color: #a71d5d;
}

.token.string,
.token.url,
.token.regex,
.token.attr-value {
  color: #183691;
}

.token.property,
.token.number,
.token.boolean,
.token.entity,
.token.atrule,
.token.constant,
.token.symbol,
.token.command,
.token.code {
  color: #0086b3;
}

.token.tag,
.token.selector,
.token.prolog {
  color: #63a35c;
}

.token.function,
.token.namespace,
.token.pseudo-element,
.token.class,
.token.class-name,
.token.pseudo-class,
.token.id,
.token.url-reference .token.variable,
.token.attr-name {
  color: #795da3;
}

.token.entity {
  cursor: help;
}

.token.title,
.token.title .token.punctuation {
  font-weight: bold;
  color: #1d3e81;
}

.token.list {
  color: #ed6a43;
}

.token.inserted {
  background-color: #eaffea;
  color: #55a532;
}

.token.deleted {
  background-color: #ffecec;
  color: #bd2c00;
}

.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}


/* JSON */
.language-json .token.property {
  color: #183691;
}

.language-markup .token.tag .token.punctuation {
  color: #333;
}

/* CSS */
code.language-css,
.language-css .token.function {
  color: #0086b3;
}

/* YAML */
.language-yaml .token.atrule {
  color: #63a35c;
}

code.language-yaml {
  color: #183691;
}

/* Ruby */
.language-ruby .token.function {
  color: #333;
}

/* Markdown */
.language-markdown .token.url {
  color: #795da3;
}

/* Makefile */
.language-makefile .token.symbol {
  color: #795da3;
}

.language-makefile .token.variable {
  color: #183691;
}

.language-makefile .token.builtin {
  color: #0086b3;
}

/* Bash */
.language-bash .token.keyword {
  color: #0086b3;
}

/* highlight */
pre[data-line] {
  position: relative;
  padding: 1em 0 1em 3em;
}
pre[data-line] .line-highlight-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  background-color: transparent;
  display: block;
  width: 100%;
}

pre[data-line] .line-highlight {
  position: absolute;
  left: 0;
  right: 0;
  padding: inherit 0;
  margin-top: 1em;
  background: hsla(24, 20%, 50%,.08);
  background: linear-gradient(to right, hsla(24, 20%, 50%,.1) 70%, hsla(24, 20%, 50%,0));
  pointer-events: none;
  line-height: inherit;
  white-space: pre;
}

pre[data-line] .line-highlight:before,
pre[data-line] .line-highlight[data-end]:after {
  content: attr(data-start);
  position: absolute;
  top: .4em;
  left: .6em;
  min-width: 1em;
  padding: 0 .5em;
  background-color: hsla(24, 20%, 50%,.4);
  color: hsl(24, 20%, 95%);
  font: bold 65%/1.5 sans-serif;
  text-align: center;
  vertical-align: .3em;
  border-radius: 999px;
  text-shadow: none;
  box-shadow: 0 1px white;
}

pre[data-line] .line-highlight[data-end]:after {
  content: attr(data-end);
  top: auto;
  bottom: .4em;
}html body{font-family:"Helvetica Neue",Helvetica,"Segoe UI",Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ul,html body>ol{margin-bottom:16px}html body ul,html body ol{padding-left:2em}html body ul.no-list,html body ol.no-list{padding:0;list-style-type:none}html body ul ul,html body ul ol,html body ol ol,html body ol ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:bold;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:bold}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em !important;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::before,html body code::after{letter-spacing:-0.2em;content:"\00a0"}html body pre>code{padding:0;margin:0;font-size:.85em !important;word-break:normal;white-space:pre;background:transparent;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;font-size:.85em !important;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:before,html body pre tt:before,html body pre code:after,html body pre tt:after{content:normal}html body p,html body blockquote,html body ul,html body ol,html body dl,html body pre{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body pre,html body code{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview .pagebreak,.markdown-preview .newpage{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center !important}.markdown-preview:not([for="preview"]) .code-chunk .btn-group{display:none}.markdown-preview:not([for="preview"]) .code-chunk .status{display:none}.markdown-preview:not([for="preview"]) .code-chunk .output-div{margin-bottom:16px}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,0.66);border:4px solid rgba(150,150,150,0.66);background-clip:content-box}html body[for="html-export"]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for="html-export"]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0}@media screen and (min-width:914px){html body[for="html-export"]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for="html-export"]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for="html-export"]:not([data-presentation-mode]) .markdown-preview{font-size:14px !important;padding:1em}}@media print{html body[for="html-export"]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for="html-export"]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,0.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,0.66);border:4px solid rgba(150,150,150,0.66);background-clip:content-box}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc ul{padding:0 1.6em;margin-top:.8em}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc li{margin-bottom:.8em}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc ul{list-style-type:none}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% -  300px);padding:2em calc(50% - 457px -  150px);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for="html-export"]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for="html-export"]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
    </head>
    <body for="html-export">
      <div class="mume markdown-preview  ">
      <h3 class="mume-header" id="%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6">&#x914D;&#x7F6E;&#x6587;&#x4EF6;</h3>

<ul>
<li>
<p>&#x914D;&#x7F6E;&#x6587;&#x4EF6;</p>
<ul>
<li>&#x5F00;&#x53D1;&#x73AF;&#x5883;&#xFF1A;.env.development &amp; .env.development.local</li>
<li>&#x6D4B;&#x8BD5;&#x73AF;&#x5883;&#xFF1A;.env.production-stage</li>
<li>&#x751F;&#x4EA7;&#x73AF;&#x5883;&#xFF1A;.env.production</li>
<li>&#x81EA;&#x52A8;&#x5316;&#x6D4B;&#x8BD5;&#xFF1A;.env.test</li>
</ul>
</li>
<li>
<p>&#x53D8;&#x91CF;&#x540C;&#x540D;&#x65F6;&#xFF0C;&#x4F1A;&#x8986;&#x76D6;&#x524D;&#x9762;&#x5B9A;&#x4E49;&#x7684;</p>
</li>
<li>
<p>.env.development.local &#x4E2D;&#x7684;&#x53D8;&#x91CF;&#x4F18;&#x5148;&#x7EA7;&#x6700;&#x9AD8;</p>
</li>
<li>
<p>.env.development.local &#x4E0D;&#x4F1A;&#x88AB; git &#x8FFD;&#x8E2A;&#xFF0C;&#x901A;&#x5E38;&#x7528;&#x4E8E;&#x91CD;&#x5199;&#x9891;&#x7E41;&#x53D8;&#x52A8;&#x7684;&#x53D8;&#x91CF;&#xFF0C;&#x5982;&#xFF1A;</p>
<pre data-role="codeBlock" data-info class="language-"><code>VUE_APP_MOCK = true
VUE_APP_MOCK = false

DEV_PROXY_TARGET_API = &#x540E;&#x7AEF;&#x5F00;&#x53D1;A&#x7684;&#x63A5;&#x53E3;&#x670D;&#x52A1;
DEV_PROXY_TARGET_API = &#x540E;&#x7AEF;&#x5F00;&#x53D1;B&#x7684;&#x63A5;&#x53E3;&#x670D;&#x52A1;
DEV_PROXY_TARGET_API = &#x540E;&#x7AEF;&#x5F00;&#x53D1;C&#x7684;&#x63A5;&#x53E3;&#x670D;&#x52A1;

# ...
</code></pre></li>
</ul>
<h3 class="mume-header" id="%E8%BF%90%E8%A1%8C%E6%97%B6%E5%AE%A2%E6%88%B7%E7%AB%AF%E5%8F%AF%E7%94%A8%E7%9A%84%E7%8E%AF%E5%A2%83%E5%8F%98%E9%87%8F">&#x8FD0;&#x884C;&#x65F6;(&#x5BA2;&#x6237;&#x7AEF;)&#x53EF;&#x7528;&#x7684;&#x73AF;&#x5883;&#x53D8;&#x91CF;</h3>

<pre data-role="codeBlock" data-info="ts" class="language-ts"><span class="token doc-comment comment">/**
 * &#x8FD0;&#x884C;&#x65F6;(&#x5BA2;&#x6237;&#x7AEF;)&#x53EF;&#x7528;&#x7684;&#xFF1A;process.env.NODE_ENV&#x3001;process.env.BASE_URL&#x3001;process.env.VUE_APP_*
 * &#x73AF;&#x5883;&#x5224;&#x65AD;&#x63A8;&#x8350;&#x4F7F;&#x7528; process.env.VUE_APP_ENV
 */</span>
<span class="token keyword">interface</span> <span class="token class-name">Env</span> <span class="token punctuation">{</span>
  <span class="token comment">// development:&#x5F00;&#x53D1;&#x73AF;&#x5883; | production:&#x975E;&#x5F00;&#x53D1;&#x73AF;&#x5883; | test:&#x81EA;&#x52A8;&#x5316;&#x6D4B;&#x8BD5;</span>
  <span class="token keyword">readonly</span> <span class="token constant">NODE_ENV</span><span class="token operator">:</span> <span class="token string">&apos;development&apos;</span> <span class="token operator">|</span> <span class="token string">&apos;production&apos;</span> <span class="token operator">|</span> <span class="token string">&apos;test&apos;</span>

  <span class="token comment">// dev:&#x5F00;&#x53D1;&#x73AF;&#x5883; | stage:&#x6D4B;&#x8BD5;&#x73AF;&#x5883; | prod:&#x751F;&#x4EA7;&#x73AF;&#x5883;</span>
  <span class="token keyword">readonly</span> <span class="token constant">VUE_APP_ENV</span><span class="token operator">:</span> <span class="token string">&apos;dev&apos;</span> <span class="token operator">|</span> <span class="token string">&apos;stage&apos;</span> <span class="token operator">|</span> <span class="token string">&apos;prod&apos;</span>

  <span class="token comment">// &#x9875;&#x9762;&#x90E8;&#x7F72;&#x8DEF;&#x5F84;&#xFF0C;&#x8BE6;&#x60C5;&#xFF1A;https://cli.vuejs.org/zh/config/#publicpath</span>
  <span class="token keyword">readonly</span> <span class="token constant">BASE_URL</span><span class="token operator">:</span> <span class="token string">&apos;&apos;</span> <span class="token operator">|</span> <span class="token string">&apos;/**/&apos;</span> <span class="token comment">// &#x9700;&#x8981;&#x5C3E;&#x659C;&#x6760;&#xFF0C;&#x5982;&#x6CA1;&#x6709;&#x5219;&#x6784;&#x5EFA;&#x65F6;&#x4F1A;&#x81EA;&#x52A8;&#x52A0;&#x4E0A;</span>

  <span class="token comment">// &#x63A5;&#x53E3;&#x524D;&#x7F00;</span>
  <span class="token keyword">readonly</span> <span class="token constant">VUE_APP_BASE_API</span><span class="token operator">:</span> <span class="token string">&apos;/**/*&apos;</span> <span class="token operator">|</span> <span class="token string">&apos;http*(s)://**/*&apos;</span> <span class="token comment">// &#x4E0D;&#x9700;&#x8981;&#x5C3E;&#x659C;&#x6760;</span>

  <span class="token comment">// &#x662F;&#x5426;&#x5F00;&#x542F;&#x6587;&#x6863;&#x4E2D;&#x5FC3;</span>
  <span class="token keyword">readonly</span> <span class="token constant">VUE_APP_ENABLE_DOCS</span><span class="token operator">:</span> <span class="token string">&apos;true&apos;</span> <span class="token operator">|</span> <span class="token string">&apos;false&apos;</span>

  <span class="token comment">// &#x662F;&#x5426;&#x542F;&#x7528; mockjs</span>
  <span class="token keyword">readonly</span> <span class="token constant">VUE_APP_MOCK</span><span class="token operator">:</span> <span class="token string">&apos;true&apos;</span> <span class="token operator">|</span> <span class="token string">&apos;false&apos;</span>

  <span class="token comment">// ...</span>
<span class="token punctuation">}</span>
</pre><h3 class="mume-header" id="%E5%9C%A8-public-%E4%B8%AD%E9%80%9A%E8%BF%87-ejs-%E8%AF%AD%E6%B3%95%E5%8F%96%E7%94%A8">&#x5728; public/**/* &#x4E2D;&#x901A;&#x8FC7; EJS &#x8BED;&#x6CD5;&#x53D6;&#x7528;</h3>

<ul>
<li>
<p>html/htm</p>
<pre data-role="codeBlock" data-info="html" class="language-html"><span class="token comment">&lt;!-- &#x5F15;&#x7528; public &#x4E2D;&#x7684;&#x8D44;&#x6E90;&#x65F6;&#x4E00;&#x5B9A;&#x8981;&#x52A0;&#x4E0A; BASE_URL --&gt;</span>
&lt;% if (VUE_APP_ENV === &apos;dev&apos; || VUE_APP_ENV === &apos;stage&apos;) { %&gt;
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>script</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>&lt;%= BASE_URL %&gt;libs/xxx.js<span class="token punctuation">&quot;</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>script</span><span class="token punctuation">&gt;</span></span>
&lt;% } else { %&gt;
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>script</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>&lt;%= BASE_URL %&gt;libs/xxx.min.js<span class="token punctuation">&quot;</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>script</span><span class="token punctuation">&gt;</span></span>
&lt;% } %&gt;
</pre></li>
<li>
<p>js</p>
<pre data-role="codeBlock" data-info="js" class="language-javascript"><span class="token keyword">var</span> appEnv <span class="token operator">=</span> <span class="token string">&apos;&lt;%= VUE_APP_ENV %&gt;&apos;</span>

<span class="token comment">// &#x5BF9;&#x4E8E;&#x4E0D;&#x660E;&#x786E;&#x7684;&#x53D8;&#x91CF;&#xFF0C;&#x4F7F;&#x7528;&#x5B89;&#x5168;&#x7684;&#x53D6;&#x7528;&#x65B9;&#x5F0F;&#xFF0C;&#x9664;&#x4E86;&#x5165;&#x53E3; html&#xFF08;&#x5165;&#x53E3; html &#x4E0D;&#x5B58;&#x5728; obj &#x53D8;&#x91CF;&#xFF09;</span>
<span class="token keyword">var</span> xxx <span class="token operator">=</span> <span class="token string">&apos;&lt;%= obj.VUE_APP_XXX || &quot;xxx&quot; %&gt;&apos;</span>
</pre></li>
<li>
<p>json</p>
<pre data-role="codeBlock" data-info="json" class="language-json"><span class="token punctuation">{</span>
  <span class="token property">&quot;xxx&quot;</span><span class="token operator">:</span> <span class="token string">&quot;&lt;%= obj.VUE_APP_XXX || &apos;xxx&apos; %&gt;&quot;</span>
<span class="token punctuation">}</span>
</pre></li>
</ul>
<h3 class="mume-header" id="%E5%9C%A8-src-%E4%B8%AD%E9%80%9A%E8%BF%87-processenv-%E5%8F%96%E7%94%A8">&#x5728; src/**/* &#x4E2D;&#x901A;&#x8FC7; process.env &#x53D6;&#x7528;</h3>

<ul>
<li>
<p>vue</p>
<pre data-role="codeBlock" data-info="html" class="language-html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>script</span><span class="token punctuation">&gt;</span></span><span class="token script"><span class="token language-javascript">
  <span class="token keyword module">export</span> <span class="token keyword module">default</span> <span class="token punctuation">{</span>
    <span class="token function">data</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
      <span class="token keyword control-flow">return</span> <span class="token punctuation">{</span>
        xxx<span class="token operator">:</span> process<span class="token punctuation">.</span><span class="token property-access">env</span><span class="token punctuation">.</span><span class="token constant">VUE_APP_XXX</span><span class="token punctuation">,</span>
        xxx2<span class="token operator">:</span> <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">$env</span><span class="token punctuation">.</span><span class="token constant">VUE_APP_XXX2</span><span class="token punctuation">,</span> <span class="token comment">// &#x901A;&#x8FC7; vm.$env &#x95F4;&#x63A5;&#x53D6;&#x7528;&#xFF08;&#x63A8;&#x8350;&#xFF09;</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">}</span>
</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>script</span><span class="token punctuation">&gt;</span></span>

<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>template</span><span class="token punctuation">&gt;</span></span>
  <span class="token comment">&lt;!-- &#x5728;&#x6A21;&#x677F;&#x4E2D;&#x53EA;&#x80FD;&#x95F4;&#x63A5;&#x53D6;&#x7528; --&gt;</span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span>
    <span class="token attr-name">download</span>
    <span class="token attr-name">target</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>_blank<span class="token punctuation">&quot;</span></span>
    <span class="token attr-name">:href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>$env.BASE_URL + <span class="token punctuation">&apos;</span>docs/&#x7CFB;&#x7EDF;&#x64CD;&#x4F5C;&#x6307;&#x5357;v1.0.0.pdf<span class="token punctuation">&apos;</span><span class="token punctuation">&quot;</span></span>
  <span class="token punctuation">&gt;</span></span>
    &#x7CFB;&#x7EDF;&#x64CD;&#x4F5C;&#x6307;&#x5357;
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>template</span><span class="token punctuation">&gt;</span></span>
</pre></li>
<li>
<p>js</p>
<pre data-role="codeBlock" data-info="js" class="language-javascript"><span class="token keyword">const</span> xxx <span class="token operator">=</span> process<span class="token punctuation">.</span><span class="token property-access">env</span><span class="token punctuation">.</span><span class="token constant">VUE_APP_XXX</span>
</pre></li>
</ul>
<h3 class="mume-header" id="%E5%9C%A8-src-%E4%B8%AD%E8%BF%9B%E8%A1%8C%E6%9D%A1%E4%BB%B6%E7%BC%96%E8%AF%91">&#x5728; src/**/* &#x4E2D;&#x8FDB;&#x884C;&#x6761;&#x4EF6;&#x7F16;&#x8BD1;</h3>

<p>&#x5FC5;&#x987B;&#x662F;&#x8FD0;&#x884C;&#x65F6;(&#x5BA2;&#x6237;&#x7AEF;)&#x53EF;&#x7528;&#x7684;&#x73AF;&#x5883;&#x53D8;&#x91CF;&#xFF0C;&#x5E76;&#x4E14;&#x53D8;&#x91CF;&#x503C;&#x4E0D;&#x80FD;&#x4E3A; undefined&#xFF0C;&#x5426;&#x5219;&#x6A21;&#x5757;&#x5FC5;&#x5B9A;&#x4F1A;&#x6253;&#x5305;</p>
<pre data-role="codeBlock" data-info="js" class="language-javascript"><span class="token comment">// &#x6B63;&#x786E;&#x7684;&#x7528;&#x6CD5;1</span>
<span class="token keyword control-flow">if</span> <span class="token punctuation">(</span>process<span class="token punctuation">.</span><span class="token property-access">env</span><span class="token punctuation">.</span><span class="token constant">VUE_APP_ENV</span> <span class="token operator">===</span> <span class="token string">&apos;dev&apos;</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  <span class="token function">require</span><span class="token punctuation">(</span><span class="token string">&apos;xxx&apos;</span><span class="token punctuation">)</span>
<span class="token punctuation">}</span>
<span class="token comment">// &#x6B63;&#x786E;&#x7684;&#x7528;&#x6CD5;2</span>
<span class="token keyword control-flow">if</span> <span class="token punctuation">(</span>process<span class="token punctuation">.</span><span class="token property-access">env</span><span class="token punctuation">.</span><span class="token constant">VUE_APP_ENV</span> <span class="token operator">===</span> <span class="token string">&apos;dev&apos;</span> <span class="token operator">||</span> process<span class="token punctuation">.</span><span class="token property-access">env</span><span class="token punctuation">.</span><span class="token constant">VUE_APP_ENV</span> <span class="token operator">===</span> <span class="token string">&apos;stage&apos;</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  <span class="token function">require</span><span class="token punctuation">(</span><span class="token string">&apos;xxx&apos;</span><span class="token punctuation">)</span>
<span class="token punctuation">}</span>

<span class="token comment">// &#x9519;&#x8BEF;&#x7684;&#x7528;&#x6CD5;1</span>
<span class="token keyword">const</span> env <span class="token operator">=</span> process<span class="token punctuation">.</span><span class="token property-access">env</span>
<span class="token keyword control-flow">if</span> <span class="token punctuation">(</span>env<span class="token punctuation">.</span><span class="token constant">VUE_APP_ENV</span> <span class="token operator">===</span> <span class="token string">&apos;dev&apos;</span> <span class="token operator">||</span> env<span class="token punctuation">.</span><span class="token constant">VUE_APP_ENV</span> <span class="token operator">===</span> <span class="token string">&apos;stage&apos;</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  <span class="token function">require</span><span class="token punctuation">(</span><span class="token string">&apos;xxx&apos;</span><span class="token punctuation">)</span>
<span class="token punctuation">}</span>
<span class="token comment">// &#x9519;&#x8BEF;&#x7684;&#x7528;&#x6CD5;2</span>
<span class="token keyword control-flow">if</span> <span class="token punctuation">(</span><span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">$env</span><span class="token punctuation">.</span><span class="token constant">VUE_APP_ENV</span> <span class="token operator">===</span> <span class="token string">&apos;dev&apos;</span> <span class="token operator">||</span> <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">$env</span><span class="token punctuation">.</span><span class="token constant">VUE_APP_ENV</span> <span class="token operator">===</span> <span class="token string">&apos;stage&apos;</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  <span class="token function">require</span><span class="token punctuation">(</span><span class="token string">&apos;xxx&apos;</span><span class="token punctuation">)</span>
<span class="token punctuation">}</span>
</pre>
      </div>











    </body></html>
