<!DOCTYPE html><html><head>
      <title>README</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.13.2/dist/katex.min.css">
      
      
      
      
      
      
      
      
      
      <style>
      /**
 * prism.js Github theme based on GitHub's theme.
 * <AUTHOR>
 */
code[class*="language-"],
pre[class*="language-"] {
  color: #333;
  background: none;
  font-family: Consolas, "Liberation Mono", Menlo, Courier, monospace;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.4;

  -moz-tab-size: 8;
  -o-tab-size: 8;
  tab-size: 8;

  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}

/* Code blocks */
pre[class*="language-"] {
  padding: .8em;
  overflow: auto;
  /* border: 1px solid #ddd; */
  border-radius: 3px;
  /* background: #fff; */
  background: #f5f5f5;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
  padding: .1em;
  border-radius: .3em;
  white-space: normal;
  background: #f5f5f5;
}

.token.comment,
.token.blockquote {
  color: #969896;
}

.token.cdata {
  color: #183691;
}

.token.doctype,
.token.punctuation,
.token.variable,
.token.macro.property {
  color: #333;
}

.token.operator,
.token.important,
.token.keyword,
.token.rule,
.token.builtin {
  color: #a71d5d;
}

.token.string,
.token.url,
.token.regex,
.token.attr-value {
  color: #183691;
}

.token.property,
.token.number,
.token.boolean,
.token.entity,
.token.atrule,
.token.constant,
.token.symbol,
.token.command,
.token.code {
  color: #0086b3;
}

.token.tag,
.token.selector,
.token.prolog {
  color: #63a35c;
}

.token.function,
.token.namespace,
.token.pseudo-element,
.token.class,
.token.class-name,
.token.pseudo-class,
.token.id,
.token.url-reference .token.variable,
.token.attr-name {
  color: #795da3;
}

.token.entity {
  cursor: help;
}

.token.title,
.token.title .token.punctuation {
  font-weight: bold;
  color: #1d3e81;
}

.token.list {
  color: #ed6a43;
}

.token.inserted {
  background-color: #eaffea;
  color: #55a532;
}

.token.deleted {
  background-color: #ffecec;
  color: #bd2c00;
}

.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}


/* JSON */
.language-json .token.property {
  color: #183691;
}

.language-markup .token.tag .token.punctuation {
  color: #333;
}

/* CSS */
code.language-css,
.language-css .token.function {
  color: #0086b3;
}

/* YAML */
.language-yaml .token.atrule {
  color: #63a35c;
}

code.language-yaml {
  color: #183691;
}

/* Ruby */
.language-ruby .token.function {
  color: #333;
}

/* Markdown */
.language-markdown .token.url {
  color: #795da3;
}

/* Makefile */
.language-makefile .token.symbol {
  color: #795da3;
}

.language-makefile .token.variable {
  color: #183691;
}

.language-makefile .token.builtin {
  color: #0086b3;
}

/* Bash */
.language-bash .token.keyword {
  color: #0086b3;
}

/* highlight */
pre[data-line] {
  position: relative;
  padding: 1em 0 1em 3em;
}
pre[data-line] .line-highlight-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  background-color: transparent;
  display: block;
  width: 100%;
}

pre[data-line] .line-highlight {
  position: absolute;
  left: 0;
  right: 0;
  padding: inherit 0;
  margin-top: 1em;
  background: hsla(24, 20%, 50%,.08);
  background: linear-gradient(to right, hsla(24, 20%, 50%,.1) 70%, hsla(24, 20%, 50%,0));
  pointer-events: none;
  line-height: inherit;
  white-space: pre;
}

pre[data-line] .line-highlight:before, 
pre[data-line] .line-highlight[data-end]:after {
  content: attr(data-start);
  position: absolute;
  top: .4em;
  left: .6em;
  min-width: 1em;
  padding: 0 .5em;
  background-color: hsla(24, 20%, 50%,.4);
  color: hsl(24, 20%, 95%);
  font: bold 65%/1.5 sans-serif;
  text-align: center;
  vertical-align: .3em;
  border-radius: 999px;
  text-shadow: none;
  box-shadow: 0 1px white;
}

pre[data-line] .line-highlight[data-end]:after {
  content: attr(data-end);
  top: auto;
  bottom: .4em;
}html body{font-family:"Helvetica Neue",Helvetica,"Segoe UI",Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ul,html body>ol{margin-bottom:16px}html body ul,html body ol{padding-left:2em}html body ul.no-list,html body ol.no-list{padding:0;list-style-type:none}html body ul ul,html body ul ol,html body ol ol,html body ol ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:bold;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:bold}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em !important;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::before,html body code::after{letter-spacing:-0.2em;content:"\00a0"}html body pre>code{padding:0;margin:0;font-size:.85em !important;word-break:normal;white-space:pre;background:transparent;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;font-size:.85em !important;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:before,html body pre tt:before,html body pre code:after,html body pre tt:after{content:normal}html body p,html body blockquote,html body ul,html body ol,html body dl,html body pre{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body pre,html body code{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview .pagebreak,.markdown-preview .newpage{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center !important}.markdown-preview:not([for="preview"]) .code-chunk .btn-group{display:none}.markdown-preview:not([for="preview"]) .code-chunk .status{display:none}.markdown-preview:not([for="preview"]) .code-chunk .output-div{margin-bottom:16px}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,0.66);border:4px solid rgba(150,150,150,0.66);background-clip:content-box}html body[for="html-export"]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for="html-export"]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0}@media screen and (min-width:914px){html body[for="html-export"]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for="html-export"]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for="html-export"]:not([data-presentation-mode]) .markdown-preview{font-size:14px !important;padding:1em}}@media print{html body[for="html-export"]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for="html-export"]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,0.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,0.66);border:4px solid rgba(150,150,150,0.66);background-clip:content-box}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc ul{padding:0 1.6em;margin-top:.8em}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc li{margin-bottom:.8em}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc ul{list-style-type:none}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% -  300px);padding:2em calc(50% - 457px -  150px);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for="html-export"]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for="html-export"]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
    </head>
    <body for="html-export">
      <div class="mume markdown-preview  ">
      <div class="code-chunk" data-id="code-chunk-id-0" data-cmd="toc"><div class="input-div"><div class="btn-group"><div class="run-btn btn"><span>&#x25B6;&#xFE0E;</span></div><div class="run-all-btn btn">all</div></div><div class="status">running...</div></div><div class="output-div"></div></div><ul>
<li><a href="#%E9%A1%B9%E7%9B%AE%E8%BF%90%E8%A1%8C%E6%8C%87%E5%8D%97">&#x9879;&#x76EE;&#x8FD0;&#x884C;&#x6307;&#x5357;</a></li>
<li><a href="#%E5%BC%80%E5%8F%91%E6%9C%AC%E5%9C%B0%E7%8E%AF%E5%A2%83">&#x5F00;&#x53D1;&#x672C;&#x5730;&#x73AF;&#x5883;</a></li>
<li><a href="#stage-%E6%B5%8B%E8%AF%95%E7%8E%AF%E5%A2%83">stage &#x6D4B;&#x8BD5;&#x73AF;&#x5883;</a></li>
<li><a href="#%E5%BC%80%E5%8F%91%E7%9B%B8%E5%85%B3%E6%8F%92%E4%BB%B6%E5%B7%A5%E5%85%B7">&#x5F00;&#x53D1;&#x76F8;&#x5173;&#x63D2;&#x4EF6;/&#x5DE5;&#x5177;</a></li>
<li><a href="#%E5%BC%80%E5%8F%91%E8%A7%84%E8%8C%83">&#x5F00;&#x53D1;&#x89C4;&#x8303;</a>
<ul>
<li><a href="#%E7%8E%AF%E5%A2%83%E5%8F%98%E9%87%8F%E4%BD%BF%E7%94%A8%E8%A7%84%E8%8C%83doc">&#x73AF;&#x5883;&#x53D8;&#x91CF;&#x4F7F;&#x7528;&#x89C4;&#x8303;&#xFF08;doc&#xFF09;</a></li>
<li><a href="#axios-%E4%BD%BF%E7%94%A8%E8%A7%84%E8%8C%83doc">axios &#x4F7F;&#x7528;&#x89C4;&#x8303;&#xFF08;doc&#xFF09;</a></li>
<li><a href="#vue">vue</a>
<ul>
<li><a href="#%E6%95%B0%E6%8D%AE%E6%B5%81%E5%90%91">&#x3010;&#x6570;&#x636E;&#x6D41;&#x5411;&#x3011;</a></li>
<li><a href="#%E6%85%8E%E7%94%A8%E5%85%A8%E5%B1%80%E6%B3%A8%E5%86%8C">&#x3010;&#x614E;&#x7528;&#x5168;&#x5C40;&#x6CE8;&#x518C;&#x3011;</a></li>
<li><a href="#%E7%BB%84%E4%BB%B6%E5%90%8D%E7%A7%B0">&#x3010;&#x7EC4;&#x4EF6;&#x540D;&#x79F0;&#x3011;</a></li>
<li><a href="#%E7%BB%84%E4%BB%B6%E4%B8%AD%E7%9A%84-css">&#x3010;&#x7EC4;&#x4EF6;&#x4E2D;&#x7684; CSS&#x3011;</a></li>
<li><a href="#%E7%BB%9F%E4%B8%80%E6%A0%87%E7%AD%BE%E9%A1%BA%E5%BA%8F">&#x3010;&#x7EDF;&#x4E00;&#x6807;&#x7B7E;&#x987A;&#x5E8F;&#x3011;</a></li>
<li><a href="#%E5%85%B6%E5%AE%83%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9">&#x3010;&#x5176;&#x5B83;&#x6CE8;&#x610F;&#x4E8B;&#x9879;&#x3011;</a></li>
<li><a href="#a-target_blank-hrefhttpscnvuejsorgv2style-guide%E5%85%B6%E5%AE%83%E5%88%99%E9%81%B5%E5%AE%88-vue-%E5%AE%98%E6%96%B9%E9%A3%8E%E6%A0%BC%E6%8C%87%E5%8D%97a">&#x3010; !!!&#x5176;&#x5B83;&#x5219;&#x9075;&#x5B88; vue &#x5B98;&#x65B9;&#x98CE;&#x683C;&#x6307;&#x5357;&#x3011;</a></li>
</ul>
</li>
<li><a href="#vue-router">vue-router</a></li>
<li><a href="#vuex">vuex</a></li>
<li><a href="#%E6%A8%A1%E5%9D%97%E5%A4%8D%E7%94%A8">&#x6A21;&#x5757;&#x590D;&#x7528;</a></li>
<li><a href="#%E5%85%B6%E5%AE%83%E6%9D%82%E9%A1%B9">&#x5176;&#x5B83;&#x6742;&#x9879;</a></li>
<li><a href="#%E4%BB%A3%E7%A0%81%E6%B3%A8%E9%87%8A">&#x4EE3;&#x7801;&#x6CE8;&#x91CA;</a></li>
<li><a href="#%E5%B7%A5%E7%A8%8B%E7%9B%AE%E5%BD%95%E7%BB%93%E6%9E%84">&#x5DE5;&#x7A0B;&#x76EE;&#x5F55;&#x7ED3;&#x6784;</a></li>
<li><a href="#%E5%89%8D%E7%AB%AF%E9%83%A8%E7%BD%B2">&#x524D;&#x7AEF;&#x90E8;&#x7F72;</a></li>
<li><a href="#%E7%BB%99-ui-%E7%9A%84%E5%BB%BA%E8%AE%AE">&#x7ED9; UI &#x7684;&#x5EFA;&#x8BAE;</a></li>
</ul>
</li>
<li><a href="#%E5%A1%AB%E5%9D%91-q-%E7%BE%A4901842001">&#x586B;&#x5751; Q &#x7FA4;&#xFF1A;901842001</a></li>
</ul>
<h1 class="mume-header" id="%E9%A1%B9%E7%9B%AE%E8%BF%90%E8%A1%8C%E6%8C%87%E5%8D%97">&#x9879;&#x76EE;&#x8FD0;&#x884C;&#x6307;&#x5357;</h1>

<ul>
<li>&#x5B89;&#x88C5;&#x4F9D;&#x8D56;&#x5305;&#xFF1A;<code>npm install</code>
<ul>
<li>&#x8BF4;&#x660E;&#xFF1A;&#x6B63;&#x5F0F;&#x5F00;&#x53D1;&#x524D;&#x6700;&#x597D;&#x63D0;&#x4EA4; package-lock.json&#xFF0C;&#x6B63;&#x5F0F;&#x5F00;&#x53D1;&#x540E;&#x614E;&#x7528; <code>npm update</code></li>
</ul>
</li>
<li>&#x8FD0;&#x884C;&#xFF1A;
<ul>
<li>&#x542F;&#x52A8;&#x4E3A; dev &#x73AF;&#x5883;&#xFF1A;<code>npm run serve</code> &#x6216; <code>npm start</code></li>
<li>&#x6253;&#x5305;&#x4E3A; stage &#x73AF;&#x5883;&#xFF1A;<code>npm run build:stage</code></li>
<li>&#x6253;&#x5305;&#x4E3A; prod &#x73AF;&#x5883;&#xFF1A;<code>npm run build:prod</code></li>
<li>&#x68C0;&#x67E5;&#x5E76;&#x4FEE;&#x590D;&#x6E90;&#x7801;&#xFF1A;<code>npm run lint</code></li>
<li>&#x8FD0;&#x884C;&#x5355;&#x5143;&#x6D4B;&#x8BD5;&#xFF1A;<code>npm run test:unit</code></li>
<li>&#x542F;&#x52A8;&#x9759;&#x6001;&#x8D44;&#x6E90;&#x670D;&#x52A1;&#xFF1A;<code>npm run dist</code></li>
<li>&#x7248;&#x672C;&#x53F7;&#x64CD;&#x4F5C;&#xFF1A;<code>npm version major|minor|patch</code>
<ul>
<li>&#x7248;&#x672C;&#x53F7;&#x683C;&#x5F0F;&#x8BF4;&#x660E;&#xFF1A;major(&#x4E3B;&#x7248;&#x672C;&#x53F7;).minor(&#x6B21;&#x7248;&#x672C;&#x53F7;).patch(&#x4FEE;&#x8BA2;&#x53F7;)</li>
</ul>
</li>
</ul>
</li>
</ul>
<h1 class="mume-header" id="%E5%BC%80%E5%8F%91%E6%9C%AC%E5%9C%B0%E7%8E%AF%E5%A2%83">&#x5F00;&#x53D1;&#x672C;&#x5730;&#x73AF;&#x5883;</h1>

<ul>
<li>&#x65B0;&#x5EFA; .env.development.local &#x6765;&#x91CD;&#x5199;&#x90E8;&#x5206;&#x73AF;&#x5883;&#x53D8;&#x91CF;&#xFF0C;&#x5982;&#xFF1A;
<ul>
<li>&#x6A21;&#x62DF;&#x6570;&#x636E;&#xFF1A;<code>VUE_APP_MOCK = true</code></li>
<li>&#x63A5;&#x53E3;&#x670D;&#x52A1;&#xFF1A;<code>DEV_PROXY_TARGET_API = http://************:8081</code></li>
<li>...</li>
</ul>
</li>
</ul>
<h1 class="mume-header" id="stage-%E6%B5%8B%E8%AF%95%E7%8E%AF%E5%A2%83">stage &#x6D4B;&#x8BD5;&#x73AF;&#x5883;</h1>

<ul>
<li>stage &#x73AF;&#x5883;&#x5BA2;&#x6237;&#x7AEF;&#x4FA7;&#x5141;&#x8BB8;&#x81EA;&#x5B9A;&#x4E49;&#x63A5;&#x53E3;&#x524D;&#x7F00;&#xFF0C;&#x65B9;&#x4FBF;&#x8C03;&#x8BD5;&#xFF08;&#x7279;&#x522B;&#x662F;&#x540E;&#x7AEF;&#x5F00;&#x53D1;&#xFF09;&#xFF0C;&#x53EF;&#x901A;&#x8FC7;&#x6D4F;&#x89C8;&#x5668;&#x63A7;&#x5236;&#x53F0;&#x8F93;&#x5165;&#xFF0C;&#x5982;&#xFF1A;<pre data-role="codeBlock" data-info="js" class="language-javascript"><span class="token comment">/* &#x9700;&#x8981;&#x63A5;&#x53E3;&#x652F;&#x6301; cors &#x8DE8;&#x57DF;&#xFF0C;&#x6216;&#x8BBE;&#x7F6E;&#x6D4F;&#x89C8;&#x5668;&#x5141;&#x8BB8;&#x8DE8;&#x57DF; */</span>
<span class="token dom variable">localStorage</span><span class="token punctuation">.</span><span class="token property-access">vue_web_baseurl_api</span> <span class="token operator">=</span> <span class="token string">&apos;http://127.0.0.1:8081&apos;</span>
<span class="token dom variable">localStorage</span><span class="token punctuation">.</span><span class="token property-access">vue_web_baseurl_api</span> <span class="token operator">=</span> <span class="token string">&apos;http://127.0.0.1:8081/api&apos;</span>
<span class="token comment">// ...</span>
</pre></li>
</ul>
<h1 class="mume-header" id="%E5%BC%80%E5%8F%91%E7%9B%B8%E5%85%B3%E6%8F%92%E4%BB%B6%E5%B7%A5%E5%85%B7">&#x5F00;&#x53D1;&#x76F8;&#x5173;&#x63D2;&#x4EF6;/&#x5DE5;&#x5177;</h1>

<ul>
<li>VSCode &#x76F8;&#x5173;&#x63D2;&#x4EF6;
<ul>
<li>&#x5FC5;&#x8981;&#x63D2;&#x4EF6;
<ul>
<li><code>ESLint</code></li>
<li><code>Vetur</code></li>
<li><code>Prettier - Code formatter</code></li>
<li><code>path Autocomplete</code></li>
</ul>
</li>
<li>&#x63A8;&#x8350;&#x63D2;&#x4EF6;
<ul>
<li><code>stylelint</code></li>
<li><code>vscode-element-helper</code> (element-ui &#x4E13;&#x7528;)</li>
<li><code>SVG Gallery</code></li>
<li><code>Debugger for Chrome</code></li>
<li><code>GitLens -- Git supercharged</code></li>
</ul>
</li>
</ul>
</li>
<li>Chrome &#x76F8;&#x5173;&#x63D2;&#x4EF6;
<ul>
<li>&#x5FC5;&#x8981;&#x63D2;&#x4EF6;
<ul>
<li><code>vue-devtools</code></li>
</ul>
</li>
</ul>
</li>
<li>&#x63A8;&#x8350;&#x63D2;&#x4EF6;
<ul>
<li><code>JSON Viewer</code> (&#x76F4;&#x63A5;&#x5728;&#x5730;&#x5740;&#x680F;&#x4E2D;&#x53D1; get &#x8BF7;&#x6C42;&#x65F6;&#xFF0C;&#x65B9;&#x4FBF;&#x67E5;&#x770B; json &#x6570;&#x636E;)</li>
</ul>
</li>
</ul>
<h1 class="mume-header" id="%E5%BC%80%E5%8F%91%E8%A7%84%E8%8C%83">&#x5F00;&#x53D1;&#x89C4;&#x8303;</h1>

<h2 class="mume-header" id="%E7%8E%AF%E5%A2%83%E5%8F%98%E9%87%8F%E4%BD%BF%E7%94%A8%E8%A7%84%E8%8C%83doc">&#x73AF;&#x5883;&#x53D8;&#x91CF;&#x4F7F;&#x7528;&#x89C4;&#x8303;&#xFF08;doc&#xFF09;</h2>

<ul>
<li>&#x8BE6;&#x89C1;&#xFF1A;<code>@/../docs/&#x73AF;&#x5883;&#x53D8;&#x91CF;&#x4F7F;&#x7528;&#x89C4;&#x8303;</code></li>
</ul>
<h2 class="mume-header" id="axios-%E4%BD%BF%E7%94%A8%E8%A7%84%E8%8C%83doc">axios &#x4F7F;&#x7528;&#x89C4;&#x8303;&#xFF08;doc&#xFF09;</h2>

<ul>
<li>&#x8BE6;&#x89C1;&#xFF1A;<code>@/../docs/axios&#x4F7F;&#x7528;&#x89C4;&#x8303;</code></li>
</ul>
<h2 class="mume-header" id="vue">vue</h2>

<h3 class="mume-header" id="%E6%95%B0%E6%8D%AE%E6%B5%81%E5%90%91">&#x3010;&#x6570;&#x636E;&#x6D41;&#x5411;&#x3011;</h3>

<ul>
<li>
<p>&#x5355;&#x4E2A;&#x7EC4;&#x4EF6;&#x7684;&#x6570;&#x636E;&#x6D41;</p>
<pre data-role="codeBlock" data-info class="language-"><code>props&#x3001;data/$store/$route&#x3001;computed (&#x7531;&#x524D;&#x9762;&#x6D3E;&#x751F;)
  &#x2193;
template/render
  &#x2193;
&#x7528;&#x6237;&#x4EA4;&#x4E92;&#x4E8B;&#x4EF6;&#x3001;&#x521D;&#x59CB;&#x5316;&#x7684;&#x5F02;&#x6B65;&#x56DE;&#x8C03;
  &#x2193;
data/$store/$route
</code></pre></li>
<li>
<p>&#x7EC4;&#x4EF6;&#x95F4;&#x7684;&#x6570;&#x636E;&#x6D41;</p>
<ul>
<li>&#x7236;&#x5411;&#x5B50;&#x4F20;&#x9012;&#x7528; props</li>
<li>&#x5B50;&#x5411;&#x7236;&#x4F20;&#x9012;&#x7528; vue &#x5185;&#x7F6E;&#x7684;&#x81EA;&#x5B9A;&#x4E49;&#x4E8B;&#x4EF6;&#xFF0C;&#x5373; <code>$emit</code></li>
<li>&#x7236;&#x5B50;&#x53CC;&#x5411;&#x4F20;&#x9012;&#x7528; <a target="_blank" href="https://cn.vuejs.org/v2/guide/components-custom-events.html">v-model</a> &#x6216; <a target="_blank" href="https://cn.vuejs.org/v2/guide/components-custom-events.html">.sync</a></li>
<li>&#x8DE8;&#x8D8A;&#x4F20;&#x9012;&#x7528; vuex</li>
<li>&#x8DE8;&#x8D8A;&#x4F20;&#x9012;&#x7528; eventBus&#xFF08;&#x614E;&#x7528;&#xFF09;
<ul>
<li>&#x89C4;&#x5212;&#x597D;&#x4F5C;&#x7528;&#x8303;&#x56F4;
<ul>
<li>&#x901A;&#x8FC7;&#x5B9E;&#x4F8B;&#x63A7;&#x5236;&#x8303;&#x56F4;&#xFF08;&#x4E0D;&#x540C;&#x8303;&#x56F4;&#x4E0D;&#x540C;&#x5B9E;&#x4F8B;&#xFF09;</li>
<li>&#x901A;&#x8FC7;&#x547D;&#x540D;&#x7A7A;&#x95F4;&#x63A7;&#x5236;&#x8303;&#x56F4;&#xFF08;&#x4E0D;&#x540C;&#x8303;&#x56F4;&#x5171;&#x4EAB;&#x540C;&#x4E00;&#x5B9E;&#x4F8B;&#xFF09;</li>
</ul>
</li>
<li>&#x4E8B;&#x4EF6;&#x540D;&#x4F7F;&#x7528; kebab-case &#x547D;&#x540D;&#x6CD5;</li>
<li>&#x5907;&#x6CE8;&#x597D;&#x4F7F;&#x7528;&#x8BF4;&#x660E;&#xFF08;&#x6700;&#x597D;&#x80FD;&#x7EF4;&#x62A4;&#x5230;&#x76F8;&#x5173; md &#x6587;&#x6863;&#xFF0C;&#x5F62;&#x6210;&#x4E8B;&#x4EF6;&#x6E05;&#x5355;&#xFF0C;&#x65B9;&#x4FBF;&#x7D22;&#x5F15;/&#x67E5;&#x9605;/&#x7406;&#x89E3;&#xFF09;</li>
</ul>
</li>
<li>&#x7D27;&#x5BC6;&#x8026;&#x5408;&#x7684;&#x9694;&#x4EE3;&#x4F20;&#x9012;&#x4E5F;&#x53EF;&#x4EE5;&#x7528; provide/inject&#xFF08;&#x6CE8;&#x610F;&#x54CD;&#x5E94;&#x5F0F;&#x95EE;&#x9898;&#xFF09;
<ul>
<li>&#x5F53;&#x9700;&#x8981;&#x8FDB;&#x884C;&#x53CD;&#x5411;&#x4F20;&#x9012;&#x65F6;&#xFF0C;&#x53EF;&#x4EE5;&#x901A;&#x8FC7;&#x56DE;&#x8C03;&#x65B9;&#x5F0F;&#xFF08;&#x53C2;&#x8003; React&#xFF09;</li>
</ul>
</li>
</ul>
</li>
</ul>
<h3 class="mume-header" id="%E6%85%8E%E7%94%A8%E5%85%A8%E5%B1%80%E6%B3%A8%E5%86%8C">&#x3010;&#x614E;&#x7528;&#x5168;&#x5C40;&#x6CE8;&#x518C;&#x3011;</h3>

<ul>
<li>
<p>&#x7EC4;&#x4EF6;&#x3001;&#x6DF7;&#x5165; ... &#x5E94;&#x4F7F;&#x7528;&#x5C40;&#x90E8;&#x6CE8;&#x518C;</p>
<p>&#x5C40;&#x90E8;&#x6CE8;&#x518C;&#x53EF;&#x4FDD;&#x6301;&#x6E05;&#x6670;&#x7684;&#x4F9D;&#x8D56;&#x5173;&#x7CFB;&#xFF0C;&#x5E76;&#x4E14; IDE &#x667A;&#x80FD;&#x611F;&#x77E5;&#x66F4;&#x4E3A;&#x53CB;&#x597D;</p>
</li>
</ul>
<h3 class="mume-header" id="%E7%BB%84%E4%BB%B6%E5%90%8D%E7%A7%B0">&#x3010;&#x7EC4;&#x4EF6;&#x540D;&#x79F0;&#x3011;</h3>

<ul>
<li>
<p>&#x540D;&#x79F0;&#x5927;&#x5C0F;&#x5199;</p>
<pre data-role="codeBlock" data-info="html" class="language-html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>script</span><span class="token punctuation">&gt;</span></span><span class="token script"><span class="token language-javascript">
  <span class="token keyword module">import</span> <span class="token imports"><span class="token maybe-class-name">MyComponent</span></span> <span class="token keyword module">from</span> <span class="token string">&apos;@/components/MyComponent.vue&apos;</span> <span class="token comment">// &#x6587;&#x4EF6;&#x540D;&#x4F7F;&#x7528; PascalCase &#x547D;&#x540D;&#x6CD5;</span>
  <span class="token keyword module">export</span> <span class="token keyword module">default</span> <span class="token punctuation">{</span>
    components<span class="token operator">:</span> <span class="token punctuation">{</span> <span class="token maybe-class-name">MyComponent</span> <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">}</span>
</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>script</span><span class="token punctuation">&gt;</span></span>

<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>template</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span><span class="token punctuation">&gt;</span></span>
    <span class="token comment">&lt;!-- &#x5C40;&#x90E8;&#x6CE8;&#x518C;&#x7684;&#x4F7F;&#x7528; PascalCase &#x65B9;&#x5F0F;&#x8C03;&#x7528;&#xFF08;&#x533A;&#x522B;&#x4E8E;&#x5168;&#x5C40;&#x6CE8;&#x518C;&#x7684;&#xFF0C;&#x540C;&#x65F6;&#x53C8;&#x65B9;&#x4FBF;&#x9009;&#x4E2D;&#x5B9A;&#x4F4D;&#xFF09; --&gt;</span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>MyComponent</span> <span class="token punctuation">/&gt;</span></span>
    <span class="token comment">&lt;!-- &#x5168;&#x5C40;&#x6CE8;&#x518C;&#x7684;&#x4F7F;&#x7528; kebab-case &#x65B9;&#x5F0F;&#x8C03;&#x7528; --&gt;</span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>el-input</span> <span class="token punctuation">/&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>template</span><span class="token punctuation">&gt;</span></span>
</pre></li>
<li>
<p>&#x4F7F;&#x7528;&#x524D;&#x7F00;</p>
<ul>
<li><a href="#hash_Ex">&#x6269;&#x5C55;/&#x5305;&#x88C5;&#x7B2C;&#x4E09;&#x65B9;&#x5F00;&#x6E90;&#x7EC4;&#x4EF6;&#x6216;&#x5185;&#x90E8;&#x516C;&#x5171;&#x5E93;&#x7EC4;&#x4EF6;</a> &#x4F7F;&#x7528; Ex &#x524D;&#x7F00;</li>
<li>&#x5355;&#x4F8B;&#x7EC4;&#x4EF6;&#x4F7F;&#x7528; The &#x524D;&#x7F00;</li>
</ul>
</li>
</ul>
<h3 class="mume-header" id="%E7%BB%84%E4%BB%B6%E4%B8%AD%E7%9A%84-css">&#x3010;&#x7EC4;&#x4EF6;&#x4E2D;&#x7684; CSS&#x3011;</h3>

<ul>
<li>
<p>&#x4F7F;&#x7528; <a target="_blank" href="https://vue-loader.vuejs.org/zh/guide/css-modules.html">CSS Modules</a>&#xFF0C;&#x57FA;&#x4E8E;&#x5982;&#x4E0B;&#x8003;&#x8651;&#xFF1A;</p>
<ul>
<li>&#x4E0D;&#x8BA9;&#x5916;&#x90E8;&#x8FDB;&#x884C;&#x6837;&#x5F0F;&#x91CD;&#x5199;&#xFF0C;&#x907F;&#x514D;&#x5F3A;&#x8026;&#x5408; (&#x53EF;&#x901A;&#x8FC7; props &#x6765;&#x5904;&#x7406;&#x5185;&#x90E8;&#x6837;&#x5F0F;&#x7684;&#x53D8;&#x5316;)</li>
<li>&#x653E;&#x5FC3;&#x4F7F;&#x7528;&#x7B80;&#x77ED;&#x4E14;&#x8BED;&#x4E49;&#x5F3A;&#x7684; class &#x540D;&#xFF0C;&#x65E0;&#x9700;&#x591A;&#x4F59;&#x7684;&#x547D;&#x540D;&#x7A7A;&#x95F4;</li>
<li>&#x6837;&#x5F0F;&#x5F7B;&#x5E95;&#x6A21;&#x5757;&#x5316;&#xFF08;&#x5373;&#x6211;&#x7684;&#x89C4;&#x5219;&#x5F71;&#x54CD;&#x4E0D;&#x4E86;&#x522B;&#x4EBA;&#xFF0C;&#x522B;&#x4EBA;&#x7684;&#x89C4;&#x5219;&#x4E5F;&#x5F71;&#x54CD;&#x4E0D;&#x4E86;&#x6211;&#xFF09;</li>
</ul>
</li>
<li>
<p>&#x4F7F;&#x7528;&#x65B9;&#x5F0F;</p>
<ul>
<li>
<p>&#x8BED;&#x6CD5;</p>
<pre data-role="codeBlock" data-info="less" class="language-less"><span class="token comment">// &#x9ED8;&#x8BA4;&#x4E3A; local &#x533A;&#x57DF;</span>
<span class="token selector">.xxx_xxx</span> <span class="token punctuation">{</span>
<span class="token punctuation">}</span>

<span class="token comment">// &#x8F6C;&#x5230; global &#x533A;&#x57DF;</span>
<span class="token selector">:global</span> <span class="token punctuation">{</span>
  <span class="token selector">.yyy-yyy</span> <span class="token punctuation">{</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token selector">:global</span> <span class="token punctuation">{</span>
  <span class="token comment">// &#x8F6C;&#x5230; local &#x533A;&#x57DF;</span>
  <span class="token selector">:local</span> <span class="token punctuation">{</span>
    <span class="token selector">.xxx_xxx</span> <span class="token punctuation">{</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// &#x4EC5;&#x8F6C;&#x6362;&#x9009;&#x62E9;&#x5668;</span>
<span class="token selector">.xxx_xxx :global(.yyy-yyy):hover</span> <span class="token punctuation">{</span>
<span class="token punctuation">}</span>
<span class="token selector">.xxx_xxx:global(.yyy-yyy):hover</span> <span class="token punctuation">{</span>
<span class="token punctuation">}</span>
<span class="token selector">:global</span> <span class="token punctuation">{</span>
  <span class="token selector">.yyy-yyy :local(.xxx_xxx):hover</span> <span class="token punctuation">{</span>
  <span class="token punctuation">}</span>
  <span class="token selector">.yyy-yyy:local(.xxx_xxx):hover</span> <span class="token punctuation">{</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</pre></li>
<li>
<p>&#x5355;&#x4E2A;&#x7EC4;&#x4EF6;&#x4E13;&#x5C5E;</p>
<pre data-role="codeBlock" data-info="html" class="language-html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>style</span> <span class="token attr-name">lang</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>less<span class="token punctuation">&quot;</span></span> <span class="token attr-name">module</span><span class="token punctuation">&gt;</span></span><span class="token style"></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>style</span><span class="token punctuation">&gt;</span></span>
</pre></li>
<li>
<p>&#x591A;&#x4E2A;&#x7EC4;&#x4EF6;&#x5171;&#x7528; (*.module.less)</p>
<pre data-role="codeBlock" data-info="js" class="language-javascript"><span class="token keyword module">import</span> <span class="token imports">style</span> <span class="token keyword module">from</span> <span class="token string">&apos;./style.module.less&apos;</span>
</pre></li>
</ul>
</li>
<li>
<p>&#x6CE8;&#x610F;&#x4E8B;&#x9879;</p>
<ul>
<li>&#x9009;&#x62E9;&#x5668;&#x5C11;&#x5D4C;&#x5957;&#xFF0C;&#x5C3D;&#x91CF;&#x7684;&#x6241;&#x5E73;&#x5316;</li>
<li>local &#x7684;&#x547D;&#x540D;&#x63A8;&#x8350;&#x5982;&#x4E0B;&#x98CE;&#x683C;&#xFF0C;&#x8FD9;&#x6837;&#x533A;&#x522B;&#x4E8E; global&#xFF0C;&#x4E5F;&#x4EE5;&#x533A;&#x522B;&#x4E8E;&#x9A7C;&#x5CF0;&#x5F0F;&#x7684; js &#x53D8;&#x91CF;/&#x5C5E;&#x6027;<pre data-role="codeBlock" data-info="less" class="language-less"><span class="token selector">.xxx_xxx</span> <span class="token punctuation">{</span>
  <span class="token comment">// &#x5355;&#x8BCD;&#x5168;&#x5C0F;&#x5199;&#xFF0C;&#x591A;&#x4E2A;&#x5355;&#x8BCD;&#x95F4;&#x7528;&#x4E0B;&#x5212;&#x7EBF;&#x8FDE;&#x63A5;</span>
<span class="token punctuation">}</span>
</pre></li>
</ul>
</li>
</ul>
<h3 class="mume-header" id="%E7%BB%9F%E4%B8%80%E6%A0%87%E7%AD%BE%E9%A1%BA%E5%BA%8F">&#x3010;&#x7EDF;&#x4E00;&#x6807;&#x7B7E;&#x987A;&#x5E8F;&#x3011;</h3>

<ul>
<li>script --&gt; template --&gt; style&#xFF0C;&#x5E76;&#x4F7F;&#x7528;&#x7A7A;&#x884C;&#x5206;&#x9694;</li>
</ul>
<h3 class="mume-header" id="%E5%85%B6%E5%AE%83%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9">&#x3010;&#x5176;&#x5B83;&#x6CE8;&#x610F;&#x4E8B;&#x9879;&#x3011;</h3>

<ul>
<li>&#x614E;&#x7528; <code>$refs</code>&#x3001;<code>$parent</code>&#x3001;<code>$root</code>&#x3001;<code>provide/inject</code>
<ul>
<li><code>$refs</code> &#x4E00;&#x822C;&#x7528;&#x4E8E;&#x7B2C;&#x4E09;&#x65B9;&#x5F00;&#x6E90;&#x7EC4;&#x4EF6;&#x6216;&#x5185;&#x90E8;&#x516C;&#x5171;&#x5E93;&#x7EC4;&#x4EF6;&#x6216;&#x975E;&#x5E38;&#x7A33;&#x5B9A;&#x7684;&#x7EC4;&#x4EF6;&#xFF0C;&#x4EE5;&#x8C03;&#x7528;&#x663E;&#x5F0F;&#x58F0;&#x660E;&#x7684;&#x65B9;&#x6CD5;</li>
</ul>
</li>
<li>&#x7EC4;&#x4EF6;&#x4E2D;&#x7684; data &#x53CA; vuex &#x4E2D;&#x7684; state &#x5E94;&#x8BE5;&#x53EF;&#x5E8F;&#x5217;&#x5316;&#xFF0C;&#x5373;&#x4E0D;&#x8981;&#x5B58; undefined&#x3001;function &#x7B49;</li>
</ul>
<h3 class="mume-header" id="a-target_blank-hrefhttpscnvuejsorgv2style-guide%E5%85%B6%E5%AE%83%E5%88%99%E9%81%B5%E5%AE%88-vue-%E5%AE%98%E6%96%B9%E9%A3%8E%E6%A0%BC%E6%8C%87%E5%8D%97a">&#x3010; <a target="_blank" href="https://cn.vuejs.org/v2/style-guide/">!!!&#x5176;&#x5B83;&#x5219;&#x9075;&#x5B88; vue &#x5B98;&#x65B9;&#x98CE;&#x683C;&#x6307;&#x5357;</a>&#x3011;</h3>

<hr>
<h2 class="mume-header" id="vue-router">vue-router</h2>

<ul>
<li>
<p>url &#x5B9A;&#x4E49;&#x51C6;&#x5219;</p>
<ul>
<li>
<p>path &#x5BF9;&#x5E94;&#x89C6;&#x56FE;&#x53D8;&#x5316;&#xFF0C;query &#x5BF9;&#x5E94;&#x6570;&#x636E;&#x53D8;&#x5316;&#xFF0C;hash &#x5BF9;&#x5E94;&#x6EDA;&#x52A8;&#x6761;&#x4F4D;&#x7F6E;</p>
</li>
<li>
<p>path &#x4F7F;&#x7528; kebab-case &#x547D;&#x540D;&#x6CD5;&#xFF0C;&#x5E76;&#x4E14;&#x5C3D;&#x91CF;&#x4E0E;&#x7EC4;&#x4EF6;&#x540D;&#x76F8;&#x5339;&#x914D;&#xFF08;&#x5373;&#x4E00;&#x773C;&#x770B;&#x5230; path &#x5C31;&#x80FD;&#x8FC5;&#x901F;&#x627E;&#x5230;&#x5BF9;&#x5E94;&#x7684;&#x7EC4;&#x4EF6;&#xFF09;</p>
<pre data-role="codeBlock" data-info class="language-"><code>&#x8DEF;&#x7531; path&#xFF1A;/project-list
  &#x2193;
&#x8DEF;&#x7531;&#x7EC4;&#x4EF6;&#xFF1A;@/views/ProjectList.vue | @/views/ProjectList/index.vue
</code></pre></li>
</ul>
</li>
<li>
<p>&#x547D;&#x540D;&#x8DEF;&#x7531;&#x7684; name &#x503C;&#x4F7F;&#x7528; kebab-case &#x547D;&#x540D;&#x6CD5;&#xFF0C;&#x5E76;&#x4E14;&#x5728;&#x5D4C;&#x5957;&#x65F6;&#x5E26;&#x547D;&#x540D;&#x7A7A;&#x95F4;&#xFF08;&#x907F;&#x514D;&#x51B2;&#x7A81;&#xFF09;</p>
<pre data-role="codeBlock" data-info="js" class="language-javascript"><span class="token keyword module">export</span> <span class="token keyword">const</span> routes <span class="token operator">=</span> <span class="token punctuation">{</span>
  path<span class="token operator">:</span> <span class="token string">&apos;/user-center&apos;</span><span class="token punctuation">,</span>
  name<span class="token operator">:</span> <span class="token string">&apos;user-center&apos;</span><span class="token punctuation">,</span>
  <span class="token comment">// ...</span>
  children<span class="token operator">:</span> <span class="token punctuation">[</span>
    <span class="token punctuation">{</span>
      path<span class="token operator">:</span> <span class="token string">&apos;base-info&apos;</span><span class="token punctuation">,</span>
      name<span class="token operator">:</span> <span class="token string">&apos;uc-base-info&apos;</span><span class="token punctuation">,</span> <span class="token comment">// &#x5E26;&#x547D;&#x540D;&#x7A7A;&#x95F4; uc-</span>
      <span class="token comment">// ...</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">]</span><span class="token punctuation">,</span>
<span class="token punctuation">}</span>
</pre></li>
<li>
<p>&#x5F53;&#x7EC4;&#x4EF6;&#x4F9D;&#x8D56; <code>$route</code> &#x4F5C;&#x4E3A;&#x6838;&#x5FC3;&#x6570;&#x636E;&#x65F6;&#xFF0C;&#x8981;&#x4F7F;&#x7528;<a target="_blank" href="https://router.vuejs.org/zh/guide/essentials/passing-props.html">&#x8DEF;&#x7531;&#x7EC4;&#x4EF6;&#x4F20;&#x53C2;</a>&#xFF0C;&#x4E0E; <code>$route</code> &#x89E3;&#x8026;&#xFF0C;&#x4E5F;&#x4F7F;&#x5F97;&#x4F9D;&#x8D56;&#x66F4;&#x4E3A;&#x663E;&#x5F0F;&#x6E05;&#x6670;</p>
<pre data-role="codeBlock" data-info="js" class="language-javascript"><span class="token keyword module">export</span> <span class="token keyword">const</span> routes <span class="token operator">=</span> <span class="token punctuation">{</span>
  path<span class="token operator">:</span> <span class="token string">&apos;/project-detail&apos;</span><span class="token punctuation">,</span>
  <span class="token function-variable function">props</span><span class="token operator">:</span> <span class="token punctuation">(</span><span class="token parameter"><span class="token punctuation">{</span> query<span class="token operator">:</span> <span class="token punctuation">{</span> id <span class="token punctuation">}</span> <span class="token punctuation">}</span></span><span class="token punctuation">)</span> <span class="token arrow operator">=&gt;</span> <span class="token punctuation">(</span><span class="token punctuation">{</span> id <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
  <span class="token comment">// ...</span>
<span class="token punctuation">}</span>
</pre></li>
<li>
<p>&#x89C6;&#x56FE;&#x8DF3;&#x8F6C;&#x80FD;&#x7528;&#x58F0;&#x660E;&#x5F0F;&#x5C31;&#x7528;&#x58F0;&#x660E;&#x5F0F;</p>
<pre data-role="codeBlock" data-info="html" class="language-html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>ul</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>router-link</span> <span class="token attr-name">tag</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>li<span class="token punctuation">&quot;</span></span> <span class="token attr-name">:to</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>...<span class="token punctuation">&quot;</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span><span class="token punctuation">&gt;</span></span>&#x4F7F;&#x7528;&#x58F0;&#x660E;&#x5F0F;<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
    ...
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>router-link</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>ul</span><span class="token punctuation">&gt;</span></span>

<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>ul</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">@click</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>$router.push(...)<span class="token punctuation">&quot;</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span><span class="token punctuation">&gt;</span></span>&#x4F7F;&#x7528;&#x547D;&#x4EE4;&#x5F0F;<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
    ...
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>ul</span><span class="token punctuation">&gt;</span></span>
</pre></li>
</ul>
<hr>
<h2 class="mume-header" id="vuex">vuex</h2>

<ul>
<li>
<p>&#x9700;&#x8981;&#x7531; vuex &#x7BA1;&#x7406;&#x7684;&#x6570;&#x636E;</p>
<ul>
<li>&#x7EC4;&#x4EF6;&#x95F4;&#x5171;&#x4EAB;&#x7684;&#x54CD;&#x5E94;&#x5F0F;&#x6570;&#x636E;</li>
<li>&#x7EC4;&#x4EF6;&#x95F4;&#x9700;&#x8981;&#x8DE8;&#x8D8A;&#x4F20;&#x9012;&#x7684;&#x6570;&#x636E;</li>
</ul>
</li>
<li>
<p>getter&#x3001;mutation&#x3001;action&#x3001;module &#x4F7F;&#x7528;&#x9A7C;&#x5CF0;&#x547D;&#x540D;&#x6CD5;</p>
</li>
<li>
<p>module &#x5E94;&#x907F;&#x514D;&#x5D4C;&#x5957;&#xFF0C;&#x5C3D;&#x91CF;&#x6241;&#x5E73;&#x5316;</p>
</li>
<li>
<p><mark>module &#x5E94;&#x8BE5;&#x542F;&#x7528;&#x547D;&#x540D;&#x7A7A;&#x95F4; <code>namespaced: true</code></mark></p>
</li>
</ul>
<hr>
<h2 class="mume-header" id="%E6%A8%A1%E5%9D%97%E5%A4%8D%E7%94%A8">&#x6A21;&#x5757;&#x590D;&#x7528;</h2>

<ul>
<li>
<p>&#x907F;&#x514D;&#x91CD;&#x590D;&#x9020;&#x8F6E;&#x5B50;&#xFF0C;&#x591A;&#x4F7F;&#x7528;&#x6210;&#x719F;&#x7684;&#x73B0;&#x6210;&#x5DE5;&#x5177;/&#x7C7B;&#x5E93;/&#x7EC4;&#x4EF6;&#xFF0C;&#x5982;&#xFF1A;lodash&#x3001;qs&#x3001;url-parse&#x3001;date-fns/format &#x7B49;</p>
</li>
<li>
<p>&#x6A21;&#x5757;&#x8BBE;&#x8BA1;&#x539F;&#x5219;&#xFF1A;</p>
<ul>
<li>&#x9AD8;&#x5185;&#x805A;&#x4F4E;&#x8026;&#x5408;&#x3001;&#x53EF;&#x6269;&#x5C55;</li>
<li>&#x4E0D;&#x8981;&#x53BB;&#x6539;&#x53D8;&#x6A21;&#x5757;&#x7684;&#x5165;&#x53C2; (&#x5F15;&#x7528;&#x7C7B;&#x578B;)&#xFF0C;&#x5982;&#xFF1A;&#x51FD;&#x6570;&#x53C2;&#x6570;&#x3001;&#x7EC4;&#x4EF6; prop</li>
<li>&#x2026;</li>
</ul>
</li>
<li>
<p>&#x65B9;&#x6CD5;&#x5165;&#x53C2;&#x8BBE;&#x8BA1;</p>
<pre data-role="codeBlock" data-info="js" class="language-javascript"><span class="token comment">// &#x53C2;&#x6570;&#x7C7B;&#x578B;&#x4E0E;&#x4E2A;&#x6570;&#x8981;&#x4FDD;&#x6301;&#x7A33;&#x5B9A;</span>
<span class="token comment">// &#x5EFA;&#x8BAE;&#x53C2;&#x6570;&#x4E0D;&#x8981;&#x8D85;&#x8FC7;3&#x4E2A;&#xFF0C;&#x4E14;&#x9884;&#x7559;&#x4E00;&#x4E2A; options &#x5BF9;&#x8C61;&#xFF0C;&#x4EE5;&#x63D0;&#x9AD8;&#x6269;&#x5C55;&#x6027;</span>
<span class="token comment">// &#x65B9;&#x6CD5;&#x5C3D;&#x91CF;&#x7EAF;&#x51C0; (&#x7EAF;&#x51FD;&#x6570;&#x601D;&#x60F3;)</span>
<span class="token keyword module">export</span> <span class="token keyword">function</span> <span class="token function">myMethod1</span><span class="token punctuation">(</span><span class="token parameter">a<span class="token punctuation">,</span> options</span><span class="token punctuation">)</span> <span class="token punctuation">{</span><span class="token punctuation">}</span> <span class="token comment">// &#x5F53;&#x5FC5;&#x9009;&#x53C2;&#x6570;&#x53EA;&#x6709;&#x4E00;&#x4E2A;&#x65F6;</span>
<span class="token keyword module">export</span> <span class="token keyword">function</span> <span class="token function">myMethod2</span><span class="token punctuation">(</span><span class="token parameter">a<span class="token punctuation">,</span> b<span class="token punctuation">,</span> options</span><span class="token punctuation">)</span> <span class="token punctuation">{</span><span class="token punctuation">}</span> <span class="token comment">// &#x5F53;&#x5FC5;&#x9009;&#x53C2;&#x6570;&#x53EA;&#x6709;&#x4E24;&#x4E2A;&#x65F6;</span>
<span class="token keyword module">export</span> <span class="token keyword">function</span> <span class="token function">myMethod3</span><span class="token punctuation">(</span><span class="token parameter">options</span><span class="token punctuation">)</span> <span class="token punctuation">{</span><span class="token punctuation">}</span> <span class="token comment">// &#x5F53;&#x5FC5;&#x9009;&#x53C2;&#x6570;&#x6709;&#x4E24;&#x4E2A;&#x4EE5;&#x4E0A;&#x65F6;</span>
<span class="token keyword module">export</span> <span class="token keyword">function</span> <span class="token function">myMethod4</span><span class="token punctuation">(</span><span class="token parameter">options</span><span class="token punctuation">)</span> <span class="token punctuation">{</span><span class="token punctuation">}</span> <span class="token comment">// &#x5F53;&#x6240;&#x6709;&#x53C2;&#x6570;&#x90FD;&#x662F;&#x53EF;&#x9009;&#x65F6;</span>

<span class="token comment">// &#x6709;&#x65F6;&#x4E3A;&#x4E86;&#x63D0;&#x9AD8;&#x7075;&#x6D3B;&#x6027;&#xFF0C;&#x53C2;&#x6570;&#x7C7B;&#x578B;&#x53EF;&#x4EE5;&#x662F;&#x4E24;&#x91CD;&#xFF0C;&#x4E00;&#x91CD;&#x662F;&#x671F;&#x671B;&#x503C;&#xFF0C;&#x53E6;&#x4E00;&#x91CD;&#x662F;&#x8FD4;&#x56DE;&#x671F;&#x671B;&#x503C;&#x7684;&#x51FD;&#x6570; (&#x53EF;&#x5E26;&#x53C2;)</span>
<span class="token keyword module">export</span> <span class="token keyword">function</span> <span class="token function">myMethod5</span><span class="token punctuation">(</span><span class="token parameter">a</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  a <span class="token operator">=</span> <span class="token keyword">typeof</span> a <span class="token operator">===</span> <span class="token string">&apos;function&apos;</span> <span class="token operator">?</span> <span class="token function">a</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">:</span> a
<span class="token punctuation">}</span>
</pre></li>
<li>
<p><span id="hash_Ex">&#x6269;&#x5C55;/&#x5305;&#x88C5;&#x7B2C;&#x4E09;&#x65B9;&#x5F00;&#x6E90;&#x7EC4;&#x4EF6;&#x6216;&#x5185;&#x90E8;&#x516C;&#x5171;&#x5E93;&#x7EC4;&#x4EF6;</span></p>
<ul>
<li>&#x666E;&#x901A;&#x5305;&#x88C5;&#xFF08;&#x4F1A;&#x591A;&#x51FA;&#x4E00;&#x5C42;&#x5B9E;&#x4F8B;&#xFF0C;&#x5BFC;&#x81F4; ref &#x4E22;&#x5931;&#xFF09;
<ul>
<li>&#x9700;&#x624B;&#x52A8;&#x900F;&#x4F20; <code>$attrs</code>&#x3001;<code>$listeners</code>&#xFF0C;&#x900F;&#x4F20;&#x524D;&#x53EF;&#x5148;&#x64CD;&#x63A7;</li>
<li>ref &#x4E22;&#x5931;&#x89E3;&#x51B3;&#x65B9;&#x5F0F;&#xFF1A;&#x4EE3;&#x7406;&#x539F;&#x7EC4;&#x4EF6;&#x7684;&#x6240;&#x6709;&#x5BF9;&#x5916;&#x65B9;&#x6CD5;</li>
</ul>
</li>
<li>&#x4F7F;&#x7528; extends &#x6DF7;&#x5165; (&#x76F8;&#x5173;&#x547D;&#x540D;&#x9700;&#x8981;&#x52A0; ex_ &#x524D;&#x7F00;&#xFF0C;&#x9632;&#x6B62;&#x8986;&#x76D6;)</li>
<li>&#x4F7F;&#x7528;<a target="_blank" href="https://cn.vuejs.org/v2/guide/render-function.html">&#x51FD;&#x6570;&#x5F0F;&#x7EC4;&#x4EF6;</a>&#x5305;&#x88C5;</li>
</ul>
</li>
</ul>
<hr>
<h2 class="mume-header" id="%E5%85%B6%E5%AE%83%E6%9D%82%E9%A1%B9">&#x5176;&#x5B83;&#x6742;&#x9879;</h2>

<ul>
<li>
<p>IDE &#x7EDF;&#x4E00;&#x4F7F;&#x7528; VSCode&#xFF0C;&#x5E76;&#x7EDF;&#x4E00;&#x4F7F;&#x7528;&#x76F8;&#x5173;&#x63D2;&#x4EF6;&#x53CA;&#x914D;&#x7F6E;</p>
</li>
<li>
<p>js &#x53D8;&#x91CF;&#x58F0;&#x660E;&#x5C3D;&#x91CF;&#x4F7F;&#x7528; const</p>
</li>
<li>
<p>js &#x53D8;&#x91CF;&#x6216;&#x5BF9;&#x8C61;&#x5C5E;&#x6027;&#x4F7F;&#x7528;&#x9A7C;&#x5CF0;&#x547D;&#x540D;&#x6CD5;</p>
</li>
<li>
<p>js &#x79C1;&#x6709;&#x53D8;&#x91CF;&#x6216;&#x5BF9;&#x8C61;&#x79C1;&#x6709;&#x5C5E;&#x6027;&#x4F7F;&#x7528; _ &#x524D;&#x7F00;&#xFF0C;&#x4F46;&#x662F; <a target="_blank" href="https://cn.vuejs.org/v2/style-guide">vue &#x5B9E;&#x4F8B;&#x5C5E;&#x6027;&#x4E0D;&#x8981;&#x4F7F;&#x7528; _ &#x524D;&#x7F00;</a>&#xFF0C;&#x907F;&#x514D;&#x4E0E;&#x5185;&#x7F6E;&#x79C1;&#x6709;&#x5C5E;&#x6027;&#x4EA7;&#x751F;&#x51B2;&#x7A81;&#xFF0C;&#x63A8;&#x8350;&#x4F7F;&#x7528; _ &#x540E;&#x7F00;&#x8FDB;&#x884C;&#x6807;&#x8BC6;</p>
<pre data-role="codeBlock" data-info="js" class="language-javascript"><span class="token keyword">let</span> _count <span class="token operator">=</span> <span class="token number">0</span> <span class="token comment">// &#x8868;&#x660E;&#x8BE5;&#x53D8;&#x91CF;&#x4EC5;&#x5728; createId &#x65B9;&#x6CD5;&#x4E2D;&#x4F7F;&#x7528; (&#x4E0E; createId &#x65B9;&#x6CD5;&#x7D27;&#x6328;&#x7740;)</span>
<span class="token keyword">const</span> <span class="token function-variable function">createId</span> <span class="token operator">=</span> <span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token arrow operator">=&gt;</span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span><span class="token known-class-name class-name">Date</span><span class="token punctuation">.</span><span class="token method function property-access">now</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token interpolation-punctuation punctuation">}</span></span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span><span class="token operator">++</span>_count<span class="token interpolation-punctuation punctuation">}</span></span><span class="token template-punctuation string">`</span></span>

<span class="token keyword">const</span> createId <span class="token operator">=</span> <span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token arrow operator">=&gt;</span> <span class="token punctuation">{</span>
  <span class="token keyword">let</span> count <span class="token operator">=</span> <span class="token number">0</span> <span class="token comment">// &#x9002;&#x65F6;&#x4F7F;&#x7528;&#x7ACB;&#x5373;&#x6267;&#x884C;&#x51FD;&#x6570;&#x53EF;&#x4EE5;&#x7B80;&#x6D01;&#x4F5C;&#x7528;&#x57DF;&#x53CA;&#x4FDD;&#x62A4;&#x79C1;&#x6709;&#x53D8;&#x91CF;</span>
  <span class="token keyword control-flow">return</span> <span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token arrow operator">=&gt;</span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span><span class="token known-class-name class-name">Date</span><span class="token punctuation">.</span><span class="token method function property-access">now</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token interpolation-punctuation punctuation">}</span></span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span><span class="token operator">++</span>count<span class="token interpolation-punctuation punctuation">}</span></span><span class="token template-punctuation string">`</span></span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">(</span><span class="token punctuation">)</span>

<span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">uid_</span> <span class="token operator">=</span> <span class="token function">createId</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token comment">// vue &#x5B9E;&#x4F8B;&#x5C5E;&#x6027;&#x4E0D;&#x8981;&#x4F7F;&#x7528; _ &#x524D;&#x7F00;&#xFF0C;&#x63A8;&#x8350;&#x4F7F;&#x7528; _ &#x540E;&#x7F00;&#x8FDB;&#x884C;&#x6807;&#x8BC6;</span>
</pre></li>
<li>
<p>&#x5BFC;&#x5165;&#x6A21;&#x5757;&#x65F6;&#x4E0D;&#x8981;&#x7701;&#x7565;&#x540E;&#x7F00;&#xFF08;js &#x9664;&#x5916;&#xFF09;&#xFF0C;&#x8FD9;&#x6837;&#x6709;&#x5229;&#x4E8E; IDE &#x611F;&#x77E5;&#xFF08;&#x7279;&#x522B;&#x662F; .vue&#xFF09;</p>
</li>
<li>
<p>&#x5BFC;&#x5165;&#x5F53;&#x524D;&#x76EE;&#x5F55;&#x4EE5;&#x5916;&#x7684;&#x6A21;&#x5757;&#x65F6;&#xFF0C;&#x5EFA;&#x8BAE;&#x4F7F;&#x7528;&apos;@&apos;&#x522B;&#x540D;</p>
<pre data-role="codeBlock" data-info="js" class="language-javascript"><span class="token comment">// js</span>
<span class="token keyword module">import</span> <span class="token imports"><span class="token maybe-class-name">XxxXxx</span></span> <span class="token keyword module">from</span> <span class="token string">&apos;@/components/XxxXxx.vue&apos;</span>
</pre><pre data-role="codeBlock" data-info="html" class="language-html"><span class="token comment">&lt;!-- template --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>img</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>@/assets/logo.png<span class="token punctuation">&quot;</span></span> <span class="token punctuation">/&gt;</span></span>
</pre><pre data-role="codeBlock" data-info="less" class="language-less"><span class="token comment">/* style */</span>
<span class="token variable">@import</span> <span class="token string">&apos;~@/styles/vars.less&apos;</span><span class="token punctuation">;</span>
<span class="token selector">.xxx</span> <span class="token punctuation">{</span>
  <span class="token property">background</span><span class="token punctuation">:</span> <span class="token url"><span class="token function">url</span><span class="token punctuation">(</span><span class="token string url">&apos;~@/assets/logo.png&apos;</span><span class="token punctuation">)</span></span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</pre></li>
<li>
<p><strong>&#x4E25;&#x683C;&#x9075;&#x5B88; ESLint &#x8BED;&#x6CD5;&#x6821;&#x9A8C;</strong>&#xFF0C;&#x8B66;&#x544A;&#x7EA7;&#x522B;&#x7684;&#x4E5F;&#x8981;&#x5904;&#x7406; (&#x6682;&#x65F6;&#x7528;&#x4E0D;&#x5230;&#x7684;&#x4EE3;&#x7801;&#x53EF;&#x4EE5;&#x5148;&#x6CE8;&#x91CA;&#x6389;)</p>
</li>
<li>
<p>css</p>
<ul>
<li>&#x5168;&#x5C40; class &#x4F7F;&#x7528; g- &#x524D;&#x7F00;</li>
<li>CSS &#x9009;&#x62E9;&#x5668;&#x5E94;&#x907F;&#x514D;&#x6DF1;&#x5D4C;&#x5957;&#xFF0C;&#x5C3D;&#x91CF;&#x7684;&#x6241;&#x5E73;&#x5316;</li>
<li>&#x5173;&#x952E;&#x9009;&#x62E9;&#x5668; (&#x6700;&#x53F3;&#x8FB9;) &#x907F;&#x514D;&#x4F7F;&#x7528;&#x901A;&#x914D;&#x7B26; *</li>
</ul>
</li>
</ul>
<hr>
<h2 class="mume-header" id="%E4%BB%A3%E7%A0%81%E6%B3%A8%E9%87%8A">&#x4EE3;&#x7801;&#x6CE8;&#x91CA;</h2>

<ul>
<li>
<p>&#x6587;&#x4EF6;&#x5934;&#x90E8;&#x6CE8;&#x91CA;</p>
<ul>
<li>
<p>&#x811A;&#x672C;&#x6587;&#x4EF6;&#x3001;&#x6837;&#x5F0F;&#x6587;&#x4EF6;</p>
<pre data-role="codeBlock" data-info="js" class="language-javascript"><span class="token doc-comment comment">/**
 * &#x8BF4;&#x660E;
 * <span class="token keyword">@author</span> &#x4F5C;&#x8005;
 */</span>
</pre></li>
<li>
<p>vue &#x6587;&#x4EF6;</p>
<pre data-role="codeBlock" data-info="html" class="language-html"><span class="token comment">&lt;!-- &#x8BF4;&#x660E; --&gt;</span>
<span class="token comment">&lt;!-- <AUTHOR> --&gt;</span>
</pre></li>
</ul>
</li>
<li>
<p>js &#x6CE8;&#x91CA; (&#x7ED3;&#x5408; <a target="_blank" href="https://jsdoc.app/">JSDoc &#x6CE8;&#x91CA;&#x6807;&#x51C6;</a>&#xFF0C;&#x5E2E;&#x52A9; IDE &#x667A;&#x80FD;&#x611F;&#x77E5;)</p>
<ul>
<li>
<p>&#x6CE8;&#x91CA;&#x683C;&#x5F0F;</p>
<pre data-role="codeBlock" data-info="js" class="language-javascript"><span class="token doc-comment comment">/**
 * &#x6587;&#x4EF6;&#x5934;&#x90E8;&#x3001;&#x5927;&#x7684;&#x533A;&#x5757;&#x3001;JSDoc
 */</span>

<span class="token comment">/* &#x4E00;&#x822C;&#x7684;&#x533A;&#x5757; */</span>

<span class="token comment">// &#x5C0F;&#x7684;&#x533A;&#x5757;&#x3001;&#x884C;</span>
</pre></li>
<li>
<p><a target="_blank" href="https://jsdoc.app/howto-es2015-modules.html">ES 2015 Modules</a></p>
<pre data-role="codeBlock" data-info="js" class="language-javascript"><span class="token doc-comment comment">/**
 * &#x4F7F;&#x7528; param &#x8868;&#x793A;&#x51FD;&#x6570;&#x5F62;&#x53C2;
 * &#x4F7F;&#x7528; returns &#x8868;&#x793A;&#x51FD;&#x6570;&#x8FD4;&#x56DE;&#x503C;
 * <span class="token keyword">@param</span> <span class="token class-name"><span class="token punctuation">{</span>&#x7C7B;&#x578B;<span class="token punctuation">}</span></span> <span class="token parameter">data</span>
 * <span class="token keyword">@param</span> <span class="token class-name"><span class="token punctuation">{</span>object<span class="token punctuation">}</span></span> <span class="token optional-parameter"><span class="token punctuation">[</span><span class="token parameter">options</span><span class="token punctuation">]</span></span> &#x53EF;&#x9009;&#x53C2;&#x6570;
 * <span class="token keyword">@param</span> <span class="token class-name"><span class="token punctuation">{</span>&#x7C7B;&#x578B;<span class="token punctuation">}</span></span> <span class="token parameter">options<span class="token punctuation">.</span>xxx</span>
 * <span class="token keyword">@param</span> <span class="token class-name"><span class="token punctuation">{</span>&#x7C7B;&#x578B;<span class="token punctuation">}</span></span> <span class="token optional-parameter"><span class="token punctuation">[</span><span class="token parameter">options<span class="token punctuation">.</span>yyy</span><span class="token punctuation">]</span></span> &#x53EF;&#x9009;&#x5C5E;&#x6027;
 * <span class="token keyword">@returns</span> <span class="token class-name"><span class="token punctuation">{</span>&#x7C7B;&#x578B;<span class="token punctuation">}</span></span>
 */</span>
<span class="token keyword module">export</span> <span class="token keyword">function</span> <span class="token function">myMethod</span><span class="token punctuation">(</span><span class="token parameter">data<span class="token punctuation">,</span> options</span><span class="token punctuation">)</span> <span class="token punctuation">{</span><span class="token punctuation">}</span>

<span class="token doc-comment comment">/**
 * &#x4F7F;&#x7528; type &#x8FDB;&#x884C;&#x7C7B;&#x578B;&#x65AD;&#x8A00;
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span><span class="token keyword">import</span><span class="token punctuation">(</span><span class="token string">&apos;vue-router&apos;</span><span class="token punctuation">)</span><span class="token punctuation">.</span>RouteConfig<span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">}</span></span>
 */</span>
<span class="token keyword">const</span> routes <span class="token operator">=</span> <span class="token punctuation">[</span><span class="token punctuation">]</span>

<span class="token doc-comment comment">/**
 * &#x4F7F;&#x7528; typedef &#x5B9A;&#x4E49;&#x7C7B;&#x578B;&#xFF0C;&#x65B9;&#x4FBF;&#x591A;&#x5904;&#x4F7F;&#x7528;&#xFF08;&#x547D;&#x540D;&#x65F6;&#x9700;&#x8981;&#x9996;&#x5B57;&#x6BCD;&#x5927;&#x5199;&#xFF09;
 * <span class="token keyword">@typedef</span> <span class="token class-name"><span class="token punctuation">{</span>routes<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">}</span></span> <span class="token class-name">RouteConfig</span>
 * <span class="token keyword">@param</span> <span class="token class-name"><span class="token punctuation">{</span><span class="token punctuation">(</span>meta<span class="token operator">:</span> object<span class="token punctuation">,</span> route<span class="token operator">:</span> RouteConfig<span class="token punctuation">)</span> <span class="token operator">=&gt;</span> boolean<span class="token punctuation">}</span></span> <span class="token parameter">filterCallback</span>
 * <span class="token keyword">@returns</span> <span class="token class-name"><span class="token punctuation">{</span>RouteConfig<span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">}</span></span>
 */</span>
<span class="token keyword module">export</span> <span class="token keyword">const</span> <span class="token function-variable function">filterMapRoutes</span> <span class="token operator">=</span> <span class="token keyword">function</span><span class="token punctuation">(</span><span class="token parameter">filterCallback</span><span class="token punctuation">)</span> <span class="token punctuation">{</span><span class="token punctuation">}</span>

<span class="token doc-comment comment">/**
 * &#x7C7B;&#x578B;&#x53C2;&#x8003;&#xFF1A;https://www.tslang.cn/docs/handbook/basic-types.html
 *
 * &#x57FA;&#x672C;
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span>boolean<span class="token punctuation">}</span></span>
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span>string<span class="token punctuation">}</span></span>
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span>number<span class="token punctuation">}</span></span>
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span><span class="token string">&apos;a&apos;</span> <span class="token operator">|</span> <span class="token string">&apos;b&apos;</span> <span class="token operator">|</span> <span class="token string">&apos;c&apos;</span><span class="token punctuation">}</span></span>
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span><span class="token number">1</span> <span class="token operator">|</span> <span class="token number">2</span> <span class="token operator">|</span> <span class="token number">3</span><span class="token punctuation">}</span></span>
 *
 * &#x6570;&#x7EC4;
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span>Array<span class="token punctuation">}</span></span>
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span>string<span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">}</span></span>
 *
 * &#x51FD;&#x6570;
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span>Function<span class="token punctuation">}</span></span>
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span><span class="token punctuation">(</span>data<span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token keyword">void</span><span class="token punctuation">}</span></span>
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span><span class="token punctuation">(</span>data<span class="token operator">:</span> Array<span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token keyword">void</span> <span class="token operator">|</span> boolean<span class="token punctuation">}</span></span>
 *
 * &#x5BF9;&#x8C61;
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span>object<span class="token punctuation">}</span></span>
 *
 * &#x8054;&#x5408;
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span>number <span class="token operator">|</span> string<span class="token punctuation">}</span></span>
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span>boolean <span class="token operator">|</span> <span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> boolean<span class="token punctuation">)</span><span class="token punctuation">}</span></span>
 *
 * &#x5BFC;&#x5165; ts &#x7C7B;&#x578B;
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span><span class="token keyword">import</span><span class="token punctuation">(</span><span class="token string">&apos;xxx&apos;</span><span class="token punctuation">)</span><span class="token punctuation">.</span>Yyy<span class="token punctuation">}</span></span>
 *
 * &#x4ECE;&#x73B0;&#x6709;&#x7684; js &#x53D8;&#x91CF;&#x6216; ts &#x7C7B;&#x578B;&#x8FDB;&#x884C;&#x63A8;&#x5BFC;
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span>Parameters<span class="token punctuation">&lt;</span>fn<span class="token punctuation">&gt;</span><span class="token punctuation">}</span></span> &#x53D6;&#x51FD;&#x6570;&#x5F62;&#x53C2;&#x7684;&#x7C7B;&#x578B;
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span>Parameters<span class="token punctuation">&lt;</span>fn<span class="token punctuation">&gt;</span><span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">}</span></span> &#x53D6;&#x51FD;&#x6570;&#x7B2C;&#x4E00;&#x4E2A;&#x5F62;&#x53C2;&#x7684;&#x7C7B;&#x578B;
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span>ReturnType<span class="token punctuation">&lt;</span>fn<span class="token punctuation">&gt;</span><span class="token punctuation">}</span></span> &#x53D6;&#x51FD;&#x6570;&#x8FD4;&#x56DE;&#x503C;&#x7684;&#x7C7B;&#x578B;
 * <span class="token keyword">@type</span> <span class="token class-name"><span class="token punctuation">{</span>obj<span class="token punctuation">[</span><span class="token string">&apos;xxx&apos;</span><span class="token punctuation">]</span><span class="token punctuation">}</span></span> &#x53D6;&#x6307;&#x5B9A;&#x5C5E;&#x6027;&#x503C;&#x7684;&#x7C7B;&#x578B;&#xFF08;&#x4E0D;&#x80FD;&#x4F7F;&#x7528;&#x70B9;&#x8BED;&#x6CD5;&#xFF09;
 * ...
 */</span>
</pre></li>
<li>
<p><a target="_blank" href="https://jsdoc.app/howto-es2015-classes.html">ES 2015 Classes</a></p>
</li>
<li>
<p>&#x5F85;&#x5B8C;&#x6210;&#x6216;&#x5F85;&#x4F18;&#x5316;&#x7684;&#x5730;&#x65B9;</p>
<pre data-role="codeBlock" data-info="js" class="language-javascript"><span class="token comment">/* TODO: &#x8BF4;&#x660E; */</span>
</pre></li>
</ul>
</li>
<li>
<p>css &#x6CE8;&#x91CA;</p>
<ul>
<li>
<p>&#x5168;&#x5C40;&#x6837;&#x5F0F;&#x9700;&#x8981;&#x5199;&#x6CE8;&#x91CA;</p>
<pre data-role="codeBlock" data-info="less" class="language-less"><span class="token comment">/* &#x8BF4;&#x660E; */</span>
<span class="token selector">.g-class1</span> <span class="token punctuation">{</span>
<span class="token punctuation">}</span>

<span class="token comment">/* &#x8BF4;&#x660E; */</span>
<span class="token selector">.g-class2</span> <span class="token punctuation">{</span>
<span class="token punctuation">}</span>
</pre></li>
</ul>
</li>
<li>
<p>vue template &#x6CE8;&#x91CA;</p>
<ul>
<li>
<p>&#x9002;&#x5F53;&#x4F7F;&#x7528;&#x6CE8;&#x91CA;&#x4E0E;&#x7A7A;&#x884C;</p>
<pre data-role="codeBlock" data-info="html" class="language-html"><span class="token comment">&lt;!-- &#x8BF4;&#x660E; --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span><span class="token punctuation">&gt;</span></span>block1<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>

<span class="token comment">&lt;!-- &#x8BF4;&#x660E; --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span><span class="token punctuation">&gt;</span></span>block2<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
</pre></li>
</ul>
</li>
</ul>
<hr>
<h2 class="mume-header" id="%E5%B7%A5%E7%A8%8B%E7%9B%AE%E5%BD%95%E7%BB%93%E6%9E%84">&#x5DE5;&#x7A0B;&#x76EE;&#x5F55;&#x7ED3;&#x6784;</h2>

<pre data-role="codeBlock" data-info class="language-"><code>|-- .env.development ------------ dev &#x73AF;&#x5883;&#x53D8;&#x91CF;
|-- .env.development.local ------ dev &#x672C;&#x5730;&#x73AF;&#x5883;&#x53D8;&#x91CF; (&#x88AB; git &#x5FFD;&#x7565;&#xFF0C;&#x9700;&#x624B;&#x52A8;&#x65B0;&#x5EFA;&#xFF0C;&#x7528;&#x6765;&#x91CD;&#x5199;&#x90E8;&#x5206;&#x73AF;&#x5883;&#x53D8;&#x91CF;)
|-- .env.production-stage ------- stage &#x73AF;&#x5883;&#x53D8;&#x91CF;
|-- .env.production ------------- prod &#x73AF;&#x5883;&#x53D8;&#x91CF;
|-- .env.test
|-- .vscode --------------------- &#x7EDF;&#x4E00; VSCode &#x914D;&#x7F6E;
|-- static-server.js ------------ &#x9759;&#x6001;&#x8D44;&#x6E90;&#x670D;&#x52A1; (node &#x8FD0;&#x884C;)&#xFF0C;&#x901A;&#x5E38;&#x7528;&#x4E8E;&#x9884;&#x89C8;/&#x68C0;&#x67E5;&#x6253;&#x5305;&#x7ED3;&#x679C;&#xFF0C;&#x6216;&#x8005;&#x4E34;&#x65F6;&#x7ED9;&#x5176;&#x4ED6;&#x4EBA;&#x5458;&#x542F;&#x7528;&#x524D;&#x7AEF;&#x670D;&#x52A1;
|-- docs ------------------------ &#x5F00;&#x53D1;&#x6587;&#x6863;
|   |-- README.html ------------- &#x7531; ../README.md &#x624B;&#x52A8;&#x751F;&#x6210; (&#x4F7F;&#x7528; VSCode &#x63D2;&#x4EF6; Markdown Preview Enhanced)
|   |-- xxx.md
|   |-- xxx.html
|-- public
|   |-- favicon.ico
|   |-- index.html
|   |-- libs -------------------- &#x4E0D;&#x652F;&#x6301;&#x6A21;&#x5757;&#x5316;&#x52A0;&#x8F7D;&#x7684;&#x7B2C;&#x4E09;&#x65B9; ES5 &#x7C7B;&#x5E93;/&#x6A21;&#x5757; (&#x53EA;&#x80FD;&#x901A;&#x8FC7;&#x5168;&#x5C40;&#x53D8;&#x91CF;&#x5F15;&#x7528;)
|-- src
    |-- main.js
    |-- App.vue
    |-- libs -------------------- &#x652F;&#x6301;&#x6A21;&#x5757;&#x5316;&#x52A0;&#x8F7D;&#x4F46;&#x662F;&#x65E0;&#x6CD5;&#x901A;&#x8FC7; npm &#x5B89;&#x88C5;&#x7684;&#x7B2C;&#x4E09;&#x65B9; ES5 &#x7C7B;&#x5E93;/&#x6A21;&#x5757;
    |-- assets
    |-- styles
    |   |-- global.less
    |   |-- reset.less
    |   |-- vars.less ----------- less &#x5168;&#x5C40;&#x53D8;&#x91CF;/&#x51FD;&#x6570; (webpack &#x81EA;&#x52A8;&#x6CE8;&#x5165;)
    |   |-- xxx.less
    |-- scripts
    |   |-- utils --------------- &#x901A;&#x7528;&#x65B9;&#x6CD5;
    |   |-- constants ----------- &#x5E38;&#x91CF; (&#x591A;&#x4F7F;&#x7528; Object.freeze)
    |   |-- eventBus ------------ &#x4E8B;&#x4EF6;&#x603B;&#x7EBF;
    |   |-- xxx.js
    |   |-- http ---------------- axios &#x5B9E;&#x4F8B;
    |       |-- index.js
    |       |-- http.js
    |       |-- createAxios.js
    |       |-- xxx.js
    |-- injects ----------------- vue &#x5168;&#x5C40;&#x6CE8;&#x518C; (&#x614E;&#x7528;)
    |   |-- index.js
    |   |-- $xxx.js
    |   |-- v-xxx.js
    |   |-- mixin-xxx.js
    |   |-- xxx.js
    |-- element-ui
    |   |-- index.js
    |   |-- rewrite ------------- &#x4E3B;&#x9898;&#x6837;&#x5F0F;&#x590D;&#x5199;
    |       |-- index.less
    |       |-- xxx.less
    |-- vant
    |   |-- index.js
    |   |-- vars.less ----------- &#x5185;&#x7F6E;&#x53D8;&#x91CF;&#x590D;&#x5199;
    |   |-- rewrite ------------- &#x4E3B;&#x9898;&#x6837;&#x5F0F;&#x590D;&#x5199;
    |       |-- index.less
    |       |-- xxx.less
    |-- router
    |   |-- index.js
    |   |-- routes.js
    |   |-- registerInterceptor.js
    |-- store
    |   |-- index.js
    |   |-- root.js
    |   |-- xxx.js
    |-- api
    |   |-- xxx.js
    |   |-- mock ---------------- &#x6A21;&#x62DF;&#x6570;&#x636E;
    |       |-- index.js
    |       |-- createMock.js
    |       |-- xxx.js
    |-- components
    |   |-- TheXxx.vue ---------- &#x5355;&#x4F8B;&#x7EC4;&#x4EF6;
    |   |-- ExXxx.vue ----------- &#x6269;&#x5C55;/&#x5305;&#x88C5;&#x7B2C;&#x4E09;&#x65B9;&#x5F00;&#x6E90;&#x7EC4;&#x4EF6;&#x6216;&#x5185;&#x90E8;&#x516C;&#x5171;&#x5E93;&#x7EC4;&#x4EF6;
    |   |-- XxxXxx.vue
    |   |-- ComponentExamples --- &#x975E;&#x5355;&#x4F8B;&#x516C;&#x5171;&#x7EC4;&#x4EF6;&#x9700;&#x8981;&#x5728;&#x8FD9;&#x91CC;&#x5199;&#x793A;&#x4F8B;
    |   |   |-- index.vue
    |   |   |-- XxxXxx.vue
    |   |-- SvgIcon ------------- svg-sprite &#x56FE;&#x6807;&#x7EC4;&#x4EF6;
    |   |   |-- index.vue
    |   |   |-- icons
    |   |-- directives ---------- &#x53EF;&#x590D;&#x7528;&#x7684;&#x81EA;&#x5B9A;&#x4E49;&#x6307;&#x4EE4;&#xFF08;&#x5C40;&#x90E8;&#x6CE8;&#x518C;&#xFF09;
    |   |   |-- xxx.js
    |   |-- mixins -------------- &#x53EF;&#x590D;&#x7528;&#x7684;&#x6DF7;&#x5165;&#xFF08;&#x5C40;&#x90E8;&#x6CE8;&#x518C;&#xFF09;
    |       |-- xxx.js
    |-- views
        |-- Xxx.vue
        |-- Xxx ----------------- &#x9664;&#x4E86; api &#x548C; vuex&#xFF0C;&#x5176;&#x5B83;&#x7684;&#x4E13;&#x5C5E;&#x6A21;&#x5757;&#x8981;&#x5185;&#x805A;&#x5728;&#x540C;&#x4E00;&#x76EE;&#x5F55;&#x4E0B;
            |-- index.vue
            |-- Xxx.vue --------- &#x76F8;&#x5173;&#x9875;&#x9762;/&#x5B50;&#x9875;&#x9762;/&#x5B50;&#x8DEF;&#x7531;
            |-- xxx.js
            |-- xxx.module.less
            |-- components ------ &#x5B58;&#x653E;&#x79C1;&#x6709;&#x7EC4;&#x4EF6;
</code></pre><hr>
<h2 class="mume-header" id="%E5%89%8D%E7%AB%AF%E9%83%A8%E7%BD%B2">&#x524D;&#x7AEF;&#x90E8;&#x7F72;</h2>

<ul>
<li>
<p>&#x8DE8;&#x57DF;&#x5904;&#x7406;</p>
<ul>
<li>&#x4F7F;&#x7528;&#x4EE3;&#x7406;&#x6216; <a target="_blank" href="https://www.baidu.com/s?wd=cors&#x8DE8;&#x57DF;">CORS</a></li>
</ul>
</li>
<li>
<p>history &#x6A21;&#x5F0F;<a target="_blank" href="https://router.vuejs.org/zh/guide/essentials/history-mode.html">&#x8DEF;&#x7531;&#x5904;&#x7406;</a></p>
<ul>
<li>&#x5982;&#x679C; url &#x5339;&#x914D;&#x4E0D;&#x5230;&#x9759;&#x6001;&#x8D44;&#x6E90;&#xFF0C;&#x5219;&#x8FD4;&#x56DE; /index.html &#x9875;&#x9762;</li>
</ul>
</li>
<li>
<p>&#x5BA2;&#x6237;&#x7AEF;&#x7F13;&#x5B58;&#x5904;&#x7406; (&#x914D;&#x7F6E;&#x54CD;&#x5E94;&#x5934;)</p>
<ul>
<li>
<p>&#x9759;&#x6001;&#x8D44;&#x6E90;</p>
<ul>
<li>
<p>&#x4E0D;&#x7F13;&#x5B58; <code>/index.html</code></p>
<pre data-role="codeBlock" data-info class="language-"><code>Cache-Control: &apos;no-store&apos;
</code></pre></li>
<li>
<p>&#x5F3A;&#x7F13;&#x5B58; <code>/static-hash/**/*</code></p>
<pre data-role="codeBlock" data-info class="language-"><code>Cache-Control: &apos;public,max-age=31536000&apos;
</code></pre></li>
<li>
<p><a target="_blank" href="https://www.baidu.com/s?wd=http&#x534F;&#x5546;&#x7F13;&#x5B58;">&#x534F;&#x5546;&#x7F13;&#x5B58;</a> (&#x9ED8;&#x8BA4;)</p>
<pre data-role="codeBlock" data-info class="language-"><code>Cache-Control: &apos;no-cache&apos; // &#x8FD9;&#x4E2A;&#x4E00;&#x5B9A;&#x8981;&#x52A0;&#x4E0A;&#xFF0C;&#x5426;&#x5219;&#x90E8;&#x5206;&#x6D4F;&#x89C8;&#x5668;&#x5237;&#x65B0;&#x9875;&#x9762;&#x65F6;&#x4E0D;&#x4F1A;&#x8D70;&#x534F;&#x5546;&#x7F13;&#x5B58;
Etag: &apos;xxx&apos; // &#x6216;&#x8005;&#x4F7F;&#x7528; Last-Modified&#xFF0C;&#x6216;&#x8005;&#x540C;&#x65F6;&#x4F7F;&#x7528;
</code></pre></li>
</ul>
</li>
<li>
<p>XHR (&#x89E3;&#x51B3; IE &#x7F13;&#x5B58;&#x95EE;&#x9898;)</p>
<pre data-role="codeBlock" data-info class="language-"><code>Cache-Control: &apos;no-cache&apos;
</code></pre></li>
</ul>
</li>
<li>
<p>gzip &#x538B;&#x7F29;</p>
<ul>
<li>
<p>&#x9759;&#x6001;&#x8D44;&#x6E90;&#xFF1A;&#x542F;&#x7528; gzip &#x538B;&#x7F29; (&#x9664;&#x4E86;&#x50CF;&#x7D20;&#x578B;&#x56FE;&#x7247;)</p>
</li>
<li>
<p>XHR&#xFF1A;&#x53D1;&#x7ED9;&#x5BA2;&#x6237;&#x7AEF;&#x7684;&#x54CD;&#x5E94;&#x6570;&#x636E;&#x8D85;&#x8FC7;&#x6307;&#x5B9A;&#x9600;&#x503C;&#x65F6;&#x5E94;&#x542F;&#x7528; gzip &#x538B;&#x7F29;</p>
</li>
</ul>
</li>
</ul>
<hr>
<h2 class="mume-header" id="%E7%BB%99-ui-%E7%9A%84%E5%BB%BA%E8%AE%AE">&#x7ED9; UI &#x7684;&#x5EFA;&#x8BAE;</h2>

<ul>
<li>&#x5BF9;&#x4E8E;&#x4E2D;&#x540E;&#x53F0;&#x9879;&#x76EE;&#xFF0C;&#x5728;&#x753B; UI &#x754C;&#x9762;&#x65F6;&#xFF0C;&#x5EFA;&#x8BAE;&#x53C2;&#x8003;&#x524D;&#x7AEF;&#x5DF2;&#x9009;&#x578B;&#x7684;&#x5F00;&#x6E90;&#x7EC4;&#x4EF6;&#x5E93;&#xFF0C;&#x5E76;&#x63A8;&#x8350;&#x4F7F;&#x7528;&#x5F00;&#x6E90;&#x7EC4;&#x4EF6;&#x5E93;&#x63D0;&#x4F9B;&#x7684;&#x5236;&#x56FE;&#x5143;&#x4EF6;/&#x6A21;&#x677F;&#xFF0C;&#x5982;&#xFF1A;<a target="_blank" href="http://element-cn.eleme.io/#/zh-CN/resource">element-ui</a></li>
<li>&#x5BF9;&#x4E8E; H5 &#x9879;&#x76EE;&#xFF0C;&#x5982;&#xFF1A;<a target="_blank" href="https://youzan.github.io/vant/#/zh-CN/design">vant</a></li>
</ul>
<h1 class="mume-header" id="%E5%A1%AB%E5%9D%91-q-%E7%BE%A4901842001">&#x586B;&#x5751; Q &#x7FA4;&#xFF1A;901842001</h1>

<ul>
<li><a target="_blank" href="https://jq.qq.com/?_wv=1027&amp;k=5soIl0R" title="901842001">&#x70B9;&#x51FB;&#x94FE;&#x63A5;&#x52A0;&#x5165;&#x7FA4;&#x804A;</a></li>
</ul>

      </div>
      
      
    
    
    
    
    
    
    
    
  
    </body></html>