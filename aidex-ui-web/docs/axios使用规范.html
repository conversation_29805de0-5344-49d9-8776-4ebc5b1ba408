<!DOCTYPE html><html><head>
      <title>axios&#x4F7F;&#x7528;&#x89C4;&#x8303;</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.13.2/dist/katex.min.css">
      
      
      
      
      
      
      
      
      
      <style>
      /**
 * prism.js Github theme based on GitHub's theme.
 * <AUTHOR>
 */
code[class*="language-"],
pre[class*="language-"] {
  color: #333;
  background: none;
  font-family: Consolas, "Liberation Mono", Menlo, Courier, monospace;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.4;

  -moz-tab-size: 8;
  -o-tab-size: 8;
  tab-size: 8;

  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}

/* Code blocks */
pre[class*="language-"] {
  padding: .8em;
  overflow: auto;
  /* border: 1px solid #ddd; */
  border-radius: 3px;
  /* background: #fff; */
  background: #f5f5f5;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
  padding: .1em;
  border-radius: .3em;
  white-space: normal;
  background: #f5f5f5;
}

.token.comment,
.token.blockquote {
  color: #969896;
}

.token.cdata {
  color: #183691;
}

.token.doctype,
.token.punctuation,
.token.variable,
.token.macro.property {
  color: #333;
}

.token.operator,
.token.important,
.token.keyword,
.token.rule,
.token.builtin {
  color: #a71d5d;
}

.token.string,
.token.url,
.token.regex,
.token.attr-value {
  color: #183691;
}

.token.property,
.token.number,
.token.boolean,
.token.entity,
.token.atrule,
.token.constant,
.token.symbol,
.token.command,
.token.code {
  color: #0086b3;
}

.token.tag,
.token.selector,
.token.prolog {
  color: #63a35c;
}

.token.function,
.token.namespace,
.token.pseudo-element,
.token.class,
.token.class-name,
.token.pseudo-class,
.token.id,
.token.url-reference .token.variable,
.token.attr-name {
  color: #795da3;
}

.token.entity {
  cursor: help;
}

.token.title,
.token.title .token.punctuation {
  font-weight: bold;
  color: #1d3e81;
}

.token.list {
  color: #ed6a43;
}

.token.inserted {
  background-color: #eaffea;
  color: #55a532;
}

.token.deleted {
  background-color: #ffecec;
  color: #bd2c00;
}

.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}


/* JSON */
.language-json .token.property {
  color: #183691;
}

.language-markup .token.tag .token.punctuation {
  color: #333;
}

/* CSS */
code.language-css,
.language-css .token.function {
  color: #0086b3;
}

/* YAML */
.language-yaml .token.atrule {
  color: #63a35c;
}

code.language-yaml {
  color: #183691;
}

/* Ruby */
.language-ruby .token.function {
  color: #333;
}

/* Markdown */
.language-markdown .token.url {
  color: #795da3;
}

/* Makefile */
.language-makefile .token.symbol {
  color: #795da3;
}

.language-makefile .token.variable {
  color: #183691;
}

.language-makefile .token.builtin {
  color: #0086b3;
}

/* Bash */
.language-bash .token.keyword {
  color: #0086b3;
}

/* highlight */
pre[data-line] {
  position: relative;
  padding: 1em 0 1em 3em;
}
pre[data-line] .line-highlight-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  background-color: transparent;
  display: block;
  width: 100%;
}

pre[data-line] .line-highlight {
  position: absolute;
  left: 0;
  right: 0;
  padding: inherit 0;
  margin-top: 1em;
  background: hsla(24, 20%, 50%,.08);
  background: linear-gradient(to right, hsla(24, 20%, 50%,.1) 70%, hsla(24, 20%, 50%,0));
  pointer-events: none;
  line-height: inherit;
  white-space: pre;
}

pre[data-line] .line-highlight:before, 
pre[data-line] .line-highlight[data-end]:after {
  content: attr(data-start);
  position: absolute;
  top: .4em;
  left: .6em;
  min-width: 1em;
  padding: 0 .5em;
  background-color: hsla(24, 20%, 50%,.4);
  color: hsl(24, 20%, 95%);
  font: bold 65%/1.5 sans-serif;
  text-align: center;
  vertical-align: .3em;
  border-radius: 999px;
  text-shadow: none;
  box-shadow: 0 1px white;
}

pre[data-line] .line-highlight[data-end]:after {
  content: attr(data-end);
  top: auto;
  bottom: .4em;
}html body{font-family:"Helvetica Neue",Helvetica,"Segoe UI",Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ul,html body>ol{margin-bottom:16px}html body ul,html body ol{padding-left:2em}html body ul.no-list,html body ol.no-list{padding:0;list-style-type:none}html body ul ul,html body ul ol,html body ol ol,html body ol ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:bold;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:bold}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em !important;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::before,html body code::after{letter-spacing:-0.2em;content:"\00a0"}html body pre>code{padding:0;margin:0;font-size:.85em !important;word-break:normal;white-space:pre;background:transparent;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;font-size:.85em !important;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:before,html body pre tt:before,html body pre code:after,html body pre tt:after{content:normal}html body p,html body blockquote,html body ul,html body ol,html body dl,html body pre{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body pre,html body code{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview .pagebreak,.markdown-preview .newpage{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center !important}.markdown-preview:not([for="preview"]) .code-chunk .btn-group{display:none}.markdown-preview:not([for="preview"]) .code-chunk .status{display:none}.markdown-preview:not([for="preview"]) .code-chunk .output-div{margin-bottom:16px}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,0.66);border:4px solid rgba(150,150,150,0.66);background-clip:content-box}html body[for="html-export"]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for="html-export"]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0}@media screen and (min-width:914px){html body[for="html-export"]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for="html-export"]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for="html-export"]:not([data-presentation-mode]) .markdown-preview{font-size:14px !important;padding:1em}}@media print{html body[for="html-export"]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for="html-export"]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,0.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,0.66);border:4px solid rgba(150,150,150,0.66);background-clip:content-box}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc ul{padding:0 1.6em;margin-top:.8em}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc li{margin-bottom:.8em}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc ul{list-style-type:none}html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% -  300px);padding:2em calc(50% - 457px -  150px);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for="html-export"]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for="html-export"]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for="html-export"]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
    </head>
    <body for="html-export">
      <div class="mume markdown-preview  ">
      <p>axios &#x4E2D;&#x6587;&#x6587;&#x6863;&#xFF1A;<a href="http://www.axios-js.com/zh-cn/docs/" target="_blank">http://www.axios-js.com/zh-cn/docs/</a></p>
<h3 class="mume-header" id="%E8%AF%B7%E6%B1%82%E5%85%A5%E5%8F%82%E7%94%A8%E6%B3%95">&#x8BF7;&#x6C42;&#x5165;&#x53C2;&#x7528;&#x6CD5;</h3>

<h4 class="mume-header" id="query-string-parameters">Query String Parameters</h4>

<pre data-role="codeBlock" data-info="ts" class="language-ts"><span class="token keyword">import</span> http <span class="token keyword">from</span> <span class="token string">&apos;@/scripts/http&apos;</span>
<span class="token keyword">import</span> <span class="token punctuation">{</span> qsStringify <span class="token punctuation">}</span> <span class="token keyword">from</span> <span class="token string">&apos;@/scripts/utils&apos;</span>

<span class="token keyword">const</span> params <span class="token operator">=</span> <span class="token punctuation">{</span>
  pageNum<span class="token operator">:</span> <span class="token number">1</span><span class="token punctuation">,</span>
  pageSize<span class="token operator">:</span> <span class="token number">10</span><span class="token punctuation">,</span>
  status<span class="token operator">:</span> <span class="token punctuation">[</span><span class="token number">1</span><span class="token punctuation">,</span> <span class="token number">2</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
  <span class="token comment">// ...</span>
<span class="token punctuation">}</span>

<span class="token doc-comment comment">/**
 * Parameters&lt;qsStringify&gt;[1][&apos;arrayFormat&apos;] &#x5BF9;&#x5E94;&#x7684;&#x8F93;&#x51FA;
 * comma ----------&gt; pageNum=1&amp;pageSize=10&amp;status=1,2
 * repeat&#xFF08;&#x9ED8;&#x8BA4;&#xFF09;----&gt; pageNum=1&amp;pageSize=10&amp;status=1&amp;status=2
 * brackets --------&gt; pageNum=1&amp;pageSize=10&amp;status[]=1&amp;status[]=2
 * indices ---------&gt; pageNum=1&amp;pageSize=10&amp;status[0]=1&amp;status[1]=2
 */</span>
<span class="token keyword">const</span> <span class="token function-variable function">paramsSerializer</span> <span class="token operator">=</span> params <span class="token operator">=&gt;</span> <span class="token function">qsStringify</span><span class="token punctuation">(</span>params<span class="token punctuation">,</span> <span class="token punctuation">{</span> arrayFormat<span class="token operator">:</span> <span class="token string">&apos;comma&apos;</span> <span class="token punctuation">}</span><span class="token punctuation">)</span>

<span class="token keyword">const</span> config <span class="token operator">=</span> <span class="token punctuation">{</span>
  params<span class="token punctuation">,</span>
  paramsSerializer<span class="token punctuation">,</span> <span class="token comment">// &#x5728;&#x62E6;&#x622A;&#x5668;&#x4E2D;&#x5DF2;&#x914D;&#x7F6E;&#x8BE5;&#x9ED8;&#x8BA4;&#x503C;&#xFF0C;&#x5728;&#x4E1A;&#x52A1;&#x4E2D;&#x901A;&#x5E38;&#x4E0D;&#x9700;&#x8981;&#x91CD;&#x5199;</span>
<span class="token punctuation">}</span>

http<span class="token punctuation">.</span><span class="token keyword">get</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> config<span class="token punctuation">)</span>
http<span class="token punctuation">.</span><span class="token keyword">delete</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> config<span class="token punctuation">)</span>
http<span class="token punctuation">.</span><span class="token function">post</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> <span class="token keyword">null</span><span class="token punctuation">,</span> config<span class="token punctuation">)</span>
http<span class="token punctuation">.</span><span class="token function">put</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> <span class="token keyword">null</span><span class="token punctuation">,</span> config<span class="token punctuation">)</span>
http<span class="token punctuation">.</span><span class="token function">patch</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> <span class="token keyword">null</span><span class="token punctuation">,</span> config<span class="token punctuation">)</span>
</pre><h4 class="mume-header" id="request-payloadapplicationjson">Request Payload&#xFF1A;application/json</h4>

<pre data-role="codeBlock" data-info="ts" class="language-ts"><span class="token keyword">import</span> http <span class="token keyword">from</span> <span class="token string">&apos;@/scripts/http&apos;</span>

<span class="token keyword">const</span> data<span class="token operator">:</span> object <span class="token comment">/* json &#x5BF9;&#x8C61; */</span> <span class="token operator">=</span> <span class="token punctuation">{</span>
  <span class="token comment">//...</span>
<span class="token punctuation">}</span>
<span class="token keyword">const</span> config <span class="token operator">=</span> <span class="token punctuation">{</span> data <span class="token punctuation">}</span>

http<span class="token punctuation">.</span><span class="token keyword">delete</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> config<span class="token punctuation">)</span>
http<span class="token punctuation">.</span><span class="token function">post</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> data<span class="token punctuation">)</span>
http<span class="token punctuation">.</span><span class="token function">put</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> data<span class="token punctuation">)</span>
http<span class="token punctuation">.</span><span class="token function">patch</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> data<span class="token punctuation">)</span>
</pre><h4 class="mume-header" id="form-dataapplicationx-www-form-urlencoded">Form Data&#xFF1A;application/x-www-form-urlencoded</h4>

<pre data-role="codeBlock" data-info="ts" class="language-ts"><span class="token keyword">import</span> http <span class="token keyword">from</span> <span class="token string">&apos;@/scripts/http&apos;</span>
<span class="token keyword">import</span> <span class="token punctuation">{</span> qsStringify <span class="token punctuation">}</span> <span class="token keyword">from</span> <span class="token string">&apos;@/scripts/utils&apos;</span>

<span class="token keyword">const</span> data<span class="token operator">:</span> <span class="token builtin">string</span> <span class="token operator">=</span> <span class="token function">qsStringify</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token comment">// ...</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
<span class="token keyword">const</span> config <span class="token operator">=</span> <span class="token punctuation">{</span> data <span class="token punctuation">}</span>

http<span class="token punctuation">.</span><span class="token keyword">delete</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> config<span class="token punctuation">)</span>
http<span class="token punctuation">.</span><span class="token function">post</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> data<span class="token punctuation">)</span>
http<span class="token punctuation">.</span><span class="token function">put</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> data<span class="token punctuation">)</span>
http<span class="token punctuation">.</span><span class="token function">patch</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> data<span class="token punctuation">)</span>
</pre><h4 class="mume-header" id="form-datamultipartform-data">Form Data&#xFF1A;multipart/form-data</h4>

<pre data-role="codeBlock" data-info="ts" class="language-ts"><span class="token keyword">import</span> http <span class="token keyword">from</span> <span class="token string">&apos;@/scripts/http&apos;</span>
<span class="token keyword">import</span> <span class="token punctuation">{</span> toFormData <span class="token punctuation">}</span> <span class="token keyword">from</span> <span class="token string">&apos;@/scripts/utils&apos;</span>

<span class="token keyword">const</span> data<span class="token operator">:</span> FormData <span class="token operator">=</span> <span class="token function">toFormData</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token comment">// ...</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
<span class="token keyword">const</span> config <span class="token operator">=</span> <span class="token punctuation">{</span> data <span class="token punctuation">}</span>

http<span class="token punctuation">.</span><span class="token keyword">delete</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> config<span class="token punctuation">)</span>
http<span class="token punctuation">.</span><span class="token function">post</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> data<span class="token punctuation">)</span>
http<span class="token punctuation">.</span><span class="token function">put</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> data<span class="token punctuation">)</span>
http<span class="token punctuation">.</span><span class="token function">patch</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> data<span class="token punctuation">)</span>
</pre><h4 class="mume-header" id="request-headers">Request Headers</h4>

<pre data-role="codeBlock" data-info="ts" class="language-ts"><span class="token comment">// &#x901A;&#x5E38;&#x5728;&#x8BF7;&#x6C42;&#x62E6;&#x622A;&#x5668;&#x4E2D;&#x6DFB;&#x52A0;&#xFF08;&#x8BB0;&#x5F97;&#x8FDB;&#x884C;&#x76F8;&#x5173;&#x7F16;&#x7801;&#xFF09;</span>
config<span class="token punctuation">.</span>headers<span class="token punctuation">.</span>token <span class="token operator">=</span> localStorage<span class="token punctuation">.</span>token
config<span class="token punctuation">.</span>headers<span class="token punctuation">.</span>curUrl <span class="token operator">=</span> <span class="token function">encodeURI</span><span class="token punctuation">(</span>location<span class="token punctuation">.</span>href<span class="token punctuation">)</span>
</pre><hr>
<h3 class="mume-header" id="%E5%93%8D%E5%BA%94%E8%A1%8C%E4%B8%BA%E5%A4%84%E7%90%86">&#x54CD;&#x5E94;&#x884C;&#x4E3A;&#x5904;&#x7406;</h3>

<h4 class="mume-header" id="%E5%9C%A8%E6%8B%A6%E6%88%AA%E5%99%A8%E6%88%96%E7%9B%B8%E5%85%B3%E9%92%A9%E5%AD%90%E4%B8%AD">&#x5728;&#x62E6;&#x622A;&#x5668;&#x6216;&#x76F8;&#x5173;&#x94A9;&#x5B50;&#x4E2D;</h4>

<p>&#x5728;&#x62E6;&#x622A;&#x5668;&#x6216;&#x76F8;&#x5173;&#x94A9;&#x5B50;&#x4E2D;<code>&#x505A;&#x597D;&#x6570;&#x636E;&#x53CA;&#x72B6;&#x6001;&#x7684;&#x4F20;&#x9012;&#x3001;&#x5F02;&#x5E38;&#x5904;&#x7406;&#x7B49;</code>&#xFF0C;&#x5728;&#x4E1A;&#x52A1;&#x4E2D;&#x4E0D;&#x9700;&#x8981;&#x6709;&#x591A;&#x4F59;&#x7684;&#x5224;&#x65AD;&#x6216;&#x884C;&#x4E3A;&#xFF0C;&#x8BA9;&#x4E1A;&#x52A1;&#x66F4;&#x4E13;&#x6CE8;</p>
<ul>
<li>&#x5728;&#x4E1A;&#x52A1;&#x4E2D;&#xFF0C;then &#x4E0D;&#x9700;&#x8981;&#x8FDB;&#x884C; <code>res.data.code == &apos;xxx&apos;</code> &#x7B49;&#x591A;&#x4F59;&#x7684;&#x64CD;&#x4F5C;&#xFF08;&#x8BA9;&#x62E6;&#x622A;&#x5668;&#x5168;&#x5C40;&#x5904;&#x7406;&#xFF09;</li>
<li>&#x5728;&#x4E1A;&#x52A1;&#x4E2D;&#xFF0C;catch &#x4E0D;&#x9700;&#x8981;&#x5904;&#x7406;&#x5F39;&#x51FA;&#x6D88;&#x606F;&#x5C42;&#xFF08;&#x8BA9;&#x62E6;&#x622A;&#x5668;&#x5168;&#x5C40;&#x5904;&#x7406;&#xFF09;</li>
<li>&#x5728;&#x4E1A;&#x52A1;&#x4E2D;&#xFF0C;&#x8BF7;&#x6C42;&#x8FC7;&#x7A0B;&#x4E2D;&#x4E0D;&#x9700;&#x8981;&#x5904;&#x7406;&#x5168;&#x5C40; loading&#xFF08;&#x8BA9;&#x76F8;&#x5173;&#x94A9;&#x5B50;&#x5168;&#x5C40;&#x5904;&#x7406;&#xFF09;</li>
<li>...</li>
</ul>
<h4 class="mume-header" id="%E5%9C%A8%E8%AF%B7%E6%B1%82%E6%96%B9%E6%B3%95%E4%B8%AD">&#x5728;&#x8BF7;&#x6C42;&#x65B9;&#x6CD5;&#x4E2D;</h4>

<pre data-role="codeBlock" data-info="js" class="language-javascript"><span class="token keyword module">import</span> <span class="token imports">http</span> <span class="token keyword module">from</span> <span class="token string">&apos;@/scripts/http&apos;</span>

<span class="token keyword module">export</span> <span class="token keyword">const</span> <span class="token function-variable function">getXxx</span> <span class="token operator">=</span> <span class="token keyword">function</span><span class="token punctuation">(</span><span class="token parameter">params<span class="token punctuation">,</span> opts</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  params <span class="token operator">=</span> <span class="token punctuation">{</span> <span class="token spread operator">...</span>params <span class="token punctuation">}</span>
  opts <span class="token operator">=</span> <span class="token punctuation">{</span> <span class="token spread operator">...</span>opts <span class="token punctuation">}</span>
  <span class="token keyword control-flow">return</span> http<span class="token punctuation">.</span><span class="token method function property-access">get</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> <span class="token punctuation">{</span>
    params<span class="token punctuation">,</span>

    exNoErrorMassage<span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span> <span class="token comment">// &#x54CD;&#x5E94;&#x5F02;&#x5E38;&#x65F6;&#x4E0D;&#x8981;&#x5F39;&#x51FA;&#x6D88;&#x606F;&#x5C42;</span>
    exShowLoading<span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span> <span class="token comment">// &#x8BF7;&#x6C42;&#x8FC7;&#x7A0B;&#x4E2D;&#x663E;&#x793A;&#x5168;&#x5C40; loading</span>

    exCancel<span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span> <span class="token comment">// &#x8BF7;&#x6C42;&#x524D;&#x5148;&#x53D6;&#x6D88;&#x672A;&#x5B8C;&#x6210;&#x7684;&#x8BF7;&#x6C42;&#xFF08;&#x901A;&#x5E38;&#x7528;&#x4E8E;&#x5E42;&#x7B49;&#x6027;&#x8BF7;&#x6C42;&#xFF0C;&#x5982;&#x5217;&#x8868;&#x67E5;&#x8BE2;&#x7B49;&#xFF09;</span>

    <span class="token comment">// &#x5BF9;&#x4E8E; `/xxx/${id}` &#x8FD9;&#x79CD;&#x5F62;&#x5F0F;&#x7684; path&#xFF0C;&#x53C2;&#x8003;&#x5982;&#x4E0B;&#xFF1A;</span>
    <span class="token comment">// exCancel: &apos;/xxx/*&apos;,</span>
    <span class="token comment">// exCancelName: &apos;/xxx/*&apos;,</span>

    <span class="token comment">// &#x5BF9;&#x4E8E; `/xxx/${id}/yyy` &#x8FD9;&#x79CD;&#x5F62;&#x5F0F;&#x7684; path&#xFF0C;&#x53C2;&#x8003;&#x5982;&#x4E0B;&#xFF1A;</span>
    <span class="token comment">// exCancel: &apos;/xxx/*/yyy&apos;,</span>
    <span class="token comment">// exCancelName: &apos;/xxx/*/yyy&apos;,</span>

    <span class="token comment">// &#x4E25;&#x683C;&#x5339;&#x914D;&#xFF0C;&#x53C2;&#x8003;&#x5982;&#x4E0B;&#xFF08;&#x4F7F;&#x7528;&#x52A8;&#x6001;&#x540D;&#x79F0;&#xFF09;&#xFF1A;</span>
    <span class="token comment">// exCancel: `/xxx?${params.id}`,</span>
    <span class="token comment">// exCancelName: `/xxx?${params.id}`,</span>

    <span class="token comment">// &#x5339;&#x914D;&#x4E00;&#x7C7B;&#xFF0C;&#x53C2;&#x8003;&#x5982;&#x4E0B;&#xFF08;&#x7C7B;&#x540D;&#x4E0D;&#x80FD;&#x4EE5;&#x659C;&#x6760;&#x5F00;&#x5934;&#xFF09;&#xFF1A;</span>
    <span class="token comment">// exCancel: &apos;xxx&apos;,</span>
    <span class="token comment">// exCancelName: &apos;xxx&apos;,</span>

    <span class="token comment">// &#x8C03;&#x7528;&#x65F6;&#x4F20;&#x5165;&#xFF0C;&#x4F7F;&#x5176;&#x5177;&#x5907;&#x8F83;&#x9AD8;&#x7075;&#x6D3B;&#x5EA6;&#xFF0C;&#x4EE5;&#x5B9E;&#x73B0;&#x4E0D;&#x540C;&#x7684;&#x5E94;&#x7528;&#x573A;&#x666F;&#xFF08;&#x6BD4;&#x5982;&#x9501;&#x5B9A;&#x4F5C;&#x7528;&#x8303;&#x56F4;&#x7B49;&#xFF09;&#xFF1A;</span>
    <span class="token comment">// exCancel: opts.cancelName,</span>
    <span class="token comment">// exCancelName: opts.cancelName,</span>
  <span class="token punctuation">}</span><span class="token punctuation">)</span>
<span class="token punctuation">}</span>
</pre><h4 class="mume-header" id="%E5%9C%A8%E4%B8%9A%E5%8A%A1%E4%B8%AD-%E4%BD%BF%E7%94%A8-then-catch-finally">&#x5728;&#x4E1A;&#x52A1;&#x4E2D; --- &#x4F7F;&#x7528; .then .catch .finally</h4>

<pre data-role="codeBlock" data-info="js" class="language-javascript"><span class="token keyword module">import</span> <span class="token imports"><span class="token punctuation">{</span> getXxx <span class="token punctuation">}</span></span> <span class="token keyword module">from</span> <span class="token string">&apos;@/scripts/api/common&apos;</span>

<span class="token keyword module">export</span> <span class="token keyword module">default</span> <span class="token punctuation">{</span>
  methods<span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">/* &#x6210;&#x529F; &amp; &#x5931;&#x8D25; &amp; &#x5B8C;&#x6210; */</span>
    <span class="token function">getData1</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
      <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">loading</span> <span class="token operator">=</span> <span class="token boolean">true</span>
      <span class="token keyword control-flow">return</span> <span class="token function">getXxx</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
        <span class="token punctuation">.</span><span class="token method function property-access">then</span><span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token parameter"><span class="token punctuation">{</span> exData<span class="token operator">:</span> data <span class="token punctuation">}</span></span><span class="token punctuation">)</span> <span class="token arrow operator">=&gt;</span> <span class="token punctuation">{</span>
          <span class="token comment">// ...</span>
          <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">isError</span> <span class="token operator">=</span> <span class="token boolean">false</span>
        <span class="token punctuation">}</span><span class="token punctuation">)</span>
        <span class="token punctuation">.</span><span class="token keyword control-flow">catch</span><span class="token punctuation">(</span><span class="token parameter">error</span> <span class="token arrow operator">=&gt;</span> <span class="token punctuation">{</span>
          <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">isError</span> <span class="token operator">=</span> <span class="token boolean">true</span>
          <span class="token keyword control-flow">throw</span> error <span class="token comment">// &#x4E00;&#x5B9A;&#x8981;&#x629B;&#x51FA;&#x5F02;&#x5E38;&#xFF0C;&#x8BA9;&#x5168;&#x5C40;&#x7EDF;&#x4E00;&#x5904;&#x7406;</span>
        <span class="token punctuation">}</span><span class="token punctuation">)</span>
        <span class="token punctuation">.</span><span class="token keyword control-flow">finally</span><span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token arrow operator">=&gt;</span> <span class="token punctuation">{</span>
          <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">loading</span> <span class="token operator">=</span> <span class="token boolean">false</span>
        <span class="token punctuation">}</span><span class="token punctuation">)</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>

    <span class="token comment">/* &#x6210;&#x529F; &amp; &#x5931;&#x8D25;&#xFF08;&#x4E0D;&#x63A8;&#x8350;&#x7684;&#x5199;&#x6CD5;&#xFF09; */</span>
    <span class="token function">getData2</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
      <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">loading</span> <span class="token operator">=</span> <span class="token boolean">true</span>
      <span class="token keyword control-flow">return</span> <span class="token function">getXxx</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token method function property-access">then</span><span class="token punctuation">(</span>
        <span class="token punctuation">(</span><span class="token parameter"><span class="token punctuation">{</span> exData<span class="token operator">:</span> data <span class="token punctuation">}</span></span><span class="token punctuation">)</span> <span class="token arrow operator">=&gt;</span> <span class="token punctuation">{</span>
          <span class="token comment">// ...</span>
          <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">loading</span> <span class="token operator">=</span> <span class="token boolean">false</span>
          <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">isError</span> <span class="token operator">=</span> <span class="token boolean">false</span>
        <span class="token punctuation">}</span><span class="token punctuation">,</span>
        <span class="token parameter">error</span> <span class="token arrow operator">=&gt;</span> <span class="token punctuation">{</span>
          <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">loading</span> <span class="token operator">=</span> <span class="token boolean">false</span>
          <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">isError</span> <span class="token operator">=</span> <span class="token boolean">true</span>
          <span class="token keyword control-flow">throw</span> error <span class="token comment">// &#x4E00;&#x5B9A;&#x8981;&#x629B;&#x51FA;&#x5F02;&#x5E38;&#xFF0C;&#x8BA9;&#x5168;&#x5C40;&#x7EDF;&#x4E00;&#x5904;&#x7406;</span>
        <span class="token punctuation">}</span><span class="token punctuation">,</span>
      <span class="token punctuation">)</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
<span class="token punctuation">}</span>
</pre><h4 class="mume-header" id="%E5%9C%A8%E4%B8%9A%E5%8A%A1%E4%B8%AD-%E4%BD%BF%E7%94%A8-async-await%E6%8E%A8%E8%8D%90">&#x5728;&#x4E1A;&#x52A1;&#x4E2D; --- &#x4F7F;&#x7528; async await&#xFF08;&#x63A8;&#x8350;&#xFF09;</h4>

<pre data-role="codeBlock" data-info="js" class="language-javascript"><span class="token keyword module">import</span> <span class="token imports"><span class="token punctuation">{</span> getXxx <span class="token punctuation">}</span></span> <span class="token keyword module">from</span> <span class="token string">&apos;@/scripts/api/common&apos;</span>

<span class="token keyword module">export</span> <span class="token keyword module">default</span> <span class="token punctuation">{</span>
  methods<span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">/* &#x6210;&#x529F; */</span>
    <span class="token keyword">async</span> <span class="token function">getData1</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
      <span class="token keyword">const</span> <span class="token punctuation">{</span> exData<span class="token operator">:</span> data <span class="token punctuation">}</span> <span class="token operator">=</span> <span class="token keyword control-flow">await</span> <span class="token function">getXxx</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
      <span class="token comment">// ...</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>

    <span class="token comment">/* &#x6210;&#x529F; &amp; &#x5B8C;&#x6210; */</span>
    <span class="token keyword">async</span> <span class="token function">getData2</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
      <span class="token keyword control-flow">try</span> <span class="token punctuation">{</span>
        <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">loading</span> <span class="token operator">=</span> <span class="token boolean">true</span>
        <span class="token keyword">const</span> <span class="token punctuation">{</span> exData<span class="token operator">:</span> data <span class="token punctuation">}</span> <span class="token operator">=</span> <span class="token keyword control-flow">await</span> <span class="token function">getXxx</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
        <span class="token comment">// ...</span>
      <span class="token punctuation">}</span> <span class="token keyword control-flow">finally</span> <span class="token punctuation">{</span>
        <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">loading</span> <span class="token operator">=</span> <span class="token boolean">false</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>

    <span class="token comment">/* &#x6210;&#x529F; &amp; &#x5931;&#x8D25; */</span>
    <span class="token keyword">async</span> <span class="token function">getData3</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
      <span class="token keyword control-flow">try</span> <span class="token punctuation">{</span>
        <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">loading</span> <span class="token operator">=</span> <span class="token boolean">true</span>
        <span class="token keyword">const</span> <span class="token punctuation">{</span> exData<span class="token operator">:</span> data <span class="token punctuation">}</span> <span class="token operator">=</span> <span class="token keyword control-flow">await</span> <span class="token function">getXxx</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
        <span class="token comment">// ...</span>
        <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">loading</span> <span class="token operator">=</span> <span class="token boolean">false</span>
        <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">isError</span> <span class="token operator">=</span> <span class="token boolean">false</span>
      <span class="token punctuation">}</span> <span class="token keyword control-flow">catch</span> <span class="token punctuation">(</span>error<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword control-flow">if</span> <span class="token punctuation">(</span><span class="token operator">!</span><span class="token keyword">this</span><span class="token punctuation">.</span><span class="token method function property-access">$isCancel</span><span class="token punctuation">(</span>error<span class="token punctuation">)</span> <span class="token comment">/* &#x7ED3;&#x5408; exCancel */</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
          <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">loading</span> <span class="token operator">=</span> <span class="token boolean">false</span>
          <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">isError</span> <span class="token operator">=</span> <span class="token boolean">true</span>
        <span class="token punctuation">}</span>
        <span class="token keyword control-flow">throw</span> error <span class="token comment">// &#x4E00;&#x5B9A;&#x8981;&#x629B;&#x51FA;&#x5F02;&#x5E38;&#xFF0C;&#x8BA9;&#x5168;&#x5C40;&#x7EDF;&#x4E00;&#x5904;&#x7406;</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>

    <span class="token comment">/* &#x6210;&#x529F; &amp; &#x5931;&#x8D25; &amp; &#x5B8C;&#x6210; */</span>
    <span class="token keyword">async</span> <span class="token function">getData4</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
      <span class="token keyword control-flow">try</span> <span class="token punctuation">{</span>
        <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">loading</span> <span class="token operator">=</span> <span class="token boolean">true</span>
        <span class="token keyword">const</span> <span class="token punctuation">{</span> exData<span class="token operator">:</span> data <span class="token punctuation">}</span> <span class="token operator">=</span> <span class="token keyword control-flow">await</span> <span class="token function">getXxx</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
        <span class="token comment">// ...</span>
        <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">isError</span> <span class="token operator">=</span> <span class="token boolean">false</span>
      <span class="token punctuation">}</span> <span class="token keyword control-flow">catch</span> <span class="token punctuation">(</span>error<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">isError</span> <span class="token operator">=</span> <span class="token boolean">true</span>
        <span class="token keyword control-flow">throw</span> error <span class="token comment">// &#x4E00;&#x5B9A;&#x8981;&#x629B;&#x51FA;&#x5F02;&#x5E38;&#xFF0C;&#x8BA9;&#x5168;&#x5C40;&#x7EDF;&#x4E00;&#x5904;&#x7406;</span>
      <span class="token punctuation">}</span> <span class="token keyword control-flow">finally</span> <span class="token punctuation">{</span>
        <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token property-access">loading</span> <span class="token operator">=</span> <span class="token boolean">false</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
<span class="token punctuation">}</span>
</pre><h4 class="mume-header" id="%E6%96%87%E4%BB%B6%E6%B5%81%E4%B8%8B%E8%BD%BD">&#x6587;&#x4EF6;&#x6D41;&#x4E0B;&#x8F7D;</h4>

<pre data-role="codeBlock" data-info="js" class="language-javascript"><span class="token keyword module">import</span> <span class="token imports"><span class="token punctuation">{</span> download <span class="token punctuation">}</span></span> <span class="token keyword module">from</span> <span class="token string">&apos;@/scripts/utils&apos;</span>
<span class="token keyword module">export</span> <span class="token keyword">const</span> <span class="token function-variable function">downloadXxx</span> <span class="token operator">=</span> <span class="token keyword">async</span> <span class="token keyword">function</span><span class="token punctuation">(</span><span class="token parameter">data</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  <span class="token keyword">const</span> res <span class="token operator">=</span> <span class="token keyword control-flow">await</span> http<span class="token punctuation">.</span><span class="token method function property-access">post</span><span class="token punctuation">(</span><span class="token string">&apos;/xxx&apos;</span><span class="token punctuation">,</span> data<span class="token punctuation">,</span> <span class="token punctuation">{</span> responseType<span class="token operator">:</span> <span class="token string">&apos;blob&apos;</span> <span class="token punctuation">}</span><span class="token punctuation">)</span>
  <span class="token keyword control-flow">return</span> <span class="token function">download</span><span class="token punctuation">(</span>res<span class="token punctuation">)</span>
<span class="token punctuation">}</span>
</pre>
      </div>
      
      
    
    
    
    
    
    
    
    
  
    </body></html>