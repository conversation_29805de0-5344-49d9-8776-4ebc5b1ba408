### 配置文件

- 配置文件

  - 开发环境：.env.development & .env.development.local
  - 测试环境：.env.production-stage
  - 生产环境：.env.production
  - 自动化测试：.env.test

- 变量同名时，会覆盖前面定义的
- .env.development.local 中的变量优先级最高
- .env.development.local 不会被 git 追踪，通常用于重写频繁变动的变量，如：

  ```
  VUE_APP_MOCK = true
  VUE_APP_MOCK = false

  DEV_PROXY_TARGET_API = 后端开发A的接口服务
  DEV_PROXY_TARGET_API = 后端开发B的接口服务
  DEV_PROXY_TARGET_API = 后端开发C的接口服务

  # ...
  ```

### 运行时(客户端)可用的环境变量

```ts
/**
 * 运行时(客户端)可用的：process.env.NODE_ENV、process.env.BASE_URL、process.env.VUE_APP_*
 * 环境判断推荐使用 process.env.VUE_APP_ENV
 */
interface Env {
  // development:开发环境 | production:非开发环境 | test:自动化测试
  readonly NODE_ENV: 'development' | 'production' | 'test'

  // dev:开发环境 | stage:测试环境 | prod:生产环境
  readonly VUE_APP_ENV: 'dev' | 'stage' | 'prod'

  // 页面部署路径，详情：https://cli.vuejs.org/zh/config/#publicpath
  readonly BASE_URL: '' | '/**/' // 需要尾斜杠，如没有则构建时会自动加上

  // 接口前缀
  readonly VUE_APP_BASE_API: '/**/*' | 'http*(s)://**/*' // 不需要尾斜杠

  // 是否开启文档中心
  readonly VUE_APP_ENABLE_DOCS: 'true' | 'false'

  // 是否启用 mockjs
  readonly VUE_APP_MOCK: 'true' | 'false'

  // ...
}
```

### 在 public/\*\*/\* 中通过 EJS 语法取用

- html/htm

  ```html
  <!-- 引用 public 中的资源时一定要加上 BASE_URL -->
  <% if (VUE_APP_ENV === 'dev' || VUE_APP_ENV === 'stage') { %>
  <script src="<%= BASE_URL %>libs/xxx.js"></script>
  <% } else { %>
  <script src="<%= BASE_URL %>libs/xxx.min.js"></script>
  <% } %>
  ```

- js

  ```js
  var appEnv = '<%= VUE_APP_ENV %>'

  // 对于不明确的变量，使用安全的取用方式，除了入口 html（入口 html 不存在 obj 变量）
  var xxx = '<%= obj.VUE_APP_XXX || "xxx" %>'
  ```

- json
  ```json
  {
    "xxx": "<%= obj.VUE_APP_XXX || 'xxx' %>"
  }
  ```

### 在 src/\*\*/\* 中通过 process.env 取用

- vue

  ```html
  <script>
    export default {
      data() {
        return {
          xxx: process.env.VUE_APP_XXX,
          xxx2: this.$env.VUE_APP_XXX2, // 通过 vm.$env 间接取用（推荐）
        }
      },
    }
  </script>

  <template>
    <!-- 在模板中只能间接取用 -->
    <a
      download
      target="_blank"
      :href="$env.BASE_URL + 'docs/系统操作指南v1.0.0.pdf'"
    >
      系统操作指南
    </a>
  </template>
  ```

- js
  ```js
  const xxx = process.env.VUE_APP_XXX
  ```

### 在 src/\*\*/\* 中进行条件编译

必须是运行时(客户端)可用的环境变量，并且变量值不能为 undefined，否则模块必定会打包

```js
// 正确的用法1
if (process.env.VUE_APP_ENV === 'dev') {
  require('xxx')
}
// 正确的用法2
if (process.env.VUE_APP_ENV === 'dev' || process.env.VUE_APP_ENV === 'stage') {
  require('xxx')
}

// 错误的用法1
const env = process.env
if (env.VUE_APP_ENV === 'dev' || env.VUE_APP_ENV === 'stage') {
  require('xxx')
}
// 错误的用法2
if (this.$env.VUE_APP_ENV === 'dev' || this.$env.VUE_APP_ENV === 'stage') {
  require('xxx')
}
```
