#
# 编译时(node 端)可用的：process.env.*
# 运行时(客户端)可用的：process.env.NODE_ENV、process.env.BASE_URL、process.env.VUE_APP_*
#
# BASE_URL 需要尾斜杠，接口前缀不需要尾斜杠
# BASE_URL 也可以生成为相对路径，详见：https://cli.vuejs.org/zh/config/#publicpath
#
VUE_APP_TITLE = "中小学入学招生综合服务系统"
NODE_ENV = development
VUE_APP_ENV = dev
VUE_APP_BASE_API = /api
VUE_APP_RESOURCE_PREFIX = /profile
VUE_APP_ENABLE_DOCS = true
# css 源码映射
DEV_CSS_SOURCEMAP = false
# css 自动加前缀 (进行兼容性调试时再启用)
DEV_CSS_AUTOPREFIXER = false
# 接口服务 (给 devServer.proxy 使用)
DEV_PROXY_TARGET_API = http://127.0.0.1:8080
