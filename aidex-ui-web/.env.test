#
# 编译时(node 端)可用的：process.env.*
# 运行时(客户端)可用的：process.env.NODE_ENV、process.env.BASE_URL、process.env.VUE_APP_*
#
# BASE_URL 需要尾斜杠，接口前缀不需要尾斜杠
# BASE_URL 也可以生成为相对路径，详见：https://cli.vuejs.org/zh/config/#publicpath
#
VUE_APP_TITLE = ""
NODE_ENV = production
VUE_APP_ENV = prod
VUE_APP_BASE_API = https://jsxy.usoftgs.com/api
VUE_APP_ENABLE_DOCS = false
# 接口服务 (给 devServer.proxy 使用)
DEV_PROXY_TARGET_API = http://8.129.13.79:7070
VUE_APP_RESOURCE_PREFIX = /profile
