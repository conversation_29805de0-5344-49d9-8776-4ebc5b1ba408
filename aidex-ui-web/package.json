{"name": "vue_web", "version": "0.1.0", "private": true, "scripts": {"dev": "npm run serve", "serve": "vue-cli-service serve --open", "build": "vue-cli-service build --modern", "build:test": "vue-cli-service build --mode test", "build:preview": "vue-cli-service build --mode preview", "build:gsedu": "vue-cli-service build --mode gsedu", "test:unit": "vue-cli-service test:unit", "lint": "stylelint '**/*.{css,less,html,vue}' --fix && vue-cli-service lint", "dist": "node ./static-server.js"}, "dependencies": {"aliyun-aliplayer": "^2.16.0", "axios": "0.24.0", "clipboard": "^2.0.11", "core-js": "3.25.3", "date-fns": "^1.30.1", "downloadjs": "^1.4.7", "element-ui": "^2.15.6", "file-saver": "2.0.5", "highlight.js": "^9.18.5", "html2canvas": "^1.4.1", "jquery": "^3.6.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lodash.throttle": "^4.1.1", "mockjs": "1.1.0", "moment": "^2.24.0", "normalize.css": "^8.0.1", "nprogress": "0.2.0", "qrcodejs2": "^0.0.2", "qs": "^6.9.6", "quill": "1.3.7", "store": "^2.0.12", "stylus": "^0.54.7", "stylus-loader": "^3.0.2", "swiper": "^4.4.2", "url-parse": "^1.5.1", "video.js": "^8.3.0", "videojs-markers": "^1.0.1", "vue": "^2.6.12", "vue-animate-number": "^0.4.2", "vue-awesome-swiper": "^4.1.1", "vue-cropper": "^0.5.6", "vue-router": "3.4.9", "vue-seamless-scroll": "^1.1.23", "vue-video-player": "^5.0.2", "vuex": "^3.6.2"}, "devDependencies": {"@types/lodash": "^4.14.168", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-unit-jest": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/test-utils": "^1.1.3", "babel-plugin-component": "^1.1.1", "babel-plugin-lodash": "^3.3.4", "express": "^4.17.1", "http-proxy-middleware": "^1.0.6", "less": "^3.13.1", "less-loader": "^5.0.0", "lint-staged": "^9.5.0", "open": "^7.4.2", "prettier": "^1.19.1", "raw-loader": "^4.0.2", "sass": "1.32.0", "sass-loader": "10.1.0", "stylelint": "^13.11.0", "stylelint-config-prettier": "^8.0.2", "stylelint-order": "^4.1.0", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "^2.6.12"}, "browserslist": ["> 1%", "last 2 versions", "since 2016", "edge >= 12", "not ie <= 10", "not dead"], "jest": {"preset": "@vue/cli-plugin-unit-jest"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{css,less,html,vue}": ["stylelint --aei --fix", "git add"], "*.{js,jsx,mjs,vue}": ["vue-cli-service lint", "git add"]}}