import router from './router'
import store from './store'
import {Message, MessageBox} from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken, setToken } from "@/utils/auth";
import commonUtils from '@/utils/commonUtils.js'
import constants from '@/utils/constants.js'

NProgress.configure({ showSpinner: false })

const backList = ['/index','/login', '/revise', '/register', '/newsList', '/newsArticle','/list','/article','/main', '/explain','/explainArticle', '/explain_ysx', '/explain_xsc','/explainArticle_xsc', '/zs', '/help', '/guide']

const whiteList = ['/explain', '/explain_ysx','/explain_xsc', '/explainArticle', '/explainArticle_xsc', '/zs']

const loginRoutePath = '/login'
// const defaultRoutePath = '/index'
const defaultRoutePath = '/guide'

router.beforeEach((to, from, next) => {
  NProgress.start()

  // if (!whiteList.includes(to.path)) {
  //   window.location.href = "https://zwfw.gansu.gov.cn/gsspace/unscramble?unscrambleId=662&region=************";
  //   return;
  // }

  if (to.path === '/qrcodeRedirect' && to.query.params) {
    commonUtils.removeApplyParams(constants.STORE_CACHE_KEY_QRCODE)
    // 时间间隔，单位（分钟）
    const timeInterval = 30
    // 获取当前时间
    let currentTime = new Date();
    // 获取当前时间之后的时间
    let halfHourLater = currentTime.getTime() + (timeInterval * 60 * 1000);
    const data = {qrcodeKey: to.query.params, expireTime: halfHourLater}
    commonUtils.setApplyParams(data, constants.STORE_CACHE_KEY_QRCODE)
    next({ path: defaultRoutePath })
    NProgress.done()
  }

  /* has token */
  const token = getToken()
  if (token) {
    if (to.path === loginRoutePath || to.path === '/') {
      next({ path: defaultRoutePath })
      NProgress.done()
    } else {
      store.dispatch('GetInfo').then(() => {
        next();
      }).catch(err => {
        store.dispatch('LogOut').then(() => {
          sessionStorage.clear();
          next({ path: '/' })
        })
      })
    }
  } else {
    // 没有token
    if (backList.includes(to.path)) {
      // 黑名单做拦截
      next()
    } else {
      // 否则全部重定向到登录页
      next({ path: defaultRoutePath })
      NProgress.done()
    }
  }

})


router.afterEach(() => {
  NProgress.done()
})
