<template>
  <div class="choose">
    <Topbox class="top_layout" /><!--顶部-->
    <top />
    <div class="choose_body">
      <div class="body_middle">
        <div class="middle_top">
          <div>
            <el-steps :active="1" align-center>
              <el-step title="注册"></el-step>
              <el-step title="选择入学区域"></el-step>
              <el-step title="选择报名学校"></el-step>
              <el-step title="提交报名资料"></el-step>
            </el-steps>
          </div>
        </div>
        <div class="border_top"></div>
        <div class="mddle_center">
          <div class="title">选择入学区域</div>
          <div class="district_box">
            <div class="district_left">
              <div class="district_title">①·请选择市州</div>
              <a v-for="(item, index) in city" :key="index" @click="getCityData(item, index)" :class="{ active: selectedIndex === index }">{{ item.name }}</a>
              <span v-if="cityTime">{{ cityName }}报名时间为：{{ cityClassify.startTime ? parseTime(cityClassify.startTime, '{y}年{m}月{d}日') : '' }}{{ cityClassify.endTime ? ('至' + parseTime(cityClassify.endTime, '{y}年{m}月{d}日')) : '' }}</span>
            </div>
            <div class="district_right" v-if="tishishow">
              <div class="district_title">②·请选择区县</div>
              <a v-for="(item, index) in area" :key="index" @click="getCityArea(item, index)" :class="{ active: selectedAreaIndex === index }">{{ item.name }}</a>
              <span v-if="areaTime">{{ areaName }}报名时间为：{{ areaClassify.startTime ? parseTime(areaClassify.startTime, '{y}年{m}月{d}日') : '' }}{{ areaClassify.endTime ? ('至' + parseTime(areaClassify.endTime, '{y}年{m}月{d}日')) : '' }}</span>
            </div>
          </div>
<!--          <div class="center_box" >-->
<!--            <div class="box_samll" :class='{ active1:index===currentIndex}' v-for="(item, index) in address" :key="index" @click="changeClick(index, item)">{{item.areaName}}</div>-->
<!--          </div>-->
        </div>
        <div class="border_top"></div>
        <div class="middle_bottom">
          <div class="button" @click="toschool()">下一步：选择报名学校</div>
        </div>
      </div>
    </div>
    <Footbox class="foot_layout" />
     <foot />
  </div>
</template>

<script>
import top from '../../components/top/top.vue'
import foot from '../../components/foot/foot.vue'
import { getApplyAreaList, checkApplyState } from '@/api/apply'
import Topbox from "../layout/newtop";
import Footbox from "../layout/foot";
export default {
  components: {
     top,foot,
    Topbox,
    Footbox
  },
  data() {
    return {
      steps:[
        {name:'选择入学区域'},
        {name:'选择报名学校'},
        {name:'提交报名资料'}
      ],
      currentIndex: -1,
      address:[],
      routerQuery: {},
      tishishow: false,
      city:[],
      cityTime:false,
      areaTime:false,
      cityName:"",
      areaName:"",
      area:[],
      selectedCity: '',
      selectedIndex: null,
      selectedAreaIndex: null,
      cityClassify: {},
      areaClassify: {},
      type: null
    }
  },
  mounted() {},
  beforeDestroy() {},
  created() {
    this.routerQuery = this.$commonUtils.getApplyParams(this.$constants.STORE_CACHE_KEY_APPLY) || {}
    this.type = this.routerQuery.type
    this.getCityAreaData()
  },
  methods: {
    getCityAreaData() {
      const params = {
        parentId: this.$constants.DEFAULT_AREA_ROOT_ID,
        type: this.type
      }
      getApplyAreaList(params).then(response => {
        const data = response.data || []
        if (this.routerQuery.cityId) {
          let index = 0, obj = {};
          for (let i = 0; i < data.length; i++) {
            const item = data[i]
            if (item.id === this.routerQuery.cityId) {
              index = i;
              obj = item;
              break;
            }
          }
          if (obj) {
            this.cityName = obj.name;
            this.selectedIndex = index;
            if (obj.classify) {
              this.cityTime = true
              this.cityClassify = obj.classify
            }
            if (this.$constants.DEFAULT_AREA_ROOT_ID === obj.id) {
              this.tishishow = false
            } else {
              this.getAreaData(obj.id)
            }
          }
        }
        this.city = data
      })
    },
    getAreaData(parentId) {
      const params = {
        parentId: parentId,
        type: this.type
      }
      getApplyAreaList(params).then(response => {
        const data = response.data || []
        if (this.routerQuery.areaId) {
          let index = 0, obj = {};
          for (let i = 0; i < data.length; i++) {
            const item = data[i]
            if (item.id === this.routerQuery.areaId) {
              index = i;
              obj = item;
              break;
            }
          }
          if (obj) {
            this.areaName = obj.name;
            this.selectedAreaIndex = index;
            if (obj.classify) {
              this.areaTime = true
              this.areaClassify = obj.classify
            }
          }
        }
        this.area = data
        this.tishishow = true
      })
    },
    changeClick(index, val){
      this.currentIndex=index;
      this.routerQuery.areaId = val.id
    },
    toschool(){
      if (this.routerQuery.cityId || this.routerQuery.areaId) {
        const arr = []
        if (this.cityClassify.id) {
          arr.push(this.cityClassify.id)
        }
        if (this.areaClassify.id) {
          arr.push(this.areaClassify.id)
        }
        const data = {
          ids: arr
        }
        checkApplyState(data).then(response => {
          if (response && response.code === 200) {
            this.$commonUtils.setApplyParams(this.routerQuery, this.$constants.STORE_CACHE_KEY_APPLY)
            this.$router.push({path: "/school"})
          } else {
            this.$message.error("暂未开启报名!")
          }
        })
      } else {
        this.$message.warning("请选择入学区划")
      }
    },
    getCityData(item, index) {
      this.area = [];
      this.selectedAreaIndex = null;
      this.routerQuery.areaId = null;
      this.areaTime = false
      this.cityClassify = {}
      this.areaClassify = {}
      if (item) {
        this.cityName = item.name;
        this.selectedIndex = index;
        if (item.classify) {
          this.cityTime = true
          this.cityClassify = item.classify
        } else {
          this.cityTime = false
          this.cityClassify = {}
        }
        this.routerQuery.cityId = item.id
        if (this.$constants.DEFAULT_AREA_ROOT_ID === item.id) {
          this.tishishow = false
          return
        }
        this.getAreaData(item.id)
      } else {
        this.cityTime = false
        this.cityClassify = {}
        this.routerQuery.cityId = null
      }
    },
    getCityArea(item, index){
      if (item) {
        this.areaName = item.name;
        this.selectedAreaIndex = index;
        if (item.classify) {
          this.areaTime = true
          this.areaClassify = item.classify
        } else {
          this.areaTime = false
          this.areaClassify = {}
        }
        this.routerQuery.areaId = item.id
      } else {
        this.areaTime = false
        this.areaClassify = {}
        this.routerQuery.areaId = null
      }
    },
  },
}
</script>
<style lang="scss" scoped>
//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {


    .map_box_pc{
    display: block;
    display: flex;
    justify-content: space-between;
  }
  .map_box_yd{
    display: none!important;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .choose_body {
      flex: 1;
      display: flex;
      justify-content: center;
      margin-top: 0;
      .body_middle {
        width: 1200px;
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 10px 30px 1px rgba(93,132,177,0.11);
        border-radius: 0 0 16px 16px;
        .middle_top {
          width: 100%;
          margin: 40px 0px;
        }
        .border_top {
          width: 1155px;
          height: 7px;
          background: #f1f1f1;
          border-radius: 12px 12px 12px 12px;
          opacity: 1;
          margin: 0 23px;
        }
        .mddle_center {
          width: 100%;
          min-height: 450px;
          .title {
            width: 132px;
            height: 29px;
            font-size: 22px;
            font-weight: bold;
            color: #000000;
            margin: 24px 42px;
          }
          .district_box{
            width: calc(100% - 84px);
            margin: 0 42px;
            display: flex;
            flex-flow: column;
            gap:30px;
            .district_left,.district_right{
              width: 100%;
              background: #fafafa;
              border: 1px solid #e1e1e1;
              border-radius: 8px;
              padding:15px 20px;
              display: flex;
              flex-wrap: wrap;
              gap: 15px;
              .district_title{
                width: 100%;
                font-size: 20px;
                color: #3570f6;
              }
              span{
                width: 100%;
                font-size: 20px;
                color: #FC9709;
              }
              a{
                background: #f2f5ff;
                border-radius: 8px 8px 8px 8px;
                opacity: 1;
                border: 1px solid #3570f6;
                text-align: center;
                position: relative;
                padding:8px 15px;
                cursor: pointer;
                font-size: 18px;
                &.active{
                  background: #3570F6;
                  color: #fff;
                  border-radius: 8px 8px 8px 8px;
                  opacity: 1;
                  border: 1px solid #3570F6;
                  position: relative;
                  &::after{
                    content: "";
                    background: url("../../../public/image/icon.svg") no-repeat center 50% #ffffff;
                    position: absolute;
                    right: -10px;
                    top: -10px;
                    width: 18px;
                    height: 18px;
                    background-size: 80% 80%;
                    border-radius: 100px;
                    border: 4px solid #3570F6;

                  }
                }
              }
            }
          }
          .center_box {
            width: calc(100% - 84px);
            display: flex;
            margin: 0 42px;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 20px;
            .box{
              width: 26px;
              height: 26px;
              background: #FFFFFF;
              border-radius: 4px 4px 4px 4px;
              border: 1px solid #3570F6;
              display: flex;
              justify-content: center;
              align-items: center;
              position: absolute;
              top:4px;
              right: 5px ;
              img{
                width: 16px;
              }
            }
            .box_samll {
              width: calc(20% - 16px);
              height: 72px;
              line-height: 72px;
              background: #f2f5ff;
              border-radius: 8px 8px 8px 8px;
              opacity: 1;
              border: 1px solid #3570f6;
              text-align: center;
              position: relative;
              &.active1::before{
                content: "";
                background: url("../../../public/image/icon.svg") no-repeat center 50% #ffffff;
                position: absolute;
                right: 5px;
                top: 5px;
                width: 18px;
                height: 18px;
                background-size: 80% 80%;
                border-radius: 4px;
              }
            }
            .active1{
              height: 72px;
              background: #3570F6;
              color: #fff;
              border-radius: 8px 8px 8px 8px;
              opacity: 1;
              border: 1px solid #3570F6;
              position: relative;
            }
          }
        }
        .middle_bottom {
          width: 100%;
          height: 100px;
          display: flex;
          text-align: center;
          justify-content: center;
          align-items: center;
          .button {
            width: 418px;
            height: 59px;
            line-height: 59px;
            background: #3570f6;
            border-radius: 10px 10px 10px 10px;
            opacity: 1;
            color: #fff;
          }
        }
      }
    }
    .choose_foot {
      width: 100%;
      height: 80px;
      display: none;
      justify-content: center;
      align-items: center;

    }
  }
}
//移动端
@media screen and (max-width: 720px) {
  .top_layout,.foot_layout{
    display: none;
  }
    .map_box_pc{
    display: none;
  }
  .map_box_yd{
    display: block;
    display: flex;
    justify-content: space-around;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    margin-top: 1rem;
    .choose_body {
      display: flex;
      justify-content: center;
      margin-top: 5rem;
      .body_middle {
         width: calc(100% - 2rem);
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 0.5rem 1rem rgba(93,132,177,0.11);
        border-radius: 0.8rem;
        .middle_top {
          display: none;
        }
        .border_top {
          display: none;
        }
        .mddle_center {
          width: 100%;
          .title {
            width: 100%;
            height: auto;
            font-size: 1.6rem;
            font-weight: bold;
            color: #000000;
            padding:1rem 0 0 1.5rem;
          }
          .district_box{
            width: calc(100% - 2rem);
            margin: 0 1rem;
            display: flex;
            flex-flow: column;
            gap:30px;
            margin-top: 1rem;
            .district_left,.district_right{
              width: 100%;
              background: #fafafa;
              border: 1px solid #e1e1e1;
              border-radius: 0.6rem;
              padding:0.8rem 1.2rem 1.2rem 1.2rem;
              display: flex;
              flex-wrap: wrap;
              gap: 1rem;
              .district_title{
                width: 100%;
                font-size: 1.3rem;
                color: #3570f6;
              }
              span{
                width: 100%;
                font-size:1.3rem;
                color: #FC9709;
              }
              p{
                font-size: 1.1rem;
              }
              a{
                background: #f2f5ff;
                border-radius: 0.6rem;
                opacity: 1;
                border: 1px solid #3570f6;
                text-align: center;
                position: relative;
                padding:0.5rem 0.8rem;
                cursor: pointer;
                font-size: 1.1rem;
                &.active{
                  background: #3570F6;
                  color: #fff;
                  border-radius: 8px 8px 8px 8px;
                  opacity: 1;
                  border: 1px solid #3570F6;
                  position: relative;
                  &::after{
                    content: "";
                    background: url("../../../public/image/icon.svg") no-repeat center 50% #ffffff;
                    position: absolute;
                    right: -10px;
                    top: -10px;
                    width: 18px;
                    height: 18px;
                    background-size: 80% 80%;
                    border-radius: 100px;
                    border: 4px solid #3570F6;

                  }
                }
              }
            }
          }
          .center_box {
            width: 100%;
            display: flex;
            flex-direction: column;
            .box{
              width: 26px;
              height: 26px;
              background: #FFFFFF;
              border-radius: 4px 4px 4px 4px;
              border: 1px solid #3570F6;
              display: flex;
              justify-content: center;
              align-items: center;
              position: absolute;
              top:4px;
              right: 5px ;
              img{
                width: 16px;
              }
            }
            .box_samll {
              width: calc(100% - 2rem);
              margin-left: 1rem;
              height: 4rem;
              line-height: 4rem;
              background: #f2f5ff;
              border-radius: 8px;
              opacity: 1;
              border: 1px solid #3570f6;
              text-align: center;
              margin-top: 1rem;
              position: relative;
              &.active1::before{
                content: "";
                background: url("../../../public/image/icon.svg") no-repeat center 50% #ffffff;
                position: absolute;
                left: 10px;
                top: 50%;
                transform: translateY(-50%);
                width: 18px;
                height: 18px;
                background-size: 80% 80%;
                border-radius: 4px;
              }
            }
            .active1{
               width: calc(100% - 2rem);
              margin-left: 1rem;
              height: 4rem;
              line-height: 4rem;
              background: #3570F6;
              color: #fff;
              border-radius: 8px;
              border: 1px solid #3570F6;
            }
          }
        }
        .middle_bottom {
          width: 100%;
          height: 100px;
          display: flex;
          text-align: center;
          justify-content: center;
          align-items: center;
          .button {
            width: 90%;
            height: 4rem;
            line-height: 4rem;
            background: #3570f6;
            border-radius: 10px;
            color: #fff;
          }
        }
      }
    }
    .choose_foot {
      width: 100%;
      height:auto;
      display: flex;
      justify-content: center;
      align-items: center;
      padding:1.5rem 1.2rem;
    }
  }
}
::v-deep .is-finish{
  color: #383838;
  font-size: 18px;
  .el-step__line{
    background: #3570F6;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #3570F6;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    border-color: #3570F6;
  }
}

::v-deep .is-process{
  color: #383838;
  font-size: 18px;
  font-weight: normal;
  .el-step__line{
    background: #f1f1f1;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #ffffff;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    border-width: 5px;
    color: #3570F6;
    border-color: #3570F6;
  }
}

::v-deep .is-wait{
  color: #383838;
  font-size: 18px;
  font-weight: normal;
  .el-step__line{
    background: #f1f1f1;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #EEF6FF;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    border-width: 5px;
    color: #3570F6;
    border-color: #EEF6FF;
  }
}
</style>
