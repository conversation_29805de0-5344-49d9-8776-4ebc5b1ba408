<template>
  <div class="home">
    <Topbox class="top_layout" /><!--顶部-->
<!--      <top />-->

    <div class="main_box" style="margin-top: 30px;">
      <div class="className">
        <span class="titlename">{{ areaName }}</span>
        <div class="siteBox" @click="tomain">
          <span>选择区县</span>
          <img src="../../../public/image/choose.svg" />
        </div>
        <div class="top_tab">
          <span :class="{ now: tab === '1' }" @click="tab='1'">招生方案</span>
          <span :class="{ now: tab === '2' }" @click="tab='2'">划拨方案</span>
          <span :class="{ now: tab === '3' }" @click="tab='3'">政策解释</span>
          <span :class="{ now: tab === '4' }" @click="tab='4'">学校联系电话</span>
        </div>
      </div>
      <div class="content" v-if="tab === '1'">
        <div v-if="dataInfo.enrollmentScheme" v-html="dataInfo.enrollmentScheme"></div>
        <div v-else><el-empty description="暂无内容"></el-empty></div>
      </div>
      <div class="content" v-if="tab === '2'">
        <div v-if="dataInfo.scribingScheme" v-html="dataInfo.scribingScheme"></div>
        <div v-else><el-empty description="暂无内容"></el-empty></div>
      </div>
      <div class="content" v-if="tab === '3'">
        <div v-if="dataInfo.description" v-html="dataInfo.description"></div>
        <div v-else><el-empty description="暂无内容"></el-empty></div>
      </div>
      <div class="content" v-if="tab === '4'">
        <div v-if="dataInfo.enrollmentSchool" v-html="dataInfo.enrollmentSchool"></div>
        <div v-else><el-empty description="暂无内容"></el-empty></div>
      </div>
    </div>
    <Footbox class="foot_layout" />
    <foot />

  </div>
</template>

<script>
import top from '../../components/top/top.vue'
import foot from '../../components/foot/foot.vue'
import Topbox from "@/views/layout/newtop";
import Footbox from "@/views/layout/foot";
import {getBizSourceTypeExplain} from "@/api/apply";
export default {
  components: {
    top,foot,
    Topbox,
    Footbox
  },
  data() {
    return {
      tab:'1',
      dataInfo: {
        name: '',
        description: '',
        enrollmentScheme: '',
        scribingScheme: '',
        enrollmentSchool: ''
      },
      areaCode: '',
      areaName: '',
      imageUrlshow:false,
    }
  },
  mounted() {},
  beforeDestroy() {},
  created() {
    const routerQuery = this.$route.query || {};
    this.areaCode = routerQuery.areaCode || ''
    this.getArticleData();
  },

  methods: {
    getArticleData() {
      getBizSourceTypeExplain(this.areaCode, this.$constants.SOURCE_TYPE_EXPLAIN_DATATYPE_CZ).then(response => {
        const dataList = response.data.data || [];
        for (let i = 0; i < dataList.length; i++) {
          const dataItem = dataList[i];
          if(this.$constants.SOURCE_TYPE_EXPLAIN_TYPE_ZSFA === dataItem.type){
            this.dataInfo.enrollmentScheme = dataItem.enrollmentScheme;
          } else if(this.$constants.SOURCE_TYPE_EXPLAIN_TYPE_HPFA === dataItem.type){
            this.dataInfo.scribingScheme = dataItem.scribingScheme;
          } else if(this.$constants.SOURCE_TYPE_EXPLAIN_TYPE_ZCJS === dataItem.type){
            this.dataInfo.description = dataItem.description;
          } else if(this.$constants.SOURCE_TYPE_EXPLAIN_TYPE_XXLXDH === dataItem.type){
            this.dataInfo.enrollmentSchool = dataItem.enrollmentSchool;
          }
        }
        this.areaName = response.data.areaName;
      })
    },

    tomain(){
      this.$router.push({path: "/explain_xsc"}).then(() => {
        window.scrollTo(0, 0);
      });
    },


  },
}
</script>
<style lang="scss" scoped>
//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {
  .imshow{
    position: fixed;
    inset:0;
    z-index: 999999;
    background: rgba(0,0,0,.5);
    display: flex;
    justify-content: center;
    align-items: center;
    img{
      max-width: 80%;
      max-height: 80%;
    }
  }

  .home {
    width: 100%;
    display: flex;
    flex-direction: column;
    background: #eef6ff;

    .main_box{
      width: 1200px;
      height: auto;
      background: rgba(255, 255, 255, 0.62);
      box-shadow: 0px 10px 30px rgba(93, 132, 177, 0.11);
      border-radius: 16px 16px 16px 16px;
      padding:40px 45px;
      margin-top: 0px;
      margin-left: 50%;
      transform: translateX(-50%);
      .className{
        width: 100%;
        height: auto;
        font-size: 34px;
        line-height: 40px;
        margin-bottom: 15px;
        color: #000000;
        display: flex;
        align-items: center;
        .titlename{
          font-size: 34px;
          line-height: 40px;
          margin-right: 20px;
        }
        .top_tab{
          display: flex;
          gap: 20px;
          align-items: center;
          transform: translateY(-5px);
          span{
            cursor: pointer;
            display: flex;
            width: auto;
            height: 40px;
            justify-content: center;
            align-items: center;
            padding:0 25px;
            background: #fafafa;
            border-radius: 100px;
            border: 1px solid #f1f1f1;
            font-size: 16px;
            color: #2050bb;
            box-shadow: 0 5px 15px rgba(0,0,0,.15);
            transition: ease-in-out .2s all;
            &:hover{
              box-shadow: 0 2px 4px rgba(0,0,0,.3);
              transform: translateY(5px);
            }
            &.now{
              color: #ffffff;
              font-weight: bold;
              background: #3570F6;
              border: 1px solid #3570F6;
              box-shadow: 0 2px 4px #3570F633;
              transform: translateY(5px);
            }
          }
        }
      }
      .classNameSub{
        width: 100%;
        height: auto;
        font-size: 18px;
        color: #666;
      }

      .content{
        width: 100%;
        height: auto;
        padding:25px 0;
        border-top: 5px solid #f1f1f1;
        margin-top: 25px;
        font-size: 16px;
        min-height: 600px;
        ::v-deep img{
          width: auto!important;
          height: auto!important;
          max-width: 100%;
        }
      }
      .siteBox{
        position: absolute;
        top: 0;
        right: 0;
        background: rgba(0,0,0,0.6);
        padding:8px 20px;
        border-radius: 0 12px 0 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        width: auto;
        z-index: 3;
        span{
          color: #ffffff;
          font-size: 16px;
          font-weight: bold;
          cursor: pointer;
        }
        img{
          width: 24px;
          height: 18px;
          margin-left: 8px;
          cursor: pointer;
        }
      }
    }

    .choose_foot {
      display: none;
    }
  }
}
//移动端
@media screen and (max-width: 720px) {


  .imshow{
    position: fixed;
    inset:0;
    z-index: 999999;
    background: rgba(0,0,0,.3);
    display: flex;
    justify-content: center;
    align-items: center;
    img{
      max-width: 100%;
      max-height: 100%;
    }
  }
  .top_layout,.foot_layout{
    display: none;
  }
  .home {
    width: 100%;
    display: flex;
    flex-direction: column;
    background: #eef6ff;

    .main_box{
      width: calc(100% - 2rem);
      height: auto;
      background: rgba(255, 255, 255, 0.62);
      box-shadow: 0px 0.5rem 1rem rgba(93, 132, 177, 0.11);
      border-radius: 0.8rem;
      padding:1.5rem 1.5rem;
      margin-top: 30px;
      margin-left: 50%;
      margin-top: 6rem;
      transform: translateX(-50%);
      .className{
        width: 100%;
        height: auto;
        font-size: 1.8rem;
        line-height: 2.2rem;
        margin-bottom: 1rem;
        margin-top: 1rem;
        color: #000000;
        .top_tab{
          display: flex;
          gap: 0.8rem;
          align-items: center;
          transform: translateY(-5px);
          margin-top: 1rem;
          flex-wrap: wrap;
          justify-content: space-between;
          span{
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            flex-wrap: wrap;
            width: 20%;
            height: auto;
            line-height: 1.1rem;
            justify-content: center;
            align-items: center;
            padding:0.5rem 0.3rem;
            background: #fafafa;
            border-radius: 0.2rem;
            border: 1px solid #f1f1f1;
            font-size: 1rem;
            color: #2050bb;
            box-shadow: 0 5px 15px rgba(0,0,0,.15);
            transition: ease-in-out .2s all;
            &:hover{
              box-shadow: 0 2px 4px rgba(0,0,0,.3);
              transform: translateY(5px);
            }
            &.now{
              color: #ffffff;
              background: #3570F6;
              border: 1px solid #3570F6;
              box-shadow: 0 2px 4px #3570F633;
              transform: translateY(5px);
            }
          }
        }
      }
      .classNameSub{
        width: 100%;
        height: auto;
        font-size: 1rem;
        color: #666;
      }
      .content{
        width: 100%;
        height: auto;
        padding:1.2rem 0;
        border-top: 5px solid #f1f1f1;
        margin-top: 1.2rem;
        font-size: 1.2rem;
        ::v-deep img{
          width: auto!important;
          height: auto!important;
          max-width: 100%;
        }
      }
    }
    .siteBox{
      position: absolute;
      top: 0;
      right: 0;
      background: rgba(0,0,0,0.6);
      padding:0 0.6rem;
      border-radius: 0 0.8rem 0 0.8rem;
      cursor: pointer;
      display: flex;
      align-items: center;
      width: auto;
      z-index: 3;
      span{
        color: #ffffff;
        font-size: 0.8rem;
        font-weight: bold;
        cursor: pointer;
      }
      img{
        width: 0.8rem;
        height: auto;
        margin-left: 0.5rem;
        cursor: pointer;
      }
    }

  }

}

</style>
