<template>
  <div class="login">
    <Topbox class="top_layout" /><!--顶部-->
    <top />
    <div class="login_body_pc">
      <div class="main_box">
        <div class="className">{{ dataInfo.title || '--' }}</div>
        <div class="classNameSub"><span>发布时间：{{ dataInfo.releaseTime || '--' }}</span></div>
        <div class="content">
          <div v-if="dataInfo.content" v-html="dataInfo.content"></div>
          <div v-else><el-empty description="暂无内容"></el-empty></div>
        </div>
      </div>
    </div>
    <Footbox class="foot_layout" />
    <foot />
  </div>
</template>

<script>
import top from '@/components/layoutBox/top'
import foot from '@/components/layoutBox/foot'
import { getArticleInfo } from '@/api/cms'
import Topbox from "@/views/layout/newtop";
import Footbox from "@/views/layout/foot";
export default {
  components: {
    top,foot,
    Topbox,
    Footbox
  },
  data() {
    return {
      dataInfo: {
        title: '',
        releaseTime: '',
        content: ''
      },
      articleId: ''
    }
  },
  mounted() {},
  beforeDestroy() {},
  created() {},
  watch: {
    $route: {
      handler: function(route) {
        this.initData()
      },
      immediate: true
    }
  },
  methods: {
    initData() {
      const routerQuery = this.$route.query || {}
      this.articleId = routerQuery.id || ''
      this.getArticleData()
    },
    getArticleData() {
      getArticleInfo(this.articleId).then(response => {
        this.dataInfo = response.data || {}
      })
    }
  },
}
</script>
<style lang="scss" scoped>
//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {
  .pc_show{}
  .mob_show{
    display: none;
  }
  ::v-deep .login_head_pc {
    position: relative;
  }
  ::v-deep .login_foot_pc {
    position: relative;
    margin-top: 30px;
  }

  .login {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .login_body_pc {
      z-index: 1;
      width: 80%;
      min-width: 1440px;
      margin-left: 50%;
      transform: translateX(-50%);
      justify-content: center;
      align-items: center;
      background: url('../../../public/image/html_bg.png') no-repeat 100% 100%;
      padding: 10px 0;
      .main_box {
        width: 1200px;
        height: auto;
        min-height: 640px;
        background: rgba(255, 255, 255, 0.62);
        box-shadow: 0px 10px 30px rgba(93, 132, 177, 0.11);
        border-radius: 0 0 16px 16px;
        padding: 40px 45px;
        margin-top: 0px;
        margin-left: 50%;
        transform: translateX(-50%);

        .className {
          width: 100%;
          height: auto;
          font-size: 34px;
          line-height: 40px;
          margin-bottom: 15px;
          color: #000000;
        }

        .classNameSub {
          width: 100%;
          height: auto;
          font-size: 18px;
          color: #666;
        }

        .content {
          width: 100%;
          height: auto;
          padding: 25px 0;
          border-top: 5px solid #f1f1f1;
          margin-top: 25px;
          font-size: 16px;
        }
      }
    }
  }
  .choose_foot {
    display: none;
  }
}
//移动端
@media screen and (max-width: 720px) {
  .pc_show{
    display: none;
  }
  .top_layout,.foot_layout{
    display: none;
  }
  .mob_show{
  }
  .login {
    width: 100%;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .login_body_pc {
      z-index: 1;
      width: 100%;
      .main_box {
        width: calc(100% - 2rem);
        height: auto;
        background: rgba(255, 255, 255, 0.62);
        box-shadow: 0px 0.5rem 1rem rgba(93, 132, 177, 0.11);
        border-radius: 0.8rem;
        padding: 1.5rem 1.5rem;
        margin-left: 50%;
        margin-top: 6rem;
        transform: translateX(-50%);

        .className {
          width: 100%;
          height: auto;
          font-size: 1.88rem;
          line-height: 2.2rem;
          margin-bottom: 1rem;
          color: #000000;
        }

        .classNameSub {
          width: 100%;
          height: auto;
          font-size: 1rem;
          color: #666;
        }

        .content {
          width: 100%;
          height: auto;
          padding: 1.2rem 0;
          border-top: 5px solid #f1f1f1;
          margin-top: 1.2rem;
          font-size: 1.2rem;
        }
      }
    }
  }

}
</style>
