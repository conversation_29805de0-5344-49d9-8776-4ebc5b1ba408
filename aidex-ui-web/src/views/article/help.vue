<template>
  <div class="home">
    <Topbox class="top_layout" /><!--顶部-->

    <div class="main_box" style="margin-top: 30px;">
      <div class="className">
        <span class="titlename">使用帮助</span>
      </div>
      <div class="content" v-if="tab === '1'">
        <div>
          <p style="font-weight: bold;">幼升小操作手册：</p>
          <p style="margin-top: 4px;"><a style="cursor: pointer;" href="/help/version/jz.pdf" target="_blank">教育入学一件事(幼升小)-家长端操作手册.pdf</a></p>
          <p style="margin-top: 4px;"><a style="cursor: pointer;" href="/help/version/jz.mp4" target="_blank">教育入学一件事(幼升小)-家长端操作视频.mp4</a></p>
          <p style="margin-top: 4px;"><a style="cursor: pointer;" href="/help/version/sj.pdf" target="_blank">教育入学一件事(幼升小)-市级管理员操作手册.pdf</a></p>
          <p style="margin-top: 4px;"><a style="cursor: pointer;" href="/help/version/sj.mp4" target="_blank">教育入学一件事(幼升小)-市级管理员操作视频.mp4</a></p>
          <p style="margin-top: 4px;"><a style="cursor: pointer;" href="/help/version/qx.pdf" target="_blank">教育入学一件事(幼升小)-区县管理员操作手册.pdf</a></p>
          <p style="margin-top: 4px;"><a style="cursor: pointer;" href="/help/version/qx.mp4" target="_blank">教育入学一件事(幼升小)-区县管理员操作视频.mp4</a></p>
          <p style="margin-top: 4px;"><a style="cursor: pointer;" href="/help/version/xxgl.pdf" target="_blank">教育入学一件事(幼升小)-学校管理员操作手册.pdf</a></p>
          <p style="margin-top: 4px;"><a style="cursor: pointer;" href="/help/version/xxgl.mp4" target="_blank">教育入学一件事(幼升小)-学校管理员操作视频.mp4</a></p>
<!--          <p style="margin-top: 4px;"><a style="cursor: pointer;" href="/help/version/xxsh.pdf" target="_blank">教育入学一件事-学校审核员操作手册.pdf</a></p>-->
          <p style="margin-top: 4px;"><a style="cursor: pointer;" href="/help/version/xfzh.pdf" target="_blank">教育入学一件事(幼升小)-市州及区县管理员下发账号操作指引.pdf</a></p>
          <br/>
          <p style="font-weight: bold;">小升初操作手册：</p>
          <p style="margin-top: 4px;"><a style="cursor: pointer;" href="/help/xsc/version/1/jz.pdf" target="_blank">教育入学一件事(小升初)-家长端操作手册.pdf</a></p>
          <p style="margin-top: 4px;"><a style="cursor: pointer;" href="/help/xsc/version/1/qx.pdf" target="_blank">教育入学一件事(小升初)-区县管理员操作手册.pdf</a></p>
          <p style="margin-top: 4px;"><a style="cursor: pointer;" href="/help/xsc/version/1/xx.pdf" target="_blank">教育入学一件事(小升初)-学校管理员操作手册.pdf</a></p>
          <p style="margin-top: 4px;"><a style="cursor: pointer;" href="/help/version/xfzh.pdf" target="_blank">教育入学一件事(小升初)-市州及区县管理员下发账号操作指引.pdf</a></p>

          <br/>
          <p style="font-weight: bold;">常见问题：</p>
<!--          <p style="margin-top: 4px;"><a style="cursor: pointer;" href="/help/version/wt.pdf" target="_blank">教育入学一件事常见问题.pdf</a></p>-->
          <p style="margin-top: 4px;"><a style="cursor: pointer;" href="/help/version/jzcjwt.pdf" target="_blank">教育入学一件事家长常见问题.pdf</a></p>
          <p style="margin-top: 4px;"><a style="cursor: pointer;" href="/help/version/qksm.pdf" target="_blank">数据来源与政务服务网的情况说明.pdf</a></p>
          <p style="margin-top: 4px;"><a style="cursor: pointer;" href="/help/version/bdtxsm.pdf" target="_blank">报名表单填写说明.pdf</a></p>
        </div>
      </div>
    </div>
    <Footbox class="foot_layout" />
    <foot />

  </div>
</template>

<script>
import top from '../../components/top/top.vue'
import foot from '../../components/foot/foot.vue'
import Topbox from "@/views/layout/newtop";
import Footbox from "@/views/layout/foot";
export default {
  components: {
    top,foot,
    Topbox,
    Footbox
  },
  data() {
    return {
      tab:'1',
      dataInfo: {
        name: '',
        description: '',
        enrollmentScheme: '',
        scribingScheme: '',
        enrollmentSchool: ''
      },
      areaCode: '',
      areaName: '',
      imageUrlshow:false,
    }
  },
  mounted() {},
  beforeDestroy() {},
  created() {

  },

  methods: {

  },
}
</script>
<style lang="scss" scoped>
//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {
  .imshow{
    position: fixed;
    inset:0;
    z-index: 999999;
    background: rgba(0,0,0,.5);
    display: flex;
    justify-content: center;
    align-items: center;
    img{
      max-width: 80%;
      max-height: 80%;
    }
  }

  .home {
    width: 100%;
    display: flex;
    flex-direction: column;
    background: #eef6ff;

    .main_box{
      width: 1200px;
      height: auto;
      background: rgba(255, 255, 255, 0.62);
      box-shadow: 0px 10px 30px rgba(93, 132, 177, 0.11);
      border-radius: 16px 16px 16px 16px;
      padding:40px 45px;
      margin-top: 0px;
      margin-left: 50%;
      transform: translateX(-50%);
      .className{
        width: 100%;
        height: auto;
        font-size: 34px;
        line-height: 40px;
        margin-bottom: 15px;
        color: #000000;
        display: flex;
        align-items: center;
        .titlename{
          font-size: 34px;
          line-height: 40px;
          margin-right: 20px;
        }
        .top_tab{
          display: flex;
          gap: 20px;
          align-items: center;
          transform: translateY(-5px);
          span{
            cursor: pointer;
            display: flex;
            width: auto;
            height: 40px;
            justify-content: center;
            align-items: center;
            padding:0 25px;
            background: #fafafa;
            border-radius: 100px;
            border: 1px solid #f1f1f1;
            font-size: 16px;
            color: #2050bb;
            box-shadow: 0 5px 15px rgba(0,0,0,.15);
            transition: ease-in-out .2s all;
            &:hover{
              box-shadow: 0 2px 4px rgba(0,0,0,.3);
              transform: translateY(5px);
            }
            &.now{
              color: #ffffff;
              font-weight: bold;
              background: #3570F6;
              border: 1px solid #3570F6;
              box-shadow: 0 2px 4px #3570F633;
              transform: translateY(5px);
            }
          }
        }
      }
      .classNameSub{
        width: 100%;
        height: auto;
        font-size: 18px;
        color: #666;
      }

      .content{
        width: 100%;
        height: auto;
        padding:25px 0;
        border-top: 5px solid #f1f1f1;
        margin-top: 25px;
        font-size: 16px;
        min-height: 600px;
        ::v-deep img{
          width: auto!important;
          height: auto!important;
          max-width: 100%;
        }
      }
      .siteBox{
        position: absolute;
        top: 0;
        right: 0;
        background: rgba(0,0,0,0.6);
        padding:8px 20px;
        border-radius: 0 12px 0 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        width: auto;
        z-index: 3;
        span{
          color: #ffffff;
          font-size: 16px;
          font-weight: bold;
          cursor: pointer;
        }
        img{
          width: 24px;
          height: 18px;
          margin-left: 8px;
          cursor: pointer;
        }
      }
    }

    .choose_foot {
      display: none;
    }
  }
}
//移动端
@media screen and (max-width: 720px) {


  .imshow{
    position: fixed;
    inset:0;
    z-index: 999999;
    background: rgba(0,0,0,.3);
    display: flex;
    justify-content: center;
    align-items: center;
    img{
      max-width: 100%;
      max-height: 100%;
    }
  }
  .top_layout,.foot_layout{
    display: none;
  }
  .home {
    width: 100%;
    display: flex;
    flex-direction: column;
    background: #eef6ff;

    .main_box{
      width: calc(100% - 2rem);
      height: auto;
      background: rgba(255, 255, 255, 0.62);
      box-shadow: 0px 0.5rem 1rem rgba(93, 132, 177, 0.11);
      border-radius: 0.8rem;
      padding:1.5rem 1.5rem;
      margin-top: 30px;
      margin-left: 50%;
      margin-top: 6rem;
      transform: translateX(-50%);
      .className{
        width: 100%;
        height: auto;
        font-size: 1.8rem;
        line-height: 2.2rem;
        margin-bottom: 1rem;
        margin-top: 1rem;
        color: #000000;
        .top_tab{
          display: flex;
          gap: 0.8rem;
          align-items: center;
          transform: translateY(-5px);
          margin-top: 1rem;
          flex-wrap: wrap;
          justify-content: space-between;
          span{
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            flex-wrap: wrap;
            width: 20%;
            height: auto;
            line-height: 1.1rem;
            justify-content: center;
            align-items: center;
            padding:0.5rem 0.3rem;
            background: #fafafa;
            border-radius: 0.2rem;
            border: 1px solid #f1f1f1;
            font-size: 1rem;
            color: #2050bb;
            box-shadow: 0 5px 15px rgba(0,0,0,.15);
            transition: ease-in-out .2s all;
            &:hover{
              box-shadow: 0 2px 4px rgba(0,0,0,.3);
              transform: translateY(5px);
            }
            &.now{
              color: #ffffff;
              background: #3570F6;
              border: 1px solid #3570F6;
              box-shadow: 0 2px 4px #3570F633;
              transform: translateY(5px);
            }
          }
        }
      }
      .classNameSub{
        width: 100%;
        height: auto;
        font-size: 1rem;
        color: #666;
      }
      .content{
        width: 100%;
        height: auto;
        padding:1.2rem 0;
        border-top: 5px solid #f1f1f1;
        margin-top: 1.2rem;
        font-size: 1.2rem;
        ::v-deep img{
          width: auto!important;
          height: auto!important;
          max-width: 100%;
        }
      }
    }
    .siteBox{
      position: absolute;
      top: 0;
      right: 0;
      background: rgba(0,0,0,0.6);
      padding:0 0.6rem;
      border-radius: 0 0.8rem 0 0.8rem;
      cursor: pointer;
      display: flex;
      align-items: center;
      width: auto;
      z-index: 3;
      span{
        color: #ffffff;
        font-size: 0.8rem;
        font-weight: bold;
        cursor: pointer;
      }
      img{
        width: 0.8rem;
        height: auto;
        margin-left: 0.5rem;
        cursor: pointer;
      }
    }

  }

}

</style>
