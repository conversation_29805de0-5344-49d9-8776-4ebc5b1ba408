<template>
  <div class="choose">
    <Topbox class="top_layout" /><!--顶部-->
    <top />

    <div class="choose_body">
      <div class="body_middle">
<!--        <div class="middle_top">-->
<!--          <div>-->
<!--            <el-steps :active="2" align-center>-->
<!--              <el-step title="注册"></el-step>-->
<!--              <el-step title="选择入学区域"></el-step>-->
<!--              <el-step title="选择报名学校"></el-step>-->
<!--              <el-step title="提交报名资料"></el-step>-->
<!--            </el-steps>-->
<!--          </div>-->
<!--        </div>-->
<!--        <div class="border_top"></div>-->
        <div class="mddle_center">
          <div class="title_box">
            <div class="title_left">
              <div class="title">选择要报名的学校</div>
<!--              <div class="xiala">-->
<!--                <el-select v-model="region">-->
<!--                  <el-option label="七里河区" value="shanghai"></el-option>-->
<!--                  <el-option label="城关区" value="beijing"></el-option>-->
<!--                </el-select>-->
<!--                <div class="sousuo1">-->
<!--                  <input type="text" placeholder="学校快捷搜索" />-->
<!--                  <img src="../../../public/image/search.svg" alt="" />-->
<!--                </div>-->
<!--              </div>-->
            </div>
            <div class="title_right">
                <input type="text" placeholder="学校快捷搜索" v-model="searchInput" />
                <img src="../../../public/image/search.svg" alt="" @click="handleSearch" />
            </div>
          </div>
          <div style="margin-bottom: 15px;">
            <el-alert
                class="custom-alert"
                title="请选择要报名的学校，并点击“下一步“"
                type="warning"
                :closable="false"
                show-icon>
            </el-alert>
          </div>
          <div class="tabble">
            <el-table
              v-loading="loading"
              ref="singleTable"
              :data="tableData"
              tooltip-effect="dark"
              style="width: 100%"
              @current-change="handleCurrentChange"
              highlight-current-row
            >
              <el-table-column label="选择" width="60" align="center">
                <template slot-scope="scope">
                  <el-radio class="radio" v-model="singleSelected.id" :label="scope.row.id">&nbsp;</el-radio>
                </template>
              </el-table-column>
              <el-table-column
                prop="deptName"
                label="学校名称"
                style="width:100px"
              ></el-table-column>
              <el-table-column
                prop="address"
                label="学校地址"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ scope.row.address || '-/-' }}
                </template>
              </el-table-column>
              <el-table-column
                prop="scribingArea"
                label="划片区域"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ scope.row.scribingArea || '-/-' }}
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
                v-show="total > 0"
                small
                layout="prev, pager, next"
                :current-page.sync="pageNum"
                :page-size.sync="pageSize"
                :total="total"
                @current-change="handlePageCurrentChange">
            </el-pagination>
          </div>
        </div>
        <div class="border_top"></div>
        <div class="middle_bottom">
          <div class="button" @click="tochoose()">上一步：选择报名入口</div>
          <div class="button" @click="tosubmit()">下一步：填写报名资料</div>
        </div>
      </div>
    </div>
    <Footbox class="foot_layout" />
    <foot />
  </div>
</template>

<script>
import top from '../../components/top/top.vue'
import foot from '../../components/foot/foot.vue'
import { getApplySchoolList } from '@/api/apply'
import Topbox from "@/views/layout/newtop";
import Footbox from "@/views/layout/foot";
export default {
  components: {
     top,foot,
    Topbox,
    Footbox
  },
  data() {
    return {
      region: '',
      tableData: [],
      singleSelected: {},
      routerQuery: {},
      searchInput: '',
      total: 0,
      pageNum: 1,
      pageSize: 10,
      // 遮罩层
      loading: true,
    }
  },
  mounted() {},
  beforeDestroy() {},
  created() {
    this.routerQuery = this.$commonUtils.getApplyParams(this.$constants.STORE_CACHE_KEY_APPLY) || {}
    this.getSchoolData()
  },
  methods: {
    getSchoolData() {
      const area = this.routerQuery.areaId || this.routerQuery.cityId
      const type = this.routerQuery.type
      const nature = this.routerQuery.nature
      const name = this.searchInput
      if (area && type) {
        const queryParams = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          areaId: area,
          deptClassify: type,
          deptNature: nature,
          deptName: name
        }
        this.loading = true;
        getApplySchoolList(queryParams).then(response => {
          const data = response.data || {}
          const list = data.list || []
          const total = data.total || 0
          this.loading = false;
          if (this.routerQuery.schoolId) {
            const obj = list.find(item => item.id === this.routerQuery.schoolId)
            if (obj) {
              this.singleSelected.id = obj.id
              this.singleSelected.name = obj.deptName
            }
          }
          this.tableData = list
          this.total = total
        })
      }
    },
    handleCurrentChange(val) {
      this.singleSelected = { id: val.id, name: val.deptName };
    },
    tosubmit(){
      this.routerQuery.schoolId = this.singleSelected.id || undefined
      this.routerQuery.schoolName = this.singleSelected.name || undefined
      if (this.tableData && this.tableData.length > 0 && this.routerQuery.schoolId) {
        if (!this.routerQuery.operate) {
          this.routerQuery.operate = 'add'
        }
        let routerPath = 'policySubmit'
        // if (this.routerQuery.type === this.$constants.SCHOOL_XX) {
        //   routerPath = "/submit"
        // } else if (this.routerQuery.type === this.$constants.SCHOOL_CZ) {
        //   routerPath = "/submit"
        // } else {
        //   this.$message.error("系统异常，请联系管理员!")
        //   return
        // }
        this.$commonUtils.setApplyParams(this.routerQuery, this.$constants.STORE_CACHE_KEY_APPLY)
        this.$router.push({path: routerPath})
      } else {
        this.$message.warning("请选择报名学校")
      }
    },
    tochoose(){
      this.$commonUtils.setApplyParams(this.routerQuery, this.$constants.STORE_CACHE_KEY_APPLY)
      this.$router.push({path: "/index"})
    },
    handleSearch() {
      this.pageNum = 1
      this.getSchoolData()
    },
    handlePageCurrentChange(val) {
      this.pageNum = val
      this.getSchoolData()
    }
  },
}
</script>
<style lang="scss" scoped>
//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {
  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .choose_body {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 0;
      .body_middle {
        width: 1200px;
        min-height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 10px 30px 1px rgba(93,132,177,0.11);
        border-radius: 0 0 16px 16px;
        .middle_top {
          width: 100%;
          margin: 40px 0px;
        }
        .border_top {
          width: 1155px;
          margin: 0px 22px;
          height: 7px;
          background: #f1f1f1;
          border-radius: 12px 12px 12px 12px;
          opacity: 1;
        }
        .mddle_center {
          width: 95%;
          margin: 0 2.5% 2.5% 2.5% ;
          height: auto;
          display: flex;
          flex-direction: column;
          .title_box {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 25px 0;
            .title {
              width: 176px;
              height: 29px;
              font-size: 22px;
              font-weight: bold;
              color: #000000;
            }
            .title_left {
              display: flex;
              .xiala {
                .sousuo1{
                  display: none;
                }
                margin-left: 14px;
                color: #3570F6;
              ::v-deep .el-input__inner{
                border: none;
                width: 200px;
                height: 29px;
                line-height: 29px;
                font-size: 22px;
                font-weight: 400;
                color: #3570F6;
              }
              ::v-deep .el-select__caret{
                font-size: 18px;
                color: #3570F6;
                 height: 29px;
                line-height: 29px;
              }
              }
            }
            .title_right {
              width: 244px;
              height: 41px;
              line-height: 41px;
              display: flex;
              justify-content: center;
              align-items: center;
              background: #f1f1f1;
              border-radius: 21px 21px 21px 21px;
              input {
                width: 170px;
                height: 31px;
                line-height: 31px;
                font-size: 16px;
                font-weight: 400;
                color: #383838;
                border: none;
                outline: none;
                background: none;
              }
              img {
                width: 19px;
                height: 19px;
                margin-left: 10px;
              }
            }
          }
          ::v-deep .tabble {
            height: auto;
           .el-table .el-table__header .has-gutter tr th{
            background: #3570F6;
            border: 1px solid #fff;
            color: #fff;
           }
            thead{
              .el-table__cell{
                background: #3570F6;
                color: #fff;
              }
            }
           .el-table{
             font-size: 16px;
             .cell.el-tooltip{
               white-space: normal;
               th{
                 font-size: 16px;
               }
             }
           }
           .el-table .el-table__body-wrapper .el-table__body tr:nth-child(2n) td{
            background: #F0F5FF;
            border: 1px solid #fff;
           }
           .el-checkbox__inner{
             width: 20px;
             height: 20px;
            border-radius: 1000px;
             &::after{
               left: 50%!important;
               top: 50%!important;
               transform: translateY(-60%) translateX(-50%) rotate(45deg) scaleY(1);
             }
             &::before{
               left: 50%!important;
               top: 50%!important;
               transform: translateY(-60%) translateX(-50%) scaleY(1);
             }
           }
          }
        }
        .middle_bottom {
          width: 100%;
          height: 100px;
          display: flex;
          text-align: center;
          justify-content: center;
          align-items: center;
          .button {
            width: 240px;
            height: 59px;
            line-height: 59px;
            background: #3570f6;
            border-radius: 10px 10px 10px 10px;
            opacity: 1;
            color: #fff;
            margin-left: 18px;
          }
        }
      }
    }
    .choose_foot {
      z-index: 2;
      width: 100%;
      height: 80px;
      display: none;
      justify-content: center;
      align-items: center;
    }
  }
  ::v-deep .custom-alert .el-alert__content .el-alert__title {
    font-size: 18px !important;
  }
}
// 移动端
@media screen and (max-width: 720px) {
  .top_layout,.foot_layout{
    display: none;
  }
  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    margin-top: 5.8rem;
    .choose_body {
    width: calc(100% - 2rem);
      margin-left: 1rem;
      flex: 1;
      display: flex;
      justify-content: center;
      margin-top: 0.2rem;
      .body_middle {
       width: 100%;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 0.5rem 1rem rgba(93,132,177,0.11);
        border-radius: 1rem;
        padding:0 0 1rem 0;
        .middle_top {
          display: none;
        }
        .border_top {
          display: none;
        }
        .mddle_center {
       width: calc(100% - 1rem);
        margin-left: 0.5rem;
          height: auto;
          display: flex;
          flex-direction: column;
          .title_box {
            width:100%;
            display: flex;
            flex-direction: column;
            padding:1rem;
            .title {
              width: 100%;
              height: 2rem;
              font-size: 1.6rem;
              font-weight: bold;
              color: #000000;
            }
            .title_left {
              display: flex;
              flex-direction: column;
              .xiala {
                margin: 0.5rem 0;
                width:100%;
                color: #3570F6;
                display: flex;
              ::v-deep .el-input__inner{
                display: flex;
                border: none;
                width:100%;
                height: 2rem;
                font-size: 1.2rem;
                color: #3570F6;
              }
              ::v-deep .el-select__caret{
                font-size: 1.4rem;
                color: #3570F6;
                 height: 2.2rem;
              }
              }
            }
            .title_right {
              width: 100%;
              height: auto;
              display: flex;
              justify-content: space-between;
              align-items: center;
              background: #f1f1f1;
              border-radius: 0.4rem;
              padding:0 1rem;
              margin-top: 0.5rem;
              input {
                width: calc(100% - 2rem);
                height: 3rem;
                font-size: 1.2rem;
                font-weight: 400;
                color: #383838;
                border: none;
                outline: none;
                background: none;
              }
              img {
                width: 1.5rem;
                height: 1.5rem;
              }
            }
          }
          ::v-deep .tabble {
            height: auto;
            .el-table .el-table__header .has-gutter tr th{
              background: #3570F6;
              border: 1px solid #fff;
              color: #fff;
            }
            thead{
              .el-table__cell{
                background: #3570F6;
                color: #fff;
              }
            }
            .el-table{
              font-size: 1.1rem;
              .cell.el-tooltip{
                white-space: normal;
                th{
                  font-size: 1.1rem;
                }
              }
            }
            .el-table .el-table__body-wrapper .el-table__body tr:nth-child(2n) td{
              background: #F0F5FF;
              border: 1px solid #fff;
            }
            .el-checkbox__inner{
              width: 1.6rem;
              height: 1.6rem;
              border-radius: 1000px;
              &::after{
                left: 50%!important;
                top: 50%!important;
                transform: translateY(-60%) translateX(-50%) rotate(45deg) scaleY(1);
              }
              &::before{
                left: 50%!important;
                top: 50%!important;
                transform: translateY(-60%) translateX(-50%) scaleY(1);
              }
            }
          }

        }
        .middle_bottom {
          width: 100%;
          height: auto;
          display: flex;
          margin-top: 1rem;
          justify-content: space-between;
          flex-flow: column;
          padding:0 1rem;
          gap: 1rem;

          .button {
            width: 100%;
            height: 3.4rem;
            background: #3570f6;
            border-radius: 0.6rem;
            text-align: center;
            line-height: 3.4rem;
            color: #fff;
          }
        }
      }
    }
    .choose_foot {
       width: 100%;
      height:auto;
      display: flex;
      justify-content: center;
      align-items: center;
      padding:1.5rem 1.2rem;
    }
  }
  ::v-deep .custom-alert .el-alert__content .el-alert__title {
    font-size: 1.2rem !important;
  }
}
::v-deep .is-finish{
  color: #383838;
  font-size: 18px;
  .el-step__line{
    background: #3570F6;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #3570F6;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    border-color: #3570F6;
  }
}

::v-deep .is-process{
  color: #383838;
  font-size: 18px;
  font-weight: normal;
  .el-step__line{
    background: #f1f1f1;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #ffffff;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    border-width: 5px;
    color: #3570F6;
    border-color: #3570F6;
  }
}

::v-deep .is-wait{
  color: #383838;
  font-size: 18px;
  font-weight: normal;
  .el-step__line{
    background: #f1f1f1;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #EEF6FF;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    border-width: 5px;
    color: #3570F6;
    border-color: #EEF6FF;
  }
}
</style>
