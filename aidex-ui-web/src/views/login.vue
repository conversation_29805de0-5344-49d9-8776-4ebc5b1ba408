<template>
  <div class="login">
    <top />
    <div class="login_body_pc">
      <div class="body_left">
        <img src="../../public/image/<EMAIL>" alt="" />
      </div>
      <div class="body_right">
        <div class="right_top">登录系统</div>
        <div class="right_bottom">
          <el-form
            :model="ruleForm"
            status-icon
            :rules="rules"
            ref="ruleForm"
            class="demo-ruleForm"
          >
            <el-form-item prop="username">
              <el-input
                type="text"
                placeholder="手机号"
                v-model="ruleForm.username"
                autocomplete="off"
              ></el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                type="password"
                placeholder="密  码"
                v-model="ruleForm.password"
                autocomplete="off"
                show-password
              ></el-input>
            </el-form-item>
            <el-form-item prop="code">
              <el-input
                placeholder="验证码"
                v-model="ruleForm.code"
              ></el-input>
            </el-form-item>
            <div class="btnBox">
              <img class="button_box" :src="codeUrl" @click="getCaptchaCode">
            </div>
            <el-form-item prop="smsCode" v-if="smsCaptchaOnOff">
              <el-input
                  placeholder="短信验证码"
                  v-model="ruleForm.smsCode"
              ></el-input>
            </el-form-item>
            <div class="btnBox" v-if="smsCaptchaOnOff">
              <el-button
                  class="button_box button_box1"
                  type="primary"
                  size="small"
                  :disabled="isSend"
                  @click="getCode"
              >
                {{ codeName }}
              </el-button>
            </div>

            <el-button type="primary" :loading="loading" @click="submitForm('ruleForm')">
              登录
            </el-button>
          </el-form>
          <div class="body_bottom">
            <span @click="toRegister">立即注册</span>
            <span @click="toRevise">忘记密码？</span>
          </div>
        </div>
      </div>
    </div>
    <foot />
  </div>
</template>

<script>
import top from '@/components/layoutBox/top'
import foot from '@/components/layoutBox/foot'
import { getCodeImg, getSmsCaptcha } from '@/api/login'
import { getSysConfig } from '@/api/common'
import { rsaEncrypt } from '@/utils/rsaUtil'
import { mapActions } from 'vuex'
import { timeFix } from '@/utils/util'
export default {
  components: {
    top,
    foot
  },
  data() {
    var validatePhone = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入手机号码'))
      } else {
        callback()
      }
    }
    // 密码
    var validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        callback()
      }
    }
    var checkyzm = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入验证码'))
      } else {
        callback()
      }
    }
    var checkSmsCode = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入短信验证码'))
      } else {
        callback()
      }
    }
    return {
      isSend: false, //禁用
      codeName: '获取验证码',
      totalTime: 60, //一般是60
      timer: '', //定时器
      ruleForm: {
        username: '',
        password: '',
        code: '',
        uuid: '',
        phoneNumber: undefined,
        smsUuid: undefined,
        smsCode: undefined
      },
      rules: {
        username: [{ validator: validatePhone, trigger: 'blur' }],
        password: [{ validator: validatePass2, trigger: 'blur' }],
        code: [{ validator: checkyzm, trigger: 'blur' }],
        smsCode: [{ validator: checkSmsCode, trigger: 'blur' }],
      },
      loading: false,
      codeUrl: '',
      publicKey: '',
      smsCaptchaOnOff: false,
    }
  },
  mounted() {},
  beforeDestroy() {},
  created() {
    this.getSysConfigData()
    this.getCaptchaCode()
  },
  methods: {
    ...mapActions(['Login']),
    getSysConfigData () {
      getSysConfig().then(response => {
        if (response && response.publicKey) {
          this.publicKey = response.publicKey
        }
        this.smsCaptchaOnOff = (response && response.frontSmsCaptchaOnOff && response.frontSmsCaptchaOnOff === true)
      })
    },
    getCaptchaCode() {
      getCodeImg().then(res => {
        this.codeUrl = 'data:image/gif;base64,' + res.img
        this.ruleForm.uuid = res.uuid
      })
    },
    getCode() {
      if (this.isSend) return
      const phoneNumber = this.ruleForm.username
      if (!phoneNumber) {
        this.$message.warning("请输入手机号", 3)
        return;
      }
      this.ruleForm.smsUuid = undefined
      this.ruleForm.smsCode = undefined
      getSmsCaptcha({phoneNumber: phoneNumber}).then(response => {
        if (response && response.code === 200) {
          this.$message.success("短信验证码已发送，可能会有延后，请耐心等待")
          this.ruleForm.smsUuid = response ? response.uuid : ''
          this.countDown()
        }
      })
    },
    countDown() {
      this.isSend = true
      this.timer = setInterval(() => {
        this.totalTime--
        this.codeName = '重新发送(' + this.totalTime + ')s'
        if (this.totalTime < 0) {
          clearInterval(this.timer)
          this.codeName = '重新发送验证码'
          this.totalTime = 60
          this.isSend = false
        }
      }, 1000)
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.loading = true
          const loginForm = JSON.parse(JSON.stringify(this.ruleForm))
          loginForm.phoneNumber = loginForm.username
          // 加密账户
          loginForm.username = rsaEncrypt(loginForm.username + '', this.publicKey) || ''
          // 加密密码
          loginForm.password = rsaEncrypt(loginForm.password, this.publicKey) || ''
          loginForm.authorize_channel = 'front-oauth'
          this.Login(loginForm)
            .then((res) => this.loginSuccess(res))
            .catch(err => this.loginFailed(err))
            .finally(() => {
              this.loading = false
            })
        } else {
          setTimeout(() => {
            this.loading = false
          }, 600)
          return false
        }
      })
    },
    loginSuccess (res) {
      if (res && res.code === 200) {
        this.$router.push({path: '/'})
        // 延迟 1 秒显示欢迎信息
        setTimeout(() => {
          this.$notify.success({
            title: '欢迎',
            message: `${timeFix()}，欢迎回来`,
            showClose: true
          })
        }, 1000)
      }
    },
    loginFailed (err) {
      this.ruleForm.code = ''
      this.getCaptchaCode()
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    // 点击跳转
    mouseOver(index) {
      this.currentIndex = index
    },
    toRegister() {
      this.$router.push({
        path: '/register'
      })
    },
    toRevise() {
      this.$router.push({
        path: '/revise'
      })
    },
  },
}
</script>
<style lang="scss" scoped>
//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {
  .pc_show{}
  .mob_show{
    display: none;
  }
  .login {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .login_body_pc {
      position: fixed;
      top: 140px;
      bottom: 80px;
      z-index: 1;
      width: 80%;
      min-width: 1440px;
      margin-left: 50%;
      transform: translateX(-50%);
      display: flex;
      justify-content: center;
      align-items: center;
      background: url('../../public/image/html_bg.png') no-repeat 100% 100%;
      padding: 10px 0;
      .body_left {
        width: calc(100% - 613px);
        height: auto;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: auto;
          height: 32rem;
          transform: translateY(35px);
        }
      }
      .body_right {
        width: 513px;
        height: auto;
        background: rgba(255, 255, 255, 0.62);
        box-shadow: 0px 10px 30px 1px rgba(93, 132, 177, 0.11);
        border-radius: 16px 16px 16px 16px;
        display: flex;
        flex-direction: column;
        justify-content:flex-start;
        align-items: center;
        padding: 0 0 20px 0;
        .right_top {

          width: 100%;
          height: auto;
          font-size: 28px;
          font-weight: bold;
          color: #383838;
          padding: 56px 0px 24px 60px;
        }
        .right_bottom {
          width: 418px;
          display: flex;
          flex-direction: column;
          ::v-deep .el-form-item__content {
            width: 418px;
            height: 59px;
          }

          ::v-deep .el-input__inner {
            width: 418px;
            height: 59px;
            font-size: 18px;
            font-weight: 400;
            color: #383838;
            border-radius: 10px;
          }
          button {
            width: 418px;
            height: 59px;
            margin: 0;
            color: #fff;
            font-size: 18px;
            font-weight: 400;
            margin-top: 20px;
            background: #3570F6;
            border-radius: 10px;
          }
          .is-disabled{
            opacity: .6;
          }
          .btnBox{
            width: 100%;
            height: auto;
            background: #ccc;
            position: relative;
            .button_box {
              width: 150px;
              height: 46px;
              position: absolute;
              right: 10px;
              top: -74px;
              text-align: center;
              border: none;
            }
            .button_box1 {
              top: -95px;
            }
          }

        }
      }
      .body_bottom {
        width: 418px;
        height: 59px;
        display: flex;
        justify-content: space-between;
        color: #3570f6;
        font-size: 18px;
        margin: 10px 0;
      }
    }
  }
}
//移动端
@media screen and (max-width: 720px) {

  .pc_show{
    display: none;
  }
  .mob_show{
  }
  .login {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: #eef6ff;
    .login_body_pc {
      z-index: 1;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0.5rem 0;
      .body_left {
        display: none;
      }
      .body_right {
        width: calc(100% - 2.4rem);
        height: auto;
        border-radius: 100px;
        display: flex;
        flex-direction: column;
        justify-content:flex-start;
        align-items: center;
        .right_top {
          display: none;
        }
        .right_bottom {
          width: 90%;
          display: flex;
          flex-direction: column;
          ::v-deep .el-form-item{
            margin-bottom: 1.2rem;
          }
          ::v-deep .el-form-item__content {
            width: 100%;
            height: 3.6rem;
            line-height: 3.6rem;
            font-size: 1.2rem;
          }

          ::v-deep .el-input__inner {
            width: 100%;
            height: 3.6rem;
            font-size: 1.2rem;
            font-weight: 400;
            color: #383838;
            border-radius: 0.6rem;
          }
          button {
            width: 100%;
            height: 3.6rem;
            margin: 0;
            color: #fff;
            font-size: 1.2rem;
            font-weight: 400;
            background: #3570F6;
            border-radius: 0.6rem;
          }
          .is-disabled{
            opacity: .6;
          }
          .btnBox{
            width: 100%;
            height: auto;
            background: #ccc;
            position: relative;
            .button_box {
              width: 9rem;
              height: 3rem;
              line-height: 3rem;
              padding: 0;
              position: absolute;
              right: 0.4rem;
              top: -4.5rem;
              text-align: center;
              font-size: 1.1rem;
            }
          }

        }
      }
      .body_bottom {
        width: 100%;
        height: 2.4rem;
        display: flex;
        justify-content: space-between;
        color: #3570f6;
        font-size: 1.2rem;
        margin:0.5rem 0 0 0;
      }
    }
  }
}
</style>
