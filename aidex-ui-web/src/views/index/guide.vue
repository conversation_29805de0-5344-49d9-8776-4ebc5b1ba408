<template>
  <div class="home">
    <Topbox class="top_layout" /><!--顶部-->
    <div class="home_head">
      <div class="head_left">
        <img src="../../../public/image/new_logo.svg" alt="" />
      </div>
    </div>

    <div class="home_body1">

      <div class="topLogo">
        <img class="logo_text" src="../../../public/image/logo_text.svg" />
      </div>
      <div class="cityBox">
          <div class="district_box">
            <div class="district_left">
              <div class="district_title">请选择办理业务</div>
              <a href="https://zwfw.gansu.gov.cn/gsspace/unscramble?unscrambleId=662&region=620000000000" target="_blank">幼升小在线报名</a>
              <a href="https://zwfw.gansu.gov.cn/gsspace/unscramble?unscrambleId=662&region=620000000000" target="_blank">小升初在线报名</a>
              <a @click="prompt()">投诉反馈</a>
            </div>
          </div>
      </div>

    </div>
    <Footbox class="foot_layout" />
    <div class="home_foot">
      <Footbox />
    </div>
  </div>
</template>

<script>
import Topbox from "../layout/newtop";
import Footbox from "../layout/foot";
import foot from '../../components/foot/foot.vue'
import {logout} from '@/utils/logout'
import { userMessageList } from '@/api/msgSubject'

  export default {
  components: {
    foot,
    Topbox,
    Footbox
  },
  data() {
    return {
      region: '',
      messageTotal: 0,
      queryParam: {
        pageNum: 1,
        pageSize: 10
      },
      isToken: false,
      routerQuery: {},
    }
  },
  mounted() {},
  beforeDestroy() {},
  created() {
    this.routerQuery = this.$commonUtils.getApplyParams(this.$constants.STORE_CACHE_KEY_APPLY) || {}
    this.isToken = this.checkAuthToken();
    if (this.isToken) {
      this.loadData()
    }
  },
  methods: {
      loadData() {
        userMessageList({
          ...this.queryParam,
          readFlag: false,
        }).then((messageResponse) => {
          this.messageTotal = messageResponse.data.total
        })
      },
      handleLogout() {
        logout()
      },
      toapplication(){
        this.$router.push({path: "/application"})
      },
      handlePassword() {
        this.$router.push({path: "/password"})
      },
      tomessage() {
        this.$router.push({path: "/message"})
      },
      tologin(){
        this.$router.push({path: "/login"})
      },
      prompt(){
        this.$modal.msgWarning("即将上线，敬请期待......");
      }
    }
}
</script>
<style lang="scss">
//媒体查询 自适应
.el-dropdown-menu__item{
  text-align: center!important;
  span{
    color: #ff3300!important;
  }
}
//PC端
@media screen and (min-width: 720px) {


  .home {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .home_head {
      display: none;
      position: fixed;
      top: 0px;
      left: 0px;
      width: 100%;
      height: 140px;
      padding: 0 20px 0 40px;
      justify-content: space-between;
      align-items: center;
      z-index: 2;
      background: url('../../../public/image/top_bg.png') no-repeat 100% 100%;
      .head_left {
        img {
          height: 80px;
        }
      }
      .head_right {
        width: 60px;
        height: 60px;
        background: #FFFFFF;
        box-shadow: 0px 10px 30px 1px rgba(93,132,177,0.11);
        border-radius: 100px;
         text-align: center;
         margin-right: 30px;
        .el-dropdown{
          width: 60px;
          height: 60px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .el-dropdown-link{
         width: 100%;
          height: 100%;
          font-size: 18px;
          color: #242424;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;
          &.el-icon-bell::before{
            display: none;
          }
          &.el-icon-bell::after{
            content: "\e725";
            font-size: 11px;
            color: #ffffff;
            width: 16px;
            height: 16px;
            background: #ff3300;
            position: absolute;
            top: -2px;
            right: -2px;
            border-radius: 100px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          img{
            width: 70%;
          }
        }
        ::v-deep .el-icon-arrow-down{
           width: 46px;
              line-height: 46px;
        }

      }
      .head_right2 {
        width: 168px;
        height: 46px;
        background: #fff;
        margin-right: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 25px;
        box-shadow: 0 10px 30px rgba(93, 132, 177, .11);
        cursor: pointer;
        span {
          display: block;
          font-size: 18px;
          color: #242424;
          margin-left: 10px;
        }
        img {
          width: 22px;
          height: 24px;
        }
      }
    }
    .home_body1 {
      height: auto!important;
      width: 100%;
      margin-left: 50%;
      transform: translateX(-50%);
      margin-top: 30px;
      .topLogo{
        width: 1200px;
        height: 180px;
        margin-left: 50%;
        transform: translateX(-50%);
        background: url("../../../public/image/top_bg1.png") no-repeat right 50% #55bd02;
        background-size: auto 100%;
        border-radius: 12px 12px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .logo_text{
          width: auto;
          height: 34%;
          margin-left: 50px;
        }
        .logo_bg{
          width: 1200px;
          height: 120px;
          object-fit: cover;
        }
        .topLogoRight{
          display: flex;
          flex-flow: column;
          justify-content: center;
          align-items: center;
          margin: 0 30px 0 0;
          img{
            width: 120px;
            height: 120px;
            margin: 0;
          }
          span{
            font-size: 14px;
            background: rgba(0,0,0,.6);
            color: #ffffff;
            padding: 4px 14px;
            border-radius: 1000px;
            margin-top: 5px;
          }
        }
      }
      .cityBox{
        width: 1200px;
        height: auto;
        min-height: 513px;
        margin-left: 50%;
        transform: translateX(-50%);
        background: #ffffff;
        padding:40px;
        border-radius: 0 0 12px 12px;
        box-shadow: 0px 10px 30px 1px rgba(93, 132, 177, 0.11);
        .district_box{
          width: 100%;
          display: flex;
          flex-flow: column;
          gap:30px;
          .district_btn{
            width: 100%;
            padding:0 20px 15px 20px;
            display: flex;
            justify-content: center;
            a{
              background: #3570F6;
              color: #ffffff;
              border: 1px solid #3570F6;
              border-radius: 8px;
              padding:15px 55px;
              box-shadow: 0 5px 15px #3570F630;
              cursor: pointer;
            }
          }
          .district_left,.district_right{
            width: 100%;
            background: #fafafa;
            border: 1px solid #e1e1e1;
            border-radius: 8px;
            padding:15px 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            .district_title{
              width: 100%;
              font-size: 20px;
              color: #000000;
            }
            a{
              display: flex;
              align-items: center;
              justify-content: center;
              padding:16px 20px;
              background: #f2f5ff;
              border-radius: 8px 8px 8px 8px;
              opacity: 1;
              border: 1px solid #3570f6;
              text-align: center;
              position: relative;
              width: calc(20% - 12px);
              cursor: pointer;
              font-size: 18px;
              color: #3570F6;
              &.active{
                background: #3570F6;
                color: #fff;
                border-radius: 8px 8px 8px 8px;
                opacity: 1;
                border: 1px solid #3570F6;
                position: relative;
                &::after{
                  content: "";
                  background: url("../../../public/image/icon.svg") no-repeat center 50% #ffffff;
                  position: absolute;
                  right: -10px;
                  top: -10px;
                  width: 18px;
                  height: 18px;
                  background-size: 80% 80%;
                  border-radius: 100px;
                  border: 4px solid #3570F6;

                }
              }
            }
          }
        }
      }
    }
    .home_foot {
      position: fixed;
      left: 0;
      bottom: 0;
      z-index: 2;
      width: 100%;
      height: 80px;
      background: url('../../../public/image/foot_bg.png') no-repeat 100% 100%;
      display: none;
      justify-content: center;
      align-items: center;
    }
  }
}
//移动端
@media screen and (max-width: 720px) {

  .home {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
      ::v-deep .el-drawer {
    width: 50% !important;
  }
    .top_layout{
      display: none;
    }
  .home_head {
    width: 100%;
    height: auto;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    box-shadow: 0 5px 25px rgba(53, 112, 246, 0.1);
    .head_left {
      width: calc(100% - 3.4rem);
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 0.8rem 0 0.8rem 1rem;
      img {
        width: 90%;
      }
    }
    .head_right {
      display: flex;
      align-items: center;
      margin-right: 1rem;
      width: 2.4rem;
      height: 2.4rem;
      .el-dropdown-link {
        width: 2.4rem;
        height: 2.4rem;
        color: #242424;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        &.el-icon-bell::before{
          display: none;
        }
        &.el-icon-bell::after{
          content: "\e725";
          font-size: 0.7rem;
          color: #ffffff;
          width: 1rem;
          height: 1rem;
          background: #ff3300;
          position: absolute;
          top: -0.1rem;
          right: -0.1rem;
          border-radius: 100px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        img {
          width: 90%;
          cursor: pointer;
        }
      }
    }
    .head_right2 {
      width: 2.4rem;
      height: 2.4rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color:#242424;
      cursor: pointer;
      margin-right: 1rem;
      span {
        display: none;
      }
      img {
        width: 90%;
        cursor: pointer;
      }
    }
    .right_box {
      display: none;
      align-items: center;
      padding: 0 20px 0 0;
      div {
        width: auto;
        margin: 0 15px;
        cursor: pointer;
        span {
          color: #383838;
          cursor: pointer;
        }
        &:hover {
          color: #0b7dfb;
          span {
            color: #0b7dfb;
            cursor: pointer;
          }
        }
      }
    }
    .head_right1 {
      width: 46px;
      height: 46px;
      background: #f1f1f1;
      box-shadow: 0px 5px 10px rgba(93, 132, 177, 0.3);
      border-radius: 100px 100px 100px 100px;
      text-align: center;
      margin-right: 46px;
      .user {
        width: 100%;
        display: flex;
        justify-content: space-evenly;
        padding: 1rem 0;
        .icon{
            font-size: 1.2rem;
        }
      }
      .but_box {
        width: 100%;
        display: flex;
        justify-content: space-evenly;
        margin-bottom: 1.2rem;
        button {
          width: 40%;
          height: 2.4rem;
        }
      }
      .right_box {
        width: calc(100% - 2rem);
        margin-left: 1rem;
        display: flex;
        flex-wrap: wrap;
        border-top: 0.1rem solid #e1e1e1;
        div {
          width: 100%;
          margin-top: 1.2rem;
        }
      }


      ::v-deep .el-icon-arrow-down {
        width: 168px;
        line-height: 46px;
      }
    }
  }
    .home_body1 {
      margin-top: 4rem;
      width: 100%;
      height: auto;
      z-index: 1;
      background: url('../../../public/image/html_bg.png') no-repeat 100% 100%;
      .body_big {
        width: 100%;
        height: auto;
        display: flex;
        flex-flow: column;
        flex-direction: column-reverse;
      }
      .body_left {
        width: calc(100% - 3.4rem);
        margin-left: 1.7rem;
        height: auto;
        margin-top: 1rem;
        background: rgba(255, 255, 255, 0.62);
        box-shadow: 0px 10px 30px 1px rgba(93, 132, 177, 0.11);
        border-radius: 1.2rem;
        .left_logo{
          display: none;

        }
        .contentBox{
          width: 100%;
          height: auto;
          padding: 1rem 1.2rem;
        }
        .xitongBox {
          width: 100%;
          font-size: 1.6rem;
          font-weight: bold;
          color: #383838;
        }
        .dianBox {
          width: 100%;
          height: auto;
          font-size: 1.2rem;
          color: #dd6711;
          margin-top: 0.8rem;
        }
        .left_small {
          width: 100%;
          height: auto;
          font-size: 1.2rem;
          color: #383838;
          line-height: 1.8rem;
          div {
            margin-top: 0.8rem;
          }
        }
      }
      .body_right {
        width: calc(100% - 3.4rem);
        margin-left: 1.7rem;
        height: auto;
        margin-top: 1rem;
        .right_top {
          width: 100%;
          display: flex;
          flex-flow: column;
          justify-content: space-between;
          gap: 1rem;
          .right_top_con{
            width: 100%;
            height: 100%;
            text-align: center;
            background: linear-gradient(134deg, #00c6ff 0%, #0072ff 100%);
            color: #fff;
            box-shadow: 0px 10px 30px 1px rgba(93, 132, 177, 0.11);
            border-radius: 0.6rem;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding:0.5rem 0;
            &:nth-child(2){
              background: linear-gradient(134deg, #F2994A 0%, #F2C94C 100%);
            }
            img {
              width: 6rem;
              height: auto;
              margin-left: 1rem;
              transform: translateY(0.2rem);
            }
            .right_top_txt{
              width: auto;
              display: flex;
              flex-flow: column;
              justify-content: center;
              align-items: flex-start;
              .xiao {
                font-size: 1.6rem;
                font-weight: bold;
                color: #ffffff;
                text-shadow: 0 5px 15px rgba(0,0,0,.1);
              }
              .time {
                font-size: 1rem;
                font-weight: bold;
                text-shadow: 0 5px 15px rgba(0,0,0,.1);
              }
            }
          }
        }
        .right_bottom {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;
          .small {
            width: calc(50% - 0.5rem);
            text-align: center;
            align-items: center;
            background: rgba(255, 255, 255, 0.62);
            display: flex;
            flex-direction: column;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: 0px 10px 30px 1px rgba(93, 132, 177, 0.11);
            border-radius: 1.2rem;
            margin-top: 1rem;
            div {
              width: auto;
              font-size: 1.2rem;
              color: #383838;
            }
            img {
              width: auto;
              height: 30%;
              margin-bottom: 0.5rem;
            }
          }
        }
      }
      .body_bottom {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin: 10px 0;
        color: #3570f6;
        font-size: 16px;

      }
      .topLogo{
        display: none;
      }
      .cityBox{
        width: 100%;
        height: auto;
        background: #ffffff;
        padding:1.2rem;
        border-radius: 0 0 0.6rem 0.6rem;
        box-shadow: 0px 1rem 1.5rem rgba(93, 132, 177, 0.11);
        .district_box{
          width: 100%;
          display: flex;
          flex-flow: column;
          gap:0.6rem;
          .district_btn{
            width: 100%;
            padding:0 0.6rem 0.5rem 0.6rem;
            display: flex;
            justify-content: center;
            a{
              background: #3570F6;
              color: #ffffff;
              border: 1px solid #3570F6;
              border-radius: 0.6rem;
              padding:1rem 4rem;
              box-shadow: 0 0.6rem 1rem #3570F630;
              cursor: pointer;
              margin-top: 0.6rem;
              font-size: 1rem;
            }
          }
          .district_left,.district_right{
            width: 100%;
            background: #fafafa;
            border: 1px solid #e1e1e1;
            border-radius: 0.6rem;
            padding:1rem 1.5rem;
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            .district_title{
              width: 100%;
              font-size: 1.2rem;
              color: #000000;
            }
            a{
              background: #f2f5ff;
              border-radius: 0.4rem;
              opacity: 1;
              border: 1px solid #3570f6;
              text-align: center;
              position: relative;
              padding:1rem 0;
              width: calc(50% - 0.5rem);
              cursor: pointer;
              font-size: 1rem;
              color: #3570F6;
              &.active{
                background: #3570F6;
                color: #fff;
                border-radius: 0.4rem;
                opacity: 1;
                border: 1px solid #3570F6;
                position: relative;
                &::after{
                  content: "";
                  background: url("../../../public/image/icon.svg") no-repeat center 50% #ffffff;
                  position: absolute;
                  right: -10px;
                  top: -10px;
                  width: 18px;
                  height: 18px;
                  background-size: 80% 80%;
                  border-radius: 100px;
                  border: 4px solid #3570F6;
                }
              }
            }
          }
        }
      }
    }
    .foot_layout{
      display: none;
    }
    .home_foot {
      width: 100%;
      height:auto;
      background: url('../../../public/image/foot_bg.png') no-repeat 100% 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      .footbox{
        width: 100%;
        height:auto;
        background: #013fce;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16px;
        padding:1.5rem 0;
        .foot{
          width: 100%;
          height: auto;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          flex-flow: column!important;
          .logo{
            width: 100%; height: auto; display: flex; align-items: center; justify-content: center;margin: 0;
            padding:1rem 0;
            .logo_img{
              margin: 0;
              img{
                width:auto; height: 4rem; cursor: pointer;
              }
            }
            a{
              font-family: Helvetica Neue,Helvetica,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Arial,sans-serif !important;
              font-size: 0.8rem;
              color: #fff;
              letter-spacing: 0;
              margin-left: 15px;
              cursor: pointer;
              text-decoration: none;
              display: flex;
              flex-flow: column;
              strong{ width: 100%; font-size: 1.2rem;letter-spacing: 7px; color: #fff; line-height: 30px;  cursor: pointer;}
            }
          }

          p{
            width: 100%;
            height: auto;
            display: block;
            padding: 0.6rem 0;
            text-align: center;
            span{
              width: 100%;
              font-size: 1rem;
              color: #fff;
              display: block;
            }
            a{
              font-family: Helvetica Neue,Helvetica,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Arial,sans-serif !important;
              font-size: 1rem;
              color: #fff;
              text-decoration: none;
              display: block;
              cursor: pointer;
              &:hover{
                text-decoration: underline;
              }
            }
          }
        }
      }
    }
  }
  .left_bottom{
    display: none;
  }

}
</style>
