<template>
  <div class="home" id="top">
    <Topbox class="top_layout" /><!--顶部-->
    <div class="home_head">
      <div class="head_left">
        <img src="../../../public/image/new_logo.svg" alt="" />
      </div>
      <div class="head_right" v-if="isToken">
        <div>
          <el-dropdown>
            <span class="el-dropdown-link" :class="{'el-icon-bell': messageTotal > 0}"><img src="../../../public/image/icon7.svg" alt="" /></span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item><div @click="tomessage">我的消息(<span>{{ messageTotal }}</span>)</div></el-dropdown-item>
              <el-dropdown-item><div @click="toapplication()">我的报名</div></el-dropdown-item>
              <el-dropdown-item><div @click="handlePassword">修改密码</div></el-dropdown-item>
              <el-dropdown-item><div @click="handleLogout">退出登录</div></el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      <div class="head_right2" @click="tologin" v-else>
        <img src="../../../public/image/icon7.svg" alt="" />
        <span>用户登录</span>
      </div>

    </div>

    <div class="home_body">
      <div class="body_big">
        <div class="body_left">
            <div class="left_logo">
              <img class="logo_bg" src="../../../public/image/top_bg1.png" />
              <img class="logo_text" src="../../../public/image/logo_text.svg" />
              <div class="siteBox" @click="tomain()">
                <span>{{ cityName }}-{{ areaName }}</span>
                <img src="../../../public/image/choose.svg" />
              </div>
            </div>


            <div class="contentBox">
              <Timeline v-if="timeLineShow" :timelineList="timeLineArr" @scrollEvent="scrollEvent" />
              <div class="xitongBox">系统公告</div>
              <div :class="`${ timeLineShow ? 'left_small left_small1' : 'left_small'}`">
                <div>
                  1.凡年满六周岁（2017年8月31日前，含8月31日出生）的辖区户籍适龄儿童和符合条件的持有居住证的进城务工随迁子女适龄儿童必须依法注册入学，接受义务教育。
                </div>
                <div>
                  2.招生系统片区继续采用自动定位功能，根据报名信息系统自动锁定片区学校，部分二选一片区家长可根据实际需求选择学校。
                </div>
                <div>
                  3.根据市局“不得跨区流动入学”的要求，不接收其他区域户籍儿童在辖区入学，请回户籍地入学。
                </div>
                <div>
                  4.进城务工随迁子女凭原籍户口簿、居住证、房产证、有房证明或房屋租赁证明、无房证明在所在片区小学网上注册报名。
                </div>
                <div>
                  5.各学校按招生程序依次招生，按顺序招满后，未招收的学生由区教育局按相关政策统筹安排。区教育局根据各校学位对未入学学生进行调配入学，不服从调配的随迁子女视作自动放弃在本辖区公办学校入学机会，回户籍地就读。
                </div>
              </div>
            </div>
        </div>
        <div class="body_right">
          <div class="siteBoxMob" @click="tomain()">
            <span>{{ cityName }}-{{ areaName }}</span>
            <img class="logo_bg" src="../../../public/image/chooseMob.svg" />
          </div>

          <div class="right_top">
            <div class="right_top_con" @click="tochoose($constants.SCHOOL_NATURE_GB, '公办小学报名')">
              <img src="../../../public/image/icon1_new.svg" alt="" />
              <div class="right_top_txt">
                <div class="xiao">公办小学报名入口</div>
              </div>
            </div>
<!--            <div class="right_top_con" @click="tochoose($constants.SCHOOL_NATURE_MB, '民办小学报名')">-->
<!--              <img src="../../../public/image/icon1_new.svg" alt="" />-->
<!--              <div class="right_top_txt">-->
<!--                <div class="xiao">民办小学报名入口</div>-->
<!--              </div>-->
<!--            </div>-->
          </div>

          <div class="right_bottom">
            <div class="small" @click="tolist('招生政策', $constants.POLICY_ID)">
              <img src="../../../public/image/icon2.svg" alt="" />
              <div>招生政策</div>
            </div>
            <div class="small" @click="tolist('各校招生信息', $constants.ADMISSION_ID)">
              <img src="../../../public/image/icon3.svg" alt="" />
              <div>各校招生信息</div>
            </div>
            <div class="small" @click="tolist('操作指南', $constants.GUIDE_ID)">
              <img src="../../../public/image/icon4.svg" alt="" />
              <div>操作指南</div>
            </div>
            <div class="small" @click="tolist('咨询电话', $constants.CONSULTING_ID)">
              <img src="../../../public/image/icon5.svg" alt="" />
              <div>咨询电话</div>
            </div>
          </div>
        </div>
      </div>
      <div class="left_bottom">
        <p>电脑版请尽量使用谷歌浏览器，如使用360、QQ浏览器等请调至极速模式以减少兼容性问题！不支持IE10以下版本！也可用手机微信扫描移动端二维码进行报名。</p>
        <span>
          <i class="el-icon-mobile-phone"></i>
      <!--              <img src="../../assets/images/ewm.png" />-->
          <qr-code :codeUrl="codeUrl"></qr-code>
        </span>
      </div>
    </div>
    <Footbox class="foot_layout" />
    <div class="home_foot">
      <Footbox />
    </div>

    <div class="popBox" v-if="popshow">
      <div class="popBg"></div>
      <div class="popCon">
        <div class="popClose" @click="popClose()">×</div>
        <div class="popLayout" @click="handleClick($constants.RESIDENT_TYPE_HJ, '片区内户籍儿童入口')">
          <img src="../../../public/image/popimg1.png" />
          <span>片区内户籍儿童入口</span>
        </div>
        <div class="popLayout" @click="handleClick($constants.RESIDENT_TYPE_SQ, '进城务工人员随迁子女入口')">
          <img src="../../../public/image/popimg2.png" />
          <span>进城务工人员随迁子女入口</span>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import Topbox from "../layout/newtop";
import Footbox from "../layout/foot";
import foot from '../../components/foot/foot.vue'
import {logout} from '@/utils/logout'
import { userMessageList } from '@/api/msgSubject'
import { checkApplyEnable, bindUserAndQrcode, getApplyTimeTimeLine } from '@/api/apply'
import QrCode from '@/components/QrCode/index'
import Timeline from "@/components/Timeline";

  export default {
  components: {
    foot,
    QrCode,
    Topbox,
    Footbox,
    Timeline
  },
  data() {
    return {
      region: '',
      messageTotal: 0,
      queryParam: {
        pageNum: 1,
        pageSize: 10
      },
      isToken: false,
      codeUrl: '',
      routerQuery: {},
      cityName: '',
      areaName: '',
      popshow:false,
      applyType: '',
      nature: '',
      natureName: '',

      timeLineShow: false,
      // 初始话模拟数据，数据较多时即可，形成滚动条。
      timeLineArr: []
    }
  },
  mounted() {},
  beforeDestroy() {},
  created() {
    this.reset()
    this.routerQuery = this.$commonUtils.getApplyParams(this.$constants.STORE_CACHE_KEY_APPLY) || {}
    this.cityName = this.routerQuery.cityName
    this.areaName = this.routerQuery.areaName
    this.codeUrl = window.origin + '/'
    this.isToken = this.checkAuthToken();
    if (this.isToken) {
      this.loadData()
    }
    this.queryApplyTime(this.$constants.SCHOOL_NATURE_GB);
  },
  methods: {
    reset() {
      this.applyType = ''
      this.nature = ''
      this.natureName = ''
      this.codeUrl = ''
      this.popshow = false
    },
    loadData() {
      userMessageList({
        ...this.queryParam,
        readFlag: false,
      }).then((messageResponse) => {
        this.messageTotal = messageResponse.data.total
      })
    },
    handleLogout() {
      logout()
    },
    async tochoose(type, typeName){
      if (this.isToken) {
        let flag = false
        const qrcodeParams = this.$commonUtils.getApplyParams(this.$constants.STORE_CACHE_KEY_QRCODE) || {}
        if (qrcodeParams && qrcodeParams.qrcodeKey) {
          // 获取当前时间的时间戳
          const currentTimeStamp = new Date().getTime();
          // 过期时间
          const expireTimeStamp = qrcodeParams.expireTime || 0
          if (expireTimeStamp > currentTimeStamp) {
            const data = {
              key: qrcodeParams.qrcodeKey,
              cityId: this.routerQuery.cityId,
              areaId: this.routerQuery.areaId
            }
            await bindUserAndQrcode(data).then(response => {
              const {code, msg} = response
              if (code !== 200) {
                let message = msg || '验证二维码失败'
                this.$message.error(message, 3)
                flag = true;
              }
            }).catch(() => {
              flag = true;
            })
          }
        }
        if (!flag) {
          this.validateApplyEnable(type, typeName)
        }
      } else {
        const that = this
        that.$confirm('请先登录', '系统提示', {
          confirmButtonText: '确定',
          cancelButtonText: '关闭',
          type: 'warning',
          showClose: false,
        }).then(() => {
          that.$router.replace('/login')
        }).catch(() => {});
      }
    },
    validateApplyEnable(type, typeName) {
      const applyType = this.$constants.SCHOOL_XX
      const nature = type
      const data = {
        type: applyType,
        nature: type,
        cityId: this.routerQuery.cityId,
        areaId: this.routerQuery.areaId
      }
      checkApplyEnable(data).then(response => {
        if (response && response.code === 200) {
          this.applyType = applyType
          this.nature = nature
          this.natureName = typeName
          this.openPop()
        }
      })
    },
    queryApplyTime(type) {
      const applyType = this.$constants.SCHOOL_XX
      const data = {
        type: applyType,
        nature: type,
        cityId: this.routerQuery.cityId,
        areaId: this.routerQuery.areaId
      }
      getApplyTimeTimeLine(data).then(response => {
        if (response && response.code === 200) {
          console.log(response)
          this.timeLineArr = response.data || [];
          if (this.timeLineArr.length > 0) {
            this.timeLineShow = true
          }
        }
      })
    },
    tomain(){
      this.$router.push({path: "/main"}).then(() => {
        window.scrollTo(0, 0);
      });
    },
    toapplication(){
      this.$router.push({path: "/application"})
    },
    tolist(name, id){
      const data = {
        className: name,
        classId: id,
        cityId: this.routerQuery.cityId,
        cityName: this.routerQuery.cityName,
        areaId: this.routerQuery.areaId,
        areaName: this.routerQuery.areaName
      }
      this.$commonUtils.setApplyParams(data, this.$constants.STORE_CACHE_KEY_APPLY)
      this.$router.push({path: "/list", query: {name: name}})
    },
    toarticle(name, id){
      const data = {
        articleId: id,
        cityId: this.routerQuery.cityId,
        cityName: this.routerQuery.cityName,
        areaId: this.routerQuery.areaId,
        areaName: this.routerQuery.areaName
      }
      this.$commonUtils.setApplyParams(data, this.$constants.STORE_CACHE_KEY_APPLY)
      this.$router.push({path: "/article", query: {articleId: id}})
    },
    handlePassword() {
      this.$router.push({path: "/password"})
    },
    tomessage() {
      this.$router.push({path: "/message"})
    },
    tologin(){
      this.$router.push({path: "/login"})
    },
    openPop(){
      this.popshow = true
    },
    popClose(){
      this.popshow = false
    },
    handleClick(type, typeName) {
      const that = this
      that.$confirm(`您当前选择的是${this.cityName}${this.areaName}${this.natureName}${typeName}, 请问是否继续？ `, '提示', {
        confirmButtonText: '继续',
        cancelButtonText: '取消',
        type: 'warning',
        showClose: false,
        distinguishCancelAndClose: true,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        center: true
      }).then(() => {
        that.routerQuery.type = that.applyType
        that.routerQuery.nature = that.nature
        that.routerQuery.typeName = that.natureName
        that.routerQuery.personnelType = type
        that.routerQuery.personnelTypeName = typeName
        that.$commonUtils.setApplyParams(that.routerQuery, this.$constants.STORE_CACHE_KEY_APPLY)
        that.$router.push({path: "/school"})
      }).catch(() => {});
    },

    scrollEvent(e) {

    }
  },
}
</script>
<style lang="scss">
//媒体查询 自适应
.el-dropdown-menu__item{
  text-align: center!important;
  span{
    color: #ff3300!important;
  }
}
//PC端
@media screen and (min-width: 720px) {
  .popBox{
    position: fixed;
    inset: 0;
    z-index: 99;
    .popClose{
      position: absolute;
      right: 10px;
      top: 10px;
      width: 40px;
      height: 40px;
      text-align: center;
      line-height: 36px;
      border-radius: 1000px;
      background: rgba(0,0,0,.5);
      color: #ffffff;
      z-index: 2;
      font-size: 28px;
      cursor: pointer;
      transition: ease-in-out all 0.2s;
      &:hover{
        background: rgba(0,0,0,1);
        transform: scale(1.1);
      }
    }
    .popBg{
      position: absolute;
      inset: 0;
      z-index: 1;
      background: rgba(0,0,0,.5);
    }
    .popCon{
      position: absolute;
      z-index: 3;
      left: 50%;
      top: 50%;
      transform: translateY(-50%) translateX(-50%);
      background: #ffffff;
      padding:60px 0;
      border-radius: 12px;
      box-shadow: 0 10px 25px rgba(0,0,0,.4);
      display: flex;
      .popLayout{
        width: 380px;
        height: auto;
        display: flex;
        flex-flow: column;
        justify-content: flex-start;
        align-items: center;
        padding:0 30px;
        cursor: pointer;
        border-right: 1px double #cccccc;
        img{
          width: 180px;
          height: 180px;
          cursor: pointer;
          transition: ease-in-out all 0.2s;
        }
        span{
          margin-top: 20px;
          font-size: 18px;
          cursor: pointer;
          transition: ease-in-out all 0.2s;
        }
        &:hover{
          img{
            transform: scale(1.05);
          }
          span{
            color: #0072ff;
          }
        }
        &:last-child{
          border: none;
        }
      }
    }
  }

  .home {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .home_head {
      display: none;
      position: fixed;
      top: 0px;
      left: 0px;
      width: 100%;
      height: 140px;
      padding: 0 20px 0 40px;
      justify-content: space-between;
      align-items: center;
      z-index: 2;
      background: url('../../../public/image/top_bg.png') no-repeat 100% 100%;
      .head_left {
        img {
          height: 80px;
        }
      }
      .head_right {
        width: 60px;
        height: 60px;
        background: #FFFFFF;
        box-shadow: 0px 10px 30px 1px rgba(93,132,177,0.11);
        border-radius: 100px;
         text-align: center;
         margin-right: 30px;
        .el-dropdown{
          width: 60px;
          height: 60px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .el-dropdown-link{
         width: 100%;
          height: 100%;
          font-size: 18px;
          color: #242424;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;
          &.el-icon-bell::before{
            display: none;
          }
          &.el-icon-bell::after{
            content: "\e725";
            font-size: 11px;
            color: #ffffff;
            width: 16px;
            height: 16px;
            background: #ff3300;
            position: absolute;
            top: -2px;
            right: -2px;
            border-radius: 100px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          img{
            width: 70%;
          }
        }
        ::v-deep .el-icon-arrow-down{
           width: 46px;
              line-height: 46px;
        }

      }
      .head_right2 {
        width: 168px;
        height: 46px;
        background: #fff;
        margin-right: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 25px;
        box-shadow: 0 10px 30px rgba(93, 132, 177, .11);
        cursor: pointer;
        span {
          display: block;
          font-size: 18px;
          color: #242424;
          margin-left: 10px;
        }
        img {
          width: 22px;
          height: 24px;
        }
      }
    }
    .home_body {
      height: auto;
      width: 100%;
      margin-left: 50%;
      transform: translateX(-50%);
      margin-top: 30px;
      .body_big {
        margin-left: 50%;
        transform: translateX(-50%);
        height: auto;
        width: 1200px;
        display: flex;
        justify-content: space-between;
      }
      .body_left {
        width: 723px;
        height: auto;
        .left_logo{
          width: 100%;
          height: 134px;
          background: #ffffff;
          box-shadow: 0px 10px 30px 1px rgba(3, 87, 203, 0.15);
          border-radius: 12px 12px 0 0;
          overflow: hidden;
          position: relative;
          .logo_bg{
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            width: 100%;
            height: 134px;
            object-fit: cover;
          }
          .logo_text{
            position: absolute;
            top: 50%;
            left: 40px;
            transform: translateY(-50%);
            z-index: 2;
            height: 44px;
          }
          .siteBox{
            position: absolute;
            top: 0;
            right: 0;
            background: rgba(0,0,0,0.6);
            padding:8px 20px;
            border-radius: 0 12px 0 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            width: auto;
            z-index: 3;
            span{
              color: #ffffff;
              font-size: 16px;
              font-weight: bold;
              cursor: pointer;
            }
            img{
              width: 24px;
              height: 18px;
              margin-left: 8px;
              cursor: pointer;
            }
          }
        }
        .contentBox1{
          height: auto;
          width: 100%;
          padding:20px 30px;
          border-radius:0;
          position: relative;
          background: rgba(255, 255, 255, 0.62);
          box-shadow: 0px 10px 30px 1px rgba(93, 132, 177, 0.11);
        }
        .contentBox{
          height: 396px;
          width: 100%;
          padding:20px 30px;
          border-radius: 0 0 16px 16px;
          position: relative;
          background: rgba(255, 255, 255, 0.62);
          box-shadow: 0px 10px 30px 1px rgba(93, 132, 177, 0.11);
          .xitongBox {
            width: 100%;
            height: 37px;
            font-size: 28px;
            font-weight: bold;
            color: #383838;
          }
          .dianBox {
            width: 100%;
            height: auto;
            font-size: 18px;
            font-weight: 400;
            color: #dd6711;
            margin-top: 25px;
          }
          .left_small {
            position: absolute;
            left: 30px;
            right: 10px;
            top: 70px;
            bottom:30px;
            overflow-y: auto;
            font-size: 18px;
            font-weight: 400;
            color: #383838;
            line-height: 26px;
            padding:0 15px 0 0;
            &.left_small1{
              top:170px;
            }
            div {
              margin-bottom: 15px;
            }
          }
        }
      }
      .body_right {
        width: 441px;
        height: auto;
        .siteBoxMob{
          display: none;
        }
        .right_top {
          width: 100%;
          height: 240px;
          display: flex;
          flex-flow: column;
          justify-content: space-between;
          gap: 20px;
          .right_top_con{
            width: 100%;
            height: 100%;
            text-align: center;
            background: linear-gradient(134deg, #00c6ff 0%, #0072ff 100%);
            color: #fff;
            box-shadow: 0px 10px 30px 1px rgba(93, 132, 177, 0.11);
            border-radius: 16px 16px 16px 16px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            cursor: pointer;
            &:nth-child(2){
              background: linear-gradient(134deg, #F2994A 0%, #F2C94C 100%);
            }
            img {
              width: 100px;
              height: auto;
              margin-left: 26px;
              cursor: pointer;
            }
            .right_top_txt{
              width: auto;
              display: flex;
              flex-flow: column;
              justify-content: center;
              align-items: flex-start;
              cursor: pointer;
              .xiao {
                font-size: 32px;
                font-weight: bold;
                color: #ffffff;
                text-shadow: 0 5px 15px rgba(0,0,0,.1);
                cursor: pointer;
              }
              .time {
                font-size: 20px;
                font-weight: bold;
                text-shadow: 0 5px 15px rgba(0,0,0,.1);
                cursor: pointer;
              }
            }
          }
        }
        .right_bottom {
          height: 262px;
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;
          margin-top: 30px;
          .small {
            width: calc(50% - 10px);
            height: calc(50% - 10px);
            text-align: center;
            align-items: center;
            background: rgba(255, 255, 255, 0.62);
            display: flex;
            flex-direction: column;
            justify-content: center;
            font-size: 18px;
            box-shadow: 0px 10px 30px 1px rgba(93, 132, 177, 0.11);
            border-radius: 16px;
            div {
              width: auto;
              height: 29px;
              font-size: 22px;
              font-weight: 400;
              color: #383838;
            }
            img {
              width: auto;
              height: 30%;
              margin-bottom: 15px;
            }
          }
        }
      }
      .left_bottom{
        height: auto;
        width: 1200px;
        margin: 30px 0 0 50%;
        transform: translateX(-50%);
        padding:20px 30px;
        background: rgba(255, 255, 255, 0.62);
        box-shadow: 0px 10px 30px 1px rgba(93, 132, 177, 0.11);
        border-radius: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 18px;
        font-weight: 400;
        color: #dd6711;
        p{
          width: 100%;
        }
        span{
          width: 40px;
          height: 40px;
          border: 2px solid #3570f6;
          border-radius: 100px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          position: relative;
          margin-left: 20px;
          i{
            font-size: 28px;
            color: #3570f6;
            cursor: pointer;
          }
          #qrCode{
            display: none;
          }
          &:hover{
            #qrCode{
              display: block;
              width: 220px!important;
              height: 220px!important;
              border: 10px solid #ffffff;
              background: #fafafa;
              position: absolute;
              top: -230px;
              left: 50%;
              transform: translateX(-110px);
              z-index: 999;
              img{
                width: 200px;
                height: 200px;
              }
            }
          }
        }
      }
      .body_bottom {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin: 10px 0;
        color: #3570f6;
        font-size: 16px;
      }
    }
    .home_foot {
      position: fixed;
      left: 0;
      bottom: 0;
      z-index: 2;
      width: 100%;
      height: 80px;
      background: url('../../../public/image/foot_bg.png') no-repeat 100% 100%;
      display: none;
      justify-content: center;
      align-items: center;
    }
  }
  .contentBoxTop{
    margin-top: 160px;
  }
}
//移动端
@media screen and (max-width: 720px) {
  .popBox{
    position: fixed;
    inset: 0;
    z-index: 99;
    .popClose{
      position: absolute;
      right: 50%;
      transform: translateX(50%);
      bottom: -5rem;
      width: 3rem;
      height: 3rem;
      text-align: center;
      line-height: 2.5rem;
      border-radius: 1000px;
      background: rgba(0,0,0,.5);
      border: 2px solid #ffffff;
      color: #ffffff;
      z-index: 2;
      font-size: 28px;
      cursor: pointer;
      transition: ease-in-out all 0.2s;
      &::after{
        content:"";
        position: absolute;
        left: 50%;
        top: -3rem;
        width: 2px;
        height: 3rem;
        background: #ffffff;
      }
      &:hover{
        background: rgba(0,0,0,1);
        transform: scale(1.1);
      }
    }
    .popBg{
      position: absolute;
      inset: 0;
      z-index: 1;
      background: rgba(0,0,0,.5);
    }
    .popCon{
      width: 80%;
      position: absolute;
      z-index: 3;
      left: 50%;
      top: 50%;
      transform: translateY(-50%) translateX(-50%);
      background: #ffffff;
      padding:1rem 2rem;
      border-radius: 0.6rem;
      box-shadow: 0 0.6rem 1.4rem rgba(0,0,0,.4);
      display: flex;
      flex-flow: column;
      .popLayout{
        width: 100%;
        height: auto;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        cursor: pointer;
        padding:1.6rem 1rem;
        border-bottom: 1px dotted #cccccc;
        img{
          width: 6rem;
          height: 6rem;
          cursor: pointer;
          transition: ease-in-out all 0.2s;
        }
        span{
          width: calc(100% - 6rem);
          font-size:1.2rem;
          cursor: pointer;
          margin-left: 1rem;
          transition: ease-in-out all 0.2s;
        }
        &:hover{
          transform: scale(1.05);
        }
        &:last-child{
          border: none;
        }
      }
    }
  }
  .home {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
      ::v-deep .el-drawer {
    width: 50% !important;
  }
    .top_layout{
      display: none;
    }
  .home_head {
    width: 100%;
    height: auto;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    box-shadow: 0 5px 25px rgba(53, 112, 246, 0.1);
    .head_left {
      width: calc(100% - 3.4rem);
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 0.8rem 0 0.8rem 1rem;
      img {
        width: 90%;
      }
    }
    .head_right {
      display: flex;
      align-items: center;
      margin-right: 1rem;
      width: 2.4rem;
      height: 2.4rem;
      .el-dropdown-link {
        width: 2.4rem;
        height: 2.4rem;
        color: #242424;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        &.el-icon-bell::before{
          display: none;
        }
        &.el-icon-bell::after{
          content: "\e725";
          font-size: 0.7rem;
          color: #ffffff;
          width: 1rem;
          height: 1rem;
          background: #ff3300;
          position: absolute;
          top: -0.1rem;
          right: -0.1rem;
          border-radius: 100px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        img {
          width: 90%;
          cursor: pointer;
        }
      }
    }
    .head_right2 {
      width: 2.4rem;
      height: 2.4rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color:#242424;
      cursor: pointer;
      margin-right: 1rem;
      span {
        display: none;
      }
      img {
        width: 90%;
        cursor: pointer;
      }
    }
    .right_box {
      display: none;
      align-items: center;
      padding: 0 20px 0 0;
      div {
        width: auto;
        margin: 0 15px;
        cursor: pointer;
        span {
          color: #383838;
          cursor: pointer;
        }
        &:hover {
          color: #0b7dfb;
          span {
            color: #0b7dfb;
            cursor: pointer;
          }
        }
      }
    }
    .head_right1 {
      width: 46px;
      height: 46px;
      background: #f1f1f1;
      box-shadow: 0px 5px 10px rgba(93, 132, 177, 0.3);
      border-radius: 100px 100px 100px 100px;
      text-align: center;
      margin-right: 46px;
      .user {
        width: 100%;
        display: flex;
        justify-content: space-evenly;
        padding: 1rem 0;
        .icon{
            font-size: 1.2rem;
        }
      }
      .but_box {
        width: 100%;
        display: flex;
        justify-content: space-evenly;
        margin-bottom: 1.2rem;
        button {
          width: 40%;
          height: 2.4rem;
        }
      }
      .right_box {
        width: calc(100% - 2rem);
        margin-left: 1rem;
        display: flex;
        flex-wrap: wrap;
        border-top: 0.1rem solid #e1e1e1;
        div {
          width: 100%;
          margin-top: 1.2rem;
        }
      }


      ::v-deep .el-icon-arrow-down {
        width: 168px;
        line-height: 46px;
      }
    }
  }
    .home_body {
      margin-top: 4rem;
      width: 100%;
      height: auto;
      z-index: 1;
      background: url('../../../public/image/html_bg.png') no-repeat 100% 100%;
      .body_big {
        width: 100%;
        height: auto;
        display: flex;
        flex-flow: column;
        flex-direction: column-reverse;
      }
      .body_left {
        width: calc(100% - 3.4rem);
        margin-left: 1.7rem;
        height: auto;
        margin-top: 1rem;
        background: rgba(255, 255, 255, 0.62);
        box-shadow: 0px 10px 30px 1px rgba(93, 132, 177, 0.11);
        border-radius: 1.2rem;
        .siteBox{
          display: none;
        }
        .left_logo{
          display: none;

        }
        .contentBox{
          width: 100%;
          height: auto;
          padding: 1rem 1.2rem;
          overflow-y: scroll;
        }
        .xitongBox {
          width: 100%;
          font-size: 1.6rem;
          font-weight: bold;
          color: #383838;
        }
        .dianBox {
          width: 100%;
          height: auto;
          font-size: 1.2rem;
          color: #dd6711;
          margin-top: 0.8rem;
        }
        .left_small {
          width: 100%;
          height: auto;
          font-size: 1.2rem;
          color: #383838;
          line-height: 1.8rem;
          div {
            margin-top: 0.8rem;
          }
        }
      }
      .body_right {
        width: calc(100% - 3.4rem);
        margin-left: 1.7rem;
        height: auto;
        margin-top: 0.6rem;
        .siteBoxMob{
          background: #ffffff;
          border: 1px solid #1C65EF;
          padding:1rem 1.6rem;
          border-radius: 0.6rem;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: space-between;
          span{
            color: #1C65EF;
            font-size: 1.2rem;
            cursor: pointer;
          }
          img{
            height: 1.2rem;
            cursor: pointer;
          }
        }
        .right_top {
          width: 100%;
          display: flex;
          flex-flow: column;
          justify-content: space-between;
          gap: 1rem;
          margin-top: 1rem;
          .right_top_con{
            width: 100%;
            height: 100%;
            text-align: center;
            background: linear-gradient(134deg, #00c6ff 0%, #0072ff 100%);
            color: #fff;
            box-shadow: 0px 10px 30px 1px rgba(93, 132, 177, 0.11);
            border-radius: 0.6rem;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding:0.5rem 0;
            &:nth-child(2){
              background: linear-gradient(134deg, #F2994A 0%, #F2C94C 100%);
            }
            img {
              width: 6rem;
              height: auto;
              margin-left: 1rem;
              transform: translateY(0.2rem);
            }
            .right_top_txt{
              width: auto;
              display: flex;
              flex-flow: column;
              justify-content: center;
              align-items: flex-start;
              .xiao {
                font-size: 1.6rem;
                font-weight: bold;
                color: #ffffff;
                text-shadow: 0 5px 15px rgba(0,0,0,.1);
              }
              .time {
                font-size: 1rem;
                font-weight: bold;
                text-shadow: 0 5px 15px rgba(0,0,0,.1);
              }
            }
          }
        }
        .right_bottom {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;
          .small {
            width: calc(50% - 0.5rem);
            text-align: center;
            align-items: center;
            background: rgba(255, 255, 255, 0.62);
            display: flex;
            flex-direction: column;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: 0px 10px 30px 1px rgba(93, 132, 177, 0.11);
            border-radius: 1.2rem;
            margin-top: 1rem;
            div {
              width: auto;
              font-size: 1.2rem;
              color: #383838;
            }
            img {
              width: auto;
              height: 30%;
              margin-bottom: 0.5rem;
            }
          }
        }
      }
      .body_bottom {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin: 10px 0;
        color: #3570f6;
        font-size: 16px;

      }
    }
    .foot_layout{
      display: none;
    }
    .home_foot {
      width: 100%;
      height:auto;
      background: url('../../../public/image/foot_bg.png') no-repeat 100% 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      .footbox{
        width: 100%;
        height:auto;
        background: #013fce;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16px;
        position: relative;
        z-index: 8;
        padding:1.5rem 0;
        .foot{
          width: 100%;
          height: auto;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          flex-flow: column!important;
          .logo{
            width: 100%; height: auto; display: flex; align-items: center; justify-content: center;margin: 0;
            padding:1rem 0;
            .logo_img{
              margin: 0;
              img{
                width:auto; height: 4rem; cursor: pointer;
              }
            }
            a{
              font-family: Helvetica Neue,Helvetica,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Arial,sans-serif !important;
              font-size: 0.8rem;
              color: #fff;
              letter-spacing: 0;
              margin-left: 15px;
              cursor: pointer;
              text-decoration: none;
              display: flex;
              flex-flow: column;
              strong{ width: 100%; font-size: 1.2rem;letter-spacing: 7px; color: #fff; line-height: 30px;  cursor: pointer;}
            }
          }

          p{
            width: 100%;
            height: auto;
            display: block;
            padding: 0.6rem 0;
            text-align: center;
            span{
              width: 100%;
              font-size: 1rem;
              color: #fff;
              display: block;
            }
            a{
              font-family: Helvetica Neue,Helvetica,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Arial,sans-serif !important;
              font-size: 1rem;
              color: #fff;
              text-decoration: none;
              display: block;
              cursor: pointer;
              &:hover{
                text-decoration: underline;
              }
            }
          }
        }
      }
    }
  }
  .left_bottom{
    display: none;
  }
  .contentBoxTop{
    margin-top: 1rem;
  }
}
</style>
