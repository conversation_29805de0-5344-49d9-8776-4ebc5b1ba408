<template>
  <div class="choose">
    <Topbox class="top_layout" /><!--顶部-->
    <top/>

    <div class="choose_body">
      <div class="body_middle">
        <div class="title_box">
          <div class="title_left">
            <div class="title">填写报名资料</div>
          </div>
        </div>

        <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
          <div class="cardBox">
            <div class="cardTitle">报名信息</div>
            <div class="cardCon">
              <el-form-item prop="areaName">
                <div class="card">
                  <div class="cardName longName">预报名区县</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model="ruleForm.parentAreaName + ruleForm.areaName" readonly />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="schoolName">
                <div class="card">
                  <div class="cardName longName">预报名学校</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model="ruleForm.schoolName" readonly />
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="cardBox">
            <div class="cardTitle">学生信息</div>
            <div class="cardCon">
              <el-form-item prop="name">
                <div class="card">
                  <div class="cardName longName required">学生姓名</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.name" maxlength="10" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="idCard">
                <div class="card">
                  <div class="cardName longName required">身份证号</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.idCard" maxlength="20" @blur="handleIdCardChange" placeholder="请输入"  />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="gender">
                <div class="card">
                  <div class="cardName longName required">性别</div>
                  <div class="cardContent">
                    <el-radio v-model="ruleForm.gender"
                              v-for="(item, index) in genderOptions" :key="index"
                              :label="item.dictValue">{{item.dictLabel}}</el-radio>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="birthday">
                <div class="card">
                  <div class="cardName longName required">出生日期</div>
                  <div class="cardContent">
                    <el-date-picker
                        type="date"
                        v-model="ruleForm.birthday"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择"
                        style="width: 100%; border: none"
                    ></el-date-picker>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="registeredAddress" class="card_long">
                <div class="card">
                  <div class="cardName longName required">户籍地址</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.registeredAddress" maxlength="50" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="residentialAddress" class="card_long">
                <div class="card">
                  <div class="cardName longName required">实际居住地址</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.residentialAddress" maxlength="50" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item  prop="policyBasedType" class="card_long">
                <div class="card">
                  <div class="cardName longName required">学生类别</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.policyBasedType" placeholder="请选择">
                      <el-option v-for="(item, index) in policyBasedTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="cardBox">
            <div class="cardTitle">监护人信息</div>
            <div class="cardCon">
              <div class="zhanweiLong"><span>监护人（1）信息</span></div>
              <el-form-item prop="guardianName1">
                <div class="card">
                  <div class="cardName longName required">姓名</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianName1" maxlength="12" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianIdCard1">
                <div class="card">
                  <div class="cardName longName required">身份证号</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianIdCard1" maxlength="20" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianRelation1">
                <div class="card">
                  <div class="cardName longName required">与学生关系</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.guardianRelation1" placeholder="请选择">
                      <el-option v-for="(item, index) in relationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianMobile1">
                <div class="card">
                  <div class="cardName longName required">手机号码</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianMobile1" maxlength="11" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
              <template v-if="ruleForm.personnelType === $constants.RESIDENT_TYPE_HJ">
                <el-form-item prop="guardianHouseCertifyNumber1" class="card_long">
                  <div class="card">
                    <div class="cardName longName required">产权证编号</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianHouseCertifyNumber1" maxlength="20" placeholder="请严格按照产权证填写" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="guardianDomicileAddress1" class="card_long">
                  <div class="card">
                    <div class="cardName longName required">户籍所在地</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianDomicileAddress1" maxlength="50" placeholder="请严格按照户口薄填写" />
                    </div>
                  </div>
                </el-form-item>
              </template>
              <template v-if="ruleForm.personnelType === $constants.RESIDENT_TYPE_SQ">
                <el-form-item prop="guardianResideRegisterTime1">
                  <div class="card">
                    <div class="cardName longName required">居住证签发日期</div>
                    <div class="cardContent">
                      <el-date-picker
                          type="date"
                          v-model="ruleForm.guardianResideRegisterTime1"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          placeholder="请严格按照居住证选择"
                          style="width: 100%; border: none"
                      ></el-date-picker>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="guardianResideIssuePoliceStation1">
                  <div class="card">
                    <div class="cardName longName required">居住证签发机关</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianResideIssuePoliceStation1" maxlength="50" placeholder="请严格按照居住证填写" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="guardianResideAddress1" class="card_long">
                  <div class="card">
                    <div class="cardName longName required">现居住地址</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianResideAddress1" maxlength="50" placeholder="请严格按照居住证填写" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="guardianResideNumber1" class="card_long">
                  <div class="card">
                    <div class="cardName longName required">居住证编号</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianResideNumber1" maxlength="20" placeholder="请严格按照居住证填写" />
                    </div>
                  </div>
                </el-form-item>
              </template>
              <div class="zhanweiLong"><span>监护人（2）信息</span></div>
              <el-form-item prop="guardianName2">
                <div class="card">
                  <div class="cardName longName" :class="{'required': ruleForm.guardianName2 }">姓名</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianName2" maxlength="12" placeholder="请输入" @blur="handleGuardianName2Change" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianIdCard2">
                <div class="card">
                  <div class="cardName longName" :class="{'required': ruleForm.guardianName2 }">身份证号</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianIdCard2" maxlength="20" placeholder="请输入"  />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianRelation2">
                <div class="card">
                  <div class="cardName longName" :class="{'required': ruleForm.guardianName2 }">与学生关系</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.guardianRelation2" placeholder="请选择">
                      <el-option v-for="(item, index) in relationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianMobile2">
                <div class="card">
                  <div class="cardName longName" :class="{'required': ruleForm.guardianName2 }">手机号码</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianMobile2" maxlength="11" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
              <template v-if="ruleForm.personnelType === $constants.RESIDENT_TYPE_HJ">
                <el-form-item prop="guardianHouseCertifyNumber2" class="card_long">
                  <div class="card">
                    <div class="cardName longName" :class="{'required': ruleForm.guardianName2 }">产权证编号</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianHouseCertifyNumber2" maxlength="20" placeholder="请严格按照产权证填写" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="guardianDomicileAddress2" class="card_long">
                  <div class="card">
                    <div class="cardName longName" :class="{'required': ruleForm.guardianName2 }">户籍所在地</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianDomicileAddress2" maxlength="50" placeholder="请严格按照户口薄填写" />
                    </div>
                  </div>
                </el-form-item>
              </template>
              <template v-if="ruleForm.personnelType === $constants.RESIDENT_TYPE_SQ">
                <el-form-item prop="guardianResideRegisterTime2">
                  <div class="card">
                    <div class="cardName longName" :class="{'required': ruleForm.guardianName2 }">居住证签发日期</div>
                    <div class="cardContent">
                      <el-date-picker
                          type="date"
                          v-model="ruleForm.guardianResideRegisterTime2"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          placeholder="请严格按照居住证选择"
                          style="width: 100%; border: none"
                      ></el-date-picker>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="guardianResideIssuePoliceStation2">
                  <div class="card">
                    <div class="cardName longName" :class="{'required': ruleForm.guardianName2 }">居住证签发机关</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianResideIssuePoliceStation2" maxlength="50" placeholder="请严格按照居住证填写" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="guardianResideAddress2" class="card_long">
                  <div class="card">
                    <div class="cardName longName" :class="{'required': ruleForm.guardianName2 }">现居住地址</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianResideAddress2" maxlength="50" placeholder="请严格按照居住证填写" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="guardianResideNumber2" class="card_long">
                  <div class="card">
                    <div class="cardName longName" :class="{'required': ruleForm.guardianName2 }">居住证编号</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianResideNumber2" maxlength="20" placeholder="请严格按照居住证填写" />
                    </div>
                  </div>
                </el-form-item>
              </template>
            </div>
          </div>
          <div class="cardBox">
            <div class="promise-box">
              <el-checkbox class="my-checkbox" v-model="promiseCheckBox">
                  <span class="span-content">本人承诺所提交信息真实有效。</span>
              </el-checkbox>
            </div>
          </div>
          <div class="border_top"></div>
          <!-- 底部 -->
          <div class="foot_Box">
            <div @click="toschool()"><i class="el-icon-loading" v-if="loading"></i>上一步：选择报名学校</div>
            <div @click="submit('ruleForm')" :class="{'disabled_click': (!promiseCheckBox && !loading)}"><i class="el-icon-loading" v-if="loading"></i>确认保存</div>
          </div>
        </el-form>
      </div>
    </div>
    <Footbox class="foot_layout" />
    <foot />
  </div>
</template>

<script>
  import top from '../../components/top/top.vue'
  import foot from '../../components/foot/foot.vue'
  import {getApplyInfo, saveApply} from '@/api/apply'
  import {validIdCard, validMobile, validPositiveInteger} from '@/utils/validate'
  import Topbox from "@/views/layout/newtop";
  import Footbox from "@/views/layout/foot";
  import moment from 'moment';

  export default {
  components: {
    top,foot,
    Topbox,
    Footbox
  },
  data() {
    const validateIdCard = (rule, value, callback) => {
      if (value && !validIdCard(value)) {
        callback(new Error('身份证号不合法'))
      } else {
        callback()
      }
    }
    const validateMobile = (rule, value, callback) => {
      if (value && !validMobile(value)) {
        callback(new Error('请正确填写联系电话'))
      } else {
        callback()
      }
    }
    return {
      loading: false,
      sourceTypeOptions: [],
      cardTypeOptions: [],
      genderOptions: [],
      relationOptions: [],
      policyBasedTypeOptions: [],
      routerQuery: {},
      operate: '',
      id: '',
      promiseCheckBox: false,
      ruleForm: {
        id: '',
        schoolId: '',
        name: '',
        idCard: '',
        birthday: '',
        gender: '',
        registeredAddress: '',
        residentialAddress: '',
        policyBasedType: '',
        houseAddress: '',
        houseProperty: '',
        policyGuarantee: '',
        filingCard: '',
        multipleChildren: '',
        eldestChildName: '',
        eldestChildSchoolId: '',
        eldestChildSchoolName: '',
        eldestChildStudentCode: '',
        guardianId1: '',
        guardianName1: '',
        guardianIdCard1: '',
        guardianMobile1: '',
        guardianRelation1: '',
        guardianHouseCertifyNumber1: '',
        guardianHouseRegisterTime1: '',
        guardianDomicileAddress1: '',
        guardianResideRegisterTime1: '',
        guardianResideIssuePoliceStation1: '',
        guardianResideAddress1: '',
        guardianResideNumber1: '',
        guardianId2: '',
        guardianName2: '',
        guardianIdCard2: '',
        guardianMobile2: '',
        guardianRelation2: '',
        guardianHouseCertifyNumber2: '',
        guardianHouseRegisterTime2: '',
        guardianDomicileAddress2: '',
        guardianResideRegisterTime2: '',
        guardianResideIssuePoliceStation2: '',
        guardianResideAddress2: '',
        guardianResideNumber2: '',
        applyType: '',
        educationNature: '',
        personnelType: '',
        deptId: '',
        areaId: '',
        areaName: '',
        parentAreaId: '',
        parentAreaName: '',
        overageChild: '',
      },
      rules: {
        name: [{ required: true, message: '不能为空', trigger: 'blur' }],
        idCard: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { validator: validateIdCard, trigger: 'blur' }
        ],
        birthday: [{ required: true, message: '不能为空', trigger: 'change' }],
        gender: [{ required: true, message: '不能为空', trigger: 'change' }],
        registeredAddress: [{ required: true, message: '不能为空', trigger: 'blur' }],
        residentialAddress: [{ required: true, message: '不能为空', trigger: 'blur' }],
        policyBasedType: [{ required: true, message: '不能为空', trigger: 'change' }],
        guardianName1: [{ required: true, message: '不能为空', trigger: 'blur' }],
        guardianRelation1: [{ required: true, message: '不能为空', trigger: 'blur' }],
        guardianIdCard1: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { validator: validateIdCard, trigger: 'blur' },
        ],
        guardianMobile1: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' },
        ],
        guardianNation1: [{ required: false, message: '不能为空', trigger: 'blur' }],
        guardianHouseCertifyNumber1: [{ required: true, message: '不能为空', trigger: 'blur' }],
        guardianDomicileAddress1: [{ required: true, message: '不能为空', trigger: 'blur' }],
        guardianResideRegisterTime1: [{ required: true, message: '不能为空', trigger: 'change' }],
        guardianResideIssuePoliceStation1: [{ required: true, message: '不能为空', trigger: 'blur' }],
        guardianResideAddress1: [{ required: true, message: '不能为空', trigger: 'blur' }],
        guardianResideNumber1: [{ required: true, message: '不能为空', trigger: 'blur' }],
        guardianName2: [{ required: false, message: '不能为空', trigger: 'blur' }],
        guardianRelation2: [{ required: false, message: '不能为空', trigger: 'blur' }],
        guardianIdCard2: [
          { required: false, message: '不能为空', trigger: 'blur' },
          { validator: validateIdCard, trigger: 'blur' },
        ],
        guardianMobile2: [
          { required: false, message: '不能为空', trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' },
        ],
        guardianHouseCertifyNumber2: [{ required: false, message: '不能为空', trigger: 'blur' }],
        guardianDomicileAddress2: [{ required: false, message: '不能为空', trigger: 'blur' }],
        guardianResideRegisterTime2: [{ required: true, message: '不能为空', trigger: 'change' }],
        guardianResideIssuePoliceStation2: [{ required: true, message: '不能为空', trigger: 'blur' }],
        guardianResideAddress2: [{ required: true, message: '不能为空', trigger: 'blur' }],
        guardianResideNumber2: [{ required: true, message: '不能为空', trigger: 'blur' }],
      },
    }
  },
  beforeDestroy() {},
  created() {
    this.routerQuery = this.$commonUtils.getApplyParams(this.$constants.STORE_CACHE_KEY_APPLY) || {}
    this.getDicts('sys_card_type').then(response => {
      this.cardTypeOptions = response.data
    })
    this.getDicts('sys_user_sex').then(response => {
      this.genderOptions = response.data
    })
    this.getDicts('sys_relation').then(response => {
      this.relationOptions = response.data
    })
    this.getDicts('biz_policy_based_type').then(response => {
      this.policyBasedTypeOptions = response.data
    })
  },
  mounted() {
    this.ruleForm.schoolName = this.routerQuery.schoolName
    this.ruleForm.areaName = this.routerQuery.areaName
    this.ruleForm.parentAreaName = this.routerQuery.cityName
    this.operate = this.routerQuery.operate || ''
    this.id = this.routerQuery.id
    if (this.id) {
      this.getApplyData(this.id)
    } else {
      this.ruleForm.personnelType = this.routerQuery.personnelType
    }
  },
  computed: {
    handleExceedAge() {
      if (this.ruleForm && this.ruleForm.birthday) {
        return this.validateExceedAge(this.ruleForm.birthday)
      }
      return false;
    }
  },
  methods: {
    getApplyData(id) {
      getApplyInfo(id).then(async response => {
        this.loading = true
        const data = response.data || {}

        // 回显表单
        this.ruleForm = data

        // 处理路由中的参数
        if (this.routerQuery.schoolId && this.routerQuery.schoolId !== this.ruleForm.schoolId) {
          this.ruleForm.schoolId = this.routerQuery.schoolId
          this.ruleForm.schoolName = this.routerQuery.schoolName
        }
        // 设置路由
        if (!this.routerQuery.schoolId) {
          this.routerQuery.schoolId = data.schoolId
          this.routerQuery.schoolName = data.schoolName
        }
        if (!this.routerQuery.areaId) {
          this.routerQuery.areaId = data.areaId
          this.routerQuery.areaName = data.areaName
        }
        if (!this.routerQuery.type) {
          this.routerQuery.type = data.applyType
        }
        if (!this.routerQuery.cityId) {
          this.routerQuery.cityId = data.parentAreaId
          this.routerQuery.cityName = data.parentAreaName
        }
        if (!this.routerQuery.nature) {
          this.routerQuery.nature = data.educationNature
          this.routerQuery.natureName = data.educationNatureName
        }
        if (!this.routerQuery.personnelType) {
          this.routerQuery.personnelType = data.personnelType
        }

        // 回显监护人
        let guardian1 = {}, guardian2 = {}
        if (data.guardianPeopleList && data.guardianPeopleList.length > 0) {
          const length = data.guardianPeopleList.length
          if (length > 0) {
            guardian1 = data.guardianPeopleList[0] || {}
          }
          if (length > 1) {
            guardian2 = data.guardianPeopleList[1] || {}
          }
        }
        // 第一个人
        this.$set(this.ruleForm, 'guardianId1', guardian1.id)
        this.$set(this.ruleForm, 'guardianName1', guardian1.name)
        this.$set(this.ruleForm, 'guardianIdCard1', guardian1.idCard)
        this.$set(this.ruleForm, 'guardianMobile1', guardian1.mobile)
        this.$set(this.ruleForm, 'guardianRelation1', guardian1.relation)
        this.$set(this.ruleForm, 'guardianHouseCertifyNumber1', guardian1.houseCertifyNumber)
        this.$set(this.ruleForm, 'guardianDomicileAddress1', guardian1.domicileAddress)
        this.$set(this.ruleForm, 'guardianResideRegisterTime1', guardian1.resideRegisterTime)
        this.$set(this.ruleForm, 'guardianResideIssuePoliceStation1', guardian1.resideIssuePoliceStation)
        this.$set(this.ruleForm, 'guardianResideAddress1', guardian1.resideAddress)
        this.$set(this.ruleForm, 'guardianResideNumber1', guardian1.resideNumber)

        // 第二个人
        this.$set(this.ruleForm, 'guardianId2', guardian2.id)
        this.$set(this.ruleForm, 'guardianName2', guardian2.name)
        this.$set(this.ruleForm, 'guardianIdCard2', guardian2.idCard)
        this.$set(this.ruleForm, 'guardianMobile2', guardian2.mobile)
        this.$set(this.ruleForm, 'guardianRelation2', guardian2.relation)
        this.$set(this.ruleForm, 'guardianCardType2', guardian2.cardType)
        this.$set(this.ruleForm, 'guardianHouseCertifyNumber2', guardian2.houseCertifyNumber)
        this.$set(this.ruleForm, 'guardianDomicileAddress2', guardian2.domicileAddress)
        this.$set(this.ruleForm, 'guardianResideRegisterTime2', guardian2.resideRegisterTime)
        this.$set(this.ruleForm, 'guardianResideIssuePoliceStation2', guardian2.resideIssuePoliceStation)
        this.$set(this.ruleForm, 'guardianResideAddress2', guardian2.resideAddress)
        this.$set(this.ruleForm, 'guardianResideNumber2', guardian2.resideNumber)

      })
    },

    submit(formName) {
      if (this.loading) { return }
      if (this.validateApplyAge(this.ruleForm.birthday)) {
        this.$message.warning('年龄不满六周岁，不能报名！', 3)
        return
      }
      const that = this
      that.$refs[formName].validate(valid => {
        if (valid) {

          // 设置之前流程选择的数据
          if (that.routerQuery.schoolId) {
            that.ruleForm.schoolId = that.routerQuery.schoolId;
            that.ruleForm.deptId = that.routerQuery.schoolId
          }
          if (that.routerQuery.type) { that.ruleForm.applyType = that.routerQuery.type }
          if (that.routerQuery.areaId) { that.ruleForm.areaId = that.routerQuery.areaId }
          if (that.routerQuery.nature) { that.ruleForm.educationNature = that.routerQuery.nature }
          if (that.routerQuery.personnelType) { that.ruleForm.personnelType = that.routerQuery.personnelType }
          // 设置其它数据
          const saveForm = JSON.parse(JSON.stringify(that.ruleForm))

          // 超龄学生
          if (that.validateExceedAge(saveForm.birthday)) {
            saveForm.overageChild = that.yesValue
          } else {
            saveForm.overageChild = that.noValue
          }

          // 监护人
          const guardianList = []
          if (saveForm.guardianName1) {
            guardianList.push({
              id: saveForm.guardianId1,
              name: saveForm.guardianName1,
              idCard: saveForm.guardianIdCard1,
              mobile: saveForm.guardianMobile1,
              relation: saveForm.guardianRelation1,
              cardType: saveForm.guardianCardType1,
              houseCertifyNumber: saveForm.guardianHouseCertifyNumber1,
              domicileAddress: saveForm.guardianDomicileAddress1,
              resideRegisterTime: saveForm.guardianResideRegisterTime1,
              resideIssuePoliceStation: saveForm.guardianResideIssuePoliceStation1,
              resideAddress: saveForm.guardianResideAddress1,
              resideNumber: saveForm.guardianResideNumber1,
              sort: 1
            })
          }
          if (saveForm.guardianName2) {
            guardianList.push({
              id: saveForm.guardianId2,
              name: saveForm.guardianName2,
              idCard: saveForm.guardianIdCard2,
              mobile: saveForm.guardianMobile2,
              relation: saveForm.guardianRelation2,
              cardType: saveForm.guardianCardType2,
              houseCertifyNumber: saveForm.guardianHouseCertifyNumber2,
              domicileAddress: saveForm.guardianDomicileAddress2,
              resideRegisterTime: saveForm.guardianResideRegisterTime2,
              resideIssuePoliceStation: saveForm.guardianResideIssuePoliceStation2,
              resideAddress: saveForm.guardianResideAddress2,
              resideNumber: saveForm.guardianResideNumber2,
              sort: 2
            })
          }
          saveForm.guardianPeopleList = guardianList

          that.loading = true
          saveApply(saveForm).then(response => {
            that.$message.success('保存成功')
            setTimeout(() => {
              that.toapplication()
            }, 1000)
          }).catch(() => {
            that.loading = false
          })
        } else {
          this.$message.warning('请填写校验项!')
          return false
        }
      })
    },
    toschool(){
      if (this.loading) { return }
      this.$commonUtils.setApplyParams(this.routerQuery, this.$constants.STORE_CACHE_KEY_APPLY)
      this.$router.push({
        path: "/school"
      })
    },
    toapplication(){
      const data = {
        cityId: this.routerQuery.cityId,
        cityName: this.routerQuery.cityName,
        areaId: this.routerQuery.areaId,
        areaName: this.routerQuery.areaName
      }
      this.$commonUtils.setApplyParams(data, this.$constants.STORE_CACHE_KEY_APPLY)
      this.$router.push({path: "/application"})
    },
    handleIdCardChange(val) {
      if (!val) { return }
      const idCard = val.target.value
      if (this.ruleForm.cardType === '01') {
        let year, month, day, sexNumber;
        if (idCard.length === 15) {
          year = idCard.substring(6, 8)
          month = idCard.substring(8, 10)
          day = idCard.substring(10, 12)
          year = (year.startsWith('9') ? '19' : '20') + year
          sexNumber = idCard.substring(14, 15);
        } else if (idCard.length === 18) {
          year = idCard.substring(6, 10)
          month = idCard.substring(10, 12)
          day = idCard.substring(12, 14)
          sexNumber = idCard.substring(16, 17);
        }
        if (validPositiveInteger(year) && validPositiveInteger(month) && validPositiveInteger(day)) {
          this.ruleForm.birthday = year + '-' + month + '-' + day
        }
        if (validPositiveInteger(sexNumber)) {
          const gender = parseInt(sexNumber, 10)
          this.ruleForm.gender = (gender % 2 === 0) ? '2' : '1'
        }
      }
    },
    validateExceedAge(birthday) {
      if (birthday) {
        const sevenYearsAgo = moment().subtract(7, 'years').month(7).date(31);
        let birthDate = moment(birthday);
        return birthDate.isBefore(sevenYearsAgo);
      }
      return false;
    },
    validateApplyAge(birthday) {
      if (birthday) {
        const sevenYearsAgo = moment().subtract(6, 'years').month(7).date(31);
        let birthDate = moment(birthday);
        return birthDate.isAfter(sevenYearsAgo);
      }
      return false;
    },
    handleGuardianName2Change(val) {
      if (!val) { return }
      const value = val.target.value
      let verify = false
      if (value) {
        verify = true
      }
      this.rules.guardianName2[0].required = verify
      this.rules.guardianMobile2[0].required = verify
      this.rules.guardianIdCard2[0].required = verify
      this.rules.guardianRelation2[0].required = verify
      this.rules.guardianHouseCertifyNumber2[0].required = verify
      this.rules.guardianDomicileAddress2[0].required = verify
      this.rules.guardianResideRegisterTime2[0].required = verify
      this.rules.guardianResideIssuePoliceStation2[0].required = verify
      this.rules.guardianResideAddress2[0].required = verify
      this.rules.guardianResideNumber2[0].required = verify
    }
  },
}
</script>
<style lang="scss" scoped>

//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {

  .cardBox{
    width: calc(100% - 80px);
    margin: 0 0 0 40px;
    border: 1px solid #E1E1E1;
    position: relative;
    border-radius: 8px;
    margin-bottom: 30px;
    .cardTitle{
      position: absolute;
      width: auto;
      height: 30px;
      line-height: 30px;
      top: -15px;
      left: 15px;
      padding:0 15px;
      background: #ffffff;
      color: #3570f6;
      font-size: 18px;
    }
    .cardCon{
      width: 100%;
      height: auto;
      padding:30px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 20px;
      .zhanwei{
        width: 100%;
        height: 5px;
        background: #f1f1f1;
      }
      .zhanweiLong{
        width: 100%;
        height: 5px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #f1f1f1;
        margin: 15px 0;
        span{
          background: #ffffff;
          font-size: 18px;
          font-weight: bold;
          padding:0 15px;
          color: #3570f6;
        }
      }
      .el-form-item{
        width: calc(50% - 10px);
        margin: 0;
        &.card_long{
          width: 100%;
        }
      }
      ::v-deep .el-upload-dragger{
        width: 100%;
        height: 100%;
      }
      .card{
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border: 1px solid #E6E6E6;
        border-radius: 6px;
        .required {
          &::after{
            content: "*";
            color: #ff3300;
            margin: 2px 5px;
          }
        }
        .cardName{
          width: auto;
          min-width: 130px;
          height: 50px;
          background: #FAFAFA;
          padding:0 15px;
          display: flex;
          justify-content: center;
          align-items: center;
          white-space: nowrap;
          border-radius: 4px 0 0 4px;
          font-size: 18px;
          color: #383838;
          border-right: 1px solid #e6e6e6;
          &.longName{
            min-width: 200px;
          }
          &.longName1{
            min-width: 250px;
          }
        }
        .cardContent{
          width: 100%;
          height: 50px;
          border-radius: 0 4px 4px 0 ;
          padding: 0 15px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          font-size: 18px;
          color: #383838;
          input::placeholder {
            color: #C0C4CC;
          }
          ::v-deep .el-select{
            .el-input{
              input{
                border: none;
                font-size: 18px;
                padding: 0;
                background: none;
              }
            }
          }
          ::v-deep .el-cascader{
            .el-input{
              input{
                border: none;
                font-size: 18px;
                padding: 0;
                background: none;
              }
            }
          }
          ::v-deep .el-date-editor{
            width: 100%;
            height: 100%;
            input{
              height: 100%;
              border: none;
              background: none;
              font-size: 18px;
              padding-left: 40px;
            }
            .el-input__icon{
              font-size: 18px;
            }
          }
          .cardTxt{
            width: 100%;
            height: 100%;
            border: none;
            background: none;
            font-size: 18px;
            color: #383838;
            outline: none;
          }
          .cont_box_left {
            width: 42%;
            border-right: 3px solid #f1f1f1;
          }
          .cont_box_right {
            margin-left: 20px;
            width: 57%;
          }

        }
        &.card_long{
          width: 100%;
        }
      }
      .card1{
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        border-radius: 6px;
        .required {
          &::after{
            content: "*";
            color: #ff3300;
            margin: 2px 5px;
          }
        }
        .cardName{
          width: auto;
          min-width: 130px;
          height: 50px;
          background: #FAFAFA;
          padding:0 15px;
          display: flex;
          justify-content: center;
          align-items: center;
          white-space: nowrap;
          border-radius: 4px 0 0 4px;
          font-size: 18px;
          color: #383838;
          border: 1px solid #E6E6E6;
          &.longName{
            min-width: 200px;
          }
          &.longName1{
            min-width: 250px;
          }
        }
        .cardContent{
          width: 100%;
          height: auto;
          border-radius: 0 4px 4px 0 ;
          padding: 0 15px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          font-size: 18px;
          color: #383838;
          input::placeholder {
            color: #C0C4CC;
          }
          ::v-deep .el-select{
            .el-input{
              input{
                border: none;
                font-size: 18px;
                padding: 0;
                background: none;
              }
            }
          }
          ::v-deep .el-cascader{
            .el-input{
              input{
                border: none;
                font-size: 18px;
                padding: 0;
                background: none;
              }
            }
          }
          ::v-deep .el-date-editor{
            width: 100%;
            height: 100%;
            input{
              height: 100%;
              border: none;
              background: none;
              font-size: 18px;
              padding-left: 40px;
            }
            .el-input__icon{
              font-size: 18px;
            }
          }
          .cardTxt{
            width: 100%;
            height: 100%;
            border: none;
            background: none;
            font-size: 18px;
            color: #383838;
            outline: none;
          }

        }
        &.card_long{
          width: 100%;
        }
      }
      .upload-demo {
        width: 100%;
        .el-upload__tip {
          width: calc(100% - 250px);
          height: 54px;
          line-height: 54px;
          font-size: 16px;
          color: #383838;
        }
      }
    }
    .promise-box {
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      .my-checkbox {
        transform: scale(1.3);
        .span-content {
          color: #ff3e41;
        }
      }
    }
  }


  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;

    .choose_body {
      flex: 1;
      display: flex;
      justify-content: center;
      margin-top: 0px;
      .body_middle {
        width: 1200px;
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 10px 30px 1px rgba(93,132,177,0.11);
        border-radius: 0 0 16px 16px;
        .middle_top {
          width: 100%;
          margin: 40px 0px 10px 0;
        }
        .border_top {
          width: 1155px;
          margin: 30px 22px 0px 22px;
          height: 7px;
          background: #f1f1f1;
          border-radius: 12px 12px 12px 12px;
          opacity: 1;
        }
        .title_box {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin: 25px 0 25px 2.5%;
          .title {
            width: 176px;
            height: 29px;
            font-size: 22px;
            font-weight: bold;
            color: #000000;
          }
          .title_left {
            display: flex;
            .xiala {
              .sousuo1{
                display: none;
              }
              margin-left: 14px;
              color: #3570F6;
              ::v-deep .el-input__inner{
                border: none;
                width: 200px;
                height: 29px;
                line-height: 29px;
                font-size: 22px;
                font-weight: 400;
                color: #3570F6;
              }
              ::v-deep .el-select__caret{
                font-size: 18px;
                color: #3570F6;
                height: 29px;
                line-height: 29px;
              }
            }
          }
          .title_right {
            width: 244px;
            height: 41px;
            line-height: 41px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #f1f1f1;
            border-radius: 21px 21px 21px 21px;
            input {
              width: 170px;
              height: 31px;
              line-height: 31px;
              font-size: 16px;
              font-weight: 400;
              color: #383838;
              border: none;
              outline: none;
              background: none;
            }
            img {
              width: 19px;
              height: 19px;
              margin-left: 10px;
            }
          }
        }
        .foot_Box {
          width: 100%;
          height: 140px;
          display: flex;
          div:nth-child(1) {
            width: 418px;
            height: 59px;
            margin: 28px 53px 18px 173px;
            background: #3570f6;
            border-radius: 10px 10px 10px 10px;
            text-align: center;
            line-height: 59px;
            color: #fff;
          }
          div:nth-child(2) {
            width: 418px;
            height: 59px;
            margin: 28px 53px 0px 0px;
            background: #3570f6;
            border-radius: 10px 10px 10px 10px;
            color: #fff;
            text-align: center;
            line-height: 59px;
          }
          .disabled_click {
            pointer-events: none !important;
            cursor: not-allowed !important;
            opacity: 0.5 !important;
            background: #cccccc !important;
          }
        }
      }
    }
  }
  .el-select-dropdown__item{
    height: auto;
    line-height: normal;
    padding:10px 20px;
    .cardConBox{
      width: 100%;
      height: auto;
      display: flex;
      flex-flow: column;
      padding: 5px 0;
      background: #fafafa;
      border: 1px solid #f1f1f1;
      border-radius: 4px;
      padding:8px 12px;
      cursor: pointer;
      span:nth-child(1){
        color: #000000;
        font-weight: bold;
        font-size: 16px;
        cursor: pointer;
      }
      span:nth-child(2){
        color: #666;
        font-weight: normal;
        width: 360px;
        white-space:normal;
        cursor: pointer;
      }
    }
    &:hover{
      background: none;
      .cardConBox{
        background: #3570F605;
        border: 1px solid #3570F6;
        span:nth-child(1){
          color: #3570F6;
        }
      }
    }
  }

  .choose_foot {
    z-index: 2;
    width: 100%;
    height: 80px;
    display: none;
    justify-content: center;
    align-items: center;
  }

  ::v-deep .custom-alert .el-alert__content .el-alert__title {
    font-size: 18px !important;
  }
}

// 移动端
@media screen and (max-width: 720px) {

  .top_layout,.foot_layout{
    display: none;
  }
  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .choose_body {
      width: 100%;
      margin-top: 5.8rem;
      .body_middle {
        width: calc(100% - 2rem);
        margin-left: 1rem;
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 0.5rem 1rem 1px rgba(93,132,177,0.11);
        border-radius: 1rem;
        .middle_top {
          display: none;
        }
        .border_top {
          display: none;
        }
        .title_box {
          width:100%;
          display: flex;
          flex-direction: column;
          padding:1rem 1.5rem;
          .title {
            width: 100%;
            height: 2rem;
            font-size: 1.6rem;
            font-weight: bold;
            color: #000000;
          }
          .title_left {
            display: flex;
            flex-direction: column;
            .xiala {
              margin: 0.5rem 0;
              width:100%;
              color: #3570F6;
              display: flex;
              ::v-deep .el-input__inner{
                display: flex;
                border: none;
                width:100%;
                height: 2rem;
                font-size: 1.2rem;
                color: #3570F6;
              }
              ::v-deep .el-select__caret{
                font-size: 1.4rem;
                color: #3570F6;
                height: 2.2rem;
              }
            }
          }
          .title_right {
            width: 100%;
            height: auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f1f1f1;
            border-radius: 0.4rem;
            padding:0 1rem;
            margin-top: 0.5rem;
            input {
              width: calc(100% - 2rem);
              height: 3rem;
              font-size: 1.2rem;
              font-weight: 400;
              color: #383838;
              border: none;
              outline: none;
              background: none;
            }
            img {
              width: 1.5rem;
              height: 1.5rem;
            }
          }
        }


        .cardBox{
          width: calc(100% - 2rem);
          margin: 0 0 0 1rem;
          border: 1px solid #E1E1E1;
          position: relative;
          border-radius: 0.6rem;
          margin-bottom: 1.5rem;
          .cardTitle{
            position: absolute;
            width: auto;
            height: 2rem;
            line-height: 2rem;
            top: -1rem;
            left: 1rem;
            padding:0 1rem;
            background: #ffffff;
            color: #3570f6;
            font-size: 1.1rem;
            font-weight: bold;
          }
          .cardCon{
            width: 100%;
            height: auto;
            padding:1rem;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 0.6rem;
            .zhanwei{
              display: none;
            }
            .zhanweiLong{
              width: 100%;
              height: 0.2rem;
              text-align: center;
              display: flex;
              justify-content: center;
              align-items: center;
              background: #f1f1f1;
              margin: 1rem 0;
              span{
                background: #ffffff;
                font-size: 1.1rem;
                font-weight: bold;
                padding:0 1rem;
                color: #3570f6;
              }
            }
            .el-form-item{
              width: 100%;
              margin: 0 0 13px 0;
              &.card_long{
                width: 100%;
              }
            }
            ::v-deep .el-upload-dragger{
              width: 100%;
              height: 100%;
            }
            .card{
              width: 100%;
              display: flex;
              border: 1px solid #E6E6E6;
              border-radius:0.2rem;
              flex-flow: column;
              .required {
                &::before{
                  content: "*";
                  color: #ff3300;
                  font-size: 1rem;
                  margin: 0 0.2rem 0 0;
                }
              }
              .cardName{
                width: 100%;
                height:auto;
                background: #FAFAFA;
                padding:0 0.8rem;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                white-space: nowrap;
                font-size: 1rem;
                color: #999;
                border-bottom: 1px solid #e6e6e6;
                &.longName{
                  min-width: 10rem;
                }
                &.longName1{
                  min-width: 12rem;
                }
              }
              .cardContent{
                width: 100%;
                height: 3rem;
                padding: 0 0.8rem;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                font-size: 1rem;
                color: #383838;
                input::placeholder {
                  color: #C0C4CC;
                }
                ::v-deep .el-select{
                  .el-input{
                    input{
                      border: none;
                      font-size: 1rem;
                      padding: 0;
                      background: none;
                    }
                  }
                }
                ::v-deep .el-cascader{
                  .el-input{
                    input{
                      border: none;
                      font-size: 1rem;
                      padding: 0;
                      background: none;
                    }
                  }
                }
                ::v-deep .el-form-item__content{
                  line-height: 3rem;
                }
                ::v-deep .el-input__prefix{
                  left: 0;
                  height: 100%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  .el-input__icon{
                    line-height: 3rem;
                  }
                }
                ::v-deep .el-date-editor{
                  width: 100%;
                  height: 100%;
                  input{
                    height: 100%;
                    border: none;
                    background: none;
                    font-size: 1rem;
                    padding-left: 2rem;
                  }
                  .el-input__icon{
                    font-size: 1rem;
                  }
                }
                .cardTxt{
                  width: 100%;
                  height: 100%;
                  border: none;
                  background: none;
                  font-size: 1rem;
                  color: #383838;
                  outline: none;
                }
              }
              .cardContent1 {
                height: 100% !important;
                flex-wrap: wrap;
                .cont_box_left {
                  border-bottom: 2px dashed #f1f1f1;
                  flex-grow: 1;
                }
                .cont_box_right {
                  flex-grow: 1;
                }
              }
              &.card_long{
                width: 100%;
              }
            }
            .card1{
              width: 100%;
              display: flex;
              border-radius:0.2rem;
              flex-flow: column;
              .required {
                &::before{
                  content: "*";
                  color: #ff3300;
                  font-size: 1rem;
                  margin: 0 0.2rem 0 0;
                }
              }
              .cardName{
                width: 100%;
                height: 3rem;
                background: #FAFAFA;
                padding:0 0.8rem;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                white-space: nowrap;
                font-size: 1rem;
                color: #999;
                border: 1px solid #E6E6E6;
                &.longName{
                  min-width: 10rem;
                }
              }
              .cardContent{
                width: 100%;
                height: auto;
                padding: 0.8rem 0.8rem 0 0.1rem;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                font-size: 1rem;
                color: #383838;
                input::placeholder {
                  color: #C0C4CC;
                }
                ::v-deep .el-select{
                  .el-input{
                    input{
                      border: none;
                      font-size: 1rem;
                      padding: 0;
                      background: none;
                    }
                  }
                }
                ::v-deep .el-cascader{
                  .el-input{
                    input{
                      border: none;
                      font-size: 1rem;
                      padding: 0;
                      background: none;
                    }
                  }
                }
                ::v-deep .el-form-item__content{
                  line-height: 3rem;
                }
                ::v-deep .el-input__prefix{
                  left: 0;
                  height: 100%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  .el-input__icon{
                    line-height: 3rem;
                  }
                }
                ::v-deep .el-date-editor{
                  width: 100%;
                  height: 100%;
                  input{
                    height: 100%;
                    border: none;
                    background: none;
                    font-size: 1rem;
                    padding-left: 2rem;
                  }
                  .el-input__icon{
                    font-size: 1rem;
                  }
                }
                .cardTxt{
                  width: 100%;
                  height: 100%;
                  border: none;
                  background: none;
                  font-size: 1rem;
                  color: #383838;
                  outline: none;
                }
              }
              &.card_long{
                width: 100%;
              }
            }
            .upload-demo {
              width: 100%;
              .el-upload__tip {
                width: 100%;
                height: auto;
                font-size: 1rem;
                color: #999;
              }
            }
          }
          .promise-box {
            height: 3rem;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            .my-checkbox {
              transform: scale(1.3);
              .span-content {
                color: #ff3e41;
              }
            }
          }
        }


        .foot_Box {
          width: calc(100% - 2rem);
          margin-left:1rem ;
          display: flex;
          justify-content: space-evenly;
          flex-flow: column;
          gap: 1rem;
          padding-bottom: 1.5rem;
          div {
            width: 100%;
            height: 3.4rem;
            background: #3570f6;
            border-radius: 0.6rem;
            text-align: center;
            line-height: 3.4rem;
            color: #fff;
          }
          .disabled_click {
            pointer-events: none !important;
            cursor: not-allowed !important;
            opacity: 0.5 !important;
            background: #cccccc !important;
          }
        }
      }
    }
    .choose_foot {
       width: 100%;
      height:auto;
      display: flex;
      justify-content: center;
      align-items: center;
      padding:1.5rem 1.2rem;
    }
  }
  .el-select-dropdown__item{
    height: auto;
    line-height: normal;
    padding:0.6rem 1.2rem;
    .cardConBox{
      width: 80vw;
      height: auto;
      box-sizing: border-box;
      display: flex;
      flex-flow: column;
      padding: 0.3rem 0;
      background: #fafafa;
      border: 1px solid #f1f1f1;
      border-radius: 0.6rem;
      padding:0.6rem 1rem;
      cursor: pointer;
      span:nth-child(1){
        color: #000000;
        font-weight: bold;
        font-size: 1.2rem;
        cursor: pointer;
        white-space: normal;
        word-break: break-all;
      }
      span:nth-child(2){
        color: #666;
        font-weight: normal;
        width: 100%;
        white-space:normal;
        cursor: pointer;
        font-size: 1rem;
      }
    }
    &:hover{
      background: none;
      .cardConBox{
        background: #3570F605;
        border: 1px solid #3570F6;
        span:nth-child(1){
          color: #3570F6;
        }
      }
    }
  }
  ::v-deep .custom-alert .el-alert__content .el-alert__title {
    font-size: 1rem !important;
  }
}
::v-deep .el-select{
  width: 100%!important;
}
::v-deep .el-radio__label{
  font-size: 1rem;
}
.el-date-editor{
  display: flex;
  justify-content: space-between;
  flex-flow: row;
}
::v-deep .is-finish{
  color: #383838;
  font-size: 18px;
  .el-step__line{
    background: #3570F6;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #3570F6;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    border-color: #3570F6;
  }
}

::v-deep .is-process{
  color: #383838;
  font-size: 18px;
  font-weight: normal;
  .el-step__line{
    background: #f1f1f1;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #ffffff;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    border-width: 5px;
    color: #3570F6;
    border-color: #3570F6;
  }
}

::v-deep .is-wait{
  color: #383838;
  font-size: 18px;
  font-weight: normal;
  .el-step__line{
    background: #f1f1f1;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #EEF6FF;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    border-width: 5px;
    color: #3570F6;
    border-color: #EEF6FF;
  }
}
::v-deep .el-cascader {
  width: 100% !important;
}
</style>
