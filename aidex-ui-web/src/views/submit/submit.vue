<template>
  <div class="choose">
    <Topbox class="top_layout" /><!--顶部-->
    <top/>

    <div class="choose_body">
      <div class="body_middle">
        <div class="title_box">
          <div class="title_left">
            <div class="title">填写报名资料</div>
          </div>
        </div>

        <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
          <div class="cardBox">
            <div class="cardTitle">报名信息</div>
            <div class="cardCon">
              <el-form-item prop="schoolName">
                <div class="card">
                  <div class="cardName">报名学校</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model="ruleForm.schoolName" readonly />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="sourceTypeId">
                <div class="card">
                  <div class="cardName required">生源类型</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.sourceTypeId" placeholder="请选择" style="width: 100%;">
                      <el-option v-for="(item, index) in sourceTypeOptions" :key="index" :label="item.name" :value="item.id">
                        <div class="cardConBox">
                          <span>{{ item.name }}</span>
                          <span>{{ item.description }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="cardBox">
            <div class="cardTitle">儿童基本信息</div>
            <div class="cardCon">
              <el-form-item prop="name">
                <div class="card">
                  <div class="cardName required">儿童姓名</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.name" maxlength="10" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="mobile">
                <div class="card">
                  <div class="cardName required">监护人电话</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.mobile" maxlength="20" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="cardType">
                <div class="card">
                  <div class="cardName required">证件类型</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.cardType" placeholder="请选择">
                      <el-option v-for="(item, index) in cardTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="idCard">
                <div class="card">
                  <div class="cardName required">证件号码</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.idCard" maxlength="20" @blur="handleIdCardChange" placeholder="请输入"  />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="birthday">
                <div class="card">
                  <div class="cardName required">出生日期</div>
                  <div class="cardContent">
                    <el-date-picker
                        type="date"
                        v-model="ruleForm.birthday"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择"
                        style="width: 100%; border: none"
                    ></el-date-picker>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="gender">
                <div class="card">
                  <div class="cardName required">性别</div>
                  <div class="cardContent">
                    <el-radio v-model="ruleForm.gender"
                        v-for="(item, index) in genderOptions" :key="index"
                        :label="item.dictValue">{{item.dictLabel}}</el-radio>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="nation">
                <div class="card">
                  <div class="cardName required">民族</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.nation" placeholder="请选择">
                      <el-option v-for="(item, index) in nationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="country">
                <div class="card">
                  <div class="cardName required">国籍</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.country" placeholder="请选择">
                      <el-option v-for="(item, index) in countryOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="healthCondition">
                <div class="card">
                  <div class="cardName required">健康状况</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.healthCondition" placeholder="请选择">
                      <el-option v-for="(item, index) in healthConditionOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="inoculatePrevent">
                <div class="card">
                  <div class="cardName required">预防接种</div>
                  <div class="cardContent">
                    <el-radio v-model="ruleForm.inoculatePrevent"
                              v-for="(item, index) in yesOrNoOptions" :key="index"
                              :label="item.dictValue">{{item.dictLabel}}</el-radio>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="disabilityType">
                <div class="card">
                  <div class="cardName">残疾类型</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.disabilityType" placeholder="请选择">
                      <el-option v-for="(item, index) in disabilityTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>

              <div v-if="infoPhoto.visible">
                <div class="card1">
                  <div :class="{'required': infoPhoto.required}" class="cardName">免冠照片</div>
                  <div class="cardContent">
                    <image-upload :limit="1" :file-size="fileSize" :file-unit="fileUnit" :drag="true"
                      :fileType="fileType"
                      :upload-text="uploadText"
                      :tip-text="`请上传本人两寸免冠照片，照片尺寸413*626，照片大小${fileSize}${fileUnit}以内，只能上传${fileType.join('/')}文件。`"
                      :ref="uploadRefKey + $constants.FILE_TYPE_AVATAR"
                      @ok="handleChangeFile($event, $constants.FILE_TYPE_AVATAR)"
                    ></image-upload>
                  </div>
                </div>
              </div>

              <div v-if="handleExceedAge">
                <div style="margin-bottom: 10px;">
                  <el-alert
                      class="custom-alert"
                      title="适龄儿童、少年因身体状况需要延缓就读义务教育学校的，其父母或者其他法定监护人应当提出申请，报县区教育行政部门批准备案。"
                      type="warning"
                      :closable="false"
                      show-icon>
                  </el-alert>
                </div>
                <div class="card1">
                  <div class="cardName longName1">延缓入学申请批复照片</div>
                  <div class="cardContent">
                    <image-upload :limit="oneLimit" :file-size="fileSize" :file-unit="fileUnit" :drag="true"
                      :fileType="fileType"
                      :upload-text="uploadText"
                      :tip-text="handleUploadTipText(oneLimit)"
                      :ref="uploadRefKey + $constants.POSTPONE_ENTER_SCHOOL_APPLY"
                      @ok="handleChangeFile($event, $constants.POSTPONE_ENTER_SCHOOL_APPLY)"
                    ></image-upload>
                  </div>
                </div>
              </div>

            </div>
          </div>
          <div class="cardBox">
            <div class="cardTitle">监护人信息</div>
            <div class="cardCon">
              <div class="zhanweiLong"><span>监护人（1）信息</span></div>
              <el-form-item prop="guardianName1">
                <div class="card">
                  <div class="cardName required">姓名</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianName1" maxlength="12" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianMobile1">
                <div class="card">
                  <div class="cardName required">手机号码</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianMobile1" maxlength="11" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianCardType1">
                <div class="card">
                  <div class="cardName required">证件类型</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.guardianCardType1" placeholder="请选择">
                      <el-option v-for="(item, index) in cardTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianIdCard1">
                <div class="card">
                  <div class="cardName required">证件号码</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianIdCard1" maxlength="20" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianRelation1">
                <div class="card">
                  <div class="cardName required">与儿童关系</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.guardianRelation1" placeholder="请选择">
                      <el-option v-for="(item, index) in relationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <div class="zhanweiLong"><span>监护人（2）信息</span></div>
              <el-form-item prop="guardianName2">
                <div class="card">
                  <div class="cardName" :class="{'required': ruleForm.guardianName2 }">姓名</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianName2" maxlength="12" placeholder="请输入" @blur="handleGuardianName2Change" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianMobile2">
                <div class="card">
                  <div class="cardName" :class="{'required': ruleForm.guardianName2 }">手机号码</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianMobile2" maxlength="11" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianCardType2">
                <div class="card">
                  <div class="cardName" :class="{'required': ruleForm.guardianName2 }">证件类型</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.guardianCardType2" placeholder="请选择">
                      <el-option v-for="(item, index) in cardTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianIdCard2">
                <div class="card">
                  <div class="cardName" :class="{'required': ruleForm.guardianName2 }">证件号码</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianIdCard2" maxlength="20" placeholder="请输入"  />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianRelation2">
                <div class="card">
                  <div class="cardName" :class="{'required': ruleForm.guardianName2 }">与儿童关系</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.guardianRelation2" placeholder="请选择">
                      <el-option v-for="(item, index) in relationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="cardBox" v-if="ruleForm.personnelType === $constants.RESIDENT_TYPE_HJ">
            <div class="cardTitle">儿童户籍信息</div>
            <div class="cardCon">
              <el-form-item prop="domicileNumber">
                <div class="card">
                  <div class="cardName longName required">户号</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.domicileNumber" maxlength="20" placeholder="请严格按照户口簿填写" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="domicileHead">
                <div class="card">
                  <div class="cardName longName required">户主姓名</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.domicileHead" maxlength="20" placeholder="请严格按照户口簿填写" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="domicileHeadIdCard">
                <div class="card">
                  <div class="cardName longName required">户主身份证号码</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.domicileHeadIdCard" maxlength="20" placeholder="请严格按照户口簿填写" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="domicileHeadRelation">
                <div class="card">
                  <div class="cardName longName required">户主与儿童关系</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.domicileHeadRelation" placeholder="请选择">
                      <el-option v-for="(item, index) in relationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="domicileRegisterTime">
                <div class="card">
                  <div class="cardName longName required">儿童迁入时间</div>
                  <div class="cardContent">
                    <el-date-picker
                        type="date"
                        v-model="ruleForm.domicileRegisterTime"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        placeholder="请严格按照户口簿选择"
                        style="width: 100%; border: none"
                    ></el-date-picker>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="domicileAddress" class="card_long">
                <div class="card">
                  <div class="cardName longName required">户籍地址</div>
                  <div class="cardContent cardContent1">
                    <div class="cont_box_left">
                      <el-cascader
                          v-model="domicileAddressAreaCodes"
                          filterable
                          :props="cascaderProps"
                          :options="areaFourLevelTreeOptions"
                          separator=""
                          placeholder="请选择户籍所在地的行政区划"
                          @change="handleDomicileAddressAreaCodesChange"
                      >
                      </el-cascader>
                    </div>
                    <div class="cont_box_right">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.domicileAddress" maxlength="50" placeholder="请输入街/路/村/其他" />
                    </div>
                  </div>
                </div>
              </el-form-item>
              <div class="card_long">
                <div class="card1">
                  <div class="cardName longName required">户口簿首页照片</div>
                  <div class="cardContent">
                    <image-upload :limit="oneLimit" :file-size="fileSize" :file-unit="fileUnit" :drag="true"
                      :fileType="fileType"
                      :upload-text="uploadText"
                      :tip-text="handleUploadTipText(oneLimit)"
                      :ref="uploadRefKey + $constants.HOUSEHOLD_REGISTER"
                      @ok="handleChangeFile($event, $constants.HOUSEHOLD_REGISTER)"
                    ></image-upload>
                  </div>
                </div>
              </div>
              <div class="card_long">
                <div class="card1">
                  <div class="cardName longName required">户口簿户主页照片</div>
                  <div class="cardContent">
                    <image-upload :limit="oneLimit" :file-size="fileSize" :file-unit="fileUnit" :drag="true"
                      :fileType="fileType"
                      :upload-text="uploadText"
                      :tip-text="handleUploadTipText(oneLimit)"
                      :ref="uploadRefKey + $constants.HOUSEHOLD_REGISTER_HEAD"
                      @ok="handleChangeFile($event, $constants.HOUSEHOLD_REGISTER_HEAD)"
                    ></image-upload>
                  </div>
                </div>
              </div>
              <div class="card_long">
                <div class="card1">
                  <div class="cardName longName required">户口簿儿童页照片</div>
                  <div class="cardContent">
                    <image-upload :limit="oneLimit" :file-size="fileSize" :file-unit="fileUnit" :drag="true"
                      :fileType="fileType"
                      :upload-text="uploadText"
                      :tip-text="handleUploadTipText(oneLimit)"
                      :ref="uploadRefKey + $constants.HOUSEHOLD_REGISTER_CHILD"
                      @ok="handleChangeFile($event, $constants.HOUSEHOLD_REGISTER_CHILD)"
                    ></image-upload>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="cardBox" v-if="ruleForm.personnelType === $constants.RESIDENT_TYPE_SQ">
            <div class="cardTitle">监护人居住证件信息</div>
            <div class="cardCon">
              <el-form-item prop="resideHead">
                <div class="card">
                  <div class="cardName longName required">持有人姓名</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.resideHead" maxlength="20" placeholder="请严格按照居住证填写" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="resideHeadIdCard">
                <div class="card">
                  <div class="cardName longName required">持有人身份证号</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.resideHeadIdCard" maxlength="20" placeholder="请严格按照居住证填写" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="resideRegisterTime" v-if="parentsResidencePermit.visible">
                <div class="card">
                  <div :class="{'required': parentsResidencePermit.required}" class="cardName longName ">居住证签发日期</div>
                  <div class="cardContent">
                    <el-date-picker
                        type="date"
                        v-model="ruleForm.resideRegisterTime"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        placeholder="请严格按照居住证选择"
                        style="width: 100%; border: none"
                    ></el-date-picker>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="resideHeadRelation" v-if="parentsResidencePermit.visible">
                <div class="card">
                  <div :class="{'required': parentsResidencePermit.required}" class="cardName longName">持证人与儿童关系</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.resideHeadRelation" placeholder="请选择">
                      <el-option v-for="(item, index) in relationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="resideIssuePoliceStation" v-if="parentsResidencePermit.visible">
                <div class="card">
                  <div :class="{'required': parentsResidencePermit.required}" class="cardName longName">签发派出所</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.resideIssuePoliceStation" maxlength="50" placeholder="请严格按照居住证填写" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="resideAddress" class="card_long" v-if="parentsResidencePermit.visible">
                <div class="card">
                  <div :class="{'required': parentsResidencePermit.required}" class="cardName longName">居住地址</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.resideAddress" maxlength="50" placeholder="请严格按照居住证填写" />
                  </div>
                </div>
              </el-form-item>
              <el-alert
                  class="custom-alert"
                  v-if="parentsResidencePermit.visible"
                  title="如果随迁子女监护人一方为本辖区户籍，也可以以”户口簿首页照片”“户口簿监护人页“照片来分别代替“居住证正面照片””居住证反面照片”"
                  type="warning"
                  :closable="false"
                  show-icon>
              </el-alert>
              <div class="card_long" v-if="parentsResidencePermit.visible">
                <div class="card1">
                  <div :class="{'required': parentsResidencePermit.required}" class="cardName longName ">居住证正面照片</div>
                  <div class="cardContent">
                    <image-upload :limit="oneLimit" :file-size="fileSize" :file-unit="fileUnit" :drag="true"
                      :fileType="fileType"
                      :upload-text="uploadText"
                      :tip-text="handleUploadTipText(oneLimit)"
                      :ref="uploadRefKey + $constants.RESIDENCE_PERMIT_FRONT"
                      @ok="handleChangeFile($event, $constants.RESIDENCE_PERMIT_FRONT)"
                    ></image-upload>
                  </div>
                </div>
              </div>
              <div class="card_long" v-if="parentsResidencePermit.visible">
                <div class="card1">
                  <div :class="{'required': parentsResidencePermit.required}" class="cardName longName">居住证反面照片</div>
                  <div class="cardContent">
                    <image-upload :limit="oneLimit" :file-size="fileSize" :file-unit="fileUnit" :drag="true"
                      :fileType="fileType"
                      :upload-text="uploadText"
                      :tip-text="handleUploadTipText(oneLimit)"
                      :ref="uploadRefKey + $constants.RESIDENCE_PERMIT_BACK"
                      @ok="handleChangeFile($event, $constants.RESIDENCE_PERMIT_BACK)"
                    ></image-upload>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="cardBox">
            <div class="cardTitle">房产信息</div>
            <div class="cardCon">
              <el-form-item prop="houseHave" class="card_long">
                <div class="card">
                  <div class="cardName longName required">片区内有无房产</div>
                  <div class="cardContent">
                    <el-radio v-model="ruleForm.houseHave" @change="handleRadioChange"
                              v-for="(item, index) in haveOrNoOptions" :key="index"
                              :label="item.dictValue">{{item.dictLabel}}</el-radio>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="houseHead" v-if="ruleForm.houseHave === yesValue">
                <div class="card">
                  <div class="cardName longName required">权利人姓名</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.houseHead" maxlength="10" placeholder="请严格按照房产证明填写" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="houseHeadIdCard" v-if="ruleForm.houseHave === yesValue">
                <div class="card">
                  <div class="cardName longName required">权利人证件号码</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.houseHeadIdCard" maxlength="20" placeholder="请严格按照房产证明填写" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="houseCertifyType" v-if="ruleForm.houseHave === yesValue">
                <div class="card">
                  <div class="cardName longName required">房产证明类型</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.houseCertifyType" placeholder="请选择" @change="handleHouseCertifyTypeChange">
                      <el-option v-for="(item, index) in houseCertifyTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="houseRegisterTime" v-if="ruleForm.houseHave === yesValue">
                <div class="card">
                  <div class="cardName longName required">房产证明取得时间</div>
                  <div class="cardContent">
                    <el-date-picker
                        type="date"
                        v-model="ruleForm.houseRegisterTime"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        placeholder="请严格按照房产证明填写"
                        style="width: 100%; border: none"
                    ></el-date-picker>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="houseCertifyNumber" v-if="ruleForm.houseHave === yesValue">
                <div class="card">
                  <div :class="{'required': ruleForm.houseCertifyType !== '3'}" class="cardName longName">房产证明编号</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.houseCertifyNumber" maxlength="20" placeholder="请严格按照房产证明填写" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="houseHeadRelation" v-if="ruleForm.houseHave === yesValue">
                <div class="card">
                  <div class="cardName longName required">房主与儿童关系</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.houseHeadRelation" placeholder="请选择">
                      <el-option v-for="(item, index) in relationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="houseAddress" class="card_long" v-if="ruleForm.houseHave === yesValue">
                <div class="card">
                  <div class="cardName longName required">房产坐落</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.houseAddress" maxlength="50" placeholder="请严格按照房产证/购房合同/公房证明填写" />
                  </div>
                </div>
              </el-form-item>
              <div class="card_long" v-if="ruleForm.houseHave === yesValue">
                <div class="card1">
                  <div class="cardName longName required">房产证明材料照片</div>
                  <div class="cardContent">
                    <image-upload :limit="oneLimit" :file-size="fileSize" :file-unit="fileUnit" :drag="true"
                      :fileType="fileType"
                      :upload-text="uploadText"
                      :tip-text="handleUploadTipText(oneLimit)"
                      :ref="uploadRefKey + $constants.HOUSE_PROPRIETARY_CERTIFICATE"
                      @ok="handleChangeFile($event, $constants.HOUSE_PROPRIETARY_CERTIFICATE)"
                    ></image-upload>
                  </div>
                </div>
              </div>
              <div class="card_long" v-if="ruleForm.houseHave === noValue && houseNoRoomCertificate.visible">
                <div class="card1">
                  <div :class="{'required': houseNoRoomCertificate.required}" class="cardName longName">无房证明照片</div>
                  <div class="cardContent">
                    <image-upload :limit="oneLimit" :file-size="fileSize" :file-unit="fileUnit" :drag="true"
                      :fileType="fileType"
                      :upload-text="uploadText"
                      :tip-text="handleUploadTipText(oneLimit)"
                      :ref="uploadRefKey + $constants.NO_HOUSE_CERTIFICATE"
                      @ok="handleChangeFile($event, $constants.NO_HOUSE_CERTIFICATE)"
                    ></image-upload>
                  </div>
                </div>
              </div>
              <el-alert
                  class="custom-alert"  v-if="ruleForm.houseHave === noValue && houseRentRoomCertificate.visible"
                  title="若租赁证明有多页，请自行合并为一页后上传"
                  type="warning"
                  :closable="false"
                  show-icon>
              </el-alert>
              <div class="card_long" v-if="ruleForm.houseHave === noValue && houseRentRoomCertificate.visible">
                <div class="card1">
                  <div :class="{'required': houseRentRoomCertificate.required}" class="cardName longName">房屋租赁证明照片</div>
                  <div class="cardContent">
                    <image-upload :limit="oneLimit" :file-size="fileSize" :file-unit="fileUnit" :drag="true"
                      :fileType="fileType"
                      :upload-text="uploadText"
                      :tip-text="handleUploadTipText(oneLimit)"
                      :ref="uploadRefKey + $constants.HOUSE_LEASE_CERTIFICATE"
                      @ok="handleChangeFile($event, $constants.HOUSE_LEASE_CERTIFICATE)"
                    ></image-upload>
                  </div>
                </div>
              </div>
              <div class="card_long" v-if="ruleForm.houseHave === noValue">
                <div class="card1">
                  <div class="cardName longName">其他证明材料照片</div>
                  <div class="cardContent">
                    <image-upload :limit="oneLimit" :file-size="fileSize" :file-unit="fileUnit" :drag="true"
                                  :fileType="fileType"
                                  :upload-text="uploadText"
                                  :tip-text="handleUploadTipText(oneLimit)"
                                  :ref="uploadRefKey + $constants.OTHER_CERTIFICATE"
                                  @ok="handleChangeFile($event, $constants.OTHER_CERTIFICATE)"
                    ></image-upload>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="cardBox" v-if="applyPolicyGuarantee.visible || applyPoverty.visible || applyManyChildren.visible">
            <div class="cardTitle">申请入学信息</div>
            <div class="cardCon">
              <el-form-item v-if="applyPolicyGuarantee.visible" prop="policyGuarantee" class="card_long">
                <div class="card">
                  <div :class="{'required': applyPolicyGuarantee.required}" class="cardName longName1">是否为政策保障性入学儿童</div>
                  <div class="cardContent">
                    <el-radio v-model="ruleForm.policyGuarantee" @change="handlePolicyGuarantee"
                              v-for="(item, index) in yesOrNoOptions" :key="index"
                              :label="item.dictValue">{{item.dictLabel}}</el-radio>
                  </div>
                </div>
              </el-form-item>
              <el-form-item  prop="policyBasedType" class="card_long" v-if="ruleForm.policyGuarantee === yesValue && applyPolicyGuarantee.visible">
                <div class="card">
                  <div class="cardName longName1 required">政策保障性入学类型</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.policyBasedType" placeholder="请选择">
                      <el-option v-for="(item, index) in policyBasedTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <div class="card_long" v-if="ruleForm.policyGuarantee === yesValue && applyPolicyGuarantee.visible">
                <div class="card1">
                  <div class="cardName longName1 required">政策保障性入学证明材料</div>
                  <div class="cardContent">
                    <image-upload :limit="limit" :file-size="fileSize" :file-unit="fileUnit" :drag="true"
                      :fileType="fileType"
                      :upload-text="uploadText"
                      :tip-text="handleUploadTipText(limit)"
                      :ref="uploadRefKey + $constants.POLICY_GUARANTEE"
                      @ok="handleChangeFile($event, $constants.POLICY_GUARANTEE)"
                    ></image-upload>
                  </div>
                </div>
              </div>
              <div v-if="applyPoverty.visible" class="zhanwei"></div>
              <el-alert
                  class="custom-alert"
                  v-if="applyPoverty.visible"
                  :title="`在本辖区连续工作生活一年以上的农村建档立卡贫困家庭的儿童提供省扶贫办扶贫手册及${handleFilingCardTime}县级扶贫办出具的证明，可选择“是否选择建档立卡户登记”`"
                  type="warning"
                  :closable="false"
                  show-icon>
              </el-alert>
              <el-form-item v-if="applyPoverty.visible" prop="filingCard" class="card_long">
                <div class="card">
                  <div :class="{'required': applyPoverty.required}" class="cardName longName1">是否选择建档立卡户登记</div>
                  <div class="cardContent">
                    <el-radio v-model="ruleForm.filingCard" @change="handleFilingCard"
                              v-for="(item, index) in yesOrNoOptions" :key="index"
                              :label="item.dictValue">{{item.dictLabel}}</el-radio>
                  </div>
                </div>
              </el-form-item>
              <div class="card_long" v-if="ruleForm.filingCard === yesValue && applyPoverty.visible">
                <div class="card1">
                  <div class="cardName longName1 required">扶贫手册及县扶贫办证明</div>
                  <div class="cardContent">
                    <image-upload :limit="limit" :file-size="fileSize" :file-unit="fileUnit" :drag="true"
                      :fileType="fileType"
                      :upload-text="uploadText"
                      :tip-text="handleUploadTipText(limit)"
                      :ref="uploadRefKey + $constants.POVERTY_ALLEVIATION"
                      @ok="handleChangeFile($event, $constants.POVERTY_ALLEVIATION)"
                    ></image-upload>
                  </div>
                </div>
              </div>
              <div v-if="applyManyChildren.visible" class="zhanwei"></div>
              <el-alert
                  class="custom-alert"
                  v-if="applyManyChildren.visible"
                  title="另有多子女在本辖区上学，可选择“是否申请多子女登记“"
                  type="warning"
                  :closable="false"
                  show-icon>
              </el-alert>
              <el-form-item v-if="applyManyChildren.visible" prop="multipleChildren" class="card_long">
                <div class="card">
                  <div :class="{'required': applyManyChildren.required}" class="cardName longName1">是否申请多子女登记</div>
                  <div class="cardContent">
                    <el-radio v-model="ruleForm.multipleChildren" @change="handleMultipleChildren"
                              v-for="(item, index) in yesOrNoOptions" :key="index"
                              :label="item.dictValue">{{item.dictLabel}}</el-radio>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="eldestChildName" v-if="ruleForm.multipleChildren === yesValue && applyManyChildren.visible">
                <div class="card">
                  <div class="cardName longName required">长子女姓名</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.eldestChildName" maxlength="10" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="eldestChildSchoolId" v-if="ruleForm.multipleChildren === yesValue && applyManyChildren.visible">
                <div class="card">
                  <div class="cardName longName required">长子女所在学校</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.eldestChildSchoolId" placeholder="输入学校名称进行检索" filterable @change="handleSchoolChange">
                      <el-option v-for="(item, index) in schoolOptions" :key="index" :label="item.deptName" :value="item.id"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="eldestChildStudentCode" v-if="ruleForm.multipleChildren === yesValue && applyManyChildren.visible">
                <div class="card">
                  <div class="cardName longName required">长子女国网学籍号</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.eldestChildStudentCode" maxlength="20" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="cardBox">
            <div class="promise-box">
              <el-checkbox class="my-checkbox" v-model="promiseCheckBox">
                  <span class="span-content">本人承诺所提交信息真实有效。</span>
              </el-checkbox>
            </div>
          </div>
          <div class="border_top"></div>
          <!-- 底部 -->
          <div class="foot_Box">
            <div @click="toschool()"><i class="el-icon-loading" v-if="loading"></i>上一步：选择报名学校</div>
            <div @click="submit('ruleForm')" :class="{'disabled_click': (!promiseCheckBox && !loading)}"><i class="el-icon-loading" v-if="loading"></i>确认保存</div>
          </div>
        </el-form>
      </div>
    </div>
    <Footbox class="foot_layout" />
    <foot />
  </div>
</template>

<script>
  import top from '../../components/top/top.vue'
  import foot from '../../components/foot/foot.vue'
  import {getApplyInfo, getSourceTypeList, saveApply, getApplySchoolList, getFormConfigList} from '@/api/apply'
  import {validIdCard, validMobile, validNumber, validPositiveInteger} from '@/utils/validate'
  import ImageUpload from '@/components/ImageUpload/index'
  import { listAreaTree } from '@/api/area'
  import Topbox from "@/views/layout/newtop";
  import Footbox from "@/views/layout/foot";
  import moment from 'moment';

  export default {
  components: {
    top,foot,ImageUpload,
    Topbox,
    Footbox
  },
  data() {
    const validateIdCard = (rule, value, callback) => {
      this.validIdCard(this.ruleForm.cardType, value, callback)
    }
    const validateIdCard1 = (rule, value, callback) => {
      this.validIdCard(this.ruleForm.guardianCardType1, value, callback)
    }
    const validateIdCard2 = (rule, value, callback) => {
      this.validIdCard(this.ruleForm.guardianCardType2, value, callback)
    }
    const validateIdCard3 = (rule, value, callback) => {
      if (value && !validIdCard(value)) {
        callback(new Error('身份证号不合法'))
      } else {
        callback()
      }
    }
    const validateMobile = (rule, value, callback) => {
      if (value && !validMobile(value)) {
        callback(new Error('请正确填写联系电话'))
      } else {
        callback()
      }
    }
    const validateDomicileAddress = (rule, value, callback) => {
      if (rule.required) {
        if (!this.ruleForm.domicileAddressAreaCode || !value) {
          callback(new Error('不能为空'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      loading: false,
      sourceTypeOptions: [],
      formConfigData:[],
      cardTypeOptions: [],
      genderOptions: [],
      yesOrNoOptions: [],
      haveOrNoOptions: [],
      nationOptions: [],
      countryOptions: [],
      healthConditionOptions: [],
      disabilityTypeOptions: [],
      relationOptions: [],
      houseCertifyTypeOptions: [],
      policyBasedTypeOptions: [],
      schoolOptions: [],
      domicileAddressAreaCodes: [],
      routerQuery: {},
      fileList: {},
      fileUploadList: [],
      fileType: ['jpg', 'png'],
      uploadText: '选择要上传的图片',
      tipText: '只能上传jpg/png文件，且不超过500kb,最多上传9张,上传后点击可查看大图。',
      limit: 9,
      oneLimit: 1,
      fileSize: 5,
      fileUnit: 'Mb',
      multiple: false,
      uploadRefKey: 'fileUpload_',
      operate: '',
      id: '',
      areaFourLevelTreeOptions: [],
      promiseCheckBox: false,
      cascaderProps: {
        label: 'label',
        value: 'id',
        children: 'children',
        leaf: 'leaf'
      },
      yesValue: 'Y',
      noValue: 'N',
      ruleForm: {
        id: '',
        schoolId: '',
        sourceTypeId: '',
        name: '',
        cardType: '01',
        idCard: '',
        birthday: '',
        gender: '',
        nation: '',
        mobile: '',
        country: '01',
        healthCondition: '01',
        inoculatePrevent: '',
        disabilityType: '0',
        domicileNumber: '',
        domicileHead: '',
        domicileHeadIdCard: '',
        domicileHeadRelation: '',
        domicileAddressAreaCode: '',
        domicileAddress: '',
        domicileRegisterTime: '',
        resideNumber: '',
        resideHead: '',
        resideHeadIdCard: '',
        resideHeadRelation: '',
        resideAddressAreaCode: '',
        resideAddress: '',
        resideRegisterTime: '',
        resideIssuePoliceStation: '',
        houseHave: '',
        houseHead: '',
        houseHeadIdCard: '',
        houseHeadRelation: '',
        houseCertifyType: '',
        houseCertifyNumber: '',
        houseRegisterTime: '',
        houseAddress: '',
        houseProperty: '',
        residentialAddress: '',
        policyGuarantee: '',
        policyBasedType: '',
        filingCard: '',
        multipleChildren: '',
        eldestChildName: '',
        eldestChildSchoolId: '',
        eldestChildSchoolName: '',
        eldestChildStudentCode: '',
        guardianId1: '',
        guardianName1: '',
        guardianCardType1: '01',
        guardianIdCard1: '',
        guardianMobile1: '',
        guardianRelation1: '',
        guardianId2: '',
        guardianName2: '',
        guardianCardType2: '',
        guardianIdCard2: '',
        guardianMobile2: '',
        guardianRelation2: '',
        picture: '',
        applyType: '',
        educationNature: '',
        personnelType: '',
        deptId: '',
        areaId: '',
        parentAreaId: '',
        overageChild: '',
      },
      rules: {
        sourceTypeId: [{ required: true, message: '不能为空', trigger: 'change' }],
        name: [{ required: true, message: '不能为空', trigger: 'blur' }],
        cardType: [
          { required: true, message: '不能为空', trigger: 'change' },
        ],
        idCard: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { validator: validateIdCard, trigger: 'blur' }
        ],
        birthday: [{ required: true, message: '不能为空', trigger: 'change' }],
        gender: [{ required: true, message: '不能为空', trigger: 'change' }],
        nation: [{ required: true, message: '不能为空', trigger: 'change' }],
        mobile: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' },
        ],
        inoculatePrevent: [{ required: true, message: '不能为空', trigger: 'change' }],
        residentialAddress: [{ required: true, message: '不能为空', trigger: 'blur' }],
        guardianName1: [{ required: true, message: '不能为空', trigger: 'blur' }],
        guardianRelation1: [{ required: true, message: '不能为空', trigger: 'blur' }],
        guardianCardType1: [{ required: true, message: '不能为空', trigger: 'blur' }],
        guardianIdCard1: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { validator: validateIdCard1, trigger: 'blur' },
        ],
        guardianMobile1: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' },
        ],
        guardianNation1: [{ required: false, message: '不能为空', trigger: 'blur' }],
        guardianName2: [{ required: false, message: '不能为空', trigger: 'blur' }],
        guardianRelation2: [{ required: false, message: '不能为空', trigger: 'blur' }],
        guardianCardType2: [{ required: false, message: '不能为空', trigger: 'blur' }],
        guardianIdCard2: [
          { required: false, message: '不能为空', trigger: 'blur' },
          { validator: validateIdCard2, trigger: 'blur' },
        ],
        guardianMobile2: [
          { required: false, message: '不能为空', trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' },
        ],
        country: [{ required: true, message: '不能为空', trigger: 'change' }],
        nativePlace: [{ required: true, message: '不能为空', trigger: 'blur' }],
        healthCondition: [{ required: true, message: '不能为空', trigger: 'blur' }],
        disabilityType: [{ required: false, message: '不能为空', trigger: 'blur' }],
        domicileNumber: [{ required: true, message: '不能为空', trigger: 'blur' }],
        domicileHead: [{ required: true, message: '不能为空', trigger: 'blur' }],
        domicileHeadRelation: [{ required: true, message: '不能为空', trigger: 'blur' }],
        domicileHeadIdCard: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { validator: validateIdCard3, trigger: 'blur' }
        ],
        domicileAddress: [
          { required: true, validator: validateDomicileAddress, message: '', trigger: 'blur' }],
        domicileRegisterTime: [{ required: true, message: '不能为空', trigger: 'change' }],
        resideHead: [{ required: true, message: '不能为空', trigger: 'blur' }],
        resideHeadIdCard: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { validator: validateIdCard3, trigger: 'blur' }
        ],
        resideRegisterTime: [{ required: true, message: '不能为空', trigger: 'change' }],
        resideHeadRelation: [{ required: true, message: '不能为空', trigger: 'blur' }],
        resideAddress: [{ required: true, message: '不能为空', trigger: 'blur' }],
        resideIssuePoliceStation: [{ required: true, message: '不能为空', trigger: 'blur' }],
        houseHave: [{ required: true, message: '不能为空', trigger: 'blur' }],
        houseHead: [{ required: true, message: '不能为空', trigger: 'blur' }],
        houseHeadIdCard: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { validator: validateIdCard3, trigger: 'blur' }
        ],
        houseHeadRelation: [{ required: true, message: '不能为空', trigger: 'change' }],
        houseCertifyType: [{ required: true, message: '不能为空', trigger: 'change' }],
        houseCertifyNumber: [{ required: true, message: '不能为空', trigger: 'blur' }],
        houseRegisterTime: [{ required: true, message: '不能为空', trigger: 'change' }],
        houseAddress: [{ required: true, message: '不能为空', trigger: 'blur' }],
        houseProperty: [{ required: true, message: '不能为空', trigger: 'blur' }],
        policyGuarantee: [{ required: true, message: '不能为空', trigger: 'change' }],
        policyBasedType: [{ required: true, message: '不能为空', trigger: 'change' }],
        filingCard: [{ required: true, message: '不能为空', trigger: 'change' }],
        multipleChildren: [{ required: true, message: '不能为空', trigger: 'change' }],
        eldestChildName: [{ required: true, message: '不能为空', trigger: 'blur' }],
        eldestChildSchoolId: [{ required: true, message: '不能为空', trigger: 'change' }],
        eldestChildStudentCode: [{ required: true, message: '不能为空', trigger: 'blur' }],
      },
      infoPhoto:{visible: true, required: false},
      parentsResidencePermit	:{visible: true, required: true},
      houseNoRoomCertificate:{visible: true, required: true},
      houseRentRoomCertificate:{visible: true, required: false},
      applyPolicyGuarantee:{visible: true, required: true},
      applyPoverty	:{visible: true, required: true},
      applyManyChildren:{visible: true, required: true}
    }
  },
  beforeDestroy() {},
  created() {
    this.routerQuery = this.$commonUtils.getApplyParams(this.$constants.STORE_CACHE_KEY_APPLY) || {}
    this.getDicts('sys_card_type').then(response => {
      this.cardTypeOptions = response.data
    })
    this.getDicts('sys_user_sex').then(response => {
      this.genderOptions = response.data
    })
    this.getDicts('sys_nation').then(response => {
      this.nationOptions = response.data
    })
    this.getDicts('sys_yes_no').then(response => {
      this.yesOrNoOptions = response.data
    })
    this.getDicts('sys_have_no').then(response => {
      this.haveOrNoOptions = response.data
    })
    this.getDicts('sys_country').then(response => {
      this.countryOptions = response.data
    })
    this.getDicts('sys_health_condition').then(response => {
      this.healthConditionOptions = response.data
    })
    this.getDicts('sys_disability_type').then(response => {
      this.disabilityTypeOptions = response.data
    })
    this.getDicts('sys_relation').then(response => {
      this.relationOptions = response.data
    })
    this.getDicts('biz_house_certify_type').then(response => {
      this.houseCertifyTypeOptions = response.data
    })
    this.getDicts('biz_policy_based_type').then(response => {
      this.policyBasedTypeOptions = response.data
    })
    this.getTreeSelect('0', 3).then(data => {
      this.areaFourLevelTreeOptions = data
    })
  },
  mounted() {
    this.ruleForm.schoolName = this.routerQuery.schoolName
    this.operate = this.routerQuery.operate || ''
    this.id = this.routerQuery.id
    if (this.id) {
      this.getApplyData(this.id)
    } else {
      this.ruleForm.personnelType = this.routerQuery.personnelType
      this.getSourceTypeData(this.routerQuery.schoolId)
      this.getFormConfigData(this.routerQuery.schoolId)
      this.getSchoolData(this.routerQuery.areaId, this.routerQuery.type)
    }
  },
  computed: {
    handleExceedAge() {
      if (this.ruleForm && this.ruleForm.birthday) {
        return this.validateExceedAge(this.ruleForm.birthday)
      }
      return false;
    },
    handleFilingCardTime() {
      const currentYear = new Date().getFullYear();
      return ((currentYear - 1) + '年');
    }
  },
  methods: {
    handleUploadTipText(limit) {
      return `只能上传${this.fileType.join('/')}文件，且不超过${this.fileSize}${this.fileUnit}，最多上传${limit}张，上传后点击可查看大图。`
    },
    getSchoolData(areaId, type) {
      const data = {
        areaId: areaId,
        deptClassify: type
      }
      getApplySchoolList(data).then(response => {
        this.schoolOptions = (response && response.data) ? (response.data || []) : []
      })
    },
    getTreeSelect(parentId, level) {
      return new Promise((resolve, reject) => {
        listAreaTree(parentId, level).then(response => {
          const data = response.data;
          if (data && data.length > 0) {
            data.sort((a, b) => {
              if (a.id === this.$constants.DEFAULT_AREA_ROOT_ID) return -1;
              if (b.id === this.$constants.DEFAULT_AREA_ROOT_ID) return 1;
              return 0;
            });
            resolve(data)
          }
        }).catch((err) => {
          reject(err)
        })
      })
    },
    getApplyData(id) {
      getApplyInfo(id).then(async response => {
        this.loading = true
        const data = response.data || {}
        // 优先回显生源类型
        await this.getSourceTypeData(data.schoolId)
        await this.getFormConfigData(data.schoolId)
        await this.getSchoolData(data.areaId, data.applyType)
        // 回显表单
        this.ruleForm = data

        // 处理路由中的参数
        if (this.routerQuery.schoolId && this.routerQuery.schoolId !== this.ruleForm.schoolId) {
          this.ruleForm.schoolId = this.routerQuery.schoolId
          this.ruleForm.schoolName = this.routerQuery.schoolName
        }
        // 设置路由
        if (!this.routerQuery.schoolId) {
          this.routerQuery.schoolId = data.schoolId
          this.routerQuery.schoolName = data.schoolName
        }
        if (!this.routerQuery.areaId) {
          this.routerQuery.areaId = data.areaId
          this.routerQuery.areaName = data.areaName
        }
        if (!this.routerQuery.type) {
          this.routerQuery.type = data.applyType
        }
        if (!this.routerQuery.cityId) {
          this.routerQuery.cityId = data.parentAreaId
          this.routerQuery.cityName = data.parentAreaName
        }
        if (!this.routerQuery.nature) {
          this.routerQuery.nature = data.educationNature
          this.routerQuery.natureName = data.educationNatureName
        }
        if (!this.routerQuery.personnelType) {
          this.routerQuery.personnelType = data.personnelType
        }

        // 回显监护人
        let guardian1 = {}, guardian2 = {}
        if (data.guardianPeopleList && data.guardianPeopleList.length > 0) {
          const length = data.guardianPeopleList.length
          if (length > 0) {
            guardian1 = data.guardianPeopleList[0] || {}
          }
          if (length > 1) {
            guardian2 = data.guardianPeopleList[1] || {}
          }
        }
        // 第一个人
        this.$set(this.ruleForm, 'guardianId1', guardian1.id)
        this.$set(this.ruleForm, 'guardianName1', guardian1.name)
        this.$set(this.ruleForm, 'guardianIdCard1', guardian1.idCard)
        this.$set(this.ruleForm, 'guardianMobile1', guardian1.mobile)
        this.$set(this.ruleForm, 'guardianRelation1', guardian1.relation)
        this.$set(this.ruleForm, 'guardianCardType1', guardian1.cardType)

        // 第二个人
        this.$set(this.ruleForm, 'guardianId2', guardian2.id)
        this.$set(this.ruleForm, 'guardianName2', guardian2.name)
        this.$set(this.ruleForm, 'guardianIdCard2', guardian2.idCard)
        this.$set(this.ruleForm, 'guardianMobile2', guardian2.mobile)
        this.$set(this.ruleForm, 'guardianRelation2', guardian2.relation)
        this.$set(this.ruleForm, 'guardianCardType2', guardian2.cardType)

        // 回显级联选
        if (data.domicileAddressAreaCode) {
          this.domicileAddressAreaCodes = data.domicileAddressAreaCode.split('/')
        }

        // 回显附件
        if (data && data.attachmentList && data.attachmentList.length > 0) {
          this.$nextTick(() => {
            this.showFileList(data.attachmentList)
            this.loading = false
          })
        }

      })
    },
    showFileList(files) {
      const that = this
      const fileList = {}
      if (files && files.length > 0) {
        for (let i = 0; i < files.length; i++) {
          const file = files[i]
          const fileData = {
            id: file.id,
            size: file.attachSize,
            name: file.attachName,
            url: file.fileUrl,
            path: file.fileUrl
          }
          let urlArr = fileList[file.businessType]
          if (!urlArr) {
            urlArr = []
          }
          urlArr.push(fileData)
          fileList[file.businessType] = urlArr
        }
        Object.keys(fileList).forEach(item => {
          const refKey = that.uploadRefKey + item
          const tp = that.$refs[refKey]
          if (tp !== null && tp !== undefined && tp !== '') {
            tp.showFiles(fileList[item])
          }
        })
      }
      that.fileList = fileList
    },
    getSourceTypeData(schoolId) {
      getSourceTypeList({schoolId: schoolId}).then(response => {
        this.sourceTypeOptions = response.data || []
      })
    },

    getFormConfigData(schoolId) {
      getFormConfigList({schoolId: schoolId}).then(response => {
        this.formConfigData = response.data || []
        this.formConfigData.forEach(item => {
          if (item.code === this.$constants.DICT_INFO_PHOTO) {
            this.infoPhoto.visible = item.visible === this.yesValue;
            this.infoPhoto.required = item.required === this.yesValue;
          }
          if (item.code === this.$constants.DICT_PARENTS_RESIDENCE_PERMIT) {
            this.parentsResidencePermit.visible = item.visible === this.yesValue;
            this.parentsResidencePermit.required = item.required === this.yesValue;
            this.rules.resideRegisterTime[0].required = this.parentsResidencePermit.required;
            this.rules.resideHeadRelation[0].required = this.parentsResidencePermit.required;
            this.rules.resideIssuePoliceStation[0].required = this.parentsResidencePermit.required;
            this.rules.resideAddress[0].required = this.parentsResidencePermit.required;
          }
          if (item.code === this.$constants.DICT_HOUSE_NO_ROOM_CERTIFICATE) {
            this.houseNoRoomCertificate.visible = item.visible === this.yesValue;
            this.houseNoRoomCertificate.required = item.required === this.yesValue;
          }
          if (item.code === this.$constants.DICT_HOUSE_RENT_ROOM_CERTIFICATE) {
            this.houseRentRoomCertificate.visible = item.visible === this.yesValue;
            this.houseRentRoomCertificate.required = item.required === this.yesValue;
          }
          if (item.code === this.$constants.DICT_APPLY_POLICY_GUARANTEE) {
            this.applyPolicyGuarantee.visible = item.visible === this.yesValue;
            this.applyPolicyGuarantee.required = item.required === this.yesValue;
            this.rules.policyGuarantee[0].required = this.applyPolicyGuarantee.required;
          }
          if (item.code === this.$constants.DICT_APPLY_POVERTY) {
            this.applyPoverty.visible = item.visible === this.yesValue;
            this.applyPoverty.required = item.required === this.yesValue;
            this.rules.filingCard[0].required = this.applyPoverty.required;
          }
          if (item.code === this.$constants.DICT_APPLY_MANY_CHILDREN) {
            this.applyManyChildren.visible = item.visible === this.yesValue;
            this.applyManyChildren.required = item.required === this.yesValue;
            this.rules.multipleChildren[0].required = this.applyManyChildren.required;
          }
        })
      })
    },

    submit(formName) {
      if (this.loading) { return }
      if (this.validateApplyAge(this.ruleForm.birthday)) {
        this.$message.warning('年龄不满六周岁，不能报名！', 3)
        return
      }
      const that = this
      that.$refs[formName].validate(valid => {
        if (valid) {
          if (!this.handleBeforeValidate()) {
            return;
          }

          // 设置之前流程选择的数据
          if (that.routerQuery.schoolId) {
            that.ruleForm.schoolId = that.routerQuery.schoolId;
            that.ruleForm.deptId = that.routerQuery.schoolId
          }
          if (that.routerQuery.type) { that.ruleForm.applyType = that.routerQuery.type }
          if (that.routerQuery.areaId) { that.ruleForm.areaId = that.routerQuery.areaId }
          if (that.routerQuery.nature) { that.ruleForm.educationNature = that.routerQuery.nature }
          if (that.routerQuery.personnelType) { that.ruleForm.personnelType = that.routerQuery.personnelType }
          // 设置其它数据
          const saveForm = JSON.parse(JSON.stringify(that.ruleForm))
          let allFileList = {}
          if (that.fileList) {
            allFileList = JSON.parse(JSON.stringify(that.fileList))
          }
          // 设置房产信息
          if (!saveForm.houseHave || saveForm.houseHave === this.noValue) {
            saveForm.houseHead = ''
            saveForm.houseHeadIdCard = ''
            saveForm.houseCertifyType = ''
            saveForm.houseCertifyNumber = ''
            saveForm.houseRegisterTime = ''
            saveForm.houseHeadRelation = ''
            saveForm.houseAddress = ''
            allFileList[that.$constants.HOUSE_PROPRIETARY_CERTIFICATE] = []
          } else {
            allFileList[that.$constants.NO_HOUSE_CERTIFICATE] = []
            allFileList[that.$constants.HOUSE_LEASE_CERTIFICATE] = []
            allFileList[that.$constants.OTHER_CERTIFICATE] = []
          }
          // 政策保障性入学儿童
          if (!saveForm.policyGuarantee || saveForm.policyGuarantee === this.noValue) {
            saveForm.policyBasedType = ''
            allFileList[that.$constants.POLICY_GUARANTEE] = []
          }
          // 建档立卡户登记
          if (!saveForm.filingCard || saveForm.filingCard === this.noValue) {
            allFileList[that.$constants.POVERTY_ALLEVIATION] = []
          }
          // 多子女登记
          if (!saveForm.multipleChildren || saveForm.multipleChildren === this.noValue) {
            saveForm.eldestChildName = ''
            saveForm.eldestChildSchoolId = ''
            saveForm.eldestChildStudentCode = ''
          }
          // 超龄儿童
          if (that.validateExceedAge(saveForm.birthday)) {
            saveForm.overageChild = that.yesValue
          } else {
            saveForm.overageChild = that.noValue
            allFileList[that.$constants.POSTPONE_ENTER_SCHOOL_APPLY] = []
          }
          // 免冠照片
          let picture = ''
          const fileList = []
          // 保存其它附件
          if (allFileList) {
            let ids = ''
            Object.keys(allFileList).forEach(item => {
              const files = allFileList[item]
              if (files && files.length > 0) {
                files.forEach(obj => {
                  if (this.$constants.FILE_TYPE_AVATAR === item) {
                    ids +=  (',' + obj.id)
                  }
                  fileList.push({
                    id: obj.id,
                    businessType: item,
                    attachName: obj.name,
                    fileUrl: obj.path,
                    attachSize: obj.size,
                    fileId: obj.id
                  })
                })
              }
            })
            picture = ids.substring(1)
          }
          saveForm.picture = picture
          saveForm.attachmentList = fileList
          // 监护人
          const guardianList = []
          if (saveForm.guardianName1) {
            guardianList.push({
              id: saveForm.guardianId1,
              name: saveForm.guardianName1,
              idCard: saveForm.guardianIdCard1,
              mobile: saveForm.guardianMobile1,
              relation: saveForm.guardianRelation1,
              cardType: saveForm.guardianCardType1,
              sort: 1
            })
          }
          if (saveForm.guardianName2) {
            guardianList.push({
              id: saveForm.guardianId2,
              name: saveForm.guardianName2,
              idCard: saveForm.guardianIdCard2,
              mobile: saveForm.guardianMobile2,
              relation: saveForm.guardianRelation2,
              cardType: saveForm.guardianCardType2,
              sort: 2
            })
          }
          saveForm.guardianPeopleList = guardianList

          that.loading = true
          saveApply(saveForm).then(response => {
            that.$message.success('保存成功')
            setTimeout(() => {
              that.toapplication()
            }, 1000)
          }).catch(() => {
            that.loading = false
          })
        } else {
          this.$message.warning('请填写校验项!')
          return false
        }
      })
    },
    handleBeforeValidate() {
      const arr = []
      if (this.applyPolicyGuarantee.visible) {
        arr.push({id: this.ruleForm.policyGuarantee, name: '政策保障性入学儿童'})
      }
      if (this.applyPoverty.visible) {
        arr.push({id: this.ruleForm.filingCard, name: '建档立卡户登记'})
      }
      if (this.applyManyChildren.visible) {
        arr.push({id: this.ruleForm.multipleChildren, name: '多子女登记'})
      }
      const filter = arr.filter(item => item.id === this.yesValue);
      if (filter && filter.length > 1) {
        const title = filter.map(item => item.name).join('、')
        this.$message.warning(title + '中只能选择一项申请', 3)
        return false;
      }

      if (this.infoPhoto.visible && this.infoPhoto.required
        && (!this.fileList
          || !this.fileList[this.$constants.FILE_TYPE_AVATAR]
          || this.fileList[this.$constants.FILE_TYPE_AVATAR].length <= 0)) {
        this.$message.warning('免冠照片未上传', 3)
        return false;
      }

      if (this.ruleForm.personnelType === this.$constants.RESIDENT_TYPE_HJ) {
        if (!this.fileList
          || !this.fileList[this.$constants.HOUSEHOLD_REGISTER]
          || this.fileList[this.$constants.HOUSEHOLD_REGISTER].length <= 0) {
          this.$message.warning('户口簿首页照片未上传', 3)
          return false;
        } else if (!this.fileList
          || !this.fileList[this.$constants.HOUSEHOLD_REGISTER_HEAD]
          || this.fileList[this.$constants.HOUSEHOLD_REGISTER_HEAD].length <= 0) {
          this.$message.warning('户口簿户主页照片未上传', 3)
          return false;
        } else if (!this.fileList
          || !this.fileList[this.$constants.HOUSEHOLD_REGISTER_CHILD]
          || this.fileList[this.$constants.HOUSEHOLD_REGISTER_CHILD].length <= 0) {
          this.$message.warning('户口簿儿童页照片未上传', 3)
          return false;
        }
      } else if (this.ruleForm.personnelType === this.$constants.RESIDENT_TYPE_SQ) {
        if (this.parentsResidencePermit.visible && this.parentsResidencePermit.required && (!this.fileList
          || !this.fileList[this.$constants.RESIDENCE_PERMIT_FRONT]
          || this.fileList[this.$constants.RESIDENCE_PERMIT_FRONT].length <= 0)) {
          this.$message.warning('居住证正面照片未上传', 3)
          return false;
        } else if (this.parentsResidencePermit.visible && this.parentsResidencePermit.required && (!this.fileList
          || !this.fileList[this.$constants.RESIDENCE_PERMIT_BACK]
          || this.fileList[this.$constants.RESIDENCE_PERMIT_BACK].length <= 0)) {
          this.$message.warning('居住证反面照片未上传', 3)
          return false;
        }
      }

      if(!this.ruleForm.houseHave || this.ruleForm.houseHave === ""){
        this.$message.warning('请选择片区内有无房产', 3)
        return false;;
      }

      if (this.ruleForm.houseHave === this.yesValue) {
        if (!this.fileList
          || !this.fileList[this.$constants.HOUSE_PROPRIETARY_CERTIFICATE]
          || this.fileList[this.$constants.HOUSE_PROPRIETARY_CERTIFICATE].length <= 0) {
          this.$message.warning('房产证主体信息页照片未上传', 3)
          return false;
        }
      } else {
        if (this.houseNoRoomCertificate.visible && this.houseNoRoomCertificate.required && (!this.fileList
          || !this.fileList[this.$constants.NO_HOUSE_CERTIFICATE]
          || this.fileList[this.$constants.NO_HOUSE_CERTIFICATE].length <= 0)) {
          this.$message.warning('无房证明照片未上传', 3)
          return false;
        }

        if (this.houseRentRoomCertificate.visible && this.houseRentRoomCertificate.required && (!this.fileList
          || !this.fileList[this.$constants.HOUSE_LEASE_CERTIFICATE]
          || this.fileList[this.$constants.HOUSE_LEASE_CERTIFICATE].length <= 0)) {
          this.$message.warning('房屋租赁证明照片未上传', 3)
          return false;
        }
      }

      if(this.applyManyChildren.visible && this.applyManyChildren.required && (!this.ruleForm.policyGuarantee || this.ruleForm.policyGuarantee === "")){
        this.$message.warning('请选择是否为政策保障性入学儿童', 3)
        return false;;
      }

      if (this.ruleForm.policyGuarantee === this.yesValue) {
        if (this.applyPolicyGuarantee.visible && this.applyPolicyGuarantee.required
          && (!this.fileList
            || !this.fileList[this.$constants.POLICY_GUARANTEE]
            || this.fileList[this.$constants.POLICY_GUARANTEE].length <= 0)) {
          this.$message.warning('政策保障性入学证明材料未上传', 3)
          return false;
        }
      }

      if(this.applyPoverty.visible && this.applyPoverty.required && (!this.ruleForm.filingCard || this.ruleForm.filingCard === "")){
        this.$message.warning('请选择是否选择建档立卡户登记', 3)
        return false;;
      }

      if (this.ruleForm.filingCard === this.yesValue) {
        if (this.applyPoverty.visible && this.applyPoverty.required
          && (!this.fileList
            || !this.fileList[this.$constants.POVERTY_ALLEVIATION]
            || this.fileList[this.$constants.POVERTY_ALLEVIATION].length <= 0)) {
          this.$message.warning('扶贫手册及县扶贫办证明未上传', 3)
          return false;
        }
      }

      if(this.applyManyChildren.visible && this.applyManyChildren.required && (!this.ruleForm.multipleChildren || this.ruleForm.multipleChildren === "")){
        this.$message.warning('请选择是否申请多子女登记', 3)
        return false;;
      }

      if (this.ruleForm.houseHave === this.noValue) {
        const fileArr = []
        if (this.houseNoRoomCertificate.visible && !this.houseNoRoomCertificate.required) {
          fileArr.push({id: this.$constants.NO_HOUSE_CERTIFICATE, name: '无房证明照片'})
        }
        if (this.houseRentRoomCertificate.visible && !this.houseRentRoomCertificate.required) {
          fileArr.push({id: this.$constants.HOUSE_LEASE_CERTIFICATE, name: '房屋租赁证明照片'})
        }
        fileArr.push({id: this.$constants.OTHER_CERTIFICATE, name: '其他证明材料照片'})
        let flag = false
        fileArr.forEach(item => {
          if ((this.fileList
            && this.fileList[item.id]
            && this.fileList[item.id].length > 0)) {
            flag = true;
          }
        })
        if (!flag) {
          const title = fileArr.map(item => item.name).join('、')
          this.$message.warning(title + '中必须上传一项', 3)
          return false;
        }
      }

      return true;
    },
    handleChangeFile(fileList, type) {
      this.fileList[type] = fileList
    },
    toschool(){
      if (this.loading) { return }
      this.$commonUtils.setApplyParams(this.routerQuery, this.$constants.STORE_CACHE_KEY_APPLY)
      this.$router.push({
        path: "/school"
      })
    },
    toapplication(){
      const data = {
        cityId: this.routerQuery.cityId,
        cityName: this.routerQuery.cityName,
        areaId: this.routerQuery.areaId,
        areaName: this.routerQuery.areaName
      }
      this.$commonUtils.setApplyParams(data, this.$constants.STORE_CACHE_KEY_APPLY)
      this.$router.push({path: "/application"})
    },
    handleDomicileAddressAreaCodesChange(val) {
      let code = ''
      if (val && val.length > 0) {
        code = val.join('/')
      }
      this.ruleForm.domicileAddressAreaCode = code
    },
    validIdCard(type, value, callback) {
      if (type === '01' && value) {
        if (!validIdCard(value)) {
          callback(new Error('身份证号不合法'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    handleIdCardChange(val) {
      if (!val) { return }
      const idCard = val.target.value
      if (this.ruleForm.cardType === '01') {
        let year, month, day, sexNumber;
        if (idCard.length === 15) {
          year = idCard.substring(6, 8)
          month = idCard.substring(8, 10)
          day = idCard.substring(10, 12)
          year = (year.startsWith('9') ? '19' : '20') + year
          sexNumber = idCard.substring(14, 15);
        } else if (idCard.length === 18) {
          year = idCard.substring(6, 10)
          month = idCard.substring(10, 12)
          day = idCard.substring(12, 14)
          sexNumber = idCard.substring(16, 17);
        }
        if (validPositiveInteger(year) && validPositiveInteger(month) && validPositiveInteger(day)) {
          this.ruleForm.birthday = year + '-' + month + '-' + day
        }
        if (validPositiveInteger(sexNumber)) {
          const gender = parseInt(sexNumber, 10)
          this.ruleForm.gender = (gender % 2 === 0) ? '2' : '1'
        }
      }
    },
    handlePolicyGuarantee(val) {
      const title = '政策保障性入学儿童'
      if (val === this.yesValue) {
        if (this.ruleForm.filingCard === this.yesValue) {
          this.$message.warning(`您已经选择了建档立卡户登记，不能再选择${title}`, 3)
          this.ruleForm.policyGuarantee = this.noValue
          return
        }
        if (this.ruleForm.multipleChildren === this.yesValue) {
          this.$message.warning(`您已经选择了多子女登记，不能再选择${title}`, 3)
          this.ruleForm.policyGuarantee = this.noValue
          return
        }
      }
    },
    handleFilingCard(val) {
      const title = '建档立卡户登记'
      if (val === this.yesValue) {
        if (this.ruleForm.policyGuarantee === this.yesValue) {
          this.$message.warning(`您已经选择了政策保障性入学儿童，不能再选择${title}`, 3)
          this.ruleForm.filingCard = this.noValue
          return
        }
        if (this.ruleForm.multipleChildren === this.yesValue) {
          this.$message.warning(`您已经选择了多子女登记，不能再选择${title}`, 3)
          this.ruleForm.filingCard = this.noValue
          return
        }
      }
    },
    handleMultipleChildren(val) {
      const title = '多子女登记'
      if (val === this.yesValue) {
        if (this.ruleForm.policyGuarantee === this.yesValue) {
          this.$message.warning(`您已经选择了政策保障性入学儿童，不能再选择${title}`, 3)
          this.ruleForm.multipleChildren = this.noValue
          return
        }
        if (this.ruleForm.filingCard === this.yesValue) {
          this.$message.warning(`您已经选择了建档立卡户登记，不能再选择${title}`, 3)
          this.ruleForm.multipleChildren = this.noValue
          return
        }
      }
    },
    handleSchoolChange(val) {
      let schoolId = '', schoolName = ''
      if (val) {
        const obj = this.schoolOptions.find(item => item.id === val)
        if (obj) {
          schoolId = obj.id
          schoolName = obj.deptName
        }
      }
      this.ruleForm.eldestChildSchoolId = schoolId
      this.ruleForm.eldestChildSchoolName = schoolName
    },
    validateExceedAge(birthday) {
      if (birthday) {
        const sevenYearsAgo = moment().subtract(7, 'years').month(7).date(31);
        let birthDate = moment(birthday);
        return birthDate.isBefore(sevenYearsAgo);
      }
      return false;
    },
    validateApplyAge(birthday) {
      if (birthday) {
        const sevenYearsAgo = moment().subtract(6, 'years').month(7).date(31);
        let birthDate = moment(birthday);
        return birthDate.isAfter(sevenYearsAgo);
      }
      return false;
    },
    handleHouseCertifyTypeChange(val) {
      if (val === '3') {
        this.rules.houseCertifyNumber[0].required = false
      } else {
        this.rules.houseCertifyNumber[0].required = true
      }
    },
    handleGuardianName2Change(val) {
      if (!val) { return }
      const value = val.target.value
      let verify = false
      if (value) {
        verify = true
      }
      this.rules.guardianName2[0].required = verify
      this.rules.guardianMobile2[0].required = verify
      this.rules.guardianCardType2[0].required = verify
      this.rules.guardianIdCard2[0].required = verify
      this.rules.guardianRelation2[0].required = verify
    },
    handleRadioChange(val) {
      const arr = []
      if (val === this.noValue) {
        arr.push(this.$constants.NO_HOUSE_CERTIFICATE)
        arr.push(this.$constants.HOUSE_LEASE_CERTIFICATE)
        arr.push(this.$constants.OTHER_CERTIFICATE)
      } else if (val === this.yesValue){
        arr.push(this.$constants.HOUSE_PROPRIETARY_CERTIFICATE)
      }
      if (arr && arr.length > 0) {
        arr.forEach(item => {
          const refKey = this.uploadRefKey + item
          const tp = this.$refs[refKey]
          if (tp !== null && tp !== undefined && tp !== '') {
            tp.showFiles([])
          }
        })
      }
    }
  },
}
</script>
<style lang="scss" scoped>

//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {

  .cardBox{
    width: calc(100% - 80px);
    margin: 0 0 0 40px;
    border: 1px solid #E1E1E1;
    position: relative;
    border-radius: 8px;
    margin-bottom: 30px;
    .cardTitle{
      position: absolute;
      width: auto;
      height: 30px;
      line-height: 30px;
      top: -15px;
      left: 15px;
      padding:0 15px;
      background: #ffffff;
      color: #3570f6;
      font-size: 18px;
    }
    .cardCon{
      width: 100%;
      height: auto;
      padding:30px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 20px;
      .zhanwei{
        width: 100%;
        height: 5px;
        background: #f1f1f1;
      }
      .zhanweiLong{
        width: 100%;
        height: 5px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #f1f1f1;
        margin: 15px 0;
        span{
          background: #ffffff;
          font-size: 18px;
          font-weight: bold;
          padding:0 15px;
          color: #3570f6;
        }
      }
      .el-form-item{
        width: calc(50% - 10px);
        margin: 0;
        &.card_long{
          width: 100%;
        }
      }
      ::v-deep .el-upload-dragger{
        width: 100%;
        height: 100%;
      }
      .card{
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border: 1px solid #E6E6E6;
        border-radius: 6px;
        .required {
          &::after{
            content: "*";
            color: #ff3300;
            margin: 2px 5px;
          }
        }
        .cardName{
          width: auto;
          min-width: 130px;
          height: 50px;
          background: #FAFAFA;
          padding:0 15px;
          display: flex;
          justify-content: center;
          align-items: center;
          white-space: nowrap;
          border-radius: 4px 0 0 4px;
          font-size: 18px;
          color: #383838;
          border-right: 1px solid #e6e6e6;
          &.longName{
            min-width: 200px;
          }
          &.longName1{
            min-width: 250px;
          }
        }
        .cardContent{
          width: 100%;
          height: 50px;
          border-radius: 0 4px 4px 0 ;
          padding: 0 15px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          font-size: 18px;
          color: #383838;
          input::placeholder {
            color: #C0C4CC;
          }
          ::v-deep .el-select{
            .el-input{
              input{
                border: none;
                font-size: 18px;
                padding: 0;
                background: none;
              }
            }
          }
          ::v-deep .el-cascader{
            .el-input{
              input{
                border: none;
                font-size: 18px;
                padding: 0;
                background: none;
              }
            }
          }
          ::v-deep .el-date-editor{
            width: 100%;
            height: 100%;
            input{
              height: 100%;
              border: none;
              background: none;
              font-size: 18px;
              padding-left: 40px;
            }
            .el-input__icon{
              font-size: 18px;
            }
          }
          .cardTxt{
            width: 100%;
            height: 100%;
            border: none;
            background: none;
            font-size: 18px;
            color: #383838;
            outline: none;
          }
          .cont_box_left {
            width: 42%;
            border-right: 3px solid #f1f1f1;
          }
          .cont_box_right {
            margin-left: 20px;
            width: 57%;
          }

        }
        &.card_long{
          width: 100%;
        }
      }
      .card1{
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        border-radius: 6px;
        .required {
          &::after{
            content: "*";
            color: #ff3300;
            margin: 2px 5px;
          }
        }
        .cardName{
          width: auto;
          min-width: 130px;
          height: 50px;
          background: #FAFAFA;
          padding:0 15px;
          display: flex;
          justify-content: center;
          align-items: center;
          white-space: nowrap;
          border-radius: 4px 0 0 4px;
          font-size: 18px;
          color: #383838;
          border: 1px solid #E6E6E6;
          &.longName{
            min-width: 200px;
          }
          &.longName1{
            min-width: 250px;
          }
        }
        .cardContent{
          width: 100%;
          height: auto;
          border-radius: 0 4px 4px 0 ;
          padding: 0 15px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          font-size: 18px;
          color: #383838;
          input::placeholder {
            color: #C0C4CC;
          }
          ::v-deep .el-select{
            .el-input{
              input{
                border: none;
                font-size: 18px;
                padding: 0;
                background: none;
              }
            }
          }
          ::v-deep .el-cascader{
            .el-input{
              input{
                border: none;
                font-size: 18px;
                padding: 0;
                background: none;
              }
            }
          }
          ::v-deep .el-date-editor{
            width: 100%;
            height: 100%;
            input{
              height: 100%;
              border: none;
              background: none;
              font-size: 18px;
              padding-left: 40px;
            }
            .el-input__icon{
              font-size: 18px;
            }
          }
          .cardTxt{
            width: 100%;
            height: 100%;
            border: none;
            background: none;
            font-size: 18px;
            color: #383838;
            outline: none;
          }

        }
        &.card_long{
          width: 100%;
        }
      }
      .upload-demo {
        width: 100%;
        .el-upload__tip {
          width: calc(100% - 250px);
          height: 54px;
          line-height: 54px;
          font-size: 16px;
          color: #383838;
        }
      }
    }
    .promise-box {
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      .my-checkbox {
        transform: scale(1.3);
        .span-content {
          color: #ff3e41;
        }
      }
    }
  }


  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;

    .choose_body {
      flex: 1;
      display: flex;
      justify-content: center;
      margin-top: 0px;
      .body_middle {
        width: 1200px;
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 10px 30px 1px rgba(93,132,177,0.11);
        border-radius: 0 0 16px 16px;
        .middle_top {
          width: 100%;
          margin: 40px 0px 10px 0;
        }
        .border_top {
          width: 1155px;
          margin: 30px 22px 0px 22px;
          height: 7px;
          background: #f1f1f1;
          border-radius: 12px 12px 12px 12px;
          opacity: 1;
        }
        .title_box {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin: 25px 0 25px 2.5%;
          .title {
            width: 176px;
            height: 29px;
            font-size: 22px;
            font-weight: bold;
            color: #000000;
          }
          .title_left {
            display: flex;
            .xiala {
              .sousuo1{
                display: none;
              }
              margin-left: 14px;
              color: #3570F6;
              ::v-deep .el-input__inner{
                border: none;
                width: 200px;
                height: 29px;
                line-height: 29px;
                font-size: 22px;
                font-weight: 400;
                color: #3570F6;
              }
              ::v-deep .el-select__caret{
                font-size: 18px;
                color: #3570F6;
                height: 29px;
                line-height: 29px;
              }
            }
          }
          .title_right {
            width: 244px;
            height: 41px;
            line-height: 41px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #f1f1f1;
            border-radius: 21px 21px 21px 21px;
            input {
              width: 170px;
              height: 31px;
              line-height: 31px;
              font-size: 16px;
              font-weight: 400;
              color: #383838;
              border: none;
              outline: none;
              background: none;
            }
            img {
              width: 19px;
              height: 19px;
              margin-left: 10px;
            }
          }
        }
        .foot_Box {
          width: 100%;
          height: 140px;
          display: flex;
          div:nth-child(1) {
            width: 418px;
            height: 59px;
            margin: 28px 53px 18px 173px;
            background: #3570f6;
            border-radius: 10px 10px 10px 10px;
            text-align: center;
            line-height: 59px;
            color: #fff;
          }
          div:nth-child(2) {
            width: 418px;
            height: 59px;
            margin: 28px 53px 0px 0px;
            background: #3570f6;
            border-radius: 10px 10px 10px 10px;
            color: #fff;
            text-align: center;
            line-height: 59px;
          }
          .disabled_click {
            pointer-events: none !important;
            cursor: not-allowed !important;
            opacity: 0.5 !important;
            background: #cccccc !important;
          }
        }
      }
    }
  }
  .el-select-dropdown__item{
    height: auto;
    line-height: normal;
    padding:10px 20px;
    .cardConBox{
      width: 100%;
      height: auto;
      display: flex;
      flex-flow: column;
      padding: 5px 0;
      background: #fafafa;
      border: 1px solid #f1f1f1;
      border-radius: 4px;
      padding:8px 12px;
      cursor: pointer;
      span:nth-child(1){
        color: #000000;
        font-weight: bold;
        font-size: 16px;
        cursor: pointer;
      }
      span:nth-child(2){
        color: #666;
        font-weight: normal;
        width: 360px;
        white-space:normal;
        cursor: pointer;
      }
    }
    &:hover{
      background: none;
      .cardConBox{
        background: #3570F605;
        border: 1px solid #3570F6;
        span:nth-child(1){
          color: #3570F6;
        }
      }
    }
  }

  .choose_foot {
    z-index: 2;
    width: 100%;
    height: 80px;
    display: none;
    justify-content: center;
    align-items: center;
  }

  ::v-deep .custom-alert .el-alert__content .el-alert__title {
    font-size: 18px !important;
  }
}

// 移动端
@media screen and (max-width: 720px) {

  .top_layout,.foot_layout{
    display: none;
  }
  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .choose_body {
      width: 100%;
      margin-top: 5.8rem;
      .body_middle {
        width: calc(100% - 2rem);
        margin-left: 1rem;
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 0.5rem 1rem 1px rgba(93,132,177,0.11);
        border-radius: 1rem;
        .middle_top {
          display: none;
        }
        .border_top {
          display: none;
        }
        .title_box {
          width:100%;
          display: flex;
          flex-direction: column;
          padding:1rem 1.5rem;
          .title {
            width: 100%;
            height: 2rem;
            font-size: 1.6rem;
            font-weight: bold;
            color: #000000;
          }
          .title_left {
            display: flex;
            flex-direction: column;
            .xiala {
              margin: 0.5rem 0;
              width:100%;
              color: #3570F6;
              display: flex;
              ::v-deep .el-input__inner{
                display: flex;
                border: none;
                width:100%;
                height: 2rem;
                font-size: 1.2rem;
                color: #3570F6;
              }
              ::v-deep .el-select__caret{
                font-size: 1.4rem;
                color: #3570F6;
                height: 2.2rem;
              }
            }
          }
          .title_right {
            width: 100%;
            height: auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f1f1f1;
            border-radius: 0.4rem;
            padding:0 1rem;
            margin-top: 0.5rem;
            input {
              width: calc(100% - 2rem);
              height: 3rem;
              font-size: 1.2rem;
              font-weight: 400;
              color: #383838;
              border: none;
              outline: none;
              background: none;
            }
            img {
              width: 1.5rem;
              height: 1.5rem;
            }
          }
        }


        .cardBox{
          width: calc(100% - 2rem);
          margin: 0 0 0 1rem;
          border: 1px solid #E1E1E1;
          position: relative;
          border-radius: 0.6rem;
          margin-bottom: 1.5rem;
          .cardTitle{
            position: absolute;
            width: auto;
            height: 2rem;
            line-height: 2rem;
            top: -1rem;
            left: 1rem;
            padding:0 1rem;
            background: #ffffff;
            color: #3570f6;
            font-size: 1.1rem;
            font-weight: bold;
          }
          .cardCon{
            width: 100%;
            height: auto;
            padding:1rem;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 0.6rem;
            .zhanwei{
              display: none;
            }
            .zhanweiLong{
              width: 100%;
              height: 0.2rem;
              text-align: center;
              display: flex;
              justify-content: center;
              align-items: center;
              background: #f1f1f1;
              margin: 1rem 0;
              span{
                background: #ffffff;
                font-size: 1.1rem;
                font-weight: bold;
                padding:0 1rem;
                color: #3570f6;
              }
            }
            .el-form-item{
              width: 100%;
              margin: 0 0 13px 0;
              &.card_long{
                width: 100%;
              }
            }
            ::v-deep .el-upload-dragger{
              width: 100%;
              height: 100%;
            }
            .card{
              width: 100%;
              display: flex;
              border: 1px solid #E6E6E6;
              border-radius:0.2rem;
              flex-flow: column;
              .required {
                &::before{
                  content: "*";
                  color: #ff3300;
                  font-size: 1rem;
                  margin: 0 0.2rem 0 0;
                }
              }
              .cardName{
                width: 100%;
                height:auto;
                background: #FAFAFA;
                padding:0 0.8rem;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                white-space: nowrap;
                font-size: 1rem;
                color: #999;
                border-bottom: 1px solid #e6e6e6;
                &.longName{
                  min-width: 10rem;
                }
                &.longName1{
                  min-width: 12rem;
                }
              }
              .cardContent{
                width: 100%;
                height: 3rem;
                padding: 0 0.8rem;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                font-size: 1rem;
                color: #383838;
                input::placeholder {
                  color: #C0C4CC;
                }
                ::v-deep .el-select{
                  .el-input{
                    input{
                      border: none;
                      font-size: 1rem;
                      padding: 0;
                      background: none;
                    }
                  }
                }
                ::v-deep .el-cascader{
                  .el-input{
                    input{
                      border: none;
                      font-size: 1rem;
                      padding: 0;
                      background: none;
                    }
                  }
                }
                ::v-deep .el-form-item__content{
                  line-height: 3rem;
                }
                ::v-deep .el-input__prefix{
                  left: 0;
                  height: 100%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  .el-input__icon{
                    line-height: 3rem;
                  }
                }
                ::v-deep .el-date-editor{
                  width: 100%;
                  height: 100%;
                  input{
                    height: 100%;
                    border: none;
                    background: none;
                    font-size: 1rem;
                    padding-left: 2rem;
                  }
                  .el-input__icon{
                    font-size: 1rem;
                  }
                }
                .cardTxt{
                  width: 100%;
                  height: 100%;
                  border: none;
                  background: none;
                  font-size: 1rem;
                  color: #383838;
                  outline: none;
                }
              }
              .cardContent1 {
                height: 100% !important;
                flex-wrap: wrap;
                .cont_box_left {
                  border-bottom: 2px dashed #f1f1f1;
                  flex-grow: 1;
                }
                .cont_box_right {
                  flex-grow: 1;
                }
              }
              &.card_long{
                width: 100%;
              }
            }
            .card1{
              width: 100%;
              display: flex;
              border-radius:0.2rem;
              flex-flow: column;
              .required {
                &::before{
                  content: "*";
                  color: #ff3300;
                  font-size: 1rem;
                  margin: 0 0.2rem 0 0;
                }
              }
              .cardName{
                width: 100%;
                height: 3rem;
                background: #FAFAFA;
                padding:0 0.8rem;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                white-space: nowrap;
                font-size: 1rem;
                color: #999;
                border: 1px solid #E6E6E6;
                &.longName{
                  min-width: 10rem;
                }
              }
              .cardContent{
                width: 100%;
                height: auto;
                padding: 0.8rem 0.8rem 0 0.1rem;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                font-size: 1rem;
                color: #383838;
                input::placeholder {
                  color: #C0C4CC;
                }
                ::v-deep .el-select{
                  .el-input{
                    input{
                      border: none;
                      font-size: 1rem;
                      padding: 0;
                      background: none;
                    }
                  }
                }
                ::v-deep .el-cascader{
                  .el-input{
                    input{
                      border: none;
                      font-size: 1rem;
                      padding: 0;
                      background: none;
                    }
                  }
                }
                ::v-deep .el-form-item__content{
                  line-height: 3rem;
                }
                ::v-deep .el-input__prefix{
                  left: 0;
                  height: 100%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  .el-input__icon{
                    line-height: 3rem;
                  }
                }
                ::v-deep .el-date-editor{
                  width: 100%;
                  height: 100%;
                  input{
                    height: 100%;
                    border: none;
                    background: none;
                    font-size: 1rem;
                    padding-left: 2rem;
                  }
                  .el-input__icon{
                    font-size: 1rem;
                  }
                }
                .cardTxt{
                  width: 100%;
                  height: 100%;
                  border: none;
                  background: none;
                  font-size: 1rem;
                  color: #383838;
                  outline: none;
                }
              }
              &.card_long{
                width: 100%;
              }
            }
            .upload-demo {
              width: 100%;
              .el-upload__tip {
                width: 100%;
                height: auto;
                font-size: 1rem;
                color: #999;
              }
            }
          }
          .promise-box {
            height: 3rem;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            .my-checkbox {
              transform: scale(1.3);
              .span-content {
                color: #ff3e41;
              }
            }
          }
        }


        .foot_Box {
          width: calc(100% - 2rem);
          margin-left:1rem ;
          display: flex;
          justify-content: space-evenly;
          flex-flow: column;
          gap: 1rem;
          padding-bottom: 1.5rem;
          div {
            width: 100%;
            height: 3.4rem;
            background: #3570f6;
            border-radius: 0.6rem;
            text-align: center;
            line-height: 3.4rem;
            color: #fff;
          }
          .disabled_click {
            pointer-events: none !important;
            cursor: not-allowed !important;
            opacity: 0.5 !important;
            background: #cccccc !important;
          }
        }
      }
    }
    .choose_foot {
       width: 100%;
      height:auto;
      display: flex;
      justify-content: center;
      align-items: center;
      padding:1.5rem 1.2rem;
    }
  }
  .el-select-dropdown__item{
    height: auto;
    line-height: normal;
    padding:0.6rem 1.2rem;
    .cardConBox{
      width: 80vw;
      height: auto;
      box-sizing: border-box;
      display: flex;
      flex-flow: column;
      padding: 0.3rem 0;
      background: #fafafa;
      border: 1px solid #f1f1f1;
      border-radius: 0.6rem;
      padding:0.6rem 1rem;
      cursor: pointer;
      span:nth-child(1){
        color: #000000;
        font-weight: bold;
        font-size: 1.2rem;
        cursor: pointer;
        white-space: normal;
        word-break: break-all;
      }
      span:nth-child(2){
        color: #666;
        font-weight: normal;
        width: 100%;
        white-space:normal;
        cursor: pointer;
        font-size: 1rem;
      }
    }
    &:hover{
      background: none;
      .cardConBox{
        background: #3570F605;
        border: 1px solid #3570F6;
        span:nth-child(1){
          color: #3570F6;
        }
      }
    }
  }
  ::v-deep .custom-alert .el-alert__content .el-alert__title {
    font-size: 1rem !important;
  }
}
::v-deep .el-select{
  width: 100%!important;
}
::v-deep .el-radio__label{
  font-size: 1rem;
}
.el-date-editor{
  display: flex;
  justify-content: space-between;
  flex-flow: row;
}
::v-deep .is-finish{
  color: #383838;
  font-size: 18px;
  .el-step__line{
    background: #3570F6;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #3570F6;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    border-color: #3570F6;
  }
}

::v-deep .is-process{
  color: #383838;
  font-size: 18px;
  font-weight: normal;
  .el-step__line{
    background: #f1f1f1;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #ffffff;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    border-width: 5px;
    color: #3570F6;
    border-color: #3570F6;
  }
}

::v-deep .is-wait{
  color: #383838;
  font-size: 18px;
  font-weight: normal;
  .el-step__line{
    background: #f1f1f1;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #EEF6FF;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    border-width: 5px;
    color: #3570F6;
    border-color: #EEF6FF;
  }
}
::v-deep .el-cascader {
  width: 100% !important;
}
</style>
