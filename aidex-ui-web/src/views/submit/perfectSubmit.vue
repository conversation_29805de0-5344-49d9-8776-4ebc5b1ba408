<template>
  <div class="choose">
   <top/>

    <div class="choose_body">
      <div class="body_middle">
        <div class="title_box">
          <div class="title_left">
            <div class="title">完善基本信息</div>
          </div>
        </div>

        <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
        <div class="cardBox">
          <div class="cardTitle">基本信息</div>
          <div class="cardCon">
            <el-form-item prop="name">
              <div class="card">
                <div class="cardName">学生姓名</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.name" :disabled="disabled" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="mobile">
              <div class="card">
                <div class="cardName">联系电话</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.mobile" :disabled="disabled" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="cardType">
              <div class="card">
                <div class="cardName">身份证类型</div>
                <div class="cardContent">
                  <el-select v-model="ruleForm.cardType" placeholder="请选择身份证类型" :disabled="disabled">
                    <el-option v-for="(item, index) in cardTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="idCard">
              <div class="card">
                <div class="cardName">身份证号</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.idCard"  :disabled="disabled" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="birthday">
              <div class="card">
                <div class="cardName">出生日期</div>
                <div class="cardContent">
                  <el-date-picker
                      type="date"
                      v-model="ruleForm.birthday"
                      format="yyyy-MM-dd"
                      style="width: 100%; border: none"
                      :disabled="disabled"
                  ></el-date-picker>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="gender">
              <div class="card">
                <div class="cardName">性别</div>
                <div class="cardContent">
                  <el-radio v-model="ruleForm.gender"
                      v-if="item.dictValue !== '2'"
                      v-for="(item, index) in genderOptions" :key="index" :disabled="disabled"
                      :label="item.dictValue">{{item.dictLabel}}</el-radio>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="nation">
              <div class="card">
                <div class="cardName">民族</div>
                <div class="cardContent">
                  <el-select v-model="ruleForm.nation" placeholder="请选择民族" :disabled="disabled">
                    <el-option v-for="(item, index) in nationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="country">
              <div class="card">
                <div class="cardName">国籍/地区</div>
                <div class="cardContent">
                  <el-select v-model="ruleForm.country" placeholder="请选择国籍/地区" :disabled="disabled">
                    <el-option v-for="(item, index) in countryOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="nativePlace">
              <div class="card">
                <div class="cardName required">籍贯</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.nativePlace" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="politicsStatus">
              <div class="card">
                <div class="cardName required">政治面貌</div>
                <div class="cardContent">
                  <el-select v-model="ruleForm.politicsStatus" placeholder="请选择政治面貌">
                    <el-option v-for="(item, index) in politicsStatusOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="postalCode">
              <div class="card">
                <div class="cardName required">邮政编码</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.postalCode" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="email">
              <div class="card">
                <div class="cardName">电子信箱</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.email" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="formerName">
              <div class="card">
                <div class="cardName">曾用名</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.formerName" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="bloodType">
              <div class="card">
                <div class="cardName required">血型</div>
                <div class="cardContent">
                  <el-select v-model="ruleForm.bloodType" placeholder="请选择血型">
                    <el-option v-for="(item, index) in bloodTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="healthCondition">
              <div class="card">
                <div class="cardName required">健康状况</div>
                <div class="cardContent">
                  <el-select v-model="ruleForm.healthCondition" placeholder="请选择健康状况">
                    <el-option v-for="(item, index) in healthConditionOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="inoculatePrevent">
              <div class="card">
                <div class="cardName">预防接种</div>
                <div class="cardContent">
                  <el-radio v-model="ruleForm.inoculatePrevent"
                            v-for="(item, index) in yesOrNoOptions" :key="index" :disabled="disabled"
                            :label="item.dictValue">{{item.dictLabel}}</el-radio>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="disabilityType">
              <div class="card">
                <div class="cardName required">残疾类型</div>
                <div class="cardContent">
                  <el-select v-model="ruleForm.disabilityType" placeholder="请选择残疾类型">
                    <el-option v-for="(item, index) in disabilityTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="strongPoint">
              <div class="card">
                <div class="cardName">特长</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.strongPoint" />
                </div>
              </div>
            </el-form-item>
            <div class="zhanwei"></div>
            <el-form-item prop="birthplaceAreaCode">
              <div class="card">
                <div class="cardName required">出生地区划</div>
                <div class="cardContent">
                  <el-cascader
                      v-model="birthplaceAreaCodes"
                      filterable
                      :props="cascaderProps"
                      :options="areaTreeOptions"
                      placeholder="请选择出生地区划"
                      @change="handleBirthplaceAreaCodeChange"
                  >
                  </el-cascader>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="registeredResidenceCategory">
              <div class="card">
                <div class="cardName required">户口性质</div>
                <div class="cardContent">
                  <el-select v-model="ruleForm.registeredResidenceCategory" placeholder="请选择户口性质">
                    <el-option v-for="(item, index) in registeredResidenceCategoryOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="registeredResidenceAreaCode" class="card_long">
              <div class="card">
                <div class="cardName required">户口所在地区划</div>
                <div class="cardContent">
                  <el-cascader
                      v-model="registeredResidenceAreaCodes"
                      filterable
                      :props="cascaderProps"
                      :options="areaTreeOptions"
                      placeholder="请选择户口所在地区划"
                      @change="handleRegisteredResidenceChange"
                  >
                  </el-cascader>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="domicileAddress" class="card_long">
              <div class="card">
                <div class="cardName">户籍地址</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.domicileAddress" :disabled="disabled" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="houseProperty">
              <div class="card">
                <div class="cardName">居住地产权</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.houseProperty" :disabled="disabled" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="houseRegisterTime">
              <div class="card">
                <div class="cardName">产权注册时间</div>
                <div class="cardContent">
                  <el-date-picker
                      type="date"
                      v-model="ruleForm.houseRegisterTime"
                      format="yyyy-MM-dd"
                      style="width: 100%; border: none"
                      :disabled="disabled"
                  ></el-date-picker>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="residentialAddress" class="card_long">
              <div class="card">
                <div class="cardName">居住地址</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.residentialAddress" :disabled="disabled" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="familyAddress" class="card_long">
              <div class="card">
                <div class="cardName required">家庭地址</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.familyAddress" />
                </div>
              </div>
            </el-form-item>
            <div class="zhanwei"></div>
            <el-form-item prop="countrymenAbroad">
              <div class="card">
                <div class="cardName longName required">港澳台侨外</div>
                <div class="cardContent">
                  <el-radio v-model="ruleForm.countrymenAbroad"
                            v-for="(item, index) in yesOrNoOptions" :key="index"
                            :label="item.dictValue">{{item.dictLabel}}</el-radio>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="onlyChild">
              <div class="card">
                <div class="cardName longName required">独生子女</div>
                <div class="cardContent">
                  <el-radio v-model="ruleForm.onlyChild"
                            v-for="(item, index) in yesOrNoOptions" :key="index"
                            :label="item.dictValue">{{item.dictLabel}}</el-radio>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="preschoolEducation">
              <div class="card">
                <div class="cardName longName required">受过学前教育</div>
                <div class="cardContent">
                  <el-radio v-model="ruleForm.preschoolEducation"
                            v-for="(item, index) in yesOrNoOptions" :key="index"
                            :label="item.dictValue">{{item.dictLabel}}</el-radio>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="applyFunding">
              <div class="card">
                <div class="cardName longName required">需要申请资助</div>
                <div class="cardContent">
                  <el-radio v-model="ruleForm.applyFunding"
                            v-for="(item, index) in yesOrNoOptions" :key="index"
                            :label="item.dictValue">{{item.dictLabel}}</el-radio>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="enjoyGrants">
              <div class="card">
                <div class="cardName longName required">享受一补</div>
                <div class="cardContent">
                  <el-radio v-model="ruleForm.enjoyGrants"
                            v-for="(item, index) in yesOrNoOptions" :key="index"
                            :label="item.dictValue">{{item.dictLabel}}</el-radio>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="orphan">
              <div class="card">
                <div class="cardName longName required">是否孤儿</div>
                <div class="cardContent">
                  <el-radio v-model="ruleForm.orphan"
                            v-for="(item, index) in yesOrNoOptions" :key="index"
                            :label="item.dictValue">{{item.dictLabel}}</el-radio>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="priorityRaising">
              <div class="card">
                <div class="cardName longName required">烈士或优抚子女</div>
                <div class="cardContent">
                  <el-radio v-model="ruleForm.priorityRaising"
                            v-for="(item, index) in yesOrNoOptions" :key="index"
                            :label="item.dictValue">{{item.dictLabel}}</el-radio>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="workAccompany">
              <div class="card">
                <div class="cardName longName required">务工随迁子女</div>
                <div class="cardContent">
                  <el-radio v-model="ruleForm.workAccompany"
                            v-for="(item, index) in yesOrNoOptions" :key="index"
                            :label="item.dictValue">{{item.dictLabel}}</el-radio>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="leftBehindChildren">
              <div class="card">
                <div class="cardName longName required">是否留守儿童</div>
                <div class="cardContent">
                  <el-radio v-model="ruleForm.leftBehindChildren"
                            v-for="(item, index) in yesOrNoOptions" :key="index"
                            :label="item.dictValue">{{item.dictLabel}}</el-radio>
                </div>
              </div>
            </el-form-item>
            <div class="zhanwei"></div>
            <el-form-item prop="entryWay">
              <div class="card">
                <div class="cardName longName required">入学方式</div>
                <div class="cardContent">
                  <el-select v-model="ruleForm.entryWay" placeholder="请选择入学方式">
                    <el-option v-for="(item, index) in entryWayOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="attendWay">
              <div class="card">
                <div class="cardName longName required">就读方式</div>
                <div class="cardContent">
                  <el-select v-model="ruleForm.attendWay" placeholder="请选择就读方式">
                    <el-option v-for="(item, index) in attendWayOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="schoolDistance">
              <div class="card">
                <div class="cardName longName required">上下学距离</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.schoolDistance" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="schoolWay">
              <div class="card">
                <div class="cardName longName required">上下学方式</div>
                <div class="cardContent">
                  <el-select v-model="ruleForm.schoolWay" placeholder="请选择上下学方式">
                    <el-option v-for="(item, index) in schoolWayOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="schoolBusTake">
              <div class="card">
                <div class="cardName longName required">是否需要乘坐校车</div>
                <div class="cardContent">
                  <el-radio v-model="ruleForm.schoolBusTake"
                            v-for="(item, index) in yesOrNoOptions" :key="index"
                            :label="item.dictValue">{{item.dictLabel}}</el-radio>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="governmentBuyDegree">
              <div class="card">
                <div class="cardName longName required">是否由政府购买学位</div>
                <div class="cardContent">
                  <el-radio v-model="ruleForm.governmentBuyDegree"
                            v-for="(item, index) in yesOrNoOptions" :key="index"
                            :label="item.dictValue">{{item.dictLabel}}</el-radio>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="studyFollowClass">
              <div class="card">
                <div class="cardName longName required">随班就读</div>
                <div class="cardContent">
                  <el-select v-model="ruleForm.studyFollowClass" placeholder="请选择随班就读">
                    <el-option v-for="(item, index) in studyFollowClassOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
            <div class="zhanwei"></div>
            <el-form-item prop="guardianName1">
              <div class="card">
                <div class="cardName longName">监护人1姓名</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianName1" :disabled="disabled" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianMobile1">
              <div class="card">
                <div class="cardName longName">监护人1电话</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianMobile1" :disabled="disabled" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianCardType1">
              <div class="card">
                <div class="cardName longName">监护人1身份证类型</div>
                <div class="cardContent">
                  <el-select v-model="ruleForm.guardianCardType1" placeholder="请选择身份证类型" :disabled="disabled">
                    <el-option v-for="(item, index) in cardTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianIdCard1">
              <div class="card">
                <div class="cardName longName">监护人1身份证号</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianIdCard1" :disabled="disabled" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianRelation1">
              <div class="card">
                <div class="cardName longName">监护人1与学生关系</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianRelation1" :disabled="disabled" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianNation1">
              <div class="card">
                <div class="cardName longName">监护人1民族</div>
                <div class="cardContent">
                  <el-select v-model="ruleForm.guardianNation1" placeholder="请选择民族" :disabled="disabled" @change="handleChange">
                    <el-option v-for="(item, index) in nationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianResidentialAddress1" class="card_long">
              <div class="card">
                <div class="cardName longName">监护人1现住址</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianResidentialAddress1" :disabled="disabled" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianship1">
              <div class="card">
                <div class="cardName longName">监护人1是否监护人</div>
                <div class="cardContent">
                  <el-radio v-model="ruleForm.guardianship1"  @change="handleChange"
                            v-for="(item, index) in yesOrNoOptions" :key="index" :disabled="disabled"
                            :label="item.dictValue">{{item.dictLabel}}</el-radio>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianRegisteredResidenceAreaCode1">
              <div class="card">
                <div class="cardName longName required">监护人1户口所在地区划</div>
                <div class="cardContent">
                  <el-cascader
                      v-model="guardianRegisteredResidenceAreaCodes1"
                      filterable
                      :props="cascaderProps"
                      :options="areaTreeOptions"
                      placeholder="请选择户口所在地区划"
                      @change="handleGuardianRegisteredResidence1Change"
                  >
                  </el-cascader>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianWorkUnit1">
              <div class="card">
                <div class="cardName longName required">监护人1工作单位</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianWorkUnit1" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianWorkPost1">
              <div class="card">
                <div class="cardName longName required">监护人1职务</div>
                <div class="cardContent">
                  <el-select v-model="ruleForm.guardianWorkPost1" placeholder="请选择"  @change="handleChange" >
                    <el-option v-for="(item, index) in postOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
            <div class="zhanwei"></div>
            <el-form-item prop="guardianName2">
              <div class="card">
                <div class="cardName longName">监护人2姓名</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianName2" :disabled="disabled" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianMobile2">
              <div class="card">
                <div class="cardName longName">监护人2电话</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianMobile2" :disabled="disabled" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianCardType2">
              <div class="card">
                <div class="cardName longName">监护人2身份证类型</div>
                <div class="cardContent">
                  <el-select v-model="ruleForm.guardianCardType2" placeholder="请选择身份证类型" :disabled="disabled">
                    <el-option v-for="(item, index) in cardTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianIdCard2">
              <div class="card">
                <div class="cardName longName">监护人2身份证号</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianIdCard2" :disabled="disabled"  />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianRelation2">
              <div class="card">
                <div class="cardName longName">监护人2与学生关系</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianRelation2" :disabled="disabled" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianNation2">
              <div class="card">
                <div class="cardName longName">监护人2民族</div>
                <div class="cardContent">
                  <el-select v-model="ruleForm.guardianNation2" placeholder="请选择民族" :disabled="disabled" @change="handleChange">
                    <el-option v-for="(item, index) in nationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianResidentialAddress2" class="card_long">
              <div class="card">
                <div class="cardName longName">监护人2现住址</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianResidentialAddress2" :disabled="disabled" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianship2">
              <div class="card">
                <div class="cardName longName">监护人2是否监护人</div>
                <div class="cardContent">
                  <el-radio v-model="ruleForm.guardianship2"  @change="handleChange"
                            v-for="(item, index) in yesOrNoOptions" :key="index" :disabled="disabled"
                            :label="item.dictValue">{{item.dictLabel}}</el-radio>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianRegisteredResidenceAreaCode2">
              <div class="card">
                <div class="cardName longName">监护人2户口所在地区划</div>
                <div class="cardContent">
                  <el-cascader
                      v-model="guardianRegisteredResidenceAreaCodes2"
                      filterable
                      :props="cascaderProps"
                      :options="areaTreeOptions"
                      placeholder="请选择户口所在地区划"
                      @change="handleGuardianRegisteredResidence2Change"
                  >
                  </el-cascader>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianWorkUnit2">
              <div class="card">
                <div class="cardName longName">监护人2工作单位</div>
                <div class="cardContent">
                  <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianWorkUnit2" />
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="guardianWorkPost2">
              <div class="card">
                <div class="cardName longName">监护人2职务</div>
                <div class="cardContent">
                  <el-select v-model="ruleForm.guardianWorkPost2" placeholder="请选择" @change="handleChange">
                    <el-option v-for="(item, index) in postOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </div>
            </el-form-item>
          </div>
        </div>
        <div class="border_top"></div>
        <!-- 底部 -->
        <div class="foot_Box">
          <div @click="submit('ruleForm')"><i class="el-icon-loading" v-if="loading"></i>确认保存</div>
        </div>
        </el-form>
      </div>
    </div>
    <foot />
  </div>
</template>

<script>
  import top from '../../components/top/top.vue'
  import foot from '../../components/foot/foot.vue'
  import {getApplyInfo, savePerfectData} from '@/api/apply'
  import {validIdCard, validMobile} from '@/utils/validate'
  import { listAreaTree } from '@/api/area'

  export default {
  components: {
    top,foot
  },
  data() {
    const validateIdCard = (rule, value, callback) => {
      if (value) {
        if (!validIdCard(value)) {
          callback(new Error('身份证号不合法'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    const validateMobile = (rule, value, callback) => {
      if (value && !validMobile(value)) {
        callback(new Error('请正确填写联系电话'))
      } else {
        callback()
      }
    }
    return {
      disabled: true,
      loading: false,
      sourceTypeOptions: [],
      cardTypeOptions: [],
      genderOptions: [],
      yesOrNoOptions: [],
      nationOptions: [],
      countryOptions: [],
      politicsStatusOptions: [],
      healthConditionOptions: [],
      disabilityTypeOptions: [],
      areaTreeOptions: [],
      registeredResidenceCategoryOptions: [],
      bloodTypeOptions: [],
      entryWayOptions: [],
      attendWayOptions: [],
      schoolWayOptions: [],
      studyFollowClassOptions: [],
      postOptions: [],
      birthplaceAreaCodes: [],
      registeredResidenceAreaCodes: [],
      guardianRegisteredResidenceAreaCodes1: [],
      guardianRegisteredResidenceAreaCodes2: [],
      cascaderProps: {
        label: 'label',
        value: 'id',
        children: 'children',
        leaf: 'leaf'
      },
      routerQuery: {},
      id: '',
      ruleForm: {
        id: '',
        schoolId: '',
        sourceTypeId: '',
        name: '',
        cardType: '',
        idCard: '',
        birthday: '',
        gender: '',
        nation: '',
        mobile: '',
        inoculatePrevent: '',
        domicileAddress: '',
        houseProperty: '',
        houseRegisterTime: '',
        residentialAddress: '',
        guardianId1: '',
        guardianName1: '',
        guardianIdCard1: '',
        guardianMobile1: '',
        guardianRelation1: '',
        guardianResidentialAddress1: '',
        guardianship1: '',
        guardianCardType1: '',
        guardianNation1: '',
        guardianRegisteredResidenceAreaCode1: '',
        guardianWorkUnit1: '',
        guardianWorkPost1: '',
        guardianId2: '',
        guardianName2: '',
        guardianIdCard2: '',
        guardianMobile2: '',
        guardianRelation2: '',
        guardianResidentialAddress2: '',
        guardianship2: '',
        guardianCardType2: '',
        guardianNation2: '',
        guardianRegisteredResidenceAreaCode2: '',
        guardianWorkUnit2: '',
        guardianWorkPost2: '',
        picture: '',
        attachmentId: '',
        applyType: '',
        country: '',
        nativePlace: '',
        healthCondition: '',
        politicsStatus: '',
        postalCode: '',
        email: '',
        disabilityType: '',
        birthplaceAreaCode: '',
        registeredResidenceCategory: '',
        familyAddress: '',
        formerName: '',
        bloodType: '',
        countrymenAbroad: '',
        onlyChild: '',
        preschoolEducation: '',
        applyFunding: '',
        enjoyGrants: '',
        orphan: '',
        priorityRaising: '',
        workAccompany: '',
        strongPoint: '',
        leftBehindChildren: '',
        entryWay: '',
        attendWay: '',
        schoolDistance: '',
        schoolWay: '',
        schoolBusTake: '',
        governmentBuyDegree: '',
        studyFollowClass: '',
      },
      rules: {
        name: [{ required: false, message: '学生姓名不能为空', trigger: 'blur' }],
        cardType: [
          { required: false, message: '身份证类型不能为空', trigger: 'blur' },
        ],
        idCard: [
          { required: false, message: '身份证号不能为空', trigger: 'blur' },
          { validator: validateIdCard, trigger: 'blur' }
        ],
        birthday: [{ required: false, message: '出生日期不能为空', trigger: 'blur' }],
        gender: [{ required: false, message: '性别不能为空', trigger: 'blur' }],
        nation: [{ required: false, message: '民族不能为空', trigger: 'blur' }],
        mobile: [
          { required: false, message: '联系电话不能为空', trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' },
        ],
        inoculatePrevent: [{ required: false, message: '预防接种不能为空', trigger: 'blur' }],
        domicileAddress: [{ required: false, message: '户籍地址不能为空', trigger: 'blur' }],
        houseProperty: [{ required: false, message: '居住地产权不能为空', trigger: 'blur' }],
        houseRegisterTime: [{ required: false, message: '产权注册时间不能为空', trigger: 'blur' }],
        residentialAddress: [{ required: false, message: '居住地址不能为空', trigger: 'blur' }],
        guardianName1: [{ required: false, message: '监护人1姓名不能为空', trigger: 'blur' }],
        guardianRelation1: [{ required: false, message: '监护人1与学生关系不能为空', trigger: 'blur' }],
        guardianIdCard1: [
          { required: false, message: '监护人1身份证号不能为空', trigger: 'blur' },
          { validator: validateIdCard, trigger: 'blur' },
        ],
        guardianMobile1: [
          { required: false, message: '监护人1电话不能为空', trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' },
        ],
        guardianResidentialAddress1: [{ required: false, message: '监护人1现住址不能为空', trigger: 'blur' }],
        guardianship1: [{ required: false, message: '监护人1是否监护人不能为空', trigger: 'blur' }],
        guardianCardType1: [{ required: false, message: '监护人1身份证类型不能为空', trigger: 'blur' }],
        guardianNation1: [{ required: false, message: '监护人1民族不能为空', trigger: 'blur' }],
        guardianRegisteredResidenceAreaCode1: [{ required: true, message: '监护人1户口所在地区划不能为空', trigger: 'blur' }],
        guardianWorkUnit1: [{ required: true, message: '监护人1工作单位不能为空', trigger: 'blur' }],
        guardianWorkPost1: [{ required: true, message: '监护人1职务不能为空', trigger: 'blur' }],
        guardianName2: [{ required: false, message: '监护人2姓名不能为空', trigger: 'blur' }],
        guardianRelation2: [{ required: false, message: '监护人2与学生关系不能为空', trigger: 'blur' }],
        guardianIdCard2: [
          { required: false, message: '监护人2身份证号不能为空', trigger: 'blur' },
          { validator: validateIdCard, trigger: 'blur' },
        ],
        guardianMobile2: [
          { required: false, message: '监护人2电话不能为空', trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' },
        ],
        guardianResidentialAddress2: [{ required: false, message: '监护人2现住址不能为空', trigger: 'blur' }],
        guardianship2: [{ required: false, message: '监护人2是否监护人不能为空', trigger: 'blur' }],
        guardianCardType2: [{ required: false, message: '监护人2身份证类型不能为空', trigger: 'blur' }],
        guardianNation2: [{ required: false, message: '监护人2民族不能为空', trigger: 'blur' }],
        guardianRegisteredResidenceAreaCode2: [{ required: false, message: '监护人2户口所在地区划不能为空', trigger: 'blur' }],
        guardianWorkUnit2: [{ required: false, message: '监护人2工作单位不能为空', trigger: 'blur' }],
        guardianWorkPost2: [{ required: false, message: '监护人2职务不能为空', trigger: 'blur' }],
        country: [{ required: false, message: '国籍/地区不能为空', trigger: 'blur' }],
        nativePlace: [{ required: true, message: '籍贯不能为空', trigger: 'blur' }],
        healthCondition: [{ required: true, message: '健康状况不能为空', trigger: 'blur' }],
        politicsStatus: [{ required: true, message: '政治面貌不能为空', trigger: 'blur' }],
        postalCode: [{ required: true, message: '邮政编码不能为空', trigger: 'blur' }],
        email: [{ required: false, message: '电子信箱不能为空', trigger: 'blur' }],
        disabilityType: [{ required: true, message: '残疾类型不能为空', trigger: 'blur' }],
        birthplaceAreaCode: [{ required: true, message: '出生地区划不能为空', trigger: 'blur' }],
        registeredResidenceCategory: [{ required: true, message: '户口性质不能为空', trigger: 'blur' }],
        registeredResidenceAreaCode: [{ required: true, message: '户口所在地区划不能为空', trigger: 'blur' }],
        familyAddress: [{ required: true, message: '家庭地址不能为空', trigger: 'blur' }],
        formerName: [{ required: false, message: '曾用名不能为空', trigger: 'blur' }],
        bloodType: [{ required: true, message: '血型不能为空', trigger: 'blur' }],
        countrymenAbroad: [{ required: true, message: '港澳台侨外不能为空', trigger: 'blur' }],
        onlyChild: [{ required: true, message: '独生子女不能为空', trigger: 'blur' }],
        preschoolEducation: [{ required: true, message: '受过学前教育不能为空', trigger: 'blur' }],
        applyFunding: [{ required: true, message: '需要申请资助不能为空', trigger: 'blur' }],
        enjoyGrants: [{ required: true, message: '享受一补不能为空', trigger: 'blur' }],
        orphan: [{ required: true, message: '是否孤儿不能为空', trigger: 'blur' }],
        priorityRaising: [{ required: true, message: '烈士或优抚子女不能为空', trigger: 'blur' }],
        workAccompany: [{ required: true, message: '进城务工人员随迁子女不能为空', trigger: 'blur' }],
        strongPoint: [{ required: false, message: '特长不能为空', trigger: 'blur' }],
        leftBehindChildren: [{ required: true, message: '是否留守儿童不能为空', trigger: 'blur' }],
        entryWay: [{ required: true, message: '入学方式不能为空', trigger: 'blur' }],
        attendWay: [{ required: true, message: '就读方式不能为空', trigger: 'blur' }],
        schoolDistance: [{ required: true, message: '上下学距离不能为空', trigger: 'blur' }],
        schoolWay: [{ required: true, message: '上下学方式不能为空', trigger: 'blur' }],
        schoolBusTake: [{ required: true, message: '是否需要乘坐校车不能为空', trigger: 'blur' }],
        governmentBuyDegree: [{ required: true, message: '是否由政府购买学位不能为空', trigger: 'blur' }],
        studyFollowClass: [{ required: true, message: '随班就读不能为空', trigger: 'blur' }],
      },
    }
  },
  beforeDestroy() {},
  created() {
    this.getDicts('sys_card_type').then(response => {
      this.cardTypeOptions = response.data
    })
    this.getDicts('sys_user_sex').then(response => {
      this.genderOptions = response.data
    })
    this.getDicts('sys_nation').then(response => {
      this.nationOptions = response.data
    })
    this.getDicts('sys_yes_no').then(response => {
      this.yesOrNoOptions = response.data
    })
    this.getDicts('sys_country').then(response => {
      this.countryOptions = response.data
    })
    this.getDicts('sys_politics_status').then(response => {
      this.politicsStatusOptions = response.data
    })
    this.getDicts('sys_health_condition').then(response => {
      this.healthConditionOptions = response.data
    })
    this.getDicts('sys_disability_type').then(response => {
      this.disabilityTypeOptions = response.data
    })
    this.getDicts('sys_registered_residence_category').then(response => {
      this.registeredResidenceCategoryOptions = response.data
    })
    this.getDicts('sys_blood_type').then(response => {
      this.bloodTypeOptions = response.data
    })
    this.getDicts('sys_entry_way').then(response => {
      this.entryWayOptions = response.data
    })
    this.getDicts('sys_attend_way').then(response => {
      this.attendWayOptions = response.data
    })
    this.getDicts('sys_school_way').then(response => {
      this.schoolWayOptions = response.data
    })
    this.getDicts('sys_study_follow_class').then(response => {
      this.studyFollowClassOptions = response.data
    })
    this.getDicts('sys_post').then(response => {
      this.postOptions = response.data
    })
    this.getTreeSelect()

  },
  mounted() {
    this.routerQuery = this.$route.query || {}
    this.id = this.routerQuery.id
    if (this.id) {
      this.getApplyData(this.id)
    }
  },
  methods: {
    getTreeSelect() {
      listAreaTree('0', 3).then(response => {
        this.areaTreeOptions = response.data;
      })
    },
    getApplyData(id) {
      getApplyInfo(id).then(response => {
        this.loading = true
        const data = response.data || {}
        this.ruleForm = data
        // 回显监护人
        let guardian1 = {}, guardian2 = {}
        if (data.guardianPeopleList && data.guardianPeopleList.length > 0) {
          const length = data.guardianPeopleList.length
          if (length > 0) {
            guardian1 = data.guardianPeopleList[0] || {}
          }
          if (length > 1) {
            guardian2 = data.guardianPeopleList[1] || {}
          }
        }
        // 第一个人
        this.ruleForm.guardianId1 = guardian1.id
        this.ruleForm.guardianName1 = guardian1.name
        this.ruleForm.guardianIdCard1 = guardian1.idCard
        this.ruleForm.guardianMobile1 = guardian1.mobile
        this.ruleForm.guardianRelation1 = guardian1.relation
        this.ruleForm.guardianResidentialAddress1 = guardian1.residentialAddress
        this.ruleForm.guardianship1 = guardian1.guardianship
        this.ruleForm.guardianCardType1 = guardian1.cardType
        this.ruleForm.guardianNation1 = guardian1.nation
        this.ruleForm.guardianRegisteredResidenceAreaCode1 = guardian1.registeredResidenceAreaCode
        this.ruleForm.guardianWorkUnit1 = guardian1.workUnit
        this.ruleForm.guardianWorkPost1 = guardian1.workPost || ''
        // 第二个人
        this.ruleForm.guardianId2 = guardian2.id
        this.ruleForm.guardianName2 = guardian2.name
        this.ruleForm.guardianIdCard2 = guardian2.idCard
        this.ruleForm.guardianMobile2 = guardian2.mobile
        this.ruleForm.guardianRelation2 = guardian2.relation
        this.ruleForm.guardianResidentialAddress2 = guardian2.residentialAddress
        this.ruleForm.guardianship2 = guardian2.guardianship
        this.ruleForm.guardianCardType2 = guardian2.cardType
        this.ruleForm.guardianNation2 = guardian2.nation
        this.ruleForm.guardianRegisteredResidenceAreaCode2 = guardian2.registeredResidenceAreaCode
        this.ruleForm.guardianWorkUnit2 = guardian2.workUnit
        this.ruleForm.guardianWorkPost2 = guardian2.workPost || ''

        // 回显级联选
        if (data.birthplaceAreaCode) {
          this.birthplaceAreaCodes = data.birthplaceAreaCode.split('/')
        }
        if (data.registeredResidenceAreaCode) {
          this.registeredResidenceAreaCodes = data.registeredResidenceAreaCode.split('/')
        }
        if (guardian1.registeredResidenceAreaCode) {
          this.guardianRegisteredResidenceAreaCodes1 = guardian1.registeredResidenceAreaCode.split('/')
        }
        if (guardian2.registeredResidenceAreaCode) {
          this.guardianRegisteredResidenceAreaCodes2 = guardian2.registeredResidenceAreaCode.split('/')
        }
        this.loading = false
      })
    },
    submit(formName) {
      const that = this
      if (that.loading) { return }
      that.$refs[formName].validate(valid => {
        if (valid) {
          const saveForm = JSON.parse(JSON.stringify(that.ruleForm))
          const guardianList = []
          if (saveForm.guardianName1) {
            guardianList.push({
              id: saveForm.guardianId1,
              name: saveForm.guardianName1,
              idCard: saveForm.guardianIdCard1,
              mobile: saveForm.guardianMobile1,
              relation: saveForm.guardianRelation1,
              residentialAddress: saveForm.guardianResidentialAddress1,
              guardianship: saveForm.guardianship1,
              cardType: saveForm.guardianCardType1,
              nation: saveForm.guardianNation1,
              registeredResidenceAreaCode: saveForm.guardianRegisteredResidenceAreaCode1,
              workUnit: saveForm.guardianWorkUnit1,
              workPost: saveForm.guardianWorkPost1,
              sort: 1
            })
          }
          if (saveForm.guardianName2) {
            guardianList.push({
              id: saveForm.guardianId2,
              name: saveForm.guardianName2,
              idCard: saveForm.guardianIdCard2,
              mobile: saveForm.guardianMobile2,
              relation: saveForm.guardianRelation2,
              residentialAddress: saveForm.guardianResidentialAddress2,
              guardianship: saveForm.guardianship2,
              cardType: saveForm.guardianCardType2,
              nation: saveForm.guardianNation2,
              registeredResidenceAreaCode: saveForm.guardianRegisteredResidenceAreaCode2,
              workUnit: saveForm.guardianWorkUnit2,
              workPost: saveForm.guardianWorkPost2,
              sort: 2
            })
          }
          saveForm.guardianPeopleList = guardianList
          that.loading = true
          savePerfectData(saveForm).then(response => {
            that.$message.success('保存成功')
            setTimeout(() => {
              that.loading = false
              that.toapplication()
            }, 1000)
          }).catch(() => {
            that.loading = false
          })
        } else {
          return false
        }
      })
    },
    toapplication(){
      this.$router.push({path: "/application"})
    },
    handleBirthplaceAreaCodeChange(val) {
      let code = ''
      if (val && val.length > 0) {
        code = val.join('/')
      }
      this.ruleForm.birthplaceAreaCode = code
    },
    handleRegisteredResidenceChange(val) {
      let code = ''
      if (val && val.length > 0) {
        code = val.join('/')
      }
      this.ruleForm.registeredResidenceAreaCode = code
    },
    handleGuardianRegisteredResidence1Change(val) {
      let code = ''
      if (val && val.length > 0) {
        code = val.join('/')
      }
      this.ruleForm.guardianRegisteredResidenceAreaCode1 = code
    },
    handleGuardianRegisteredResidence2Change(val) {
      let code = ''
      if (val && val.length > 0) {
        code = val.join('/')
      }
      this.ruleForm.guardianRegisteredResidenceAreaCode2 = code
    },
    handleChange(val, fields) {
      this.$forceUpdate()
    }
  },
}
</script>
<style lang="scss" scoped>

//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {

  .cardBox{
    width: calc(100% - 80px);
    margin: 0 0 0 40px;
    border: 1px solid #E1E1E1;
    position: relative;
    border-radius: 8px;
    margin-bottom: 30px;
    .cardTitle{
      position: absolute;
      width: auto;
      height: 30px;
      line-height: 30px;
      top: -15px;
      left: 15px;
      padding:0 15px;
      background: #ffffff;
      color: #3570f6;
      font-size: 18px;
    }
    .cardCon{
      width: 100%;
      height: auto;
      padding:30px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 20px;
      .zhanwei{
        width: 100%;
        height: 5px;
        background: #f1f1f1;
      }
      .el-form-item{
        width: calc(50% - 10px);
        margin: 0;
        &.card_long{
          width: 100%;
        }
      }
      ::v-deep .el-upload-dragger{
        width: 100%;
        height: 100%;
      }
      .card{
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border: 1px solid #E6E6E6;
        border-radius: 6px;
        .required {
          &::after{
            content: "*";
            color: #ff3300;
            margin: 2px 5px;
          }
        }
        .cardName{
          width: auto;
          min-width: 130px;
          height: 50px;
          background: #FAFAFA;
          padding:0 15px;
          display: flex;
          justify-content: center;
          align-items: center;
          white-space: nowrap;
          border-radius: 4px 0 0 4px;
          font-size: 18px;
          color: #383838;
          border-right: 1px solid #e6e6e6;
          &.longName{
            min-width: 200px;
          }
        }
        .cardContent{
          width: 100%;
          height: 50px;
          border-radius: 0 4px 4px 0 ;
          padding: 0 15px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          font-size: 18px;
          color: #383838;
          ::v-deep .el-select{
            .el-input{
              input{
                border: none;
                font-size: 18px;
                padding: 0;
                background: none;
              }
            }
          }
          ::v-deep .el-cascader{
            .el-input{
              input{
                border: none;
                font-size: 18px;
                padding: 0;
                background: none;
              }
            }
          }
          ::v-deep .el-date-editor{
            width: 100%;
            height: 100%;
            input{
              height: 100%;
              border: none;
              background: none;
              font-size: 18px;
              padding-left: 40px;
            }
            .el-input__icon{
              font-size: 18px;
            }
          }
          .cardTxt{
            width: 100%;
            height: 100%;
            border: none;
            background: none;
            font-size: 18px;
            color: #383838;
            outline: none;
          }

        }
        &.card_long{
          width: 100%;
        }
      }
      .upload-demo {
        width: 100%;
        .el-upload__tip {
          width: calc(100% - 250px);
          height: 54px;
          line-height: 54px;
          font-size: 16px;
          color: #383838;
        }
      }
    }
  }


  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;

    .choose_body {
      flex: 1;
      display: flex;
      justify-content: center;
      margin-top: 64px;
      .body_middle {
        width: 1200px;
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 10px 30px 1px rgba(93,132,177,0.11);
        border-radius: 16px;
        .middle_top {
          width: 100%;
          margin: 40px 0px 10px 0;
        }
        .border_top {
          width: 1155px;
          margin: 30px 22px 0px 22px;
          height: 7px;
          background: #f1f1f1;
          border-radius: 12px 12px 12px 12px;
          opacity: 1;
        }
        .title_box {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin: 25px 0 25px 2.5%;
          .title {
            width: 176px;
            height: 29px;
            font-size: 22px;
            font-weight: bold;
            color: #000000;
          }
          .title_left {
            display: flex;
            .xiala {
              .sousuo1{
                display: none;
              }
              margin-left: 14px;
              color: #3570F6;
              ::v-deep .el-input__inner{
                border: none;
                width: 200px;
                height: 29px;
                line-height: 29px;
                font-size: 22px;
                font-weight: 400;
                color: #3570F6;
              }
              ::v-deep .el-select__caret{
                font-size: 18px;
                color: #3570F6;
                height: 29px;
                line-height: 29px;
              }
            }
          }
          .title_right {
            width: 244px;
            height: 41px;
            line-height: 41px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #f1f1f1;
            border-radius: 21px 21px 21px 21px;
            input {
              width: 170px;
              height: 31px;
              line-height: 31px;
              font-size: 16px;
              font-weight: 400;
              color: #383838;
              border: none;
              outline: none;
              background: none;
            }
            img {
              width: 19px;
              height: 19px;
              margin-left: 10px;
            }
          }
        }
        .foot_Box {
          width: 100%;
          height: 140px;
          display: flex;
          justify-content: center;
          align-items: center;
          div:nth-child(1) {
            width: 418px;
            height: 59px;
            margin: 28px 53px 18px 53px;
            background: #3570f6;
            border-radius: 10px 10px 10px 10px;
            text-align: center;
            line-height: 59px;
            color: #fff;
          }
        }
      }
    }
  }
  .el-select-dropdown__item{
    height: auto;
    line-height: normal;
    padding:10px 20px;
    .cardConBox{
      width: 100%;
      height: auto;
      display: flex;
      flex-flow: column;
      padding: 5px 0;
      background: #fafafa;
      border: 1px solid #f1f1f1;
      border-radius: 4px;
      padding:8px 12px;
      cursor: pointer;
      span:nth-child(1){
        color: #000000;
        font-weight: bold;
        font-size: 16px;
        cursor: pointer;
      }
      span:nth-child(2){
        color: #666;
        font-weight: normal;
        width: 360px;
        white-space:normal;
        cursor: pointer;
      }
    }
    &:hover{
      background: none;
      .cardConBox{
        background: #3570F605;
        border: 1px solid #3570F6;
        span:nth-child(1){
          color: #3570F6;
        }
      }
    }
  }

}

// 移动端
@media screen and (max-width: 720px) {


  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .choose_body {
      width: 100%;
      margin-top: 5.8rem;
      .body_middle {
        width: calc(100% - 2rem);
        margin-left: 1rem;
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 0.5rem 1rem 1px rgba(93,132,177,0.11);
        border-radius: 1rem;
        .middle_top {
          display: none;
        }
        .border_top {
          display: none;
        }
        .title_box {
          width:100%;
          display: flex;
          flex-direction: column;
          padding:1rem 1.5rem;
          .title {
            width: 100%;
            height: 2rem;
            font-size: 1.6rem;
            font-weight: bold;
            color: #000000;
          }
          .title_left {
            display: flex;
            flex-direction: column;
            .xiala {
              margin: 0.5rem 0;
              width:100%;
              color: #3570F6;
              display: flex;
              ::v-deep .el-input__inner{
                display: flex;
                border: none;
                width:100%;
                height: 2rem;
                font-size: 1.2rem;
                color: #3570F6;
              }
              ::v-deep .el-select__caret{
                font-size: 1.4rem;
                color: #3570F6;
                height: 2.2rem;
              }
            }
          }
          .title_right {
            width: 100%;
            height: auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f1f1f1;
            border-radius: 0.4rem;
            padding:0 1rem;
            margin-top: 0.5rem;
            input {
              width: calc(100% - 2rem);
              height: 3rem;
              font-size: 1.2rem;
              font-weight: 400;
              color: #383838;
              border: none;
              outline: none;
              background: none;
            }
            img {
              width: 1.5rem;
              height: 1.5rem;
            }
          }
        }


        .cardBox{
          width: calc(100% - 2rem);
          margin: 0 0 0 1rem;
          border: 1px solid #E1E1E1;
          position: relative;
          border-radius: 0.6rem;
          margin-bottom: 1.5rem;
          .cardTitle{
            position: absolute;
            width: auto;
            height: 2rem;
            line-height: 2rem;
            top: -1rem;
            left: 1rem;
            padding:0 1rem;
            background: #ffffff;
            color: #3570f6;
            font-size: 1.1rem;
            font-weight: bold;
          }
          .cardCon{
            width: 100%;
            height: auto;
            padding:1rem;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 0.6rem;
            .zhanwei{
              display: none;
            }
            .el-form-item{
              width: 100%;
              margin: 0 0 13px 0;
              &.card_long{
                width: 100%;
              }
            }
            ::v-deep .el-upload-dragger{
              width: 100%;
              height: 100%;
            }
            .card{
              width: 100%;
              display: flex;
              border: 1px solid #E6E6E6;
              border-radius:0.2rem;
              flex-flow: column;
              .required {
                &::before{
                  content: "*";
                  color: #ff3300;
                  font-size: 1rem;
                  margin: 0 0.2rem 0 0;
                }
              }
              .cardName{
                width: 100%;
                height:auto;
                background: #FAFAFA;
                padding:0 0.8rem;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                white-space: nowrap;
                font-size: 1rem;
                color: #999;
                border-bottom: 1px solid #e6e6e6;
                &.longName{
                  min-width: 10rem;
                }
              }
              .cardContent{
                width: 100%;
                height: 3rem;
                padding: 0 0.8rem;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                font-size: 1rem;
                color: #383838;
                ::v-deep .el-select{
                  .el-input{
                    input{
                      border: none;
                      font-size: 1rem;
                      padding: 0;
                      background: none;
                    }
                  }
                }
                ::v-deep .el-cascader{
                  .el-input{
                    input{
                      border: none;
                      font-size: 1rem;
                      padding: 0;
                      background: none;
                    }
                  }
                }
                ::v-deep .el-form-item__content{
                  line-height: 3rem;
                }
                ::v-deep .el-input__prefix{
                  left: 0;
                  height: 100%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  .el-input__icon{
                    line-height: 3rem;
                  }
                }
                ::v-deep .el-date-editor{
                  width: 100%;
                  height: 100%;
                  input{
                    height: 100%;
                    border: none;
                    background: none;
                    font-size: 1rem;
                    padding-left: 2rem;
                  }
                  .el-input__icon{
                    font-size: 1rem;
                  }
                }
                .cardTxt{
                  width: 100%;
                  height: 100%;
                  border: none;
                  background: none;
                  font-size: 1rem;
                  color: #383838;
                  outline: none;
                }
              }
              &.card_long{
                width: 100%;
              }
            }
            .upload-demo {
              width: 100%;
              .el-upload__tip {
                width: 100%;
                height: auto;
                font-size: 1rem;
                color: #999;
              }
            }
          }
        }


        .foot_Box {
          width: calc(100% - 2rem);
          margin-left:1rem ;
          display: flex;
          justify-content: space-evenly;
          flex-flow: column;
          gap: 1rem;
          padding-bottom: 1.5rem;
          div {
            width: 100%;
            height: 3.4rem;
            background: #3570f6;
            border-radius: 0.6rem;
            text-align: center;
            line-height: 3.4rem;
            color: #fff;
          }

        }
      }
    }
    .choose_foot {
       width: 100%;
      height:auto;
      display: flex;
      justify-content: center;
      align-items: center;
      padding:1.5rem 1.2rem;
    }
  }
  .el-select-dropdown__item{
    height: auto;
    line-height: normal;
    padding:0.6rem 1.2rem;
    .cardConBox{
      width: 80vw;
      height: auto;
      box-sizing: border-box;
      display: flex;
      flex-flow: column;
      padding: 0.3rem 0;
      background: #fafafa;
      border: 1px solid #f1f1f1;
      border-radius: 0.6rem;
      padding:0.6rem 1rem;
      cursor: pointer;
      span:nth-child(1){
        color: #000000;
        font-weight: bold;
        font-size: 1.2rem;
        cursor: pointer;
        white-space: normal;
        word-break: break-all;
      }
      span:nth-child(2){
        color: #666;
        font-weight: normal;
        width: 100%;
        white-space:normal;
        cursor: pointer;
        font-size: 1rem;
      }
    }
    &:hover{
      background: none;
      .cardConBox{
        background: #3570F605;
        border: 1px solid #3570F6;
        span:nth-child(1){
          color: #3570F6;
        }
      }
    }
  }
}
::v-deep .el-select{
  width: 100%!important;
}
::v-deep .el-radio__label{
  font-size: 1rem;
}
.el-date-editor{
  display: flex;
  justify-content: space-between;
  flex-flow: row;
}
::v-deep .is-finish{
  color: #383838;
  font-size: 18px;
  .el-step__line{
    background: #3570F6;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #3570F6;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    border-color: #3570F6;
  }
}

::v-deep .is-process{
  color: #383838;
  font-size: 18px;
  font-weight: normal;
  .el-step__line{
    background: #f1f1f1;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #ffffff;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    border-width: 5px;
    color: #3570F6;
    border-color: #3570F6;
  }
}

::v-deep .is-wait{
  color: #383838;
  font-size: 18px;
  font-weight: normal;
  .el-step__line{
    background: #f1f1f1;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #EEF6FF;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    border-width: 5px;
    color: #3570F6;
    border-color: #EEF6FF;
  }
}
::v-deep .el-cascader {
  width: 100% !important;
}
</style>
