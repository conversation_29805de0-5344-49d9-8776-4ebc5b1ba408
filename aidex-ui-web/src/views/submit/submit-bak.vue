<template>
  <div class="choose">
    <Topbox class="top_layout" /><!--顶部-->
   <top/>

    <div class="choose_body">
      <div class="body_middle">
        <div class="middle_top">
          <el-steps :active="3" align-center>
            <el-step title="注册"></el-step>
            <el-step title="选择入学区域"></el-step>
            <el-step title="选择报名学校"></el-step>
            <el-step title="提交报名资料"></el-step>
          </el-steps>
        </div>
        <div class="border_top"></div>
        <div class="title_box">
          <div class="title_left">
            <div class="title">填写报名资料</div>
          </div>
        </div>

        <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
          <div class="cardBox">
            <div class="cardTitle">报名资料</div>
            <div class="cardCon">
              <el-form-item prop="schoolName">
                <div class="card">
                  <div class="cardName">报名学校</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model="ruleForm.schoolName" readonly />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="sourceTypeId" v-if="routerQuery.type === $constants.SCHOOL_XX">
                <div class="card">
                  <div class="cardName required">生源类型</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.sourceTypeId" placeholder="请选择" style="width: 100%;" @change="handleSelectChange">
                      <el-option v-for="(item, index) in sourceTypeOptions" :key="index" :label="item.name" :value="item.id">
                        <div class="cardConBox">
                          <span>{{ item.name }}</span>
                          <span>{{ item.description }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="studentCode" v-if="routerQuery.type === $constants.SCHOOL_CZ">
                <div class="card">
                  <div class="cardName required">电子学籍号</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.studentCode" maxlength="30" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="graduationSchoolAreaCode" v-if="routerQuery.type === $constants.SCHOOL_CZ">
                <div class="card">
                  <div class="cardName required">毕业学校区划</div>
                  <div class="cardContent">
                    <el-cascader
                        v-model="graduationSchoolAreaCodes"
                        filterable
                        :props="cascaderProps"
                        :options="areaTreeOptions"
                        separator=""
                        placeholder="请选择"
                        @change="handleAreaChange"
                    >
                    </el-cascader>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="graduationSchool" v-if="routerQuery.type === $constants.SCHOOL_CZ">
                <div class="card">
                  <div class="cardName required">毕业学校</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.graduationSchool" maxlength="30" />
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>

          <div class="cardBox">
            <div class="cardTitle">基本信息</div>
            <div class="cardCon">
                <el-form-item prop="name">
                  <div class="card">
                    <div class="cardName required">学生姓名</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.name" maxlength="10" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="mobile">
                  <div class="card">
                    <div class="cardName required">联系电话</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.mobile" maxlength="20" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="cardType">
                  <div class="card">
                    <div class="cardName required">身份证类型</div>
                    <div class="cardContent">
                      <el-select v-model="ruleForm.cardType" placeholder="请选择">
                        <el-option v-for="(item, index) in cardTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                      </el-select>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="idCard">
                  <div class="card">
                    <div class="cardName required">身份证号</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.idCard" maxlength="20" @blur="handleIdCardChange"  />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="birthday">
                  <div class="card">
                    <div class="cardName required">出生日期</div>
                    <div class="cardContent">
                      <el-date-picker
                          type="date"
                          v-model="ruleForm.birthday"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          style="width: 100%; border: none"
                      ></el-date-picker>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="gender">
                  <div class="card">
                    <div class="cardName required">性别</div>
                    <div class="cardContent">
                      <el-radio v-model="ruleForm.gender"
                          v-for="(item, index) in genderOptions" :key="index"
                          :label="item.dictValue">{{item.dictLabel}}</el-radio>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="nation">
                  <div class="card">
                    <div class="cardName required">民族</div>
                    <div class="cardContent">
                      <el-select v-model="ruleForm.nation" placeholder="请选择">
                        <el-option v-for="(item, index) in nationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                      </el-select>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="country">
                  <div class="card">
                    <div class="cardName required">国籍/地区</div>
                    <div class="cardContent">
                      <el-select v-model="ruleForm.country" placeholder="请选择">
                        <el-option v-for="(item, index) in countryOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                      </el-select>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="nativePlace">
                  <div class="card">
                    <div class="cardName required">籍贯</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.nativePlace" maxlength="80" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="politicsStatus">
                  <div class="card">
                    <div class="cardName required">政治面貌</div>
                    <div class="cardContent">
                      <el-select v-model="ruleForm.politicsStatus" placeholder="请选择" disabled>
                        <el-option v-for="(item, index) in politicsStatusOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                      </el-select>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="postalCode">
                  <div class="card">
                    <div class="cardName required">邮政编码</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.postalCode" maxlength="10" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="email">
                  <div class="card">
                    <div class="cardName">电子信箱</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.email" maxlength="25" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="formerName">
                  <div class="card">
                    <div class="cardName">曾用名</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.formerName" maxlength="12" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="bloodType">
                  <div class="card">
                    <div class="cardName">血型</div>
                    <div class="cardContent">
                      <el-select v-model="ruleForm.bloodType" placeholder="请选择">
                        <el-option v-for="(item, index) in bloodTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                      </el-select>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="healthCondition">
                  <div class="card">
                    <div class="cardName required">健康状况</div>
                    <div class="cardContent">
                      <el-select v-model="ruleForm.healthCondition" placeholder="请选择">
                        <el-option v-for="(item, index) in healthConditionOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                      </el-select>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="inoculatePrevent">
                  <div class="card">
                    <div class="cardName required">预防接种</div>
                    <div class="cardContent">
                      <el-radio v-model="ruleForm.inoculatePrevent"
                                v-for="(item, index) in yesOrNoOptions" :key="index"
                                :label="item.dictValue">{{item.dictLabel}}</el-radio>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="disabilityType">
                  <div class="card">
                    <div class="cardName">残疾类型</div>
                    <div class="cardContent">
                      <el-select v-model="ruleForm.disabilityType" placeholder="请选择">
                        <el-option v-for="(item, index) in disabilityTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                      </el-select>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="strongPoint">
                  <div class="card">
                    <div class="cardName">特长</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.strongPoint" maxlength="80" />
                    </div>
                  </div>
                </el-form-item>
                <div class="zhanwei"></div>
                <el-form-item prop="birthplaceAreaCode">
                  <div class="card">
                    <div class="cardName required">出生地区划</div>
                    <div class="cardContent">
                      <el-cascader
                          v-model="birthplaceAreaCodes"
                          filterable
                          :props="cascaderProps"
                          :options="areaTreeOptions"
                          separator=""
                          placeholder="请选择"
                          @change="handleBirthplaceAreaCodeChange"
                      >
                      </el-cascader>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="registeredResidenceCategory">
                  <div class="card">
                    <div class="cardName required">户口性质</div>
                    <div class="cardContent">
                      <el-select v-model="ruleForm.registeredResidenceCategory" placeholder="请选择">
                        <el-option v-for="(item, index) in registeredResidenceCategoryOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                      </el-select>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="registeredResidenceAreaCode">
                  <div class="card">
                    <div class="cardName required">户口所在地</div>
                    <div class="cardContent">
                      <el-cascader
                          v-model="registeredResidenceAreaCodes"
                          filterable
                          :props="cascaderProps"
                          :options="areaFourLevelTreeOptions"
                          separator=""
                          placeholder="请选择"
                          @change="handleRegisteredResidenceChange"
                      >
                      </el-cascader>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="domicileRegisterTime">
                  <div class="card">
                    <div class="cardName required">户籍注册时间</div>
                    <div class="cardContent">
                      <el-date-picker
                          type="date"
                          v-model="ruleForm.domicileRegisterTime"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          style="width: 100%; border: none"
                      ></el-date-picker>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="domicileAddress" class="card_long">
                  <div class="card">
                    <div class="cardName required">户籍地址</div>
                    <div class="cardContent">
                      <div style="width: 42%; border-right: 3px solid #f1f1f1;">
                        <el-cascader
                            v-model="domicileAddressAreaCodes"
                            filterable
                            :props="cascaderProps"
                            :options="areaFourLevelTreeOptions"
                            separator=""
                            placeholder="请选择"
                            @change="handleDomicileAddressAreaCodesChange"
                        >
                        </el-cascader>
                      </div>
                      <div style="margin-left: 20px; width: 57%;">
                        <input class="cardTxt" type="text" v-model.trim="ruleForm.domicileAddress" maxlength="50" />
                      </div>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="houseProperty">
                  <div class="card">
                    <div class="cardName required">居住地产权</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.houseProperty" maxlength="15" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="houseRegisterTime">
                  <div class="card">
                    <div class="cardName required">产权注册时间</div>
                    <div class="cardContent">
                      <el-date-picker
                          type="date"
                          v-model="ruleForm.houseRegisterTime"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          style="width: 100%; border: none"
                      ></el-date-picker>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="residentialAddress" class="card_long">
                  <div class="card">
                    <div class="cardName required">居住地址</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.residentialAddress" maxlength="50" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="familyAddress" class="card_long">
                  <div class="card">
                    <div class="cardName required">家庭地址</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.familyAddress" maxlength="50" />
                    </div>
                  </div>
                </el-form-item>
                <div class="zhanwei"></div>
                <el-form-item prop="countrymenAbroad">
                  <div class="card">
                    <div class="cardName longName required">港澳台侨外</div>
                    <div class="cardContent">
                      <el-radio v-model="ruleForm.countrymenAbroad"
                                v-for="(item, index) in yesOrNoOptions" :key="index"
                                :label="item.dictValue">{{item.dictLabel}}</el-radio>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="onlyChild">
                  <div class="card">
                    <div class="cardName longName required">独生子女</div>
                    <div class="cardContent">
                      <el-radio v-model="ruleForm.onlyChild"
                                v-for="(item, index) in yesOrNoOptions" :key="index"
                                :label="item.dictValue">{{item.dictLabel}}</el-radio>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="preschoolEducation">
                  <div class="card">
                    <div class="cardName longName required">受过学前教育</div>
                    <div class="cardContent">
                      <el-radio v-model="ruleForm.preschoolEducation"
                                v-for="(item, index) in yesOrNoOptions" :key="index"
                                :label="item.dictValue">{{item.dictLabel}}</el-radio>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="applyFunding">
                  <div class="card">
                    <div class="cardName longName required">需要申请资助</div>
                    <div class="cardContent">
                      <el-radio v-model="ruleForm.applyFunding"
                                v-for="(item, index) in yesOrNoOptions" :key="index"
                                :label="item.dictValue">{{item.dictLabel}}</el-radio>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="enjoyGrants">
                  <div class="card">
                    <div class="cardName longName required">享受一补
                      <el-popover
                          placement="top"
                          title="提示"
                          trigger="click"
                          content="一补：指对家庭经济困难寄宿生补贴生活费。">
                        <i class="el-icon-question" slot="reference"></i>
                      </el-popover>
                    </div>
                    <div class="cardContent">
                      <el-radio v-model="ruleForm.enjoyGrants"
                                v-for="(item, index) in yesOrNoOptions" :key="index"
                                :label="item.dictValue">{{item.dictLabel}}</el-radio>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="orphan">
                  <div class="card">
                    <div class="cardName longName required">是否孤儿</div>
                    <div class="cardContent">
                      <el-radio v-model="ruleForm.orphan"
                                v-for="(item, index) in yesOrNoOptions" :key="index"
                                :label="item.dictValue">{{item.dictLabel}}</el-radio>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="priorityRaising">
                  <div class="card">
                    <div class="cardName longName required">烈士或优抚子女</div>
                    <div class="cardContent">
                      <el-radio v-model="ruleForm.priorityRaising"
                                v-for="(item, index) in yesOrNoOptions" :key="index"
                                :label="item.dictValue">{{item.dictLabel}}</el-radio>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="workAccompany">
                  <div class="card">
                    <div class="cardName longName required">务工随迁子女</div>
                    <div class="cardContent">
                      <el-radio v-model="ruleForm.workAccompany"
                                v-for="(item, index) in yesOrNoOptions" :key="index"
                                :label="item.dictValue">{{item.dictLabel}}</el-radio>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="leftBehindChildren">
                  <div class="card">
                    <div class="cardName longName required">是否留守儿童</div>
                    <div class="cardContent">
                      <el-radio v-model="ruleForm.leftBehindChildren"
                                v-for="(item, index) in yesOrNoOptions" :key="index"
                                :label="item.dictValue">{{item.dictLabel}}</el-radio>
                    </div>
                  </div>
                </el-form-item>
                <div class="zhanwei"></div>
                <el-form-item prop="entryWay">
                  <div class="card">
                    <div class="cardName longName required">入学方式</div>
                    <div class="cardContent">
                      <el-select v-model="ruleForm.entryWay" placeholder="请选择">
                        <el-option v-for="(item, index) in entryWayOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                      </el-select>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="attendWay">
                  <div class="card">
                    <div class="cardName longName required">就读方式</div>
                    <div class="cardContent">
                      <el-select v-model="ruleForm.attendWay" placeholder="请选择">
                        <el-option v-for="(item, index) in attendWayOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                      </el-select>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="schoolDistance">
                  <div class="card">
                    <div class="cardName longName">上下学距离</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="number" v-model.trim="ruleForm.schoolDistance" min="0" maxlength="5" style="width: 90%;" />
                      <span style="margin-left: 3px;">km</span>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="schoolWay">
                  <div class="card">
                    <div class="cardName longName">上下学方式</div>
                    <div class="cardContent">
                      <el-select v-model="ruleForm.schoolWay" placeholder="请选择">
                        <el-option v-for="(item, index) in schoolWayOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                      </el-select>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="schoolBusTake">
                  <div class="card">
                    <div class="cardName longName">需要乘坐校车</div>
                    <div class="cardContent">
                      <el-radio v-model="ruleForm.schoolBusTake"
                                v-for="(item, index) in yesOrNoOptions" :key="index"
                                :label="item.dictValue">{{item.dictLabel}}</el-radio>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="governmentBuyDegree">
                  <div class="card">
                    <div class="cardName longName">政府购买学位</div>
                    <div class="cardContent">
                      <el-radio v-model="ruleForm.governmentBuyDegree"
                                v-for="(item, index) in yesOrNoOptions" :key="index"
                                :label="item.dictValue">{{item.dictLabel}}</el-radio>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="studyFollowClass">
                  <div class="card">
                    <div class="cardName longName">随班就读</div>
                    <div class="cardContent">
                      <el-select v-model="ruleForm.studyFollowClass" placeholder="请选择">
                        <el-option v-for="(item, index) in studyFollowClassOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                      </el-select>
                    </div>
                  </div>
                </el-form-item>
              <div class="zhanweiLong"><span>监护人（1）信息</span></div>
              <el-form-item prop="guardianName1">
                <div class="card">
                  <div class="cardName longName required">姓名</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianName1" maxlength="12" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianMobile1">
                <div class="card">
                  <div class="cardName longName required">电话</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianMobile1" maxlength="11" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianCardType1">
                <div class="card">
                  <div class="cardName longName">身份证类型</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.guardianCardType1" placeholder="请选择">
                      <el-option v-for="(item, index) in cardTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianIdCard1">
                <div class="card">
                  <div class="cardName longName">身份证号</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianIdCard1" maxlength="20" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianRelation1">
                <div class="card">
                  <div class="cardName longName required">与学生关系</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.guardianRelation1" placeholder="请选择">
                      <el-option v-for="(item, index) in relationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianNation1">
                <div class="card">
                  <div class="cardName longName">民族</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.guardianNation1" placeholder="请选择">
                      <el-option v-for="(item, index) in nationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianResidentialAddress1" class="card_long">
                <div class="card">
                  <div class="cardName longName required">现住址</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianResidentialAddress1" maxlength="50" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianship1">
                <div class="card">
                  <div class="cardName longName required">是否监护人</div>
                  <div class="cardContent">
                    <el-radio v-model="ruleForm.guardianship1"
                              v-for="(item, index) in yesOrNoOptions" :key="index"
                              :label="item.dictValue">{{item.dictLabel}}</el-radio>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianRegisteredResidenceAreaCode1">
                <div class="card">
                  <div class="cardName longName required">户口所在地</div>
                  <div class="cardContent">
                    <el-cascader
                        v-model="guardianRegisteredResidenceAreaCodes1"
                        filterable
                        :props="cascaderProps"
                        :options="areaFourLevelTreeOptions"
                        separator=""
                        placeholder="请选择"
                        @change="handleGuardianRegisteredResidence1Change"
                    >
                    </el-cascader>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianWorkUnit1">
                <div class="card">
                  <div class="cardName longName">工作单位</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianWorkUnit1" maxlength="30" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianWorkPost1">
                <div class="card">
                  <div class="cardName longName">职务</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.guardianWorkPost1" placeholder="请选择"  >
                      <el-option v-for="(item, index) in postOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <div class="zhanweiLong"><span>监护人（2）信息</span></div>
              <el-form-item prop="guardianName2">
                <div class="card">
                  <div class="cardName longName required">姓名</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianName2" maxlength="12" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianMobile2">
                <div class="card">
                  <div class="cardName longName required">电话</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianMobile2" maxlength="11" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianCardType2">
                <div class="card">
                  <div class="cardName longName">身份证类型</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.guardianCardType2" placeholder="请选择">
                      <el-option v-for="(item, index) in cardTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianIdCard2">
                <div class="card">
                  <div class="cardName longName">身份证号</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianIdCard2" maxlength="20"  />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianRelation2">
                <div class="card">
                  <div class="cardName longName required">与学生关系</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.guardianRelation2" placeholder="请选择">
                      <el-option v-for="(item, index) in relationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianNation2">
                <div class="card">
                  <div class="cardName longName">民族</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.guardianNation2" placeholder="请选择">
                      <el-option v-for="(item, index) in nationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianResidentialAddress2" class="card_long">
                <div class="card">
                  <div class="cardName longName required">现住址</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianResidentialAddress2" maxlength="50" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianship2">
                <div class="card">
                  <div class="cardName longName required">是否监护人</div>
                  <div class="cardContent">
                    <el-radio v-model="ruleForm.guardianship2"
                              v-for="(item, index) in yesOrNoOptions" :key="index"
                              :label="item.dictValue">{{item.dictLabel}}</el-radio>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianRegisteredResidenceAreaCode2">
                <div class="card">
                  <div class="cardName longName required">户口所在地</div>
                  <div class="cardContent">
                    <el-cascader
                        v-model="guardianRegisteredResidenceAreaCodes2"
                        filterable
                        size="small"
                        :props="cascaderProps"
                        :options="areaFourLevelTreeOptions"
                        separator=""
                        placeholder="请选择"
                        @change="handleGuardianRegisteredResidence2Change"
                    >
                    </el-cascader>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianWorkUnit2">
                <div class="card">
                  <div class="cardName longName">工作单位</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianWorkUnit2" maxlength="30" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianWorkPost2">
                <div class="card">
                  <div class="cardName longName">职务</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.guardianWorkPost2" placeholder="请选择">
                      <el-option v-for="(item, index) in postOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
            </div>
        </div>

        <div class="cardBox">
          <div class="cardTitle">免冠照片上传</div>
          <div class="cardCon">
            <image-upload :limit="1" :file-size="300" :drag="true"
              :fileType="fileType"
              :upload-text="uploadText"
              :tip-text="`请上传本人两寸免冠照片，照片尺寸413*626，照片大小300kb以内，只能上传jpg/png文件。`"
              ref="pictureFileUpload"
              @ok="handleChange"
            ></image-upload>
          </div>
        </div>

        <template v-for="(item, index) in fileUploadList">
        <div class="cardBox" v-show="fileClassifyList.includes(item.key)">
          <div class="cardTitle">{{ item.title + '上传' }}</div>
          <div class="cardCon">
            <image-upload :limit="item.limit" :file-size="item.fileSize" :drag="true" :multiple="item.multiple"
              :fileType="item.fileType"
              :upload-text="item.uploadText"
              :tip-text="item.tipText"
              :ref="uploadRefKey + item.key"
              @ok="handleChangeFile($event, item.key)"
            ></image-upload>
          </div>
        </div>
        </template>

        <div class="border_top"></div>
        <!-- 底部 -->
        <div class="foot_Box">
          <div @click="toschool()"><i class="el-icon-loading" v-if="loading"></i>上一步：选择报名学校</div>
          <div @click="submit('ruleForm')"><i class="el-icon-loading" v-if="loading"></i>确认保存</div>
        </div>
        </el-form>
      </div>
    </div>
    <Footbox class="foot_layout" />
    <foot />
  </div>
</template>

<script>
  import top from '../../components/top/top.vue'
  import foot from '../../components/foot/foot.vue'
  import {getApplyInfo, getSourceTypeList, saveApply} from '@/api/apply'
  import {validIdCard, validMobile, validNumber, validPositiveInteger} from '@/utils/validate'
  import ImageUpload from '@/components/ImageUpload/index'
  import { listAreaTree } from '@/api/area'
  import Topbox from "@/views/layout/newtop";
  import Footbox from "@/views/layout/foot";

  export default {
  components: {
    top,foot,ImageUpload,
    Topbox,
    Footbox
  },
  data() {
    const validateIdCard = (rule, value, callback) => {
      this.validIdCard(this.ruleForm.cardType, value, callback)
    }
    const validateIdCard1 = (rule, value, callback) => {
      this.validIdCard(this.ruleForm.guardianCardType1, value, callback)
    }
    const validateIdCard2 = (rule, value, callback) => {
      this.validIdCard(this.ruleForm.guardianCardType2, value, callback)
    }
    const validateMobile = (rule, value, callback) => {
      if (value && !validMobile(value)) {
        callback(new Error('请正确填写联系电话'))
      } else {
        callback()
      }
    }
    const validateNumber = (rule, value, callback) => {
      if (value && !validNumber(value)) {
        callback(new Error('请填写数字'))
      } else {
        callback()
      }
    }
    const validateDomicileAddress = (rule, value, callback) => {
      if (rule.required) {
        if (!this.ruleForm.domicileAddressAreaCode || !value) {
          callback(new Error('请填写户籍地址'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      loading: false,
      sourceTypeOptions: [],
      cardTypeOptions: [],
      genderOptions: [],
      yesOrNoOptions: [],
      nationOptions: [],
      countryOptions: [],
      politicsStatusOptions: [],
      healthConditionOptions: [],
      disabilityTypeOptions: [],
      registeredResidenceCategoryOptions: [],
      bloodTypeOptions: [],
      entryWayOptions: [],
      attendWayOptions: [],
      schoolWayOptions: [],
      studyFollowClassOptions: [],
      postOptions: [],
      relationOptions: [],
      birthplaceAreaCodes: [],
      registeredResidenceAreaCodes: [],
      guardianRegisteredResidenceAreaCodes1: [],
      guardianRegisteredResidenceAreaCodes2: [],
      graduationSchoolAreaCodes: [],
      domicileAddressAreaCodes: [],
      routerQuery: {},
      pictureFileList: [],
      fileList: {},
      fileUploadList: [],
      fileClassifyList: [],
      fileType: ['jpg', 'png'],
      uploadText: '选择要上传的图片',
      tipText: '只能上传jpg/png文件，且不超过500kb,最多上传9张,上传后点击可查看大图。',
      limit: 9,
      fileSize: 500,
      multiple: false,
      uploadRefKey: 'fileUpload_',
      operate: '',
      id: '',
      areaTreeOptions: [],
      areaFourLevelTreeOptions: [],
      cascaderProps: {
        label: 'label',
        value: 'id',
        children: 'children',
        leaf: 'leaf'
      },
      ruleForm: {
        id: '',
        schoolId: '',
        sourceTypeId: '',
        name: '',
        cardType: '01',
        idCard: '',
        birthday: '',
        gender: '',
        nation: '',
        mobile: '',
        inoculatePrevent: '',
        domicileAddressAreaCode: '',
        domicileAddress: '',
        domicileRegisterTime: '',
        houseProperty: '',
        houseRegisterTime: '',
        residentialAddress: '',
        guardianId1: '',
        guardianName1: '',
        guardianIdCard1: '',
        guardianMobile1: '',
        guardianRelation1: '',
        guardianResidentialAddress1: '',
        guardianship1: '',
        guardianCardType1: '',
        guardianNation1: '',
        guardianRegisteredResidenceAreaCode1: '',
        guardianWorkUnit1: '',
        guardianWorkPost1: '',
        guardianId2: '',
        guardianName2: '',
        guardianIdCard2: '',
        guardianMobile2: '',
        guardianRelation2: '',
        guardianResidentialAddress2: '',
        guardianship2: '',
        guardianCardType2: '',
        guardianNation2: '',
        guardianRegisteredResidenceAreaCode2: '',
        guardianWorkUnit2: '',
        guardianWorkPost2: '',
        picture: '',
        attachmentId: '',
        applyType: '',
        country: '01',
        nativePlace: '',
        healthCondition: '01',
        politicsStatus: '13',
        postalCode: '',
        email: '',
        disabilityType: '0',
        birthplaceAreaCode: '',
        registeredResidenceCategory: '',
        familyAddress: '',
        formerName: '',
        bloodType: '',
        countrymenAbroad: '',
        onlyChild: '',
        preschoolEducation: '',
        applyFunding: '',
        enjoyGrants: '',
        orphan: '',
        priorityRaising: '',
        workAccompany: '',
        strongPoint: '',
        leftBehindChildren: '',
        entryWay: '',
        attendWay: '',
        schoolDistance: '',
        schoolWay: '',
        schoolBusTake: '',
        governmentBuyDegree: '',
        studyFollowClass: '',
        registeredResidenceAreaCode: '',
        studentCode: '',
        graduationSchoolAreaCode: '',
        graduationSchool: '',
        deptId: '',
        areaId: '',
        parentAreaId: ''
      },
      rules: {
        sourceTypeId: [{ required: true, message: '生源类型不能为空', trigger: 'change' }],
        name: [{ required: true, message: '学生姓名不能为空', trigger: 'blur' }],
        cardType: [
          { required: true, message: '身份证类型不能为空', trigger: 'change' },
        ],
        idCard: [
          { required: true, message: '身份证号不能为空', trigger: 'blur' },
          { validator: validateIdCard, trigger: 'blur' }
        ],
        birthday: [{ required: true, message: '出生日期不能为空', trigger: 'change' }],
        gender: [{ required: true, message: '性别不能为空', trigger: 'change' }],
        nation: [{ required: true, message: '民族不能为空', trigger: 'change' }],
        mobile: [
          { required: true, message: '联系电话不能为空', trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' },
        ],
        inoculatePrevent: [{ required: true, message: '预防接种不能为空', trigger: 'change' }],
        domicileAddress: [
          { required: true, validator: validateDomicileAddress, message: '', trigger: 'blur' }],
        domicileRegisterTime: [{ required: true, message: '户籍注册时间不能为空', trigger: 'change' }],
        houseProperty: [{ required: true, message: '居住地产权不能为空', trigger: 'blur' }],
        houseRegisterTime: [{ required: true, message: '产权注册时间不能为空', trigger: 'change' }],
        residentialAddress: [{ required: true, message: '居住地址不能为空', trigger: 'blur' }],
        guardianName1: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
        guardianRelation1: [{ required: true, message: '与学生关系不能为空', trigger: 'blur' }],
        guardianIdCard1: [
          { required: false, message: '身份证号不能为空', trigger: 'blur' },
          { validator: validateIdCard1, trigger: 'blur' },
        ],
        guardianMobile1: [
          { required: true, message: '电话不能为空', trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' },
        ],
        guardianResidentialAddress1: [{ required: true, message: '现住址不能为空', trigger: 'blur' }],
        guardianship1: [{ required: true, message: '是否监护人不能为空', trigger: 'blur' }],
        guardianCardType1: [{ required: false, message: '身份证类型不能为空', trigger: 'blur' }],
        guardianNation1: [{ required: false, message: '民族不能为空', trigger: 'blur' }],
        guardianRegisteredResidenceAreaCode1: [{ required: true, message: '户口所在地不能为空', trigger: 'blur' }],
        guardianWorkUnit1: [{ required: false, message: '工作单位不能为空', trigger: 'blur' }],
        guardianWorkPost1: [{ required: false, message: '职务不能为空', trigger: 'blur' }],
        guardianName2: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
        guardianRelation2: [{ required: true, message: '与学生关系不能为空', trigger: 'blur' }],
        guardianIdCard2: [
          { required: false, message: '身份证号不能为空', trigger: 'blur' },
          { validator: validateIdCard2, trigger: 'blur' },
        ],
        guardianMobile2: [
          { required: true, message: '电话不能为空', trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' },
        ],
        guardianResidentialAddress2: [{ required: true, message: '现住址不能为空', trigger: 'blur' }],
        guardianship2: [{ required: true, message: '是否监护人不能为空', trigger: 'blur' }],
        guardianCardType2: [{ required: false, message: '身份证类型不能为空', trigger: 'blur' }],
        guardianNation2: [{ required: false, message: '民族不能为空', trigger: 'blur' }],
        guardianRegisteredResidenceAreaCode2: [{ required: true, message: '户口所在地不能为空', trigger: 'blur' }],
        guardianWorkUnit2: [{ required: false, message: '工作单位不能为空', trigger: 'blur' }],
        guardianWorkPost2: [{ required: false, message: '职务不能为空', trigger: 'blur' }],
        country: [{ required: true, message: '国籍/地区不能为空', trigger: 'change' }],
        nativePlace: [{ required: true, message: '籍贯不能为空', trigger: 'blur' }],
        healthCondition: [{ required: true, message: '健康状况不能为空', trigger: 'blur' }],
        politicsStatus: [{ required: true, message: '政治面貌不能为空', trigger: 'change' }],
        postalCode: [{ required: true, message: '邮政编码不能为空', trigger: 'blur' }],
        email: [{ required: false, message: '电子信箱不能为空', trigger: 'blur' }],
        disabilityType: [{ required: false, message: '残疾类型不能为空', trigger: 'blur' }],
        birthplaceAreaCode: [{ required: true, message: '出生地区划不能为空', trigger: 'blur' }],
        registeredResidenceCategory: [{ required: true, message: '户口性质不能为空', trigger: 'blur' }],
        registeredResidenceAreaCode: [{ required: true, message: '户口所在地不能为空', trigger: 'blur' }],
        familyAddress: [{ required: true, message: '家庭地址不能为空', trigger: 'blur' }],
        formerName: [{ required: false, message: '曾用名不能为空', trigger: 'blur' }],
        bloodType: [{ required: false, message: '血型不能为空', trigger: 'blur' }],
        countrymenAbroad: [{ required: true, message: '港澳台侨外不能为空', trigger: 'blur' }],
        onlyChild: [{ required: true, message: '独生子女不能为空', trigger: 'blur' }],
        preschoolEducation: [{ required: true, message: '受过学前教育不能为空', trigger: 'blur' }],
        applyFunding: [{ required: true, message: '需要申请资助不能为空', trigger: 'blur' }],
        enjoyGrants: [{ required: true, message: '享受一补不能为空', trigger: 'blur' }],
        orphan: [{ required: true, message: '是否孤儿不能为空', trigger: 'blur' }],
        priorityRaising: [{ required: true, message: '烈士或优抚子女不能为空', trigger: 'blur' }],
        workAccompany: [{ required: true, message: '进城务工人员随迁子女不能为空', trigger: 'blur' }],
        strongPoint: [{ required: false, message: '特长不能为空', trigger: 'blur' }],
        leftBehindChildren: [{ required: true, message: '是否留守儿童不能为空', trigger: 'blur' }],
        entryWay: [{ required: true, message: '入学方式不能为空', trigger: 'blur' }],
        attendWay: [{ required: true, message: '就读方式不能为空', trigger: 'blur' }],
        schoolDistance: [
          { required: false, message: '上下学距离不能为空', trigger: 'blur' },
          { validator: validateNumber, trigger: 'blur' },
        ],
        schoolWay: [{ required: false, message: '上下学方式不能为空', trigger: 'blur' }],
        schoolBusTake: [{ required: false, message: '是否需要乘坐校车不能为空', trigger: 'blur' }],
        governmentBuyDegree: [{ required: false, message: '是否由政府购买学位不能为空', trigger: 'blur' }],
        studyFollowClass: [{ required: false, message: '随班就读不能为空', trigger: 'blur' }],
        studentCode: [{ required: true, message: '电子学籍号不能为空', trigger: 'blur' }],
        graduationSchoolAreaCode: [{ required: true, message: '毕业学校区划不能为空', trigger: 'blur' }],
        graduationSchool: [{ required: true, message: '毕业学校不能为空', trigger: 'blur' }],
      },
    }
  },
  beforeDestroy() {},
  created() {
    this.routerQuery = this.$commonUtils.getApplyParams() || {}
    // this.fileUploadList = [
    //   { key: this.$constants.HOUSEHOLD_REGISTER, title: this.$constants.HOUSEHOLD_REGISTER_NAME, fileType: this.fileType, uploadText: this.uploadText, tipText: this.tipText, limit: this.limit, fileSize: this.fileSize, multiple: this.multiple },
    //   { key: this.$constants.HOUSE_PROPRIETARY_CERTIFICATE, title: this.$constants.HOUSE_PROPRIETARY_CERTIFICATE_NAME, fileType: this.fileType, uploadText: this.uploadText, tipText: this.tipText, limit: this.limit, fileSize: this.fileSize, multiple: this.multiple },
    //   { key: this.$constants.HAVE_HOUSE_CERTIFICATE, title: this.$constants.HAVE_HOUSE_CERTIFICATE_NAME, fileType: this.fileType, uploadText: this.uploadText, tipText: this.tipText, limit: this.limit, fileSize: this.fileSize, multiple: this.multiple },
    //   { key: this.$constants.NO_HOUSE_CERTIFICATE, title: this.$constants.NO_HOUSE_CERTIFICATE_NAME, fileType: this.fileType, uploadText: this.uploadText, tipText: this.tipText, limit: this.limit, fileSize: this.fileSize, multiple: this.multiple },
    //   { key: this.$constants.HOUSE_LEASE_CERTIFICATE, title: this.$constants.HOUSE_LEASE_CERTIFICATE_NAME, fileType: this.fileType, uploadText: this.uploadText, tipText: this.tipText, limit: this.limit, fileSize: this.fileSize, multiple: this.multiple },
    //   { key: this.$constants.WORK_CERTIFICATE, title: this.$constants.WORK_CERTIFICATE_NAME, fileType: this.fileType, uploadText: this.uploadText, tipText: this.tipText, limit: this.limit, fileSize: this.fileSize, multiple: this.multiple },
    //   { key: this.$constants.RESIDENCE_PERMIT, title: this.$constants.RESIDENCE_PERMIT_NAME, fileType: this.fileType, uploadText: this.uploadText, tipText: this.tipText, limit: this.limit, fileSize: this.fileSize, multiple: this.multiple },
    //   { key: this.$constants.SOCIAL_INSURANCE_PAYMENT_CERTIFICATE, title: this.$constants.SOCIAL_INSURANCE_PAYMENT_CERTIFICATE_NAME, fileType: this.fileType, uploadText: this.uploadText, tipText: this.tipText, limit: this.limit, fileSize: this.fileSize, multiple: this.multiple },
    // ]
    this.getDicts('biz_apply_file_type').then(response => {
      const data = response.data
      if (data && data.length > 0) {
        const arr = []
        data.forEach(item => {
          arr.push({
            key: item.dictValue,
            title: item.dictLabel,
            fileType: this.fileType,
            uploadText: this.uploadText,
            tipText: this.tipText,
            limit: this.limit,
            fileSize: this.fileSize,
            multiple: this.multiple
          })
        })
        this.fileUploadList = arr
      }
    })
    this.getDicts('sys_card_type').then(response => {
      this.cardTypeOptions = response.data
    })
    this.getDicts('sys_user_sex').then(response => {
      this.genderOptions = response.data
    })
    this.getDicts('sys_nation').then(response => {
      this.nationOptions = response.data
    })
    this.getDicts('sys_yes_no').then(response => {
      this.yesOrNoOptions = response.data
    })
    this.getDicts('sys_country').then(response => {
      this.countryOptions = response.data
    })
    this.getDicts('sys_politics_status').then(response => {
      this.politicsStatusOptions = response.data
    })
    this.getDicts('sys_health_condition').then(response => {
      this.healthConditionOptions = response.data
    })
    this.getDicts('sys_disability_type').then(response => {
      this.disabilityTypeOptions = response.data
    })
    this.getDicts('sys_registered_residence_category').then(response => {
      this.registeredResidenceCategoryOptions = response.data
    })
    this.getDicts('sys_blood_type').then(response => {
      this.bloodTypeOptions = response.data
    })
    this.getDicts('sys_entry_way').then(response => {
      this.entryWayOptions = response.data
    })
    this.getDicts('sys_attend_way').then(response => {
      this.attendWayOptions = response.data
    })
    this.getDicts('sys_school_way').then(response => {
      this.schoolWayOptions = response.data
    })
    this.getDicts('sys_study_follow_class').then(response => {
      this.studyFollowClassOptions = response.data
    })
    this.getDicts('sys_post').then(response => {
      this.postOptions = response.data
    })
    this.getDicts('sys_relation').then(response => {
      this.relationOptions = response.data
    })
    this.getSourceTypeData(this.routerQuery.schoolId)
    this.getTreeSelect('0', 3).then(data => {
      this.areaTreeOptions = data
    })
    this.getTreeSelect('0', 4).then(data => {
      this.areaFourLevelTreeOptions = data
    })
  },
  mounted() {
    this.ruleForm.schoolName = this.routerQuery.schoolName
    this.operate = this.routerQuery.operate || ''
    this.id = this.routerQuery.id
    if (this.id) {
      this.getApplyData(this.id)
    } else {
      this.setFileClassifyData(this.routerQuery.type)
    }
  },
  methods: {
    getTreeSelect(parentId, level) {
      return new Promise((resolve, reject) => {
        listAreaTree(parentId, level).then(response => {
          const data = response.data;
          if (data && data.length > 0) {
            data.sort((a, b) => {
              if (a.id === this.$constants.DEFAULT_AREA_ROOT_ID) return -1;
              if (b.id === this.$constants.DEFAULT_AREA_ROOT_ID) return 1;
              return 0;
            });
            resolve(data)
          }
        }).catch((err) => {
          reject(err)
        })
      })
    },
    getApplyData(id) {
      getApplyInfo(id).then(async response => {
        this.loading = true
        const data = response.data || {}
        // 优先回显生源类型
        await this.getSourceTypeData(data.schoolId)
        // 回显表单
        this.ruleForm = data

        // 处理路由中的参数
        if (this.routerQuery.schoolId && this.routerQuery.schoolId !== this.ruleForm.schoolId) {
          this.ruleForm.schoolId = this.routerQuery.schoolId
          this.ruleForm.schoolName = this.routerQuery.schoolName
        }
        // 设置路由
        if (!this.routerQuery.schoolId) {
          this.routerQuery.schoolId = data.schoolId
          this.routerQuery.schoolName = data.schoolName
        }
        if (!this.routerQuery.areaId) {
          this.routerQuery.areaId = data.areaId
        }
        if (!this.routerQuery.type) {
          this.routerQuery.type = data.applyType
        }
        if (!this.routerQuery.cityId) {
          this.routerQuery.cityId = data.parentAreaId
        }

        // 回显监护人
        let guardian1 = {}, guardian2 = {}
        if (data.guardianPeopleList && data.guardianPeopleList.length > 0) {
          const length = data.guardianPeopleList.length
          if (length > 0) {
            guardian1 = data.guardianPeopleList[0] || {}
          }
          if (length > 1) {
            guardian2 = data.guardianPeopleList[1] || {}
          }
        }
        // 第一个人
        this.$set(this.ruleForm, 'guardianId1', guardian1.id)
        this.$set(this.ruleForm, 'guardianName1', guardian1.name)
        this.$set(this.ruleForm, 'guardianIdCard1', guardian1.idCard)
        this.$set(this.ruleForm, 'guardianMobile1', guardian1.mobile)
        this.$set(this.ruleForm, 'guardianRelation1', guardian1.relation)
        this.$set(this.ruleForm, 'guardianResidentialAddress1', guardian1.residentialAddress)
        this.$set(this.ruleForm, 'guardianship1', guardian1.guardianship)
        this.$set(this.ruleForm, 'guardianCardType1', guardian1.cardType)
        this.$set(this.ruleForm, 'guardianNation1', guardian1.nation)
        this.$set(this.ruleForm, 'guardianRegisteredResidenceAreaCode1', guardian1.registeredResidenceAreaCode)
        this.$set(this.ruleForm, 'guardianWorkUnit1', guardian1.workUnit)
        this.$set(this.ruleForm, 'guardianWorkPost1', guardian1.workPost)

        // 第二个人
        this.$set(this.ruleForm, 'guardianId2', guardian2.id)
        this.$set(this.ruleForm, 'guardianName2', guardian2.name)
        this.$set(this.ruleForm, 'guardianIdCard2', guardian2.idCard)
        this.$set(this.ruleForm, 'guardianMobile2', guardian2.mobile)
        this.$set(this.ruleForm, 'guardianRelation2', guardian2.relation)
        this.$set(this.ruleForm, 'guardianResidentialAddress2', guardian2.residentialAddress)
        this.$set(this.ruleForm, 'guardianship2', guardian2.guardianship)
        this.$set(this.ruleForm, 'guardianCardType2', guardian2.cardType)
        this.$set(this.ruleForm, 'guardianNation2', guardian2.nation)
        this.$set(this.ruleForm, 'guardianRegisteredResidenceAreaCode2', guardian2.registeredResidenceAreaCode)
        this.$set(this.ruleForm, 'guardianWorkUnit2', guardian2.workUnit)
        this.$set(this.ruleForm, 'guardianWorkPost2', guardian2.workPost)

        // 回显级联选
        if (data.birthplaceAreaCode) {
          this.birthplaceAreaCodes = data.birthplaceAreaCode.split('/')
        }
        if (data.registeredResidenceAreaCode) {
          this.registeredResidenceAreaCodes = data.registeredResidenceAreaCode.split('/')
        }
        if (guardian1.registeredResidenceAreaCode) {
          this.guardianRegisteredResidenceAreaCodes1 = guardian1.registeredResidenceAreaCode.split('/')
        }
        if (guardian2.registeredResidenceAreaCode) {
          this.guardianRegisteredResidenceAreaCodes2 = guardian2.registeredResidenceAreaCode.split('/')
        }
        if (data.graduationSchoolAreaCode) {
          this.graduationSchoolAreaCodes = data.graduationSchoolAreaCode.split('/')
        }
        if (data.domicileAddressAreaCode) {
          this.domicileAddressAreaCodes = data.domicileAddressAreaCode.split('/')
        }

        // 回显照片
        if (data.pictureFileList && data.pictureFileList.length > 0) {
          let pictureFileList = []
          data.pictureFileList.forEach(file => {
            pictureFileList.push(
              {
                id: file.id,
                size: file.attachSize,
                name: file.attachName,
                url: file.fileUrl,
                path: file.fileUrl
              }
            )
          })
          this.pictureFileList = pictureFileList
          this.$refs.pictureFileUpload.showFiles(this.pictureFileList)
        }
        // 回显附件
        if (data && data.applyType === this.$constants.SCHOOL_XX && data.sourceFileType) {
          this.fileClassifyList = data.sourceFileType.split(',')
        } else if (data.applyType === this.$constants.SCHOOL_CZ) {
          this.setFileClassifyData(data.applyType)
        }
        this.$nextTick(() => {
          this.showFileList(data.attachmentList)
          this.loading = false
        })

      })
    },
    showFileList(files) {
      const that = this
      if (files && files.length) {
        if (this.fileClassifyList && this.fileClassifyList.length > 0) {
          this.fileClassifyList.forEach(item => {
            let urlArr = []
            for (let i = 0; i < files.length; i++) {
              const file = files[i]
              if (file.businessType === item) {
                urlArr.push({
                  id: file.id,
                  size: file.attachSize,
                  name: file.attachName,
                  url: file.fileUrl,
                  path: file.fileUrl
                })
              }
            }
            that.fileList[item] = urlArr
            const refKey = this.uploadRefKey + item
            const tp = that.$refs[refKey]
            if (tp && tp.length > 0) {
              tp[0].showFiles(urlArr)
            }
          })
        }
      }
    },
    getSourceTypeData(schoolId) {
      getSourceTypeList({schoolId: schoolId}).then(response => {
        this.sourceTypeOptions = response.data || []
      })
    },
    submit(formName) {
      const that = this
      if (that.loading) { return }
      that.$refs[formName].validate(valid => {
        if (valid) {
          // 设置之前流程选择的数据
          if (that.routerQuery.schoolId) {
            that.ruleForm.schoolId = that.routerQuery.schoolId;
            that.ruleForm.deptId = that.routerQuery.schoolId
          }
          if (that.routerQuery.applyType) { that.ruleForm.applyType = that.routerQuery.type }
          if (that.routerQuery.areaId) { that.ruleForm.areaId = that.routerQuery.areaId }
          // 设置其它数据
          const saveForm = JSON.parse(JSON.stringify(that.ruleForm))
          let picture = ''
          const fileList = []
          if (that.pictureFileList && that.pictureFileList.length > 0) {
            let ids = ''
            that.pictureFileList.forEach(obj => {
              ids +=  (',' + obj.id)
              fileList.push({
                id: obj.id,
                businessType: this.$constants.FILE_TYPE_AVATAR,
                attachName: obj.name,
                fileUrl: obj.path,
                attachSize: obj.size,
                fileId: obj.id
              })
            })
            picture = ids.substring(1)
          }
          saveForm.picture = picture
          // 保存其它附件
          if (that.fileClassifyList && that.fileClassifyList.length > 0) {
            that.fileClassifyList.forEach(item => {
              const files = that.fileList[item]
              if (files && files.length > 0) {
                files.forEach(obj => {
                  fileList.push({
                    id: obj.id,
                    businessType: item,
                    attachName: obj.name,
                    fileUrl: obj.path,
                    attachSize: obj.size,
                    fileId: obj.id
                  })
                })
              }
            })
          }
          saveForm.attachmentList = fileList
          const guardianList = []
          if (saveForm.guardianName1) {
            guardianList.push({
              id: saveForm.guardianId1,
              name: saveForm.guardianName1,
              idCard: saveForm.guardianIdCard1,
              mobile: saveForm.guardianMobile1,
              relation: saveForm.guardianRelation1,
              residentialAddress: saveForm.guardianResidentialAddress1,
              guardianship: saveForm.guardianship1,
              cardType: saveForm.guardianCardType1,
              nation: saveForm.guardianNation1,
              registeredResidenceAreaCode: saveForm.guardianRegisteredResidenceAreaCode1,
              workUnit: saveForm.guardianWorkUnit1,
              workPost: saveForm.guardianWorkPost1,
              sort: 1
            })
          }
          if (saveForm.guardianName2) {
            guardianList.push({
              id: saveForm.guardianId2,
              name: saveForm.guardianName2,
              idCard: saveForm.guardianIdCard2,
              mobile: saveForm.guardianMobile2,
              relation: saveForm.guardianRelation2,
              residentialAddress: saveForm.guardianResidentialAddress2,
              guardianship: saveForm.guardianship2,
              cardType: saveForm.guardianCardType2,
              nation: saveForm.guardianNation2,
              registeredResidenceAreaCode: saveForm.guardianRegisteredResidenceAreaCode2,
              workUnit: saveForm.guardianWorkUnit2,
              workPost: saveForm.guardianWorkPost2,
              sort: 2
            })
          }
          saveForm.guardianPeopleList = guardianList
          that.loading = true
          saveApply(saveForm).then(response => {
            that.$message.success('保存成功')
            setTimeout(() => {
              that.toapplication()
            }, 1000)
          }).catch(() => {
            that.loading = false
          })
        } else {
          this.$message.warning('请填写校验项!')
          return false
        }
      })
    },
    handleChange(fileList) {
      this.pictureFileList = fileList || []
    },
    handleChangeFile(fileList, type) {
      this.fileList[type] = fileList
    },
    handleSelectChange(val) {
      const obj = this.sourceTypeOptions.find(item => item.id === val)
      if (obj) {
        this.fileClassifyList = obj.fileType.split(',')
      }
      this.$nextTick(() => {
        this.reloadFiles()
      })
    },
    reloadFiles() {
      if (this.fileClassifyList && this.fileClassifyList.length > 0) {
        this.fileClassifyList.forEach(item => {
          const files = this.fileList[item] || []
          const refKey = this.uploadRefKey + item
          const tp = this.$refs[refKey]
          if (tp && tp.length > 0) {
            tp[0].showFiles(files)
          }
        })
      }
    },
    toschool(){
      if (this.loading) { return }
      this.$commonUtils.setApplyParams(this.routerQuery)
      this.$router.push({
        path: "/school"
      })
    },
    toapplication(){
      this.$commonUtils.removeApplyParams()
      this.$router.push({path: "/application"})
    },
    setFileClassifyData(type) {
      if (type === this.$constants.SCHOOL_CZ) {
        this.fileClassifyList = [
          this.$constants.HOUSEHOLD_REGISTER,
          this.$constants.HOUSE_PROPRIETARY_CERTIFICATE,
        ]
      }
    },
    handleBirthplaceAreaCodeChange(val) {
      let code = ''
      if (val && val.length > 0) {
        code = val.join('/')
      }
      this.ruleForm.birthplaceAreaCode = code
    },
    handleRegisteredResidenceChange(val) {
      let code = ''
      if (val && val.length > 0) {
        code = val.join('/')
      }
      this.ruleForm.registeredResidenceAreaCode = code
    },
    handleDomicileAddressAreaCodesChange(val) {
      let code = ''
      if (val && val.length > 0) {
        code = val.join('/')
      }
      this.ruleForm.domicileAddressAreaCode = code
    },
    handleGuardianRegisteredResidence1Change(val) {
      let code = ''
      if (val && val.length > 0) {
        code = val.join('/')
      }
      this.ruleForm.guardianRegisteredResidenceAreaCode1 = code
    },
    handleGuardianRegisteredResidence2Change(val) {
      let code = ''
      if (val && val.length > 0) {
        code = val.join('/')
      }
      this.ruleForm.guardianRegisteredResidenceAreaCode2 = code
    },
    handleAreaChange(val) {
      let code = ''
      if (val && val.length > 0) {
        code = val.join('/')
      }
      this.ruleForm.graduationSchoolAreaCode = code
    },
    handleSelected() {
      this.$forceUpdate()
    },
    validIdCard(type, value, callback) {
      if (type === '01' && value) {
        if (!validIdCard(value)) {
          callback(new Error('身份证号不合法'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    handleIdCardChange(val) {
      if (!val) { return }
      const idCard = val.target.value
      if (this.ruleForm.cardType === '01') {
        let year, month, day, sexNumber;
        if (idCard.length === 15) {
          year = idCard.substring(6, 8)
          month = idCard.substring(8, 10)
          day = idCard.substring(10, 12)
          year = (year.startsWith('9') ? '19' : '20') + year
          sexNumber = idCard.substring(14, 15);
        } else if (idCard.length === 18) {
          year = idCard.substring(6, 10)
          month = idCard.substring(10, 12)
          day = idCard.substring(12, 14)
          sexNumber = idCard.substring(16, 17);
        }
        if (validPositiveInteger(year) && validPositiveInteger(month) && validPositiveInteger(day)) {
          this.ruleForm.birthday = year + '-' + month + '-' + day
        }
        if (validPositiveInteger(sexNumber)) {
          const gender = parseInt(sexNumber, 10)
          this.ruleForm.gender = (gender % 2 === 0) ? '2' : '1'
        }
      }
    }

  },
}
</script>
<style lang="scss" scoped>

//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {

  .cardBox{
    width: calc(100% - 80px);
    margin: 0 0 0 40px;
    border: 1px solid #E1E1E1;
    position: relative;
    border-radius: 8px;
    margin-bottom: 30px;
    .cardTitle{
      position: absolute;
      width: auto;
      height: 30px;
      line-height: 30px;
      top: -15px;
      left: 15px;
      padding:0 15px;
      background: #ffffff;
      color: #3570f6;
      font-size: 18px;
    }
    .cardCon{
      width: 100%;
      height: auto;
      padding:30px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 20px;
      .zhanwei{
        width: 100%;
        height: 5px;
        background: #f1f1f1;
      }
      .zhanweiLong{
        width: 100%;
        height: 5px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #f1f1f1;
        margin: 15px 0;
        span{
          background: #ffffff;
          font-size: 18px;
          font-weight: bold;
          padding:0 15px;
          color: #3570f6;
        }
      }
      .el-form-item{
        width: calc(50% - 10px);
        margin: 0;
        &.card_long{
          width: 100%;
        }
      }
      ::v-deep .el-upload-dragger{
        width: 100%;
        height: 100%;
      }
      .card{
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border: 1px solid #E6E6E6;
        border-radius: 6px;
        .required {
          &::after{
            content: "*";
            color: #ff3300;
            margin: 2px 5px;
          }
        }
        .cardName{
          width: auto;
          min-width: 130px;
          height: 50px;
          background: #FAFAFA;
          padding:0 15px;
          display: flex;
          justify-content: center;
          align-items: center;
          white-space: nowrap;
          border-radius: 4px 0 0 4px;
          font-size: 18px;
          color: #383838;
          border-right: 1px solid #e6e6e6;
          &.longName{
            min-width: 200px;
          }
        }
        .cardContent{
          width: 100%;
          height: 50px;
          border-radius: 0 4px 4px 0 ;
          padding: 0 15px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          font-size: 18px;
          color: #383838;
          ::v-deep .el-select{
            .el-input{
              input{
                border: none;
                font-size: 18px;
                padding: 0;
                background: none;
              }
            }
          }
          ::v-deep .el-cascader{
            .el-input{
              input{
                border: none;
                font-size: 18px;
                padding: 0;
                background: none;
              }
            }
          }
          ::v-deep .el-date-editor{
            width: 100%;
            height: 100%;
            input{
              height: 100%;
              border: none;
              background: none;
              font-size: 18px;
              padding-left: 40px;
            }
            .el-input__icon{
              font-size: 18px;
            }
          }
          .cardTxt{
            width: 100%;
            height: 100%;
            border: none;
            background: none;
            font-size: 18px;
            color: #383838;
            outline: none;
          }

        }
        &.card_long{
          width: 100%;
        }
      }
      .upload-demo {
        width: 100%;
        .el-upload__tip {
          width: calc(100% - 250px);
          height: 54px;
          line-height: 54px;
          font-size: 16px;
          color: #383838;
        }
      }
    }
  }


  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;

    .choose_body {
      flex: 1;
      display: flex;
      justify-content: center;
      margin-top: 0px;
      .body_middle {
        width: 1200px;
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 10px 30px 1px rgba(93,132,177,0.11);
        border-radius: 0 0 16px 16px;
        .middle_top {
          width: 100%;
          margin: 40px 0px 10px 0;
        }
        .border_top {
          width: 1155px;
          margin: 30px 22px 0px 22px;
          height: 7px;
          background: #f1f1f1;
          border-radius: 12px 12px 12px 12px;
          opacity: 1;
        }
        .title_box {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin: 25px 0 25px 2.5%;
          .title {
            width: 176px;
            height: 29px;
            font-size: 22px;
            font-weight: bold;
            color: #000000;
          }
          .title_left {
            display: flex;
            .xiala {
              .sousuo1{
                display: none;
              }
              margin-left: 14px;
              color: #3570F6;
              ::v-deep .el-input__inner{
                border: none;
                width: 200px;
                height: 29px;
                line-height: 29px;
                font-size: 22px;
                font-weight: 400;
                color: #3570F6;
              }
              ::v-deep .el-select__caret{
                font-size: 18px;
                color: #3570F6;
                height: 29px;
                line-height: 29px;
              }
            }
          }
          .title_right {
            width: 244px;
            height: 41px;
            line-height: 41px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #f1f1f1;
            border-radius: 21px 21px 21px 21px;
            input {
              width: 170px;
              height: 31px;
              line-height: 31px;
              font-size: 16px;
              font-weight: 400;
              color: #383838;
              border: none;
              outline: none;
              background: none;
            }
            img {
              width: 19px;
              height: 19px;
              margin-left: 10px;
            }
          }
        }
        .foot_Box {
          width: 100%;
          height: 140px;
          display: flex;
          div:nth-child(1) {
            width: 418px;
            height: 59px;
            margin: 28px 53px 18px 173px;
            background: #3570f6;
            border-radius: 10px 10px 10px 10px;
            text-align: center;
            line-height: 59px;
            color: #fff;
          }
          div:nth-child(2) {
            width: 418px;
            height: 59px;
            margin: 28px 53px 0px 0px;
            background: #3570f6;
            border-radius: 10px 10px 10px 10px;
            color: #fff;
            text-align: center;
            line-height: 59px;
          }
        }
      }
    }
  }
  .el-select-dropdown__item{
    height: auto;
    line-height: normal;
    padding:10px 20px;
    .cardConBox{
      width: 100%;
      height: auto;
      display: flex;
      flex-flow: column;
      padding: 5px 0;
      background: #fafafa;
      border: 1px solid #f1f1f1;
      border-radius: 4px;
      padding:8px 12px;
      cursor: pointer;
      span:nth-child(1){
        color: #000000;
        font-weight: bold;
        font-size: 16px;
        cursor: pointer;
      }
      span:nth-child(2){
        color: #666;
        font-weight: normal;
        width: 360px;
        white-space:normal;
        cursor: pointer;
      }
    }
    &:hover{
      background: none;
      .cardConBox{
        background: #3570F605;
        border: 1px solid #3570F6;
        span:nth-child(1){
          color: #3570F6;
        }
      }
    }
  }

  .choose_foot {
    z-index: 2;
    width: 100%;
    height: 80px;
    display: none;
    justify-content: center;
    align-items: center;
  }
}

// 移动端
@media screen and (max-width: 720px) {

  .top_layout,.foot_layout{
    display: none;
  }
  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .choose_body {
      width: 100%;
      margin-top: 5.8rem;
      .body_middle {
        width: calc(100% - 2rem);
        margin-left: 1rem;
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 0.5rem 1rem 1px rgba(93,132,177,0.11);
        border-radius: 1rem;
        .middle_top {
          display: none;
        }
        .border_top {
          display: none;
        }
        .title_box {
          width:100%;
          display: flex;
          flex-direction: column;
          padding:1rem 1.5rem;
          .title {
            width: 100%;
            height: 2rem;
            font-size: 1.6rem;
            font-weight: bold;
            color: #000000;
          }
          .title_left {
            display: flex;
            flex-direction: column;
            .xiala {
              margin: 0.5rem 0;
              width:100%;
              color: #3570F6;
              display: flex;
              ::v-deep .el-input__inner{
                display: flex;
                border: none;
                width:100%;
                height: 2rem;
                font-size: 1.2rem;
                color: #3570F6;
              }
              ::v-deep .el-select__caret{
                font-size: 1.4rem;
                color: #3570F6;
                height: 2.2rem;
              }
            }
          }
          .title_right {
            width: 100%;
            height: auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f1f1f1;
            border-radius: 0.4rem;
            padding:0 1rem;
            margin-top: 0.5rem;
            input {
              width: calc(100% - 2rem);
              height: 3rem;
              font-size: 1.2rem;
              font-weight: 400;
              color: #383838;
              border: none;
              outline: none;
              background: none;
            }
            img {
              width: 1.5rem;
              height: 1.5rem;
            }
          }
        }


        .cardBox{
          width: calc(100% - 2rem);
          margin: 0 0 0 1rem;
          border: 1px solid #E1E1E1;
          position: relative;
          border-radius: 0.6rem;
          margin-bottom: 1.5rem;
          .cardTitle{
            position: absolute;
            width: auto;
            height: 2rem;
            line-height: 2rem;
            top: -1rem;
            left: 1rem;
            padding:0 1rem;
            background: #ffffff;
            color: #3570f6;
            font-size: 1.1rem;
            font-weight: bold;
          }
          .cardCon{
            width: 100%;
            height: auto;
            padding:1rem;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 0.6rem;
            .zhanwei{
              display: none;
            }
            .zhanweiLong{
              width: 100%;
              height: 0.2rem;
              text-align: center;
              display: flex;
              justify-content: center;
              align-items: center;
              background: #f1f1f1;
              margin: 1rem 0;
              span{
                background: #ffffff;
                font-size: 1.1rem;
                font-weight: bold;
                padding:0 1rem;
                color: #3570f6;
              }
            }
            .el-form-item{
              width: 100%;
              margin: 0 0 13px 0;
              &.card_long{
                width: 100%;
              }
            }
            ::v-deep .el-upload-dragger{
              width: 100%;
              height: 100%;
            }
            .card{
              width: 100%;
              display: flex;
              border: 1px solid #E6E6E6;
              border-radius:0.2rem;
              flex-flow: column;
              .required {
                &::before{
                  content: "*";
                  color: #ff3300;
                  font-size: 1rem;
                  margin: 0 0.2rem 0 0;
                }
              }
              .cardName{
                width: 100%;
                height:auto;
                background: #FAFAFA;
                padding:0 0.8rem;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                white-space: nowrap;
                font-size: 1rem;
                color: #999;
                border-bottom: 1px solid #e6e6e6;
                &.longName{
                  min-width: 10rem;
                }
              }
              .cardContent{
                width: 100%;
                height: 3rem;
                padding: 0 0.8rem;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                font-size: 1rem;
                color: #383838;
                ::v-deep .el-select{
                  .el-input{
                    input{
                      border: none;
                      font-size: 1rem;
                      padding: 0;
                      background: none;
                    }
                  }
                }
                ::v-deep .el-cascader{
                  .el-input{
                    input{
                      border: none;
                      font-size: 1rem;
                      padding: 0;
                      background: none;
                    }
                  }
                }
                ::v-deep .el-form-item__content{
                  line-height: 3rem;
                }
                ::v-deep .el-input__prefix{
                  left: 0;
                  height: 100%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  .el-input__icon{
                    line-height: 3rem;
                  }
                }
                ::v-deep .el-date-editor{
                  width: 100%;
                  height: 100%;
                  input{
                    height: 100%;
                    border: none;
                    background: none;
                    font-size: 1rem;
                    padding-left: 2rem;
                  }
                  .el-input__icon{
                    font-size: 1rem;
                  }
                }
                .cardTxt{
                  width: 100%;
                  height: 100%;
                  border: none;
                  background: none;
                  font-size: 1rem;
                  color: #383838;
                  outline: none;
                }
              }
              &.card_long{
                width: 100%;
              }
            }
            .upload-demo {
              width: 100%;
              .el-upload__tip {
                width: 100%;
                height: auto;
                font-size: 1rem;
                color: #999;
              }
            }
          }
        }


        .foot_Box {
          width: calc(100% - 2rem);
          margin-left:1rem ;
          display: flex;
          justify-content: space-evenly;
          flex-flow: column;
          gap: 1rem;
          padding-bottom: 1.5rem;
          div {
            width: 100%;
            height: 3.4rem;
            background: #3570f6;
            border-radius: 0.6rem;
            text-align: center;
            line-height: 3.4rem;
            color: #fff;
          }

        }
      }
    }
    .choose_foot {
       width: 100%;
      height:auto;
      display: flex;
      justify-content: center;
      align-items: center;
      padding:1.5rem 1.2rem;
    }
  }
  .el-select-dropdown__item{
    height: auto;
    line-height: normal;
    padding:0.6rem 1.2rem;
    .cardConBox{
      width: 80vw;
      height: auto;
      box-sizing: border-box;
      display: flex;
      flex-flow: column;
      padding: 0.3rem 0;
      background: #fafafa;
      border: 1px solid #f1f1f1;
      border-radius: 0.6rem;
      padding:0.6rem 1rem;
      cursor: pointer;
      span:nth-child(1){
        color: #000000;
        font-weight: bold;
        font-size: 1.2rem;
        cursor: pointer;
        white-space: normal;
        word-break: break-all;
      }
      span:nth-child(2){
        color: #666;
        font-weight: normal;
        width: 100%;
        white-space:normal;
        cursor: pointer;
        font-size: 1rem;
      }
    }
    &:hover{
      background: none;
      .cardConBox{
        background: #3570F605;
        border: 1px solid #3570F6;
        span:nth-child(1){
          color: #3570F6;
        }
      }
    }
  }
}
::v-deep .el-select{
  width: 100%!important;
}
::v-deep .el-radio__label{
  font-size: 1rem;
}
.el-date-editor{
  display: flex;
  justify-content: space-between;
  flex-flow: row;
}
::v-deep .is-finish{
  color: #383838;
  font-size: 18px;
  .el-step__line{
    background: #3570F6;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #3570F6;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    border-color: #3570F6;
  }
}

::v-deep .is-process{
  color: #383838;
  font-size: 18px;
  font-weight: normal;
  .el-step__line{
    background: #f1f1f1;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #ffffff;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    border-width: 5px;
    color: #3570F6;
    border-color: #3570F6;
  }
}

::v-deep .is-wait{
  color: #383838;
  font-size: 18px;
  font-weight: normal;
  .el-step__line{
    background: #f1f1f1;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #EEF6FF;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    border-width: 5px;
    color: #3570F6;
    border-color: #EEF6FF;
  }
}
::v-deep .el-cascader {
  width: 100% !important;
}
</style>
