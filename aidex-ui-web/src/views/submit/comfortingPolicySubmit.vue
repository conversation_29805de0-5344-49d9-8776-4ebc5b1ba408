<template>
  <div class="choose">
    <Topbox class="top_layout" /><!--顶部-->
    <top/>

    <div class="choose_body">
      <div class="body_middle">
        <div class="title_box">
          <div class="title_left">
            <div class="title">填写报名资料</div>
          </div>
        </div>

        <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
          <div class="cardBox">
            <div class="cardTitle">学校信息</div>
            <div class="cardCon">
              <el-form-item prop="schoolName">
                <div class="card">
                  <div class="cardName">报名学校</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model="ruleForm.schoolName" readonly />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="sourceTypeId">
                <div class="card">
                  <div class="cardName required">生源类型</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.sourceTypeId" placeholder="请选择" style="width: 100%;">
                      <el-option v-for="(item, index) in sourceTypeOptions" :key="index" :label="item.name" :value="item.id">
                        <div class="cardConBox">
                          <span>{{ item.name }}</span>
                          <span>{{ item.description }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="cardBox">
            <div class="cardTitle">儿童信息</div>
            <div class="cardCon">
              <el-form-item prop="name">
                <div class="card">
                  <div class="cardName required">儿童姓名</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.name" maxlength="10" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="idCard">
                <div class="card">
                  <div class="cardName required">身份证号</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.idCard" maxlength="20" @blur="handleIdCardChange" placeholder="请输入"  />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="gender">
                <div class="card">
                  <div class="cardName required">性别</div>
                  <div class="cardContent">
                    <el-radio v-model="ruleForm.gender"
                              v-for="(item, index) in genderOptions" :key="index"
                              :label="item.dictValue">{{item.dictLabel}}</el-radio>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="birthday">
                <div class="card">
                  <div class="cardName required">出生日期</div>
                  <div class="cardContent">
                    <el-date-picker
                        type="date"
                        v-model="ruleForm.birthday"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择"
                        style="width: 100%; border: none"
                    ></el-date-picker>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="nation">
                <div class="card">
                  <div class="cardName required">民族</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.nation" placeholder="请选择">
                      <el-option v-for="(item, index) in nationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="residentialAddress" class="card_long">
                <div class="card">
                  <div class="cardName required">现居住地址</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.residentialAddress" maxlength="80" placeholder="请输入"  />
                  </div>
                </div>
              </el-form-item>
              <el-form-item  prop="preferentialTreatment" class="card_long">
                <div class="card">
                  <div class="cardName required">优抚政策情况</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.preferentialTreatment" placeholder="请选择">
                      <el-option v-for="(item, index) in policyBasedTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <div class="card_long">
                <div class="card1">
                  <div class="cardName">优抚政策证明</div>
                  <div class="cardContent">
                    <image-upload :limit="limit" :file-size="fileSize" :file-unit="fileUnit" :drag="true"
                                  :fileType="fileType"
                                  :upload-text="uploadText"
                                  :tip-text="handleUploadTipText(limit)"
                                  :ref="uploadRefKey + $constants.PREFERENTIAL_TREATMENT_CERTIFICATE"
                                  @ok="handleChangeFile($event, $constants.PREFERENTIAL_TREATMENT_CERTIFICATE)"
                    ></image-upload>
                  </div>
                </div>
              </div>
          </div>
          <div class="cardBox">
            <div class="cardTitle">监护人信息</div>
            <div class="cardCon">
              <el-form-item prop="guardianName1">
                <div class="card">
                  <div class="cardName longName required">监护人姓名</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianName1" maxlength="12" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianIdCard1">
                <div class="card">
                  <div class="cardName longName required">监护人身份证号</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianIdCard1" maxlength="20" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianRelation1">
                <div class="card">
                  <div class="cardName longName required">监护人与儿童关系</div>
                  <div class="cardContent">
                    <el-select v-model="ruleForm.guardianRelation1" placeholder="请选择">
                      <el-option v-for="(item, index) in relationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item prop="guardianMobile1">
                <div class="card">
                  <div class="cardName longName required">监护人手机号</div>
                  <div class="cardContent">
                    <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianMobile1" maxlength="11" placeholder="请输入" />
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
            <div class="cardBox">
              <div class="cardTitle">紧急联系人信息</div>
              <div class="cardCon">
                <el-form-item prop="guardianName2">
                  <div class="card">
                    <div class="cardName longName required">紧急联系人姓名</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianName2" maxlength="12" placeholder="请输入" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="guardianMobile2">
                  <div class="card">
                    <div class="cardName longName required">紧急联系人手机号</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianMobile2" maxlength="11" placeholder="请输入" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="guardianRelation2">
                  <div class="card">
                    <div class="cardName longName">紧急联系人与儿童关系</div>
                    <div class="cardContent">
                      <el-select v-model="ruleForm.guardianRelation2" placeholder="请选择">
                        <el-option v-for="(item, index) in relationOptions" :key="index" :label="item.dictLabel" :value="item.dictValue"></el-option>
                      </el-select>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item prop="guardianIdCard2">
                  <div class="card">
                    <div class="cardName longName">紧急联系人身份证号</div>
                    <div class="cardContent">
                      <input class="cardTxt" type="text" v-model.trim="ruleForm.guardianIdCard2" maxlength="20" placeholder="请输入"  />
                    </div>
                  </div>
                </el-form-item>
              </div>
            </div>
          </div>
          <div class="cardBox">
            <div class="promise-box">
              <el-checkbox class="my-checkbox" v-model="promiseCheckBox">
                <span class="span-content">本人承诺所提交信息真实有效。</span>
              </el-checkbox>
            </div>
          </div>
          <div class="border_top"></div>
          <!-- 底部 -->
          <div class="foot_Box">
            <div @click="toschool()"><i class="el-icon-loading" v-if="loading"></i>上一步：选择报名学校</div>
            <div @click="submit('ruleForm', 'apply')" :class="{'disabled_click': (!promiseCheckBox && !loading)}"><i class="el-icon-loading" v-if="loading"></i>保存并提交</div>
            <div @click="submit('ruleForm')" :class="{'disabled_click': (!promiseCheckBox && !loading)}"><i class="el-icon-loading" v-if="loading"></i>暂存</div>
          </div>
        </el-form>
      </div>
    </div>
    <Footbox class="foot_layout" />
    <foot />
  </div>
</template>

<script>
  import top from '../../components/top/top.vue'
  import foot from '../../components/foot/foot.vue'
  import {getApplyInfo, getSourceTypeList, savePolicyApply, submitPolicyApply} from '@/api/apply'
  import {validIdCard, validMobile, validNumber, validPositiveInteger} from '@/utils/validate'
  import ImageUpload from '@/components/ImageUpload/index'
  import Topbox from "@/views/layout/newtop";
  import Footbox from "@/views/layout/foot";
  import moment from 'moment';

  export default {
  components: {
    top,foot,ImageUpload,
    Topbox,
    Footbox
  },
  data() {
    const validateIdCard = (rule, value, callback) => {
      if (value && !validIdCard(value)) {
        callback(new Error('身份证号不合法'))
      } else {
        callback()
      }
    }
    const validateMobile = (rule, value, callback) => {
      if (value && !validMobile(value)) {
        callback(new Error('请正确填写联系电话'))
      } else {
        callback()
      }
    }
    return {
      loading: false,
      sourceTypeOptions: [],
      genderOptions: [],
      nationOptions: [],
      relationOptions: [],
      policyBasedTypeOptions: [],
      routerQuery: {},
      fileList: {},
      fileUploadList: [],
      fileType: ['jpg', 'png'],
      uploadText: '选择要上传的图片',
      tipText: '只能上传jpg/png文件，且不超过500kb,最多上传9张,上传后点击可查看大图。',
      limit: 9,
      oneLimit: 1,
      fileSize: 5,
      fileUnit: 'Mb',
      multiple: false,
      uploadRefKey: 'fileUpload_',
      operate: '',
      id: '',
      promiseCheckBox: false,
      yesValue: 'Y',
      noValue: 'N',
      ruleForm: {
        id: '',
        schoolId: '',
        sourceTypeId: '',
        name: '',
        idCard: '',
        birthday: '',
        gender: '',
        nation: '',
        residentialAddress: '',
        preferentialTreatment: '',
        attachmentsYfzczm: '',
        guardianId1: '',
        guardianName1: '',
        guardianIdCard1: '',
        guardianMobile1: '',
        guardianRelation1: '',
        guardianId2: '',
        guardianName2: '',
        guardianIdCard2: '',
        guardianMobile2: '',
        guardianRelation2: '',
        applyType: '',
        educationNature: '',
        personnelType: '',
        deptId: '',
        areaId: '',
        parentAreaId: '',
        overageChild: '',
      },
      rules: {
        sourceTypeId: [{ required: true, message: '不能为空', trigger: 'change' }],
        name: [{ required: true, message: '不能为空', trigger: 'blur' }],
        idCard: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { validator: validateIdCard, trigger: 'blur' }
        ],
        birthday: [{ required: true, message: '不能为空', trigger: 'change' }],
        gender: [{ required: true, message: '不能为空', trigger: 'change' }],
        nation: [{ required: true, message: '不能为空', trigger: 'change' }],
        guardianName1: [{ required: true, message: '不能为空', trigger: 'blur' }],
        guardianRelation1: [{ required: true, message: '不能为空', trigger: 'blur' }],
        guardianIdCard1: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { validator: validateIdCard, trigger: 'blur' },
        ],
        guardianMobile1: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' },
        ],
        guardianNation1: [{ required: false, message: '不能为空', trigger: 'blur' }],
        guardianName2: [{ required: true, message: '不能为空', trigger: 'blur' }],
        guardianRelation2: [{ required: false, message: '不能为空', trigger: 'blur' }],
        guardianIdCard2: [
          { required: false, message: '不能为空', trigger: 'blur' },
          { validator: validateIdCard, trigger: 'blur' },
        ],
        guardianMobile2: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' },
        ],
        residentialAddress: [{ required: true, message: '不能为空', trigger: 'blur' }],
        preferentialTreatment: [{ required: true, message: '不能为空', trigger: 'change' }],
      },
    }
  },
  beforeDestroy() {},
  created() {
    this.routerQuery = this.$commonUtils.getApplyParams(this.$constants.STORE_CACHE_KEY_APPLY) || {}
    this.getDicts('sys_user_sex').then(response => {
      this.genderOptions = response.data
    })
    this.getDicts('sys_nation').then(response => {
      this.nationOptions = response.data
    })
    this.getDicts('sys_relation').then(response => {
      this.relationOptions = response.data
    })
    this.getDicts('biz_policy_based_type').then(response => {
      this.policyBasedTypeOptions = response.data
    })
  },
  mounted() {
    this.ruleForm.schoolName = this.routerQuery.schoolName
    this.operate = this.routerQuery.operate || ''
    this.id = this.routerQuery.id
    if (this.id) {
      this.getApplyData(this.id)
    } else {
      this.ruleForm.personnelType = this.routerQuery.personnelType
      this.getSourceTypeData(this.routerQuery.schoolId)
    }
  },
  computed: {
    handleExceedAge() {
      if (this.ruleForm && this.ruleForm.birthday) {
        return this.validateExceedAge(this.ruleForm.birthday)
      }
      return false;
    },
    handleFilingCardTime() {
      const currentYear = new Date().getFullYear();
      return ((currentYear - 1) + '年');
    }
  },
  methods: {
    handleUploadTipText(limit) {
      return `只能上传${this.fileType.join('/')}文件，且不超过${this.fileSize}${this.fileUnit}，最多上传${limit}张，上传后点击可查看大图。`
    },
    getSourceTypeData(schoolId) {
      getSourceTypeList({schoolId: schoolId}).then(response => {
        this.sourceTypeOptions = response.data || []
      })
    },
    getApplyData(id) {
      getApplyInfo(id).then(async response => {
        this.loading = true
        const data = response.data || {}
        // 优先回显生源类型
        await this.getSourceTypeData(data.schoolId)

        // 回显表单
        this.ruleForm = data

        // 处理路由中的参数
        if (this.routerQuery.schoolId && this.routerQuery.schoolId !== this.ruleForm.schoolId) {
          this.ruleForm.schoolId = this.routerQuery.schoolId
          this.ruleForm.schoolName = this.routerQuery.schoolName
        }
        // 设置路由
        if (!this.routerQuery.schoolId) {
          this.routerQuery.schoolId = data.schoolId
          this.routerQuery.schoolName = data.schoolName
        }
        if (!this.routerQuery.areaId) {
          this.routerQuery.areaId = data.areaId
          this.routerQuery.areaName = data.areaName
        }
        if (!this.routerQuery.type) {
          this.routerQuery.type = data.applyType
        }
        if (!this.routerQuery.cityId) {
          this.routerQuery.cityId = data.parentAreaId
          this.routerQuery.cityName = data.parentAreaName
        }
        if (!this.routerQuery.nature) {
          this.routerQuery.nature = data.educationNature
          this.routerQuery.natureName = data.educationNatureName
        }
        if (!this.routerQuery.personnelType) {
          this.routerQuery.personnelType = data.personnelType
        }

        // 回显监护人
        let guardian1 = {}, guardian2 = {}
        if (data.guardianPeopleList && data.guardianPeopleList.length > 0) {
          data.guardianPeopleList.forEach(item => {
            if (item.guardianship === this.yesValue) {
              guardian1 = item
            } else {
              guardian2 = item
            }
          })
        }
        // 第一个人
        this.$set(this.ruleForm, 'guardianId1', guardian1.id)
        this.$set(this.ruleForm, 'guardianName1', guardian1.name)
        this.$set(this.ruleForm, 'guardianIdCard1', guardian1.idCard)
        this.$set(this.ruleForm, 'guardianMobile1', guardian1.mobile)
        this.$set(this.ruleForm, 'guardianRelation1', guardian1.relation)

        // 第二个人
        this.$set(this.ruleForm, 'guardianId2', guardian2.id)
        this.$set(this.ruleForm, 'guardianName2', guardian2.name)
        this.$set(this.ruleForm, 'guardianIdCard2', guardian2.idCard)
        this.$set(this.ruleForm, 'guardianMobile2', guardian2.mobile)
        this.$set(this.ruleForm, 'guardianRelation2', guardian2.relation)

        // 回显附件
        const fileList = {}
        if (data && data.attachmentsYfzczm) {
          const attachments = data.attachmentsYfzczm
          const key = this.$constants.PREFERENTIAL_TREATMENT_CERTIFICATE
          fileList[key] = this.handleShowFileData(attachments)
        }
        this.fileList = fileList
        this.$nextTick(() => {
          this.showFileList(fileList)
          this.loading = false
        })
      })
    },
    handleShowFileData(attachments) {
      const fileArr = []
      if (attachments) {
        const arr = attachments.split(',')
        for (let i = 0; i < arr.length; i++) {
          const file = arr[i]
          const fileParams = file.split('?')
          if (fileParams.length > 1) {
            const queryParams = fileParams[1].split('&')
            const params = {}
            queryParams.forEach(param => {
              const [key, value] = param.split('=')
              params[key] = value
            })
            const fileData = {
              id: params.fileId,
              name: params.filename,
              url: file,
              path: file
            }
            fileArr.push(fileData)
          }
        }
      }
      return fileArr
    },
    showFileList(files) {
      if (files) {
        Object.keys(files).forEach(item => {
          const refKey = this.uploadRefKey + item
          const tp = this.$refs[refKey]
          if (tp !== null && tp !== undefined && tp !== '') {
            tp.showFiles(files[item])
          }
        })
      }
    },
    submit(formName, operate) {
      if (this.loading) { return }
      if (this.validateApplyAge(this.ruleForm.birthday)) {
        this.$message.warning('年龄不满六周岁，不能报名！', 3)
        return
      }
      const that = this
      that.$refs[formName].validate(valid => {
        if (valid) {
          if (!this.handleBeforeValidate()) {
            return;
          }

          // 设置之前流程选择的数据
          if (that.routerQuery.schoolId) {
            that.ruleForm.schoolId = that.routerQuery.schoolId;
            that.ruleForm.deptId = that.routerQuery.schoolId
          }
          if (that.routerQuery.type) { that.ruleForm.applyType = that.routerQuery.type }
          if (that.routerQuery.areaId) { that.ruleForm.areaId = that.routerQuery.areaId }
          if (that.routerQuery.nature) { that.ruleForm.educationNature = that.routerQuery.nature }
          if (that.routerQuery.personnelType) { that.ruleForm.personnelType = that.routerQuery.personnelType }
          // 设置其它数据
          const saveForm = JSON.parse(JSON.stringify(that.ruleForm))

          // 超龄儿童
          if (that.validateExceedAge(saveForm.birthday)) {
            saveForm.overageChild = that.yesValue
          } else {
            saveForm.overageChild = that.noValue
          }

          // 优抚政策证明材料
          const fileList = that.fileList
          let attachmentsYfzczm = null
          if (fileList) {
            const fileKey = that.$constants.PREFERENTIAL_TREATMENT_CERTIFICATE
            const files = fileList[fileKey]
            if (files && files.length > 0) {
              const arr = []
              files.forEach(obj => {
                const fileUrl = that.$fileDownloadUrl(obj.id, obj.name)
                arr.push(fileUrl)
              })
              attachmentsYfzczm = arr.join(",")
            }
          }
          saveForm.attachmentsYfzczm = attachmentsYfzczm

          // 监护人
          const guardianList = []
          if (saveForm.guardianName1) {
            guardianList.push({
              id: saveForm.guardianId1,
              name: saveForm.guardianName1,
              idCard: saveForm.guardianIdCard1,
              mobile: saveForm.guardianMobile1,
              relation: saveForm.guardianRelation1,
              guardianship: 'Y',
              sort: 1
            })
          }
          if (saveForm.guardianName2) {
            guardianList.push({
              id: saveForm.guardianId2,
              name: saveForm.guardianName2,
              idCard: saveForm.guardianIdCard2,
              mobile: saveForm.guardianMobile2,
              relation: saveForm.guardianRelation2,
              guardianship: 'N',
              sort: 2
            })
          }
          saveForm.guardianPeopleList = guardianList

          if (operate === 'apply') {
            that.handleSubmitApply(saveForm)
          } else {
            that.handleSaveApply(saveForm)
          }
        } else {
          this.$message.warning('请填写校验项!')
          return false
        }
      })
    },
    handleSaveApply(saveForm) {
      const that = this
      that.loading = true
      const loadingMessage = that.$message({
        message: '正在保存中, 请勿进行其它操作！',
        type: 'warning',
        duration: 0,
      });
      savePolicyApply(saveForm).then(response => {
        loadingMessage.close()
        if (response.code === 200) {
          that.$message.success('保存成功')
          that.handleSuccess()
        } else {
          that.$message.success('保存失败')
          that.loading = false
        }
      }).catch(() => {
        that.loading = false
        loadingMessage.close()
      })
    },
    handleSubmitApply(saveForm) {
      const that = this
      that.loading = true
      const loadingMessage = that.$message({
        message: '正在提交中, 请勿进行其它操作！',
        type: 'warning',
        duration: 0,
      });
      submitPolicyApply(saveForm).then(response => {
        loadingMessage.close()
        if (response.code === 200) {
          that.$message.success("提交成功")
          that.handleSuccess()
        } else {
          that.$message.success('提交失败')
          that.loading = false
        }
      }).catch(() => {
        that.loading = false
        loadingMessage.close()
      })
    },
    handleSuccess() {
      setTimeout(() => {
        this.toapplication()
      }, 1000)
    },
    handleBeforeValidate() {
      return true;
    },
    handleChangeFile(fileList, type) {
      this.fileList[type] = fileList
    },
    toschool(){
      if (this.loading) { return }
      this.$commonUtils.setApplyParams(this.routerQuery, this.$constants.STORE_CACHE_KEY_APPLY)
      this.$router.push({
        path: "/school"
      })
    },
    toapplication(){
      const data = {
        cityId: this.routerQuery.cityId,
        cityName: this.routerQuery.cityName,
        areaId: this.routerQuery.areaId,
        areaName: this.routerQuery.areaName
      }
      this.$commonUtils.setApplyParams(data, this.$constants.STORE_CACHE_KEY_APPLY)
      this.$router.push({path: "/application"})
    },
    handleIdCardChange(val) {
      if (!val) { return }
      const idCard = val.target.value
      let year, month, day, sexNumber;
      if (idCard.length === 15) {
        year = idCard.substring(6, 8)
        month = idCard.substring(8, 10)
        day = idCard.substring(10, 12)
        year = (year.startsWith('9') ? '19' : '20') + year
        sexNumber = idCard.substring(14, 15);
      } else if (idCard.length === 18) {
        year = idCard.substring(6, 10)
        month = idCard.substring(10, 12)
        day = idCard.substring(12, 14)
        sexNumber = idCard.substring(16, 17);
      }
      if (validPositiveInteger(year) && validPositiveInteger(month) && validPositiveInteger(day)) {
        this.ruleForm.birthday = year + '-' + month + '-' + day
      }
      if (validPositiveInteger(sexNumber)) {
        const gender = parseInt(sexNumber, 10)
        this.ruleForm.gender = (gender % 2 === 0) ? '2' : '1'
      }
    },
    validateExceedAge(birthday) {
      if (birthday) {
        const sevenYearsAgo = moment().subtract(7, 'years').month(7).date(31);
        let birthDate = moment(birthday);
        return birthDate.isBefore(sevenYearsAgo);
      }
      return false;
    },
    validateApplyAge(birthday) {
      if (birthday) {
        const sevenYearsAgo = moment().subtract(6, 'years').month(7).date(31);
        let birthDate = moment(birthday);
        return birthDate.isAfter(sevenYearsAgo);
      }
      return false;
    }
  }
}
</script>
<style lang="scss" scoped>

//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {

  .cardBox{
    width: calc(100% - 80px);
    margin: 0 0 0 40px;
    border: 1px solid #E1E1E1;
    position: relative;
    border-radius: 8px;
    margin-bottom: 30px;
    .cardTitle{
      position: absolute;
      width: auto;
      height: 30px;
      line-height: 30px;
      top: -15px;
      left: 15px;
      padding:0 15px;
      background: #ffffff;
      color: #3570f6;
      font-size: 18px;
    }
    .cardCon{
      width: 100%;
      height: auto;
      padding:30px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 20px;
      .zhanwei{
        width: 100%;
        height: 5px;
        background: #f1f1f1;
      }
      .zhanweiLong{
        width: 100%;
        height: 5px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #f1f1f1;
        margin: 15px 0;
        span{
          background: #ffffff;
          font-size: 18px;
          font-weight: bold;
          padding:0 15px;
          color: #3570f6;
        }
      }
      .el-form-item{
        width: calc(50% - 10px);
        margin: 0;
        &.card_long{
          width: 100%;
        }
      }
      ::v-deep .el-upload-dragger{
        width: 100%;
        height: 100%;
      }
      .card{
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border: 1px solid #E6E6E6;
        border-radius: 6px;
        .required {
          &::after{
            content: "*";
            color: #ff3300;
            margin: 2px 5px;
          }
        }
        .cardName{
          width: auto;
          min-width: 130px;
          height: 50px;
          background: #FAFAFA;
          padding:0 15px;
          display: flex;
          justify-content: center;
          align-items: center;
          white-space: nowrap;
          border-radius: 4px 0 0 4px;
          font-size: 18px;
          color: #383838;
          border-right: 1px solid #e6e6e6;
          &.longName{
            min-width: 200px;
          }
          &.longName1{
            min-width: 250px;
          }
        }
        .cardContent{
          width: 100%;
          height: 50px;
          border-radius: 0 4px 4px 0 ;
          padding: 0 15px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          font-size: 18px;
          color: #383838;
          input::placeholder {
            color: #C0C4CC;
          }
          ::v-deep .el-select{
            .el-input{
              input{
                border: none;
                font-size: 18px;
                padding: 0;
                background: none;
              }
            }
          }
          ::v-deep .el-cascader{
            .el-input{
              input{
                border: none;
                font-size: 18px;
                padding: 0;
                background: none;
              }
            }
          }
          ::v-deep .el-date-editor{
            width: 100%;
            height: 100%;
            input{
              height: 100%;
              border: none;
              background: none;
              font-size: 18px;
              padding-left: 40px;
            }
            .el-input__icon{
              font-size: 18px;
            }
          }
          .cardTxt{
            width: 100%;
            height: 100%;
            border: none;
            background: none;
            font-size: 18px;
            color: #383838;
            outline: none;
          }
          .cont_box_left {
            width: 42%;
            border-right: 3px solid #f1f1f1;
          }
          .cont_box_right {
            margin-left: 20px;
            width: 57%;
          }

        }
        &.card_long{
          width: 100%;
        }
      }
      .card1{
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        border-radius: 6px;
        .required {
          &::after{
            content: "*";
            color: #ff3300;
            margin: 2px 5px;
          }
        }
        .cardName{
          width: auto;
          min-width: 130px;
          height: 50px;
          background: #FAFAFA;
          padding:0 15px;
          display: flex;
          justify-content: center;
          align-items: center;
          white-space: nowrap;
          border-radius: 4px 0 0 4px;
          font-size: 18px;
          color: #383838;
          border: 1px solid #E6E6E6;
          &.longName{
            min-width: 200px;
          }
          &.longName1{
            min-width: 250px;
          }
        }
        .cardContent{
          width: 100%;
          height: auto;
          border-radius: 0 4px 4px 0 ;
          padding: 0 15px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          font-size: 18px;
          color: #383838;
          input::placeholder {
            color: #C0C4CC;
          }
          ::v-deep .el-select{
            .el-input{
              input{
                border: none;
                font-size: 18px;
                padding: 0;
                background: none;
              }
            }
          }
          ::v-deep .el-cascader{
            .el-input{
              input{
                border: none;
                font-size: 18px;
                padding: 0;
                background: none;
              }
            }
          }
          ::v-deep .el-date-editor{
            width: 100%;
            height: 100%;
            input{
              height: 100%;
              border: none;
              background: none;
              font-size: 18px;
              padding-left: 40px;
            }
            .el-input__icon{
              font-size: 18px;
            }
          }
          .cardTxt{
            width: 100%;
            height: 100%;
            border: none;
            background: none;
            font-size: 18px;
            color: #383838;
            outline: none;
          }

        }
        &.card_long{
          width: 100%;
        }
      }
      .upload-demo {
        width: 100%;
        .el-upload__tip {
          width: calc(100% - 250px);
          height: 54px;
          line-height: 54px;
          font-size: 16px;
          color: #383838;
        }
      }
    }
    .promise-box {
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      .my-checkbox {
        transform: scale(1.3);
        .span-content {
          color: #ff3e41;
        }
      }
    }
  }


  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;

    .choose_body {
      flex: 1;
      display: flex;
      justify-content: center;
      margin-top: 0px;
      .body_middle {
        width: 1200px;
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 10px 30px 1px rgba(93,132,177,0.11);
        border-radius: 0 0 16px 16px;
        .middle_top {
          width: 100%;
          margin: 40px 0px 10px 0;
        }
        .border_top {
          width: 1155px;
          margin: 30px 22px 0px 22px;
          height: 7px;
          background: #f1f1f1;
          border-radius: 12px 12px 12px 12px;
          opacity: 1;
        }
        .title_box {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin: 25px 0 25px 2.5%;
          .title {
            width: 176px;
            height: 29px;
            font-size: 22px;
            font-weight: bold;
            color: #000000;
          }
          .title_left {
            display: flex;
            .xiala {
              .sousuo1{
                display: none;
              }
              margin-left: 14px;
              color: #3570F6;
              ::v-deep .el-input__inner{
                border: none;
                width: 200px;
                height: 29px;
                line-height: 29px;
                font-size: 22px;
                font-weight: 400;
                color: #3570F6;
              }
              ::v-deep .el-select__caret{
                font-size: 18px;
                color: #3570F6;
                height: 29px;
                line-height: 29px;
              }
            }
          }
          .title_right {
            width: 244px;
            height: 41px;
            line-height: 41px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #f1f1f1;
            border-radius: 21px 21px 21px 21px;
            input {
              width: 170px;
              height: 31px;
              line-height: 31px;
              font-size: 16px;
              font-weight: 400;
              color: #383838;
              border: none;
              outline: none;
              background: none;
            }
            img {
              width: 19px;
              height: 19px;
              margin-left: 10px;
            }
          }
        }
        .foot_Box {
          width: 100%;
          height: 140px;
          display: flex;
          justify-content: center;
          text-align: center;
          div:nth-child(1) {
            width: 300px;
            height: 59px;
            margin: 28px 53px 0 53px;
            background: #3570f6;
            border-radius: 10px 10px 10px 10px;
            text-align: center;
            line-height: 59px;
            color: #fff;
          }
          div:nth-child(2) {
            width: 300px;
            height: 59px;
            margin: 28px 53px 0 53px;
            background: #3570f6;
            border-radius: 10px 10px 10px 10px;
            color: #fff;
            text-align: center;
            line-height: 59px;
          }
          div:nth-child(3) {
            width: 300px;
            height: 59px;
            margin: 28px 53px 0 53px;
            background: #3570f6;
            border-radius: 10px 10px 10px 10px;
            color: #fff;
            text-align: center;
            line-height: 59px;
          }
          .disabled_click {
            pointer-events: none !important;
            cursor: not-allowed !important;
            opacity: 0.5 !important;
            background: #cccccc !important;
          }
        }
      }
    }
  }
  .el-select-dropdown__item{
    height: auto;
    line-height: normal;
    padding:10px 20px;
    .cardConBox{
      width: 100%;
      height: auto;
      display: flex;
      flex-flow: column;
      padding: 5px 0;
      background: #fafafa;
      border: 1px solid #f1f1f1;
      border-radius: 4px;
      padding:8px 12px;
      cursor: pointer;
      span:nth-child(1){
        color: #000000;
        font-weight: bold;
        font-size: 16px;
        cursor: pointer;
      }
      span:nth-child(2){
        color: #666;
        font-weight: normal;
        width: 360px;
        white-space:normal;
        cursor: pointer;
      }
    }
    &:hover{
      background: none;
      .cardConBox{
        background: #3570F605;
        border: 1px solid #3570F6;
        span:nth-child(1){
          color: #3570F6;
        }
      }
    }
  }

  .choose_foot {
    z-index: 2;
    width: 100%;
    height: 80px;
    display: none;
    justify-content: center;
    align-items: center;
  }

  ::v-deep .custom-alert .el-alert__content .el-alert__title {
    font-size: 18px !important;
  }
}

// 移动端
@media screen and (max-width: 720px) {

  .top_layout,.foot_layout{
    display: none;
  }
  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .choose_body {
      width: 100%;
      margin-top: 5.8rem;
      .body_middle {
        width: calc(100% - 2rem);
        margin-left: 1rem;
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 0.5rem 1rem 1px rgba(93,132,177,0.11);
        border-radius: 1rem;
        .middle_top {
          display: none;
        }
        .border_top {
          display: none;
        }
        .title_box {
          width:100%;
          display: flex;
          flex-direction: column;
          padding:1rem 1.5rem;
          .title {
            width: 100%;
            height: 2rem;
            font-size: 1.6rem;
            font-weight: bold;
            color: #000000;
          }
          .title_left {
            display: flex;
            flex-direction: column;
            .xiala {
              margin: 0.5rem 0;
              width:100%;
              color: #3570F6;
              display: flex;
              ::v-deep .el-input__inner{
                display: flex;
                border: none;
                width:100%;
                height: 2rem;
                font-size: 1.2rem;
                color: #3570F6;
              }
              ::v-deep .el-select__caret{
                font-size: 1.4rem;
                color: #3570F6;
                height: 2.2rem;
              }
            }
          }
          .title_right {
            width: 100%;
            height: auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f1f1f1;
            border-radius: 0.4rem;
            padding:0 1rem;
            margin-top: 0.5rem;
            input {
              width: calc(100% - 2rem);
              height: 3rem;
              font-size: 1.2rem;
              font-weight: 400;
              color: #383838;
              border: none;
              outline: none;
              background: none;
            }
            img {
              width: 1.5rem;
              height: 1.5rem;
            }
          }
        }


        .cardBox{
          width: calc(100% - 2rem);
          margin: 0 0 0 1rem;
          border: 1px solid #E1E1E1;
          position: relative;
          border-radius: 0.6rem;
          margin-bottom: 1.5rem;
          .cardTitle{
            position: absolute;
            width: auto;
            height: 2rem;
            line-height: 2rem;
            top: -1rem;
            left: 1rem;
            padding:0 1rem;
            background: #ffffff;
            color: #3570f6;
            font-size: 1.1rem;
            font-weight: bold;
          }
          .cardCon{
            width: 100%;
            height: auto;
            padding:1rem;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 0.6rem;
            .zhanwei{
              display: none;
            }
            .zhanweiLong{
              width: 100%;
              height: 0.2rem;
              text-align: center;
              display: flex;
              justify-content: center;
              align-items: center;
              background: #f1f1f1;
              margin: 1rem 0;
              span{
                background: #ffffff;
                font-size: 1.1rem;
                font-weight: bold;
                padding:0 1rem;
                color: #3570f6;
              }
            }
            .el-form-item{
              width: 100%;
              margin: 0 0 13px 0;
              &.card_long{
                width: 100%;
              }
            }
            ::v-deep .el-upload-dragger{
              width: 100%;
              height: 100%;
            }
            .card{
              width: 100%;
              display: flex;
              border: 1px solid #E6E6E6;
              border-radius:0.2rem;
              flex-flow: column;
              .required {
                &::before{
                  content: "*";
                  color: #ff3300;
                  font-size: 1rem;
                  margin: 0 0.2rem 0 0;
                }
              }
              .cardName{
                width: 100%;
                height:auto;
                background: #FAFAFA;
                padding:0 0.8rem;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                white-space: nowrap;
                font-size: 1rem;
                color: #999;
                border-bottom: 1px solid #e6e6e6;
                &.longName{
                  min-width: 10rem;
                }
                &.longName1{
                  min-width: 12rem;
                }
              }
              .cardContent{
                width: 100%;
                height: 3rem;
                padding: 0 0.8rem;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                font-size: 1rem;
                color: #383838;
                input::placeholder {
                  color: #C0C4CC;
                }
                ::v-deep .el-select{
                  .el-input{
                    input{
                      border: none;
                      font-size: 1rem;
                      padding: 0;
                      background: none;
                    }
                  }
                }
                ::v-deep .el-cascader{
                  .el-input{
                    input{
                      border: none;
                      font-size: 1rem;
                      padding: 0;
                      background: none;
                    }
                  }
                }
                ::v-deep .el-form-item__content{
                  line-height: 3rem;
                }
                ::v-deep .el-input__prefix{
                  left: 0;
                  height: 100%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  .el-input__icon{
                    line-height: 3rem;
                  }
                }
                ::v-deep .el-date-editor{
                  width: 100%;
                  height: 100%;
                  input{
                    height: 100%;
                    border: none;
                    background: none;
                    font-size: 1rem;
                    padding-left: 2rem;
                  }
                  .el-input__icon{
                    font-size: 1rem;
                  }
                }
                .cardTxt{
                  width: 100%;
                  height: 100%;
                  border: none;
                  background: none;
                  font-size: 1rem;
                  color: #383838;
                  outline: none;
                }
              }
              .cardContent1 {
                height: 100% !important;
                flex-wrap: wrap;
                .cont_box_left {
                  border-bottom: 2px dashed #f1f1f1;
                  flex-grow: 1;
                }
                .cont_box_right {
                  flex-grow: 1;
                }
              }
              &.card_long{
                width: 100%;
              }
            }
            .card1{
              width: 100%;
              display: flex;
              border-radius:0.2rem;
              flex-flow: column;
              .required {
                &::before{
                  content: "*";
                  color: #ff3300;
                  font-size: 1rem;
                  margin: 0 0.2rem 0 0;
                }
              }
              .cardName{
                width: 100%;
                height: 3rem;
                background: #FAFAFA;
                padding:0 0.8rem;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                white-space: nowrap;
                font-size: 1rem;
                color: #999;
                border: 1px solid #E6E6E6;
                &.longName{
                  min-width: 10rem;
                }
              }
              .cardContent{
                width: 100%;
                height: auto;
                padding: 0.8rem 0.8rem 0 0.1rem;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                font-size: 1rem;
                color: #383838;
                input::placeholder {
                  color: #C0C4CC;
                }
                ::v-deep .el-select{
                  .el-input{
                    input{
                      border: none;
                      font-size: 1rem;
                      padding: 0;
                      background: none;
                    }
                  }
                }
                ::v-deep .el-cascader{
                  .el-input{
                    input{
                      border: none;
                      font-size: 1rem;
                      padding: 0;
                      background: none;
                    }
                  }
                }
                ::v-deep .el-form-item__content{
                  line-height: 3rem;
                }
                ::v-deep .el-input__prefix{
                  left: 0;
                  height: 100%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  .el-input__icon{
                    line-height: 3rem;
                  }
                }
                ::v-deep .el-date-editor{
                  width: 100%;
                  height: 100%;
                  input{
                    height: 100%;
                    border: none;
                    background: none;
                    font-size: 1rem;
                    padding-left: 2rem;
                  }
                  .el-input__icon{
                    font-size: 1rem;
                  }
                }
                .cardTxt{
                  width: 100%;
                  height: 100%;
                  border: none;
                  background: none;
                  font-size: 1rem;
                  color: #383838;
                  outline: none;
                }
              }
              &.card_long{
                width: 100%;
              }
            }
            .upload-demo {
              width: 100%;
              .el-upload__tip {
                width: 100%;
                height: auto;
                font-size: 1rem;
                color: #999;
              }
            }
          }
          .promise-box {
            height: 3rem;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            .my-checkbox {
              transform: scale(1.3);
              .span-content {
                color: #ff3e41;
              }
            }
          }
        }


        .foot_Box {
          width: calc(100% - 2rem);
          margin-left:1rem ;
          display: flex;
          justify-content: space-evenly;
          flex-flow: column;
          gap: 1rem;
          padding-bottom: 1.5rem;
          div {
            width: 100%;
            height: 3.4rem;
            background: #3570f6;
            border-radius: 0.6rem;
            text-align: center;
            line-height: 3.4rem;
            color: #fff;
          }
          .disabled_click {
            pointer-events: none !important;
            cursor: not-allowed !important;
            opacity: 0.5 !important;
            background: #cccccc !important;
          }
        }
      }
    }
    .choose_foot {
       width: 100%;
      height:auto;
      display: flex;
      justify-content: center;
      align-items: center;
      padding:1.5rem 1.2rem;
    }
  }
  .el-select-dropdown__item{
    height: auto;
    line-height: normal;
    padding:0.6rem 1.2rem;
    .cardConBox{
      width: 80vw;
      height: auto;
      box-sizing: border-box;
      display: flex;
      flex-flow: column;
      padding: 0.3rem 0;
      background: #fafafa;
      border: 1px solid #f1f1f1;
      border-radius: 0.6rem;
      padding:0.6rem 1rem;
      cursor: pointer;
      span:nth-child(1){
        color: #000000;
        font-weight: bold;
        font-size: 1.2rem;
        cursor: pointer;
        white-space: normal;
        word-break: break-all;
      }
      span:nth-child(2){
        color: #666;
        font-weight: normal;
        width: 100%;
        white-space:normal;
        cursor: pointer;
        font-size: 1rem;
      }
    }
    &:hover{
      background: none;
      .cardConBox{
        background: #3570F605;
        border: 1px solid #3570F6;
        span:nth-child(1){
          color: #3570F6;
        }
      }
    }
  }
  ::v-deep .custom-alert .el-alert__content .el-alert__title {
    font-size: 1rem !important;
  }
}
::v-deep .el-select{
  width: 100%!important;
}
::v-deep .el-radio__label{
  font-size: 1rem;
}
.el-date-editor{
  display: flex;
  justify-content: space-between;
  flex-flow: row;
}
::v-deep .is-finish{
  color: #383838;
  font-size: 18px;
  .el-step__line{
    background: #3570F6;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #3570F6;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    border-color: #3570F6;
  }
}

::v-deep .is-process{
  color: #383838;
  font-size: 18px;
  font-weight: normal;
  .el-step__line{
    background: #f1f1f1;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #ffffff;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    border-width: 5px;
    color: #3570F6;
    border-color: #3570F6;
  }
}

::v-deep .is-wait{
  color: #383838;
  font-size: 18px;
  font-weight: normal;
  .el-step__line{
    background: #f1f1f1;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #EEF6FF;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    border-width: 5px;
    color: #3570F6;
    border-color: #EEF6FF;
  }
}
::v-deep .el-cascader {
  width: 100% !important;
}
</style>
