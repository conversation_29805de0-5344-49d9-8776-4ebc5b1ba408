<template>
  <div class="home">
    <Topbox class="top_layout" /><!--顶部-->
    <top />
    <div class="main_box" v-loading="loading">
      <div class="className">{{ className }}</div>
      <div class="list_box">
        <ul v-if="dataList && dataList.length > 0">
          <li v-for="(item, index) in dataList" :key="index" @click="handleClick(item)">
            <a>{{ item.title }}</a>
            <span>{{ item.releaseTime }}</span>
          </li>
        </ul>
        <el-empty description="暂无内容" v-if="!loading && (!dataList || dataList.length <= 0)"></el-empty>
      </div>
    </div>
    <Footbox class="foot_layout" />
    <foot />
  </div>
</template>

<script>
import top from '../../components/top/top.vue'
import foot from '../../components/foot/foot.vue'
import { getArticleList } from '@/api/cms'
import { getToken } from '@/utils/auth'
import Topbox from "@/views/layout/newtop";
import Footbox from "@/views/layout/foot";
export default {
  components: {
    top,foot,
    Topbox,
    Footbox
  },
  data() {
    return {
      dataList: [],
      // 查询参数
      queryParam: {
        pageNum: 1,
        pageSize: 10,
        classId: undefined,
      },
      // 总条数
      total: 0,
      classId: '',
      className: '',
      token:"",
      routerQuery: {},
      loading: true
    }
  },
  mounted() {},
  beforeDestroy() {},
  created() {
    this.token = getToken();
  },
  watch: {
    $route: {
      handler: function(route) {
        this.initData()
      },
      immediate: true
    }
  },
  methods: {
    initData() {
      this.routerQuery = this.$commonUtils.getApplyParams(this.$constants.STORE_CACHE_KEY_APPLY) || {}
      this.classId = this.routerQuery.classId || ''
      this.className = this.routerQuery.className || ''
      this.dataList = []
      this.getList()
    },
    getList() {
      this.queryParam.classId = this.classId
      this.queryParam.areaId = this.routerQuery.areaId
      this.loading = true
      getArticleList(this.queryParam).then(response => {
        this.dataList = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    handleClick(val){
      this.routerQuery.articleId = val.id
      this.$commonUtils.setApplyParams(this.routerQuery, this.$constants.STORE_CACHE_KEY_APPLY)
      this.$router.push({path: "/article", query: {name: val.title}})
    },
  },
}
</script>
<style lang="scss" scoped>
//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {

  .home {
    width: 100%;
    display: flex;
    flex-direction: column;
    background: #eef6ff;

    .main_box{
      width: 1200px;
      height: auto;
      background: rgba(255, 255, 255, 0.62);
      box-shadow: 0px 10px 30px rgba(93, 132, 177, 0.11);
      border-radius: 0 0 16px 16px;
      padding:40px 45px;
      margin-top: 0px;
      margin-left: 50%;
      transform: translateX(-50%);
      .className{
        width: 100%;
        height: auto;
        font-size: 28px;
        font-weight: bold;
        color: #383838;
      }
      .list_box{
        width: 100%;
        height: auto;
        min-height: 450px;
        padding:15px 0;
        li{
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          gap: 20px;
          padding:15px 0 15px 26px;
          border-bottom: 1px dotted #ccc;
          cursor: pointer;
          background: url("../../../public/image/img.svg") no-repeat 0 50%;
          background-size: 14px auto;
          a{
            width: 100%;
            font-size: 18px;
            text-decoration: none;
            color: #383838;
            cursor: pointer;
          }
          span{
            white-space: nowrap;
            font-size: 18px;
            color: #999;
            cursor: pointer;
          }
          &:nth-child(6n){
            margin-top:15px ;
          }
          &:hover{
            a{
              color: #3570F6;
            }
          }
        }
      }
    }
    .choose_foot {
      display: none;
    }
  }
}
//移动端
@media screen and (max-width: 720px) {

  .top_layout,.foot_layout{
    display: none;
  }
  .home {
    width: 100%;
    display: flex;
    flex-direction: column;
    background: #eef6ff;

    .main_box{
      width: calc(100% - 2rem);
      height: auto;
      background: rgba(255, 255, 255, 0.62);
      box-shadow: 0px 0.5rem 1rem rgba(93, 132, 177, 0.11);
      border-radius: 0.8rem;
      padding:1rem 1.5rem;
      margin-top: 30px;
      margin-left: 50%;
      margin-top: 6rem;
      transform: translateX(-50%);
      .className{
        width: 100%;
        height: auto;
        font-size: 1.6rem;
        font-weight: bold;
        color: #383838;
      }
      .list_box{
        width: 100%;
        height: auto;
        li{
          display: flex;
          flex-flow: column;
          width: 100%;
          padding:1rem 1rem 1rem 2.4rem;
          cursor: pointer;
          background: url("../../../public/image/img.svg") no-repeat 1rem 1.4rem #ffffff;
          background-size: 0.8rem auto;
          margin-top: 1rem;
          border: 1px solid #DCEBFA;
          border-radius: 0.6rem;
          a{
            width: 100%;
            font-size: 1.2rem;
            text-decoration: none;
            color: #000000;
            cursor: pointer;
          }
          span{
            white-space: nowrap;
            font-size: 1.2rem;
            color: #999;
            cursor: pointer;
          }
        }
      }
    }


  }

}
</style>
