<template>
  <div class="login">
    <top />
    <div class="login_body_pc">
      <div class="main_box">
        <div class="className">{{ className }}</div>
        <div class="list_box" v-if="dataList && dataList.length > 0">
          <ul>
            <li v-for="(item, index) in dataList" :key="index" @click="handleClick(item)">
              <a>{{ item.title }}</a>
              <span>{{ item.releaseTime }}</span>
            </li>
          </ul>
        </div>
        <div class="list_box" v-else>
          <el-empty description="暂无内容"></el-empty>
        </div>
      </div>
    </div>
    <div>
      <foot />
    </div>
  </div>
</template>

<script>
import top from '@/components/layoutBox/top'
import foot from '@/components/layoutBox/foot'
import { getArticleList } from '@/api/cms'
export default {
  components: {
    top,
    foot,
  },
  data() {
    return {
      dataList: [],
      // 查询参数
      queryParam: {
        pageNum: 1,
        pageSize: 10,
        classId: undefined,
      },
      // 总条数
      total: 0,
      classId: '',
      className: ''
    }
  },
  mounted() {},
  beforeDestroy() {},
  created() {},
  watch: {
    $route: {
      handler: function(route) {
        this.initData()
      },
      immediate: true
    }
  },
  methods: {
    initData() {
      const routerQuery = this.$route.query || {}
      this.classId = routerQuery.id || ''
      this.className = routerQuery.name || ''
      this.getList()
    },
    getList() {
      this.queryParam.classId = this.classId
      getArticleList(this.queryParam).then(response => {
        this.dataList = response.data.list
        this.total = response.data.total
      })
    },
    handleClick(val){
      this.$router.push({path: "/newsArticle", query: {id: val.id}})
    }
  },
}
</script>
<style lang="scss" scoped>
//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {
  .pc_show{}
  .mob_show{
    display: none;
  }
  ::v-deep .login_head_pc {
    position: relative;
  }
  ::v-deep .login_foot_pc {
    position: relative;
    margin-top: 30px;
  }
  .login {
    width: 100%;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .login_body_pc {
      z-index: 1;
      width: 80%;
      min-width: 1440px;
      margin-left: 50%;
      transform: translateX(-50%);
      justify-content: center;
      align-items: center;
      background: url('../../../public/image/html_bg.png') no-repeat 100% 100%;
      padding: 10px 0;
      .main_box{
        width: 1200px;
        height: auto;
        background: rgba(255, 255, 255, 0.62);
        box-shadow: 0px 10px 30px rgba(93, 132, 177, 0.11);
        border-radius: 16px;
        padding:40px 45px;
        margin-top: 30px;
        margin-left: 50%;
        transform: translateX(-50%);
        .className{
          width: 100%;
          height: auto;
          font-size: 28px;
          font-weight: bold;
          color: #383838;
        }
        .list_box{
          width: 100%;
          height: auto;
          min-height: 450px;
          padding:15px 0;
          li{
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            gap: 20px;
            padding:15px 0 15px 26px;
            border-bottom: 1px dotted #ccc;
            cursor: pointer;
            background: url("../../../public/image/img.svg") no-repeat 0 50%;
            background-size: 14px auto;
            a{
              width: 100%;
              font-size: 18px;
              text-decoration: none;
              color: #383838;
              cursor: pointer;
            }
            span{
              white-space: nowrap;
              font-size: 18px;
              color: #999;
              cursor: pointer;
            }
            &:nth-child(6n){
              margin-top:15px ;
            }
            &:hover{
              a{
                color: #3570F6;
              }
            }
          }
        }
      }
    }
  }
}
//移动端
@media screen and (max-width: 720px) {
  .pc_show{
    display: none;
  }
  .mob_show{
  }
  .login {
    width: 100%;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .login_body_pc {
      z-index: 1;
      width: 100%;
      .main_box{
        width: calc(100% - 2rem);
        height: auto;
        background: rgba(255, 255, 255, 0.62);
        box-shadow: 0px 0.5rem 1rem rgba(93, 132, 177, 0.11);
        border-radius: 0.8rem;
        padding:1rem 1.5rem;
        margin-top: 30px;
        margin-left: 50%;
        transform: translateX(-50%);
        .className{
          width: 100%;
          height: auto;
          font-size: 1.6rem;
          font-weight: bold;
          color: #383838;
        }
        .list_box{
          width: 100%;
          height: auto;
          li{
            display: flex;
            flex-flow: column;
            width: 100%;
            padding:1rem 1rem 1rem 2.4rem;
            cursor: pointer;
            background: url("../../../public/image/img.svg") no-repeat 1rem 1.4rem #ffffff;
            background-size: 0.8rem auto;
            margin-top: 1rem;
            border: 1px solid #DCEBFA;
            border-radius: 0.6rem;
            a{
              width: 100%;
              font-size: 1.2rem;
              text-decoration: none;
              color: #000000;
              cursor: pointer;
            }
            span{
              white-space: nowrap;
              font-size: 1.2rem;
              color: #999;
              cursor: pointer;
            }
          }
        }
      }
    }
  }
}
</style>
