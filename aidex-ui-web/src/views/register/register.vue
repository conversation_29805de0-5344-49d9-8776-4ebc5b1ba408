<template>
  <div class="login">
    <top />
    <div class="login_body_pc">
      <div class="body_left">
        <img src="../../../public/image/<EMAIL>" alt="" />
      </div>
      <div class="body_right">
        <div class="right_top">立即注册</div>
        <div class="right_bottom">
          <el-form
            :model="ruleForm"
            status-icon
            :rules="rules"
            ref="ruleForm"
            class="demo-ruleForm"
          >
            <el-form-item prop="username">
              <el-input
                placeholder="手机号"
                v-model.number="ruleForm.username"
              ></el-input>
            </el-form-item>

            <el-form-item prop="password">
              <el-input
                type="password"
                placeholder="密 码"
                v-model="ruleForm.password"
                autocomplete="off"
                show-password
              ></el-input>
            </el-form-item>
            <el-form-item prop="confirmPassword">
              <el-input
                type="password"
                placeholder="确认密码"
                v-model="ruleForm.confirmPassword"
                autocomplete="off"
                show-password
              ></el-input>
            </el-form-item>
            <el-form-item prop="smsCode" v-if="smsCaptchaOnOff">
              <el-input
                placeholder="验证码"
                v-model="ruleForm.smsCode"
              ></el-input>
            </el-form-item>
            <div class="btnBox" v-if="smsCaptchaOnOff">
              <el-button
                class="button_box"
                type="primary"
                size="small"
                :disabled="isSend"
                @click="countDown"
              >
                {{ codeName }}
              </el-button>
            </div>
            <el-button :loading="loading" @click="submitForm('ruleForm')">注册</el-button>
          </el-form>
          <div class="body_bottom">
            <span @click="toLogin">返回登录</span>
          </div>
        </div>
      </div>
    </div>
    <foot />
  </div>
</template>

<script>
import top from '@/components/layoutBox/top'
import foot from '@/components/layoutBox/foot'
import { getSysConfig } from '@/api/common'
import { register, getSmsCaptcha } from '@/api/login'
import { rsaEncrypt } from '@/utils/rsaUtil'
import {validPassword, validPasswordTops} from '@/utils/validate'
export default {
  components: {
    top,
    foot
  },
  data() {
    var checkPhone = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入手机号码'))
      } else {
        callback()
      }
    }
    var checkYzm = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入验证码'))
      } else {
        callback()
      }
    }
    var validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else if (!validPassword(value)) {
        callback(new Error(validPasswordTops()))
      } else {
        if (this.ruleForm.confirmPassword !== '') {
          this.$refs.ruleForm.validateField('confirmPassword')
        }
        callback()
      }
    }
    var validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.ruleForm.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      isSend: false, //禁用
      codeName: '获取验证码',
      totalTime: 60, //一般是60
      timer: '', //定时器
      ruleForm: {
        username: '',
        password: '',
        confirmPassword: '',
        smsCode: '',
        smsUuid: ''
      },
      rules: {
        username: [{ validator: checkPhone, trigger: 'blur' }],
        password: [{ validator: validatePass, trigger: 'blur' }],
        confirmPassword: [{ validator: validatePass2, trigger: 'blur' }],
        smsCode: [{ validator: checkYzm, trigger: 'blur' }],
      },
      loading: false,
      publicKey: '',
      smsCaptchaOnOff: false,
    }
  },
  mounted() {},
  beforeDestroy() {},
  created() {
    this.getSysConfigData()
  },
  methods: {
    getSysConfigData () {
      getSysConfig().then(response => {
        if (response && response.publicKey) {
          this.publicKey = response.publicKey
        }
        this.smsCaptchaOnOff = (response && response.frontSmsOnOff && response.frontSmsOnOff === true)
      })
    },
    countDown() {
      if (this.isSend) return
      this.getCode() // 获取验证码的接口
    },
    getCode() {
      // 校验手机号是否填写
      const phoneNumber = this.ruleForm.username
      if (!phoneNumber) {
        this.$message.warning("请输入手机号", 3)
        return;
      }
      this.ruleForm.smsUuid = ''
      this.ruleForm.smsCode = ''
      getSmsCaptcha({phoneNumber: phoneNumber}).then(response => {
        if (response && response.code === 200) {
          this.$message.success("短信验证码已发送，可能会有延后，请耐心等待")
          this.ruleForm.smsUuid = response ? response.uuid : ''
          this.handleSMSWaitTime()
        }
      })
    },
    handleSMSWaitTime() {
      this.isSend = true
      this.timer = setInterval(() => {
        this.totalTime--
        this.codeName = '重新发送(' + this.totalTime + ')s'
        if (this.totalTime < 0) {
          clearInterval(this.timer)
          this.codeName = '重新发送验证码'
          this.totalTime = 60
          this.isSend = false
        }
      }, 1000)
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.loading = true
          const submitForm = JSON.parse(JSON.stringify(this.ruleForm))
          const username = rsaEncrypt(submitForm.username + '', this.publicKey) || ''
          const password = rsaEncrypt(submitForm.password, this.publicKey) || ''
          const data = {
            username: username,
            password: password,
            phoneNumber: this.ruleForm.username,
            smsCode: this.ruleForm.smsCode,
            smsUuid: this.ruleForm.smsUuid
          }
          register(data).then(response => {
            this.loading = false
            if (response && response.code === 200) {
              this.$message.success('注册成功')
              this.toLogin()
            }
          }).catch((err) => {
            this.loading = false
          })
        } else {
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    // 点击跳转
    mouseOver(index) {
      this.currentIndex = index
    },
    toLogin() {
      this.$router.push({
        path: '/login'
      })
    },
  },
}
</script>
<style lang="scss" scoped>
//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {
  .pc_show{}
  .mob_show{
    display: none;
  }
  .login {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .login_body_pc {
      position: fixed;
      top: 140px;
      bottom: 80px;
      z-index: 1;
      width: 80%;
      min-width: 1440px;
      margin-left: 50%;
      transform: translateX(-50%);
      display: flex;
      justify-content: center;
      align-items: center;
      background: url('../../../public/image/html_bg.png') no-repeat 100% 100%;
      padding: 10px 0;
      .body_left {
        width: calc(100% - 613px);
        height: auto;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: auto;
          height: 32rem;
          transform: translateY(35px);
        }
      }
      .body_right {
        width: 513px;
        height: auto;
        background: rgba(255, 255, 255, 0.62);
        box-shadow: 0px 10px 30px 1px rgba(93, 132, 177, 0.11);
        border-radius: 16px 16px 16px 16px;
        display: flex;
        flex-direction: column;
        justify-content:flex-start;
        align-items: center;
        padding: 0 0 60px 0;
        .right_top {
          width: 100%;
          height: auto;
          font-size: 28px;
          font-weight: bold;
          color: #383838;
          padding: 56px 0px 24px 60px;
        }
        .right_bottom {
          width: 418px;
          display: flex;
          flex-direction: column;
          ::v-deep .el-form-item__content {
            width: 418px;
            height: 59px;
          }

          ::v-deep .el-input__inner {
            width: 418px;
            height: 59px;
            font-size: 18px;
            font-weight: 400;
            color: #383838;
            border-radius: 10px;
          }
          button {
            width: 418px;
            height: 59px;
            margin: 0;
            color: #fff;
            font-size: 18px;
            font-weight: 400;
            margin-top: 20px;
            background: #3570F6;
            border-radius: 10px;
          }
          .is-disabled{
            opacity: .6;
          }
          .btnBox{
            width: 100%;
            height: auto;
            background: #ccc;
            position: relative;
            .button_box {
              width: 150px;
              height: 37px;
              position: absolute;
              right: 10px;
              top: -90px;
              text-align: center;
              border-radius:4px;
            }
          }

        }
      }
      .body_bottom {
        width: 418px;
        height: 59px;
        display: flex;
        justify-content: space-between;
        color: #3570f6;
        font-size: 18px;
        margin: 10px 0;
      }
    }
  }
}
//移动端
@media screen and (max-width: 720px) {
  .pc_show{
    display: none;
  }
  .mob_show{
  }
  .login {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: #eef6ff;
    .login_body_pc {
      z-index: 1;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0.5rem 0;
      .body_left {
        display: none;
      }
      .body_right {
        width: calc(100% - 2.4rem);
        height: auto;
        border-radius: 100px;
        display: flex;
        flex-direction: column;
        justify-content:flex-start;
        align-items: center;
        .right_top {
          display: none;
        }
        .right_bottom {
          width: 90%;
          display: flex;
          flex-direction: column;
          ::v-deep .el-form-item{
            margin-bottom: 2.3rem;
          }
          ::v-deep .el-form-item__content {
            width: 100%;
            height: 3.6rem;
            line-height: 3.6rem;
            font-size: 1.2rem;
          }

          ::v-deep .el-input__inner {
            width: 100%;
            height: 3.6rem;
            font-size: 1.2rem;
            font-weight: 400;
            color: #383838;
            border-radius: 0.6rem;
          }
          button {
            width: 100%;
            height: 3.6rem;
            margin: 0;
            color: #fff;
            font-size: 1.2rem;
            font-weight: 400;
            background: #3570F6;
            border-radius: 0.6rem;
          }
          .is-disabled{
            opacity: .6;
          }
          .btnBox{
            width: 100%;
            height: auto;
            background: #ccc;
            position: relative;
            .button_box {
              width: 9rem;
              height: 2.4rem;
              line-height: 2.4rem;
              padding: 0;
              position: absolute;
              right: 0.8rem;
              top: -5.3rem;
              text-align: center;
              border-radius:4px;
              font-size: 1.1rem;
            }
          }

        }
      }
      .body_bottom {
        width: 100%;
        height: 2.4rem;
        display: flex;
        justify-content: space-between;
        color: #3570f6;
        font-size: 1.2rem;
        margin:0.5rem 0 0 0;
      }
    }
  }
}
</style>
