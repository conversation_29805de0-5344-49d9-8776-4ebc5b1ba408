<template>
  <div class="choose">
    <Topbox class="top_layout" /><!--顶部-->
  <top />
    <div class="choose_body">
      <div class="body_middle">
        <div class="middle_title">
          <div class="title_left">信息采集表</div>
          <div @click="print" class="title_right" style="cursor: pointer">
            <img src="../../../public/image/print.svg" alt="" />
            <div>打印</div>
          </div>
        </div>

        <div class="middle_content" ref="printArea">
          <div class="school_infor">学生基本信息</div>
            <div class="photo"><img :src="userPhoto" alt="" /></div>
          <div class="school_name">姓名</div>
          <div class="school_input">{{ allData.name || defaultValue }}</div>
          <div class="school_name">性别</div>
          <div class="school_input">{{ selectDictLabel(genderOptions, allData.gender) || defaultValue }}</div>
          <div class="school_name">民族</div>
          <div class="school_input">{{ selectDictLabel(nationOptions, allData.nation) || defaultValue }}</div>
          <div class="school_name">出生日期</div>
          <div class="school_input">{{ allData.birthday || defaultValue }}</div>
          <div class="school_name">身份证类型</div>
          <div class="school_input">{{ selectDictLabel(cardTypeOptions, allData.cardType) || defaultValue }}</div>
          <div class="school_name">身份证号</div>
          <div class="school_input">{{ allData.idCard || defaultValue }}</div>
          <div class="school_name">联系电话</div>
          <div class="school_input">{{ allData.mobile || defaultValue }}</div>
          <div class="school_name">国籍/地区</div>
          <div class="school_input">{{ selectDictLabel(countryOptions, allData.country) || defaultValue }}</div>
          <div class="school_name">籍贯</div>
          <div class="school_input">{{ allData.nativePlace || defaultValue }}</div>
          <div class="school_name">政治面貌</div>
          <div class="school_input">{{ selectDictLabel(politicsStatusOptions, allData.politicsStatus) || defaultValue }}</div>
          <div class="school_name">邮政编码</div>
          <div class="school_input">{{ allData.postalCode || defaultValue }}</div>
          <div class="school_name">电子信箱</div>
          <div class="school_input1">{{ allData.email || defaultValue }}</div>
          <div class="school_name">曾用名</div>
          <div class="school_input">{{ allData.formerName || defaultValue }}</div>
          <div class="school_name">血型</div>
          <div class="school_input1">{{ selectDictLabel(bloodTypeOptions, allData.bloodType) || defaultValue }}</div>
          <div class="school_name">健康状况</div>
          <div class="school_input">{{ selectDictLabel(healthConditionOptions, allData.healthCondition) || defaultValue }}</div>
          <div class="school_name">预防接种</div>
          <div class="school_input1">{{ selectDictLabel(yesOrNoOptions, allData.inoculatePrevent) || defaultValue }}</div>
          <div class="school_name">残疾类型</div>
          <div class="school_input">{{ selectDictLabel(disabilityTypeOptions, allData.disabilityType) || defaultValue }}</div>
          <div class="school_name">特长</div>
          <div class="school_input1">{{ allData.strongPoint || defaultValue }}</div>
          <div class="school_name">出生地区划</div>
          <div class="school_input">{{ allData.birthplaceAreaCodeName || defaultValue }}</div>
          <div class="school_name">户口性质</div>
          <div class="school_input1">{{ selectDictLabel(registeredResidenceCategoryOptions, allData.registeredResidenceCategory) || defaultValue }}</div>
          <div class="school_name">户口所在地区划</div>
          <div class="school_input">{{ allData.registeredResidenceAreaCodeName || defaultValue }}</div>
          <div class="school_name">户籍注册时间</div>
          <div class="school_input1">{{ allData.domicileRegisterTime || defaultValue }}</div>
          <div class="school_name">户籍地址</div>
          <div class="school_input2">{{ (allData.domicileAddressAreaCodeName || defaultValue) + (allData.domicileAddress || defaultValue) }}</div>
          <div class="school_name">居住地产权</div>
          <div class="school_input">{{ allData.houseProperty || defaultValue }}</div>
          <div class="school_name">产权注册时间</div>
          <div class="school_input1">{{ allData.houseRegisterTime || defaultValue }}</div>
          <div class="school_name">居住地址</div>
          <div class="school_input2">{{ allData.residentialAddress || defaultValue }}</div>
          <div class="school_name">家庭地址</div>
          <div class="school_input2">{{ allData.familyAddress || defaultValue }}</div>
          <div class="school_name">港澳台侨外</div>
          <div class="school_input">{{ selectDictLabel(yesOrNoOptions, allData.countrymenAbroad) || defaultValue }}</div>
          <div class="school_name">独生子女</div>
          <div class="school_input1">{{ selectDictLabel(yesOrNoOptions, allData.onlyChild) || defaultValue }}</div>
          <div class="school_name">受过学前教育</div>
          <div class="school_input">{{ selectDictLabel(yesOrNoOptions, allData.preschoolEducation) || defaultValue }}</div>
          <div class="school_name">需要申请资助</div>
          <div class="school_input1">{{ selectDictLabel(yesOrNoOptions, allData.applyFunding) || defaultValue }}</div>
          <div class="school_name">享受一补</div>
          <div class="school_input">{{ selectDictLabel(yesOrNoOptions, allData.enjoyGrants) || defaultValue }}</div>
          <div class="school_name">是否孤儿</div>
          <div class="school_input1">{{ selectDictLabel(yesOrNoOptions, allData.orphan) || defaultValue }}</div>
          <div class="school_name">烈士或优抚子女</div>
          <div class="school_input">{{ selectDictLabel(yesOrNoOptions, allData.priorityRaising) || defaultValue }}</div>
          <div class="school_name">务工随迁子女</div>
          <div class="school_input1">{{ selectDictLabel(yesOrNoOptions, allData.workAccompany) || defaultValue }}</div>
          <div class="school_name">是否留守儿童</div>
          <div class="school_input">{{ selectDictLabel(yesOrNoOptions, allData.leftBehindChildren) || defaultValue }}</div>
          <div class="school_name">入学方式</div>
          <div class="school_input1">{{ selectDictLabel(entryWayOptions, allData.entryWay) || defaultValue }}</div>
          <div class="school_name">就读方式</div>
          <div class="school_input">{{ selectDictLabel(attendWayOptions, allData.attendWay) || defaultValue }}</div>
          <div class="school_name">上下学距离</div>
          <div class="school_input1">{{ allData.schoolDistance ? allData.schoolDistance + 'km' : defaultValue }}</div>
          <div class="school_name">上下学方式</div>
          <div class="school_input">{{ selectDictLabel(schoolWayOptions, allData.schoolWay) || defaultValue }}</div>
          <div class="school_name">需要乘坐校车</div>
          <div class="school_input1">{{ selectDictLabel(yesOrNoOptions, allData.schoolBusTake) || defaultValue }}</div>
          <div class="school_name">政府购买学位</div>
          <div class="school_input">{{ selectDictLabel(yesOrNoOptions, allData.governmentBuyDegree) || defaultValue }}</div>
          <div class="school_name">随班就读</div>
          <div class="school_input1">{{ selectDictLabel(studyFollowClassOptions, allData.studyFollowClass) || defaultValue }}</div>
          <div class="school_infor">报名信息</div>
          <div class="school_name">报名学校</div>
          <div class="school_input">{{ allData.schoolName || defaultValue }}</div>
          <div class="school_name">报名状态</div>
          <div class="school_input1">{{ selectDictLabel(statusOptions, allData.status) || defaultValue }}</div>
          <div class="school_name" v-if="allData.applyType === $constants.SCHOOL_XX">生源类型</div>
          <div class="school_input2" v-if="allData.applyType === $constants.SCHOOL_XX">{{ allData.sourceTypeName || defaultValue }}</div>
          <div class="school_name" v-if="allData.applyType === $constants.SCHOOL_CZ">电子学籍号</div>
          <div class="school_input2" v-if="allData.applyType === $constants.SCHOOL_CZ">{{ allData.studentCode || defaultValue }}</div>
          <div class="school_name" v-if="allData.applyType === $constants.SCHOOL_CZ">毕业学校区划</div>
          <div class="school_input2" v-if="allData.applyType === $constants.SCHOOL_CZ">{{ allData.graduationSchoolAreaCodeName || defaultValue }}</div>
          <div class="school_name" v-if="allData.applyType === $constants.SCHOOL_CZ">毕业学校</div>
          <div class="school_input2" v-if="allData.applyType === $constants.SCHOOL_CZ">{{ allData.graduationSchool || defaultValue }}</div>
          <template v-if="allData && allData.guardianPeopleList && allData.guardianPeopleList.length > 0">
            <div class="print-page-break"></div>
            <template v-for="(item, index) in allData.guardianPeopleList">
              <div class="school_infor">{{ '监护人' + (index + 1) + '信息' }}</div>
              <div class="school_name">姓名</div>
              <div class="school_input">{{ item.name || defaultValue }}</div>
              <div class="school_name">电话</div>
              <div class="school_input1">{{ item.mobile || defaultValue }}</div>
              <div class="school_name">身份类型</div>
              <div class="school_input">{{ selectDictLabel(cardTypeOptions, item.cardType) || defaultValue }}</div>
              <div class="school_name">身份证号</div>
              <div class="school_input1">{{ item.idCard || defaultValue }}</div>
              <div class="school_name">关系</div>
              <div class="school_input">{{ selectDictLabel(relationOptions, item.relation) || defaultValue }}</div>
              <div class="school_name">民族</div>
              <div class="school_input1">{{ selectDictLabel(nationOptions, item.nation) || defaultValue }}</div>
              <div class="school_name">现住址</div>
              <div class="school_input2">{{ item.residentialAddress || defaultValue }}</div>
              <div class="school_name">是否监护人</div>
              <div class="school_input">{{ selectDictLabel(yesOrNoOptions, item.guardianship) || defaultValue }}</div>
              <div class="school_name">户口所在地区划</div>
              <div class="school_input1">{{ item.registeredResidenceAreaCodeName || defaultValue }}</div>
              <div class="school_name">工作单位</div>
              <div class="school_input">{{ item.workUnit || defaultValue }}</div>
              <div class="school_name">职务</div>
              <div class="school_input1">{{ selectDictLabel(postOptions, item.workPost) || defaultValue }}</div>
            </template>
          </template>
          <template v-else><div class="school_infor">暂无监护人信息</div></template>
        </div>

        <img class="printImg" ref="image" alt="转换后的图片" style="width: 1000px" />

        <template v-for="(item) in fileShowList">
        <div class="middle_cont" v-if="fileClassifyList.includes(item.key)">
          <div class="cont_top">
            {{ item.title }}
          </div>
          <div class="cont_bottom" v-if="fileList && fileList[item.key] && fileList[item.key].length > 0">
            <div class="small_img" v-for="(fileItem, fileIndex) in fileList[item.key]" :key="fileIndex">
              <el-image
                style="width: 100%; height: 100%"
                :src="fileItem.url"
                :preview-src-list="srcList"
                @click="handleView(fileItem)"
                fit="contain"
              >
              </el-image>
            </div>
          </div>
          <div v-else class="cont_bottom"><div style="margin: 1em 2em;">无</div></div>
        </div>
        </template>
      </div>
    </div>
    <Footbox class="foot_layout" />
    <foot />
  </div>
</template>

<script>
import top from '../../components/top/top.vue'
import foot from '../../components/foot/foot.vue'
import { getApplyInfo } from '@/api/apply'
import html2canvas from 'html2canvas';
import Topbox from "@/views/layout/newtop";
import Footbox from "@/views/layout/foot";
export default {
  components: {
    top,foot,
    Topbox,
    Footbox
  },
  data() {
    return {
      fits: ['cover'],
      url:[],
      srcList: [],
      allData: {},
      userPhoto:'',
      guardian1: {},
      guardian2: {},
      id: '',
      defaultValue: '-/-',
      genderOptions: [],
      yesOrNoOptions: [],
      nationOptions: [],
      statusOptions: [],
      cardTypeOptions: [],
      countryOptions: [],
      politicsStatusOptions: [],
      healthConditionOptions: [],
      disabilityTypeOptions: [],
      registeredResidenceCategoryOptions: [],
      bloodTypeOptions: [],
      entryWayOptions: [],
      attendWayOptions: [],
      schoolWayOptions: [],
      studyFollowClassOptions: [],
      postOptions: [],
      relationOptions: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      fileShowList: [],
      fileList: {},
      fileClassifyList: [],
    }
  },
  mounted() {},
  beforeDestroy() {},
  created() {
    this.fileShowList = [
      { key: this.$constants.HOUSEHOLD_REGISTER, title: this.$constants.HOUSEHOLD_REGISTER_NAME },
      { key: this.$constants.HOUSE_PROPRIETARY_CERTIFICATE, title: this.$constants.HOUSE_PROPRIETARY_CERTIFICATE_NAME },
      { key: this.$constants.HAVE_HOUSE_CERTIFICATE, title: this.$constants.HAVE_HOUSE_CERTIFICATE_NAME },
      { key: this.$constants.NO_HOUSE_CERTIFICATE, title: this.$constants.NO_HOUSE_CERTIFICATE_NAME },
      { key: this.$constants.HOUSE_LEASE_CERTIFICATE, title: this.$constants.HOUSE_LEASE_CERTIFICATE_NAME },
      { key: this.$constants.WORK_CERTIFICATE, title: this.$constants.WORK_CERTIFICATE_NAME },
      { key: this.$constants.RESIDENCE_PERMIT, title: this.$constants.RESIDENCE_PERMIT_NAME },
      { key: this.$constants.SOCIAL_INSURANCE_PAYMENT_CERTIFICATE, title: this.$constants.SOCIAL_INSURANCE_PAYMENT_CERTIFICATE_NAME },
    ]
    this.getDicts('sys_user_sex').then(response => {
      this.genderOptions = response.data
    })
    this.getDicts('sys_nation').then(response => {
      this.nationOptions = response.data
    })
    this.getDicts('sys_yes_no').then(response => {
      this.yesOrNoOptions = response.data
    })
    this.getDicts('biz_apply_student_status').then(response => {
      this.statusOptions = response.data
    })
    this.getDicts('sys_card_type').then(response => {
      this.cardTypeOptions = response.data
    })
    this.getDicts('sys_country').then(response => {
      this.countryOptions = response.data
    })
    this.getDicts('sys_politics_status').then(response => {
      this.politicsStatusOptions = response.data
    })
    this.getDicts('sys_health_condition').then(response => {
      this.healthConditionOptions = response.data
    })
    this.getDicts('sys_disability_type').then(response => {
      this.disabilityTypeOptions = response.data
    })
    this.getDicts('sys_registered_residence_category').then(response => {
      this.registeredResidenceCategoryOptions = response.data
    })
    this.getDicts('sys_blood_type').then(response => {
      this.bloodTypeOptions = response.data
    })
    this.getDicts('sys_entry_way').then(response => {
      this.entryWayOptions = response.data
    })
    this.getDicts('sys_attend_way').then(response => {
      this.attendWayOptions = response.data
    })
    this.getDicts('sys_school_way').then(response => {
      this.schoolWayOptions = response.data
    })
    this.getDicts('sys_study_follow_class').then(response => {
      this.studyFollowClassOptions = response.data
    })
    this.getDicts('sys_post').then(response => {
      this.postOptions = response.data
    })
    this.getDicts('sys_relation').then(response => {
      this.relationOptions = response.data
    })
    this.routerQuery = this.$route.query || {}
    this.id = this.routerQuery.id
    if (this.id) {
      this.getDataInfo(this.id)
    }
  },
  methods: {
    imageUrlToBase64(url) {
      let image = new Image();
      var _this = this;
      //解决跨域问题
      image.setAttribute('crossOrigin', 'anonymous');
      image.src = url
      image.onload = () => {
        const canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        const context = canvas.getContext('2d');
        context.drawImage(image, 0, 0, image.width, image.height);
        const quality = 0.8;
        //这里的dataurl就是base64类型
        _this.userPhoto = canvas.toDataURL("image/jpeg", quality);//使用toDataUrl将图片转换成jpeg的格式,不要把图片压缩成png，因为压缩成png后base64的字符串可能比不转换前的长！
      }
    },
    getDataInfo(id) {
      getApplyInfo(id).then(response => {
        const data = response.data || {}
        this.allData = data
        if (data.pictureUrl) {
          // this.allData.photo = this.baseUrl + data.pictureUrl
          this.imageUrlToBase64(this.baseUrl+ data.pictureUrl)
        }
        if (data.guardianPeopleList && data.guardianPeopleList.length > 0) {
          const length = data.guardianPeopleList.length
          if (length > 0) {
            this.guardian1 = data.guardianPeopleList[0]
          }
          if (length > 1) {
            this.guardian2 = data.guardianPeopleList[1]
          }
        }
        // 回显附件
        const that = this
        if (data.applyType === this.$constants.SCHOOL_XX && data.sourceFileType) {
          this.fileClassifyList = data.sourceFileType.split(',')
        } else if (data.applyType === this.$constants.SCHOOL_CZ) {
          this.fileClassifyList = [
            this.$constants.HOUSEHOLD_REGISTER,
            this.$constants.HOUSE_PROPRIETARY_CERTIFICATE,
          ]
        }
        if (data.attachmentList && data.attachmentList.length) {
          if (that.fileClassifyList && that.fileClassifyList.length > 0) {
            that.fileClassifyList.forEach(item => {
              let urlArr = []
              for (let i = 0; i < data.attachmentList.length; i++) {
                const file = data.attachmentList[i]
                if (file.businessType === item) {
                  urlArr.push({
                    id: file.id,
                    size: file.attachSize,
                    name: file.attachName,
                    url: that.baseUrl + file.fileUrl,
                    path: file.fileUrl
                  })
                }
              }
              that.fileList[item] = urlArr
            })
          }
        }
      })
    },
    handleView(file) {
      this.srcList.push(file.url)
    },
    print() {

      let printArea = this.$refs.printArea;
      let canvas = document.createElement('canvas');
      let context = canvas.getContext('2d');

      canvas.width = printArea.offsetWidth;
      canvas.height = printArea.offsetHeight;

      let image = this.$refs.image;
      html2canvas(printArea).then((canvas) => {
        image.src = canvas.toDataURL();
        image.onload = () => {
          let printWindow = window.open('', '_blank');
          printWindow.document.open();
          printWindow.document.write('<html><head><title>Print</title></head><body>');
          printWindow.document.write('<img src="' + image.src + '" style="width:100%;" onload="window.print();window.close();" />');
          printWindow.document.write('</body></html>');
          printWindow.document.close();
        };
      });

    }
  },
}
</script>
<style lang="scss" scoped>
.printImg{
  display: none;
}
//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {
      .map_box_pc{
    display: block;
    display: flex;
    justify-content: space-between;
  }
  .map_box_yd{
    display: none!important;
  }
  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .choose_body {
      flex: 1;
      display: flex;
      justify-content: center;
      margin-top: 0px;
      .body_middle {
        width: 1200px;
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 10px 30px 1px rgba(93,132,177,0.11);
        border-radius: 0px 0px 16px 16px;
        .middle_title {
          width: 100%;
          padding: 33px 42px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .title_left {
            width: 110px;
            height: 29px;
            font-size: 22px;
            font-weight: bold;
            color: #3570f6;
          }
          .title_right {
            width: 60px;
            height: 21px;
            font-size: 16px;
            font-weight: 400;
            color: #5c5c66;
            display: flex;
            div{
              width: 32px;
              height: 21px;
              font-size: 16px;
              font-weight: 400;
              color: #5C5C66;
              line-height: 21px;
              cursor: pointer;
              &:hover {
                color: #0B7DFB;
              }
            }
            img {
              width: 18px;
              height: 21px;
              line-height: 21px;
              margin-right: 5px;
            }
          }
        }
        .middle_content {
          width: 1120px;
          height: auto;
          margin: 0 40px;
          border: 1px solid #d0d0d0;
          border-bottom: none;
          box-sizing: content-box;
          .school_infor {
            width: 100%;
            height: 57px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #f5f5f5;
            opacity: 1;
            border-bottom: 1px solid #d0d0d0;
            font-size: 22px;
            color: #383838;
          }
          .photo{
            width: 220px;
            height: 300px;
            float: right;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: center;
            align-items: center;
            img{
              max-width: 90%;
              max-height: 90%;
            }
          }
          .school_name{
            width: 150px;
            height: 60px;
            line-height: 60px;
            float: left;
            border-right: 1px solid #d0d0d0;
            border-bottom: 1px solid #d0d0d0;
            text-align: center;
            font-size: 18px;
            color: #888;
          }
          .school_input{
             width: 300px;
            height: 60px;
            float: left;
            border-right: 1px solid #d0d0d0;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding:5px 10px;
            color: #383838;
            word-wrap: break-word;
            word-break: break-all;
            input{
              width: 100%;
              height: 100%;
              border: none;
              background: none;
              font-size: 18px;
              outline: none;
              padding: 0 10px;
            }
          }
          .school_input1{
            width: 520px;
            height: 60px;
            float: left;
            border-right: none;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding:5px 10px;
            word-wrap: break-word;
            word-break: break-all;
            input{
              width: 100%;
              height: 100%;
              border: none;
              background: none;
              outline: none;
            }
          }
          .school_input2{
            width: 970px;
            height: 60px;
            float: left;
            border-right: none;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding:5px 10px;
            word-wrap: break-word;
            word-break: break-all;
            input{
              width: 100%;
              height: 100%;
              border: none;
              background: none;
              outline: none;
            }
          }
        }
        .middle_cont{
          width: 100%;
          display: flex;
          flex-direction: column;
          .cont_top{
            width: 100%;
            height: 29px;
            font-size: 22px;
            font-weight: bold;
            color: #3570F6;
            margin: 27px 42px 0px 42px;
          }
        }
        .cont_bottom{
          width: 100%;
          padding: 20px 42px 42px 42px;
          display: flex;
          flex-wrap: wrap;
          gap: 20px;

          .small_img{
            width: calc(25% - 16px);
            height: 260px;
            ::v-deep .el-image{
              img{
                background: #fafafa!important;
                border: 1px solid #f1f1f1;
              }
            }
          }
        }
      }
    }
  }
  .choose_foot {
    width: 100%;
    height: 100px;
    opacity: 1;
    display: none;
  }
}
@media screen and (max-width: 720px) {
  .top_layout,.foot_layout{
    display: none;
  }
  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
  }
  .el-icon-arrow-down {
    font-size: 1rem;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;

    .choose_body {
      flex: 1;
      display: flex;
      justify-content: center;
      margin-top: 6rem;
      .body_middle {
        width: calc(100% - 3rem);
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 1rem 3rem 1px rgba(93,132,177,0.11);
        border-radius: 1.2rem;
        padding:0 1rem;
        .middle_title {
          width: 100%;
          padding: 1rem 0;
          display: flex;
          justify-content: center;
          align-items: center;
          .title_left {
            font-size: 1.4rem;
            font-weight: bold;
            color: #3570f6;
          }
          .title_right {
            display: none;
          }
        }
        .middle_content {
          width: 100%;
          height: auto;
          border: 1px solid #d0d0d0;
          border-bottom: none;
          box-sizing: content-box;
          display: flex;
          flex-wrap: wrap;
          .school_infor {
            width: 100%;
            height: auto;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #f5f5f5;
            opacity: 1;
            border-bottom: 1px solid #d0d0d0;
            font-size: 1.2rem;
            padding:0.5rem 0;
            color: #383838;
            font-weight: bold;
          }
          .photo{
            width: 100%;
            height: 15rem;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: center;
            align-items: center;
            img{
              max-width: 90%;
              max-height: 90%;
            }
          }
          .school_name{
            width: 8rem;
            height: auto;
            border-right: 1px solid #d0d0d0;
            border-bottom: 1px solid #d0d0d0;
            font-size: 1rem;
            color: #888;
            padding: 0.5rem 0;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
          }
          .school_input{
            width: calc(100% - 8rem);
            height: auto;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 0.5rem;
            color: #383838;
            font-size: 1rem;
            word-wrap: break-word;
            word-break: break-all;
          }
          .school_input1{
            width: calc(100% - 8rem);
            height: auto;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 0.5rem;
            color: #383838;
            font-size: 1rem;
            word-wrap: break-word;
            word-break: break-all;
          }
          .school_input2{
            width: calc(100% - 8rem);
            height: auto;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 0.5rem;
            color: #383838;
            font-size: 1rem;
            word-wrap: break-word;
            word-break: break-all;
          }
        }
        .middle_cont{
          width: 100%;
          display: flex;
          flex-direction: column;
          .cont_top{
            width: 100%;
            font-size: 1.4rem;
            padding:1rem 0 0.5rem 0;
            text-align: center;
            font-weight: bold;
            color: #3570F6;
          }
        }
        .cont_bottom{
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          padding:0 0 1.5rem 0;

          .small_img{
            width: calc(50% - 0.4rem);
            height:11rem;
            margin-bottom:0.8rem;
            ::v-deep .el-image{
              img{
                background: #fafafa!important;
                border: 1px solid #f1f1f1;
              }
            }
          }
        }
      }
    }
  }
}

@media print {
  .school_name{
    display: none;
  }
  .print-page-break {
    page-break-before: always; /* 总是在元素之前进行分页 */
  }
}
</style>
