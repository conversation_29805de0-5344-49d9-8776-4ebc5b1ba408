<template>
  <div class="choose">
    <Topbox class="top_layout" /><!--顶部-->
    <top />
    <div class="choose_body">
      <div class="body_middle">
        <div class="middle_title">
          <div class="title_left">{{ allData.personnelTypeName }}入学申请信息表</div>
          <div @click="print" class="title_right" style="cursor: pointer">
            <img src="../../../public/image/print.svg" alt="" />
            <div>打印</div>
          </div>
        </div>

        <div class="middle_content" ref="printArea">
          <template>
            <div class="school_infor">学校信息</div>
            <div class="school_name">报名学校</div>
            <div class="school_input">{{ allData.schoolName || defaultValue }}</div>
            <div class="school_name">生源类型</div>
            <div class="school_input1">{{ allData.sourceTypeName || defaultValue }}</div>
          </template>
          <template>
            <div class="school_infor">儿童信息</div>
            <div class="school_name">儿童姓名</div>
            <div class="school_input">{{ allData.name || defaultValue }}</div>
            <div class="school_name">身份证号</div>
            <div class="school_input1">{{ allData.idCard || defaultValue }}</div>
            <div class="school_name">性别</div>
            <div class="school_input">{{ selectDictLabel(genderOptions, allData.gender) || defaultValue }}</div>
            <div class="school_name">出生日期</div>
            <div class="school_input1">{{ allData.birthday || defaultValue }}</div>
            <div class="school_name">民族</div>
            <div class="school_input">{{ selectDictLabel(nationOptions, allData.nation) || defaultValue }}</div>
            <div class="school_name">现居住地址</div>
            <div class="school_input1">{{ allData.residentialAddress || defaultValue }}</div>
          </template>
          <template v-if="allData.preferentialTreatment">
            <div class="school_name">优抚政策情况</div>
            <div class="school_input2">{{ selectDictLabel(policyBasedTypeOptions, allData.preferentialTreatment) || defaultValue }}</div>
            <template>
              {{ void (labelTitle = '优抚政策证明') }}
              <template v-if="fileList && fileList[$constants.PREFERENTIAL_TREATMENT_CERTIFICATE] && fileList[$constants.PREFERENTIAL_TREATMENT_CERTIFICATE].length > 0">
                <div class="school_tr no_print" :class="fileList[$constants.PREFERENTIAL_TREATMENT_CERTIFICATE].length > 6 ? 'school_tr2' : 'school_tr1'">{{ labelTitle }}</div>
                <div class="school_td school_td2 no_print">
                  <div class="cont_bottom">
                    <div class="small_img" v-for="(fileItem, fileIndex) in fileList[$constants.PREFERENTIAL_TREATMENT_CERTIFICATE]" :key="fileIndex">
                      <el-image
                          style="width: 100%; height: 100%"
                          :src="fileItem.url"
                          :preview-src-list="srcList"
                          @click="handleView(fileItem)"
                          fit="contain"
                      >
                      </el-image>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="school_name no_print">{{ labelTitle }}</div>
                <div class="school_input2 no_print">无</div>
              </template>
            </template>
          </template>
          <template>
            <template v-if="guardian1 && guardian1.name">
                <div class="school_infor">监护人信息</div>
                <div class="school_name">监护人姓名</div>
                <div class="school_input">{{ guardian1.name || defaultValue }}</div>
                <div class="school_name">监护人身份证号</div>
                <div class="school_input1">{{ guardian1.idCard || defaultValue }}</div>
                <div class="school_name">监护人与儿童关系</div>
                <div class="school_input">{{ selectDictLabel(relationOptions, guardian1.relation) || defaultValue }}</div>
                <div class="school_name">监护人手机号</div>
                <div class="school_input1">{{ guardian1.mobile || defaultValue }}</div>
              </template>
              <template v-else><div class="school_input3">暂无监护人信息</div></template>
          </template>
          <template>
            <template v-if="guardian2 && guardian2.name">
                <div class="school_infor">紧急联系人信息</div>
                <div class="school_name">紧急联系人姓名</div>
                <div class="school_input">{{ guardian2.name || defaultValue }}</div>
                <div class="school_name">紧急联系人手机号</div>
                <div class="school_input1">{{ guardian2.mobile || defaultValue }}</div>
                <div class="school_name">紧急联系人与儿童关系</div>
                <div class="school_input">{{ selectDictLabel(relationOptions, guardian2.relation) || defaultValue }}</div>
                <div class="school_name">紧急联系人身份证号</div>
                <div class="school_input1">{{ guardian2.idCard || defaultValue }}</div>
              </template>
              <template v-else><div class="school_input3">暂无紧急联系人信息</div></template>
          </template>
        </div>

      </div>

    </div>
    <img class="printImg" ref="image" alt="转换后的图片" style="width: 1000px" />
    <Footbox class="foot_layout" />
    <foot />
  </div>
</template>

<script>
import top from '../../components/top/top.vue'
import foot from '../../components/foot/foot.vue'
import { getApplyInfo } from '@/api/apply'
import html2canvas from 'html2canvas';
import Topbox from "@/views/layout/newtop";
import Footbox from "@/views/layout/foot";
export default {
  components: {
    top,foot,
    Topbox,
    Footbox
  },
  data() {
    return {
      allData: {},
      guardian1: {},
      guardian2: {},
      id: '',
      defaultValue: '-/-',
      yesValue: 'Y',
      noValue: 'N',
      genderOptions: [],
      nationOptions: [],
      relationOptions: [],
      policyBasedTypeOptions: [],
      fileList: {},
      srcList: []
    }
  },
  beforeDestroy() {},
  created() {
    this.getDicts('sys_user_sex').then(response => {
      this.genderOptions = response.data
    })
    this.getDicts('sys_nation').then(response => {
      this.nationOptions = response.data
    })
    this.getDicts('sys_relation').then(response => {
      this.relationOptions = response.data
    })
    this.getDicts('biz_policy_based_type').then(response => {
      this.policyBasedTypeOptions = response.data
    })
  },
  mounted() {
    this.reset()
    this.routerQuery = this.$route.query || {}
    this.id = this.routerQuery.id
    if (this.id) {
      this.getDataInfo(this.id)
    }
  },
  methods: {
    // 表单重置
    reset() {
      this.id = ''
      this.allData = {}
      this.guardian1 = {}
      this.guardian2 = {}
      this.fileList = []
      this.srcList = []
    },
    getDataInfo(id) {
      getApplyInfo(id).then(response => {
        const data = response.data || {}
        this.allData = data

        // 回显人员
        let guardian1 = {}, guardian2 = {}
        if (data && data.guardianPeopleList && data.guardianPeopleList.length > 0) {
          data.guardianPeopleList.forEach(item => {
            if (item.guardianship === this.yesValue) {
              guardian1 = item
            } else {
              guardian2 = item
            }
          })
        }
        this.guardian1 = guardian1
        this.guardian2 = guardian2

        // 回显附件
        const fileList = {}
        if (data && data.attachmentsYfzczm) {
          const attachments = data.attachmentsYfzczm
          const key = this.$constants.PREFERENTIAL_TREATMENT_CERTIFICATE
          fileList[key] = this.handleShowFileData(attachments)
        }
        this.fileList = fileList
      })
    },
    handleShowFileData(attachments) {
      const fileArr = []
      if (attachments) {
        const arr = attachments.split(',')
        for (let i = 0; i < arr.length; i++) {
          const file = arr[i]
          const fileParams = file.split('?')
          if (fileParams.length > 1) {
            const queryParams = fileParams[1].split('&')
            const params = {}
            queryParams.forEach(param => {
              const [key, value] = param.split('=')
              params[key] = value
            })
            const fileData = {
              id: params.fileId,
              name: params.filename,
              url: file,
              path: file
            }
            fileArr.push(fileData)
          }
        }
      }
      return fileArr
    },
    handleView(file) {
      this.srcList = []
      this.srcList.push(file.url)
    },
    print() {
      // 先移除不需要的div
      let elements = document.getElementsByClassName('no_print');
      if (elements && elements.length > 0) {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "none";
        }
      }
      let printArea = this.$refs.printArea;

      let image = this.$refs.image;
      html2canvas(printArea).then((canvas) => {
        image.src = canvas.toDataURL();
        image.onload = () => {
          let printWindow = window.open('', '_blank');
          printWindow.document.open();
          printWindow.document.write('<html><head><title>Print</title></head><body style="margin: 0; padding: 0;">');
          printWindow.document.write('<img src="' + image.src + '" style="width:100%;" onload="window.print();window.close();" />');
          printWindow.document.write('</body></html>');
          printWindow.document.close();
        };
      });

      // 转换完成后，恢复被移除的div
      if (elements && elements.length > 0) {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.removeProperty('display');
        }
      }

    }
  },
}
</script>
<style lang="scss" scoped>
.printImg{
  display: none;
}
//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {
      .map_box_pc{
    display: block;
    display: flex;
    justify-content: space-between;
  }
  .map_box_yd{
    display: none!important;
  }
  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .choose_body {
      flex: 1;
      display: flex;
      justify-content: center;
      margin-top: 0px;
      .body_middle {
        width: 1200px;
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 10px 30px 1px rgba(93,132,177,0.11);
        border-radius: 0px 0px 16px 16px;
        .middle_title {
          width: 100%;
          padding: 33px 42px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .title_left {
            width: 50%;
            height: 29px;
            font-size: 22px;
            font-weight: bold;
            color: #3570f6;
          }
          .title_right {
            width: 60px;
            height: 21px;
            font-size: 16px;
            font-weight: 400;
            color: #5c5c66;
            display: flex;
            div{
              width: 32px;
              height: 21px;
              font-size: 16px;
              font-weight: 400;
              color: #5C5C66;
              line-height: 21px;
              cursor: pointer;
              &:hover {
                color: #0B7DFB;
              }
            }
            img {
              width: 18px;
              height: 21px;
              line-height: 21px;
              margin-right: 5px;
            }
          }
        }
        .middle_content {
          width: 1120px;
          height: auto;
          margin: 0 40px 40px 40px;
          border: 1px solid #d0d0d0;
          border-bottom: none;
          box-sizing: content-box;
          .school_infor {
            width: 100%;
            height: 57px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #f5f5f5;
            opacity: 1;
            border-bottom: 1px solid #d0d0d0;
            font-size: 22px;
            color: #383838;
          }
          .photo{
            width: 220px;
            height: 300px;
            float: right;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: center;
            align-items: center;
            img{
              width: 90%;
              max-width: 90%;
              max-height: 90%;
            }
          }
          .school_name{
            width: 150px;
            height: 60px;
            float: left;
            border-right: 1px solid #d0d0d0;
            border-bottom: 1px solid #d0d0d0;
            text-align: center;
            font-size: 18px;
            color: #888;
            word-wrap: break-word;
            word-break: break-all;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .school_input{
             width: 300px;
            height: 60px;
            float: left;
            border-right: 1px solid #d0d0d0;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding:5px 10px;
            color: #383838;
            word-wrap: break-word;
            word-break: break-all;
            input{
              width: 100%;
              height: 100%;
              border: none;
              background: none;
              font-size: 18px;
              outline: none;
              padding: 0 10px;
            }
          }
          .school_input1{
            width: 520px;
            height: 60px;
            float: left;
            border-right: none;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding:5px 10px;
            word-wrap: break-word;
            word-break: break-all;
            input{
              width: 100%;
              height: 100%;
              border: none;
              background: none;
              outline: none;
            }
          }
          .school_input2{
            width: 970px;
            height: 60px;
            float: left;
            border-right: none;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding:5px 10px;
            word-wrap: break-word;
            word-break: break-all;
            input{
              width: 100%;
              height: 100%;
              border: none;
              background: none;
              outline: none;
            }
          }
          .school_input3{
            width: 100%;
            height: 60px;
            float: left;
            border-right: none;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding:5px 10px;
            word-wrap: break-word;
            word-break: break-all;
            input{
              width: 100%;
              height: 100%;
              border: none;
              background: none;
              outline: none;
            }
          }
          .school_tr{
            width: 150px;
            height: auto;
            float: left;
            border-right: 1px solid #d0d0d0;
            border-bottom: 1px solid #d0d0d0;
            text-align: center;
            font-size: 18px;
            color: #888;
            word-wrap: break-word;
            word-break: break-all;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .school_tr1 {
            height: 141px !important;
          }
          .school_tr2 {
            height: 281px !important;
          }
          .school_td{
            width: 300px;
            height: auto;
            float: left;
            border-right: 1px solid #d0d0d0;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding:5px 10px;
            .cont_bottom{
              width: 100%;
              padding: 5px;
              display: flex;
              flex-wrap: wrap;
              gap: 20px;

              .small_img{
                width: 120px;
                height: 120px;
                ::v-deep .el-image{
                  img{
                    background: #fafafa!important;
                    border: 1px solid #f1f1f1;
                  }
                }
              }
            }
          }
          .school_td1 {
            width: 520px !important;
            border-right: none !important;
          }
          .school_td2 {
            width: 970px !important;
            border-right: none !important;
          }
        }
        .middle_cont{
          width: 100%;
          display: flex;
          flex-direction: column;
          .cont_top{
            width: 100%;
            height: 29px;
            font-size: 22px;
            font-weight: bold;
            color: #3570F6;
            margin: 27px 42px 0px 42px;
          }
        }
        .cont_bottom{
          width: 100%;
          padding: 20px 42px 42px 42px;
          display: flex;
          flex-wrap: wrap;
          gap: 20px;

          .small_img{
            width: calc(25% - 16px);
            height: 260px;
            ::v-deep .el-image{
              img{
                background: #fafafa!important;
                border: 1px solid #f1f1f1;
              }
            }
          }
        }
      }
    }
  }
  .choose_foot {
    width: 100%;
    height: 100px;
    opacity: 1;
    display: none;
  }
}
@media screen and (max-width: 720px) {
  .top_layout,.foot_layout{
    display: none;
  }
  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
  }
  .el-icon-arrow-down {
    font-size: 1rem;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;

    .choose_body {
      flex: 1;
      display: flex;
      justify-content: center;
      margin-top: 6rem;
      .body_middle {
        width: calc(100% - 3rem);
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 1rem 3rem 1px rgba(93,132,177,0.11);
        border-radius: 1.2rem;
        padding:0 1rem 1rem 1rem;
        .middle_title {
          width: 100%;
          padding: 1rem 0;
          display: flex;
          justify-content: center;
          align-items: center;
          .title_left {
            font-size: 1.4rem;
            font-weight: bold;
            color: #3570f6;
          }
          .title_right {
            display: none;
          }
        }
        .middle_content {
          width: 100%;
          height: auto;
          border: 1px solid #d0d0d0;
          border-bottom: none;
          box-sizing: content-box;
          display: flex;
          flex-wrap: wrap;
          .school_infor {
            width: 100%;
            height: auto;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #f5f5f5;
            opacity: 1;
            border-bottom: 1px solid #d0d0d0;
            font-size: 1.2rem;
            padding:0.5rem 0;
            color: #383838;
            font-weight: bold;
          }
          .photo{
            width: 100%;
            height: 15rem;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: center;
            align-items: center;
            img{
              width: 60%;
              max-width: 90%;
              max-height: 90%;
            }
          }
          .school_name{
            width: 8rem;
            height: auto;
            border-right: 1px solid #d0d0d0;
            border-bottom: 1px solid #d0d0d0;
            font-size: 1rem;
            color: #888;
            padding: 0.5rem 0;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
          }
          .school_input{
            width: calc(100% - 8rem);
            height: auto;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 0.5rem;
            color: #383838;
            font-size: 1rem;
            word-wrap: break-word;
            word-break: break-all;
          }
          .school_input1{
            width: calc(100% - 8rem);
            height: auto;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 0.5rem;
            color: #383838;
            font-size: 1rem;
            word-wrap: break-word;
            word-break: break-all;
          }
          .school_input2{
            width: calc(100% - 8rem);
            height: auto;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 0.5rem;
            color: #383838;
            font-size: 1rem;
            word-wrap: break-word;
            word-break: break-all;
          }
          .school_input3{
            width: 100%;
            height: auto;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 0.5rem;
            color: #383838;
            font-size: 1rem;
            word-wrap: break-word;
            word-break: break-all;
          }
          .school_tr{
            width: 8rem;
            height: auto;
            border-right: 1px solid #d0d0d0;
            border-bottom: 1px solid #d0d0d0;
            font-size: 1rem;
            color: #888;
            padding: 0.5rem 0;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
          }
          .school_td{
            width: calc(100% - 8rem);
            height: auto;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 0.5rem;
            color: #383838;
            font-size: 1rem;
            word-wrap: break-word;
            word-break: break-all;
          }
        }
        .middle_cont{
          width: 100%;
          display: flex;
          flex-direction: column;
          .cont_top{
            width: 100%;
            font-size: 1.4rem;
            padding:1rem 0 0.5rem 0;
            text-align: center;
            font-weight: bold;
            color: #3570F6;
          }
        }
        .cont_bottom{
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          padding:0 0 0 0;

          .small_img{
            width: calc(50% - 0.4rem);
            height: 9rem;
            margin-bottom:0.8rem;
            margin-right: 0.8rem;
            ::v-deep .el-image{
              img{
                background: #fafafa!important;
                border: 1px solid #f1f1f1;
              }
            }
          }
        }
      }
    }
  }
}
</style>
