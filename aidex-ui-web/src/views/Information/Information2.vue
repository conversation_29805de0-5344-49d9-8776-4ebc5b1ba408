<template>
  <div class="choose">
    <Topbox class="top_layout" /><!--顶部-->
  <top />
    <div class="choose_body">
      <div class="body_middle">
        <div class="middle_title">
          <div class="title_left">{{ allData.personnelTypeName }}入学申请信息表</div>
          <div @click="print" class="title_right" style="cursor: pointer">
            <img src="../../../public/image/print.svg" alt="" />
            <div>打印</div>
          </div>
        </div>

        <div class="middle_content" ref="printArea">
          <div class="school_infor">报名信息</div>
          <div class="school_name">报名学校</div>
          <div class="school_input">{{ allData.schoolName || defaultValue }}</div>
          <div class="school_name">生源类型</div>
          <div class="school_input1">{{ allData.sourceTypeName || defaultValue }}</div>
          <div class="school_infor">儿童基本信息</div>
          <div class="photo" v-if="infoPhoto.visible"><img :src="userPhoto" alt="" /></div>
          <div class="school_name">儿童姓名</div>
          <div class="school_input">{{ allData.name || defaultValue }}</div>
          <div class="school_name">性别</div>
          <div :class="infoPhoto.visible ? 'school_input' : 'school_input1'">{{ selectDictLabel(genderOptions, allData.gender) || defaultValue }}</div>
          <div class="school_name">民族</div>
          <div class="school_input">{{ selectDictLabel(nationOptions, allData.nation) || defaultValue }}</div>
          <div class="school_name">出生日期</div>
          <div :class="infoPhoto.visible ? 'school_input' : 'school_input1'">{{ allData.birthday || defaultValue }}</div>
          <div class="school_name">证件类型</div>
          <div class="school_input">{{ selectDictLabel(cardTypeOptions, allData.cardType) || defaultValue }}</div>
          <div class="school_name">证件号码</div>
          <div :class="infoPhoto.visible ? 'school_input' : 'school_input1'">{{ allData.idCard || defaultValue }}</div>
          <div class="school_name">监护人电话</div>
          <div class="school_input">{{ allData.mobile || defaultValue }}</div>
          <div class="school_name">国籍</div>
          <div :class="infoPhoto.visible ? 'school_input' : 'school_input1'">{{ selectDictLabel(countryOptions, allData.country) || defaultValue }}</div>
          <div class="school_name">健康状况</div>
          <div class="school_input">{{ selectDictLabel(healthConditionOptions, allData.healthCondition) || defaultValue }}</div>
          <div class="school_name">预防接种</div>
          <div :class="infoPhoto.visible ? 'school_input' : 'school_input1'">{{ selectDictLabel(yesOrNoOptions, allData.inoculatePrevent) || defaultValue }}</div>
          <div class="school_name">残疾类型</div>
          <div class="school_input2">{{ selectDictLabel(disabilityTypeOptions, allData.disabilityType) || defaultValue }}</div>
          <template v-if="allData.overageChild === yesValue">
            {{ void (labelTitle = '延缓入学申请批复照片') }}
            <template v-if="fileList && fileList[$constants.POSTPONE_ENTER_SCHOOL_APPLY] && fileList[$constants.POSTPONE_ENTER_SCHOOL_APPLY].length > 0">
              <div class="school_tr school_tr1 no_print">{{ labelTitle }}</div>
              <div class="school_td school_td2 no_print">
                <div class="cont_bottom" >
                  <div class="small_img" v-for="(fileItem, fileIndex) in fileList[$constants.POSTPONE_ENTER_SCHOOL_APPLY]" :key="fileIndex">
                    <el-image
                        style="width: 100%; height: 100%"
                        :src="fileItem.url"
                        :preview-src-list="srcList"
                        @click="handleView(fileItem)"
                        fit="contain"
                    >
                    </el-image>
                  </div>
                </div>
              </div>
            </template>
            <template v-else>
              <div class="school_name">{{ labelTitle }}</div>
              <div class="school_input2">无</div>
            </template>
          </template>
          <template>
            <template v-if="allData && allData.guardianPeopleList && allData.guardianPeopleList.length > 0">
              <template v-for="(item, index) in allData.guardianPeopleList">
                <div class="school_infor">{{ '监护人（' + (index + 1) + '）信息'}}</div>
                <div class="school_name">姓名</div>
                <div class="school_input">{{ item.name || defaultValue }}</div>
                <div class="school_name">手机号码</div>
                <div class="school_input1">{{ item.mobile || defaultValue }}</div>
                <div class="school_name">证件类型</div>
                <div class="school_input">{{ selectDictLabel(cardTypeOptions, item.cardType) || defaultValue }}</div>
                <div class="school_name">证件号码</div>
                <div class="school_input1">{{ item.idCard || defaultValue }}</div>
                <div class="school_name">与儿童关系</div>
                <div class="school_input2">{{ selectDictLabel(relationOptions, item.relation) || defaultValue }}</div>
              </template>
            </template>
            <template v-else><div class="school_input3">暂无监护人信息</div></template>
          </template>
          <template v-if="allData.personnelType === $constants.RESIDENT_TYPE_HJ">
            <div class="school_infor">儿童户籍信息</div>
            <div class="school_name">户号</div>
            <div class="school_input">{{ allData.domicileNumber || defaultValue }}</div>
            <div class="school_name">户主姓名</div>
            <div class="school_input1">{{ allData.domicileHead || defaultValue }}</div>
            <div class="school_name">户主身份证号码</div>
            <div class="school_input">{{ allData.domicileHeadIdCard || defaultValue }}</div>
            <div class="school_name">户主与儿童关系</div>
            <div class="school_input1">{{ selectDictLabel(relationOptions, allData.domicileHeadRelation) || defaultValue }}</div>
            <div class="school_name">儿童迁入时间</div>
            <div class="school_input">{{ allData.domicileRegisterTime || defaultValue }}</div>
            <div class="school_name">户籍地址</div>
            <div class="school_input1">{{ (allData.domicileAddressAreaCodeName || defaultValue) + (allData.domicileAddress || defaultValue) }}</div>
            <template>
              {{ void (labelTitle = '户口簿首页照片') }}
              <template v-if="fileList && fileList[$constants.HOUSEHOLD_REGISTER] && fileList[$constants.HOUSEHOLD_REGISTER].length > 0">
                <div class="school_tr school_tr1 no_print">{{ labelTitle }}</div>
                <div class="school_td school_td2 no_print">
                  <div class="cont_bottom" >
                    <div class="small_img" v-for="(fileItem, fileIndex) in fileList[$constants.HOUSEHOLD_REGISTER]" :key="fileIndex">
                      <el-image
                          style="width: 100%; height: 100%"
                          :src="fileItem.url"
                          :preview-src-list="srcList"
                          @click="handleView(fileItem)"
                          fit="contain"
                      >
                      </el-image>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="school_name no_print">{{ labelTitle }}</div>
                <div class="school_input2 no_print">无</div>
              </template>
            </template>
            <template>
              {{ void (labelTitle = '户口簿户主页照片') }}
              <template v-if="fileList && fileList[$constants.HOUSEHOLD_REGISTER_HEAD] && fileList[$constants.HOUSEHOLD_REGISTER_HEAD].length > 0">
              <div class="school_tr school_tr1 no_print">{{ labelTitle }}</div>
              <div class="school_td school_td2 no_print">
                <div class="cont_bottom" >
                  <div class="small_img" v-for="(fileItem, fileIndex) in fileList[$constants.HOUSEHOLD_REGISTER_HEAD]" :key="fileIndex">
                    <el-image
                        style="width: 100%; height: 100%"
                        :src="fileItem.url"
                        :preview-src-list="srcList"
                        @click="handleView(fileItem)"
                        fit="contain"
                    >
                    </el-image>
                  </div>
                </div>
              </div>
            </template>
              <template v-else>
                <div class="school_name no_print">{{ labelTitle }}</div>
                <div class="school_input2 no_print">无</div>
              </template>
            </template>
            <template>
              {{ void (labelTitle = '户口簿儿童页照片') }}
              <template v-if="fileList && fileList[$constants.HOUSEHOLD_REGISTER_CHILD] && fileList[$constants.HOUSEHOLD_REGISTER_CHILD].length > 0">
                <div class="school_tr school_tr1 no_print">{{ labelTitle }}</div>
                <div class="school_td school_td2 no_print">
                  <div class="cont_bottom" >
                    <div class="small_img" v-for="(fileItem, fileIndex) in fileList[$constants.HOUSEHOLD_REGISTER_CHILD]" :key="fileIndex">
                      <el-image
                          style="width: 100%; height: 100%"
                          :src="fileItem.url"
                          :preview-src-list="srcList"
                          @click="handleView(fileItem)"
                          fit="contain"
                      >
                      </el-image>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="school_name no_print">{{ labelTitle }}</div>
                <div class="school_input2 no_print">无</div>
              </template>
            </template>
          </template>
          <template v-if="allData.personnelType === $constants.RESIDENT_TYPE_SQ">
            <div class="school_infor">监护人居住证件信息</div>
            <div class="school_name">持有人姓名</div>
            <div class="school_input">{{ allData.resideHead || defaultValue }}</div>
            <div class="school_name">持有人身份证号</div>
            <div class="school_input1">{{ allData.resideHeadIdCard || defaultValue }}</div>
            <template v-if="parentsResidencePermit.visible">
              <div class="school_name">居住证签发日期</div>
              <div class="school_input">{{ allData.resideRegisterTime || defaultValue }}</div>
              <div class="school_name">持证人与儿童关系</div>
              <div class="school_input1">{{ selectDictLabel(relationOptions, allData.resideHeadRelation) || defaultValue }}</div>
              <div class="school_name">签发派出所</div>
              <div class="school_input">{{ allData.resideIssuePoliceStation || defaultValue }}</div>
              <div class="school_name">居住地址</div>
              <div class="school_input1">{{ allData.resideAddress || defaultValue }}</div>
              <template>
                {{ void (labelTitle = '居住证正面照片') }}
                <template v-if="fileList && fileList[$constants.RESIDENCE_PERMIT_FRONT] && fileList[$constants.RESIDENCE_PERMIT_FRONT].length > 0">
                  <div class="school_tr school_tr1 no_print">{{ labelTitle }}</div>
                  <div class="school_td school_td2 no_print">
                    <div class="cont_bottom" >
                      <div class="small_img" v-for="(fileItem, fileIndex) in fileList[$constants.RESIDENCE_PERMIT_FRONT]" :key="fileIndex">
                        <el-image
                            style="width: 100%; height: 100%"
                            :src="fileItem.url"
                            :preview-src-list="srcList"
                            @click="handleView(fileItem)"
                            fit="contain"
                        >
                        </el-image>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="school_name no_print">{{ labelTitle }}</div>
                  <div class="school_input2 no_print">无</div>
                </template>
              </template>
              <template>
                {{ void (labelTitle = '居住证反面照片') }}
                <template v-if="fileList && fileList[$constants.RESIDENCE_PERMIT_BACK] && fileList[$constants.RESIDENCE_PERMIT_BACK].length > 0">
                  <div class="school_tr school_tr1 no_print">{{ labelTitle }}</div>
                  <div class="school_td school_td2 no_print">
                    <div class="cont_bottom" >
                      <div class="small_img" v-for="(fileItem, fileIndex) in fileList[$constants.RESIDENCE_PERMIT_BACK]" :key="fileIndex">
                        <el-image
                            style="width: 100%; height: 100%"
                            :src="fileItem.url"
                            :preview-src-list="srcList"
                            @click="handleView(fileItem)"
                            fit="contain"
                        >
                        </el-image>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="school_name no_print">{{ labelTitle }}</div>
                  <div class="school_input2 no_print">无</div>
                </template>
              </template>
            </template>
          </template>
          <div class="school_infor">房产信息</div>
          <div class="school_name">片区内有无房产</div>
          <div :class="allData.houseHave === yesValue ? 'school_input' : 'school_input2'">{{ selectDictLabel(haveOrNoOptions, allData.houseHave) || defaultValue }}</div>
          <template v-if="allData.houseHave === yesValue">
            <div class="school_name">权利人姓名</div>
            <div class="school_input1">{{ allData.houseHead || defaultValue }}</div>
            <div class="school_name">权利人证件号码</div>
            <div class="school_input">{{ allData.houseHeadIdCard || defaultValue }}</div>
            <div class="school_name">房产证明类型</div>
            <div class="school_input1">{{ selectDictLabel(houseCertifyTypeOptions, allData.houseCertifyType) || defaultValue }}</div>
            <div class="school_name">房产证明取得时间</div>
            <div class="school_input">{{ allData.houseRegisterTime || defaultValue }}</div>
            <div class="school_name">房产证明编号</div>
            <div class="school_input1">{{ allData.houseCertifyNumber || defaultValue }}</div>
            <div class="school_name">房主与儿童关系</div>
            <div class="school_input">{{ selectDictLabel(relationOptions, allData.houseHeadRelation) || defaultValue }}</div>
            <div class="school_name">房产坐落</div>
            <div class="school_input1">{{ allData.houseAddress || defaultValue }}</div>
            <template>
              {{ void (labelTitle = '房产证明材料照片') }}
              <template v-if="fileList && fileList[$constants.HOUSE_PROPRIETARY_CERTIFICATE] && fileList[$constants.HOUSE_PROPRIETARY_CERTIFICATE].length > 0">
                <div class="school_tr school_tr1 no_print">{{ labelTitle }}</div>
                <div class="school_td school_td2 no_print">
                  <div class="cont_bottom">
                    <div class="small_img" v-for="(fileItem, fileIndex) in fileList[$constants.HOUSE_PROPRIETARY_CERTIFICATE]" :key="fileIndex">
                      <el-image
                          style="width: 100%; height: 100%"
                          :src="fileItem.url"
                          :preview-src-list="srcList"
                          @click="handleView(fileItem)"
                          fit="contain"
                      >
                      </el-image>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="school_name no_print">{{ labelTitle }}</div>
                <div class="school_input2 no_print">无</div>
              </template>
            </template>
          </template>
          <template v-if="allData.houseHave === noValue">
            <template v-if="houseNoRoomCertificate.visible">
              {{ void (labelTitle = '无房证明照片') }}
              <template v-if="fileList && fileList[$constants.NO_HOUSE_CERTIFICATE] && fileList[$constants.NO_HOUSE_CERTIFICATE].length > 0">
                <div class="school_tr school_tr1 no_print">{{ labelTitle }}</div>
                <div class="school_td school_td2 no_print">
                  <div class="cont_bottom" >
                    <div class="small_img" v-for="(fileItem, fileIndex) in fileList[$constants.NO_HOUSE_CERTIFICATE]" :key="fileIndex">
                      <el-image
                          style="width: 100%; height: 100%"
                          :src="fileItem.url"
                          :preview-src-list="srcList"
                          @click="handleView(fileItem)"
                          fit="contain"
                      >
                      </el-image>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="school_name no_print">{{ labelTitle }}</div>
                <div class="school_input2 no_print">无</div>
              </template>
            </template>
            <template v-if="houseRentRoomCertificate.visible">
              {{ void (labelTitle = '房屋租赁证明照片') }}
              <template v-if="fileList && fileList[$constants.HOUSE_LEASE_CERTIFICATE] && fileList[$constants.HOUSE_LEASE_CERTIFICATE].length > 0">
                <div class="school_tr school_tr1 no_print">{{ labelTitle }}</div>
                <div class="school_td school_td2 no_print">
                  <div class="cont_bottom">
                    <div class="small_img" v-for="(fileItem, fileIndex) in fileList[$constants.HOUSE_LEASE_CERTIFICATE]" :key="fileIndex">
                      <el-image
                          style="width: 100%; height: 100%"
                          :src="fileItem.url"
                          :preview-src-list="srcList"
                          @click="handleView(fileItem)"
                          fit="contain"
                      >
                      </el-image>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="school_name no_print">{{ labelTitle }}</div>
                <div class="school_input2 no_print">无</div>
              </template>
            </template>
            <template>
              {{ void (labelTitle = '其他证明材料照片') }}
              <template v-if="fileList && fileList[$constants.OTHER_CERTIFICATE] && fileList[$constants.OTHER_CERTIFICATE].length > 0">
                <div class="school_tr school_tr1 no_print">{{ labelTitle }}</div>
                <div class="school_td school_td2 no_print">
                  <div class="cont_bottom">
                    <div class="small_img" v-for="(fileItem, fileIndex) in fileList[$constants.OTHER_CERTIFICATE]" :key="fileIndex">
                      <el-image
                          style="width: 100%; height: 100%"
                          :src="fileItem.url"
                          :preview-src-list="srcList"
                          @click="handleView(fileItem)"
                          fit="contain"
                      >
                      </el-image>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="school_name no_print">{{ labelTitle }}</div>
                <div class="school_input2 no_print">无</div>
              </template>
            </template>
          </template>
          <template v-if="applyPolicyGuarantee.visible || applyPoverty.visible || applyManyChildren.visible">
            <div class="school_infor">申请入学信息</div>
            <template v-if="applyPolicyGuarantee.visible">
              <div class="school_name">是否为政策保障性入学儿童</div>
              <div :class="allData.policyGuarantee === yesValue ? 'school_input' : 'school_input2'">{{ selectDictLabel(yesOrNoOptions, allData.policyGuarantee) || defaultValue }}</div>
              <template v-if="allData.policyGuarantee === yesValue">
              <div class="school_name">政策保障性入学类型</div>
              <div class="school_input1">{{ selectDictLabel(policyBasedTypeOptions, allData.policyBasedType) || defaultValue }}</div>
              <template>
                {{ void (labelTitle = '政策保障性入学证明材料') }}
                <template v-if="fileList && fileList[$constants.POLICY_GUARANTEE] && fileList[$constants.POLICY_GUARANTEE].length > 0">
                  <div class="school_tr no_print" :class="fileList[$constants.POLICY_GUARANTEE].length > 6 ? 'school_tr2' : 'school_tr1'">{{ labelTitle }}</div>
                  <div class="school_td school_td2 no_print">
                    <div class="cont_bottom">
                      <div class="small_img" v-for="(fileItem, fileIndex) in fileList[$constants.POLICY_GUARANTEE]" :key="fileIndex">
                        <el-image
                            style="width: 100%; height: 100%"
                            :src="fileItem.url"
                            :preview-src-list="srcList"
                            @click="handleView(fileItem)"
                            fit="contain"
                        >
                        </el-image>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="school_name no_print">{{ labelTitle }}</div>
                  <div class="school_input2 no_print">无</div>
                </template>
              </template>
            </template>
            </template>
            <template v-if="applyPoverty.visible">
              <div class="school_name">是否选择建档立卡户登记</div>
              <div class="school_input2">{{ selectDictLabel(yesOrNoOptions, allData.filingCard) || defaultValue }}</div>
              <template v-if="allData.filingCard === yesValue">
              <template>
                {{ void (labelTitle = '扶贫手册及县扶贫办证明') }}
                <template v-if="fileList && fileList[$constants.POVERTY_ALLEVIATION] && fileList[$constants.POVERTY_ALLEVIATION].length > 0">
                  <div class="school_tr no_print" :class="fileList[$constants.POVERTY_ALLEVIATION].length > 6 ? 'school_tr2' : 'school_tr1'">{{ labelTitle }}</div>
                  <div class="school_td school_td2 no_print">
                    <div class="cont_bottom">
                      <div class="small_img" v-for="(fileItem, fileIndex) in fileList[$constants.POVERTY_ALLEVIATION]" :key="fileIndex">
                        <el-image
                            style="width: 100%; height: 100%"
                            :src="fileItem.url"
                            :preview-src-list="srcList"
                            @click="handleView(fileItem)"
                            fit="contain"
                        >
                        </el-image>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="school_name no_print">{{ labelTitle }}</div>
                  <div class="school_input2 no_print">无</div>
                </template>
              </template>
            </template>
            </template>
            <template v-if="applyManyChildren.visible">
              <div class="school_name">是否申请多子女登记</div>
              <div :class="allData.multipleChildren === yesValue ? 'school_input' : 'school_input2'">{{ selectDictLabel(yesOrNoOptions, allData.multipleChildren) || defaultValue }}</div>
              <template v-if="allData.multipleChildren === yesValue">
            <div class="school_name">长子女姓名</div>
            <div class="school_input1">{{ allData.eldestChildName || defaultValue }}</div>
            <div class="school_name">长子女所在学校</div>
            <div class="school_input">{{ allData.eldestChildSchoolName || defaultValue }}</div>
            <div class="school_name">长子女国网学籍号</div>
            <div class="school_input1">{{ allData.eldestChildStudentCode || defaultValue }}</div>
          </template>
            </template>
          </template>
        </div>

        <img class="printImg" ref="image" alt="转换后的图片" style="width: 1000px" />
      </div>
    </div>
    <Footbox class="foot_layout" />
    <foot />
  </div>
</template>

<script>
import top from '../../components/top/top.vue'
import foot from '../../components/foot/foot.vue'
import { getApplyInfo, getFormConfigList } from '@/api/apply'
import html2canvas from 'html2canvas';
import Topbox from "@/views/layout/newtop";
import Footbox from "@/views/layout/foot";
export default {
  components: {
    top,foot,
    Topbox,
    Footbox
  },
  data() {
    return {
      fits: ['cover'],
      url:[],
      srcList: [],
      allData: {},
      userPhoto:'',
      guardian1: {},
      guardian2: {},
      id: '',
      defaultValue: '-/-',
      yesValue: 'Y',
      noValue: 'N',
      genderOptions: [],
      yesOrNoOptions: [],
      nationOptions: [],
      statusOptions: [],
      cardTypeOptions: [],
      countryOptions: [],
      politicsStatusOptions: [],
      healthConditionOptions: [],
      disabilityTypeOptions: [],
      relationOptions: [],
      haveOrNoOptions: [],
      houseCertifyTypeOptions: [],
      policyBasedTypeOptions: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      fileShowList: [],
      fileList: {},
      fileClassifyList: [],
      infoPhoto:{visible: true},
      parentsResidencePermit	:{visible: true},
      houseNoRoomCertificate:{visible: true},
      houseRentRoomCertificate:{visible: true},
      applyPolicyGuarantee:{visible: true},
      applyPoverty	:{visible: true},
      applyManyChildren:{visible: true}
    }
  },
  mounted() {},
  beforeDestroy() {},
  created() {
    this.fileShowList = []
    this.getDicts('sys_user_sex').then(response => {
      this.genderOptions = response.data
    })
    this.getDicts('sys_nation').then(response => {
      this.nationOptions = response.data
    })
    this.getDicts('sys_yes_no').then(response => {
      this.yesOrNoOptions = response.data
    })
    this.getDicts('biz_apply_student_status').then(response => {
      this.statusOptions = response.data
    })
    this.getDicts('sys_card_type').then(response => {
      this.cardTypeOptions = response.data
    })
    this.getDicts('sys_country').then(response => {
      this.countryOptions = response.data
    })
    this.getDicts('sys_health_condition').then(response => {
      this.healthConditionOptions = response.data
    })
    this.getDicts('sys_disability_type').then(response => {
      this.disabilityTypeOptions = response.data
    })
    this.getDicts('sys_relation').then(response => {
      this.relationOptions = response.data
    })
    this.getDicts('sys_have_no').then(response => {
      this.haveOrNoOptions = response.data
    })
    this.getDicts('biz_house_certify_type').then(response => {
      this.houseCertifyTypeOptions = response.data
    })
    this.getDicts('biz_policy_based_type').then(response => {
      this.policyBasedTypeOptions = response.data
    })
    this.routerQuery = this.$route.query || {}
    this.id = this.routerQuery.id
    if (this.id) {
      this.getDataInfo(this.id)
    }
  },
  methods: {
    imageUrlToBase64(url) {
      let image = new Image();
      var _this = this;
      //解决跨域问题
      image.setAttribute('crossOrigin', 'anonymous');
      image.src = url
      image.onload = () => {
        const canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        const context = canvas.getContext('2d');
        context.fillStyle = 'white';
        context.fillRect(0, 0, canvas.width, canvas.height);
        context.drawImage(image, 0, 0, image.width, image.height);



        const quality = 0.8;
        //这里的dataurl就是base64类型
        _this.userPhoto = canvas.toDataURL("image/jpeg", quality);//使用toDataUrl将图片转换成jpeg的格式,不要把图片压缩成png，因为压缩成png后base64的字符串可能比不转换前的长！
      }
    },
    getDataInfo(id) {
      getApplyInfo(id).then(response => {
        const data = response.data || {}
        this.allData = data

        if (data.schoolId) {
          this.getFormConfigData(data.schoolId)
        }

        if (data.pictureUrl) {
          this.imageUrlToBase64(this.baseUrl+ data.pictureUrl)
        }
        // 回显附件
        const that = this
        const fileList = {}
        if (data.attachmentList && data.attachmentList.length) {
          for (let i = 0; i < data.attachmentList.length; i++) {
            const file = data.attachmentList[i]
            const fileData = {
              id: file.id,
              size: file.attachSize,
              name: file.attachName,
              url: that.baseUrl + file.fileUrl,
              path: file.fileUrl
            }
            let urlArr = fileList[file.businessType]
            if (!urlArr) {
              urlArr = []
            }
            urlArr.push(fileData)
            fileList[file.businessType] = urlArr
          }
        }
        that.fileList = fileList
      })
    },
    handleView(file) {
      this.srcList = []
      this.srcList.push(file.url)
    },
    print() {
      // 先移除不需要的div
      let elements = document.getElementsByClassName('no_print');
      if (elements && elements.length > 0) {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "none";
        }
      }
      let printArea = this.$refs.printArea;

      let image = this.$refs.image;
      html2canvas(printArea).then((canvas) => {
        image.src = canvas.toDataURL();
        image.onload = () => {
          let printWindow = window.open('', '_blank');
          printWindow.document.open();
          printWindow.document.write('<html><head><title>Print</title></head><body style="margin: 0; padding: 0;">');
          printWindow.document.write('<img src="' + image.src + '" style="width:100%;" onload="window.print();window.close();" />');
          printWindow.document.write('</body></html>');
          printWindow.document.close();
        };
      });

      // 转换完成后，恢复被移除的div
      if (elements && elements.length > 0) {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.removeProperty('display');
        }
      }

    },
    getFormConfigData(schoolId) {
      getFormConfigList({schoolId: schoolId}).then(response => {
        this.formConfigData = response.data || []
        this.formConfigData.forEach(item => {
          if (item.code === this.$constants.DICT_INFO_PHOTO) {
            this.infoPhoto.visible = item.visible === this.yesValue;
          }
          if (item.code === this.$constants.DICT_PARENTS_RESIDENCE_PERMIT) {
            this.parentsResidencePermit.visible = item.visible === this.yesValue;
          }
          if (item.code === this.$constants.DICT_HOUSE_NO_ROOM_CERTIFICATE) {
            this.houseNoRoomCertificate.visible = item.visible === this.yesValue;
          }
          if (item.code === this.$constants.DICT_HOUSE_RENT_ROOM_CERTIFICATE) {
            this.houseRentRoomCertificate.visible = item.visible === this.yesValue;
          }
          if (item.code === this.$constants.DICT_APPLY_POLICY_GUARANTEE) {
            this.applyPolicyGuarantee.visible = item.visible === this.yesValue;
          }
          if (item.code === this.$constants.DICT_APPLY_POVERTY) {
            this.applyPoverty.visible = item.visible === this.yesValue;
          }
          if (item.code === this.$constants.DICT_APPLY_MANY_CHILDREN) {
            this.applyManyChildren.visible = item.visible === this.yesValue;
          }
        })
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.printImg{
  display: none;
}
//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {
      .map_box_pc{
    display: block;
    display: flex;
    justify-content: space-between;
  }
  .map_box_yd{
    display: none!important;
  }
  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .choose_body {
      flex: 1;
      display: flex;
      justify-content: center;
      margin-top: 0px;
      .body_middle {
        width: 1200px;
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 10px 30px 1px rgba(93,132,177,0.11);
        border-radius: 0px 0px 16px 16px;
        .middle_title {
          width: 100%;
          padding: 33px 42px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .title_left {
            width: 50%;
            height: 29px;
            font-size: 22px;
            font-weight: bold;
            color: #3570f6;
          }
          .title_right {
            width: 60px;
            height: 21px;
            font-size: 16px;
            font-weight: 400;
            color: #5c5c66;
            display: flex;
            div{
              width: 32px;
              height: 21px;
              font-size: 16px;
              font-weight: 400;
              color: #5C5C66;
              line-height: 21px;
              cursor: pointer;
              &:hover {
                color: #0B7DFB;
              }
            }
            img {
              width: 18px;
              height: 21px;
              line-height: 21px;
              margin-right: 5px;
            }
          }
        }
        .middle_content {
          width: 1120px;
          height: auto;
          margin: 0 40px 40px 40px;
          border: 1px solid #d0d0d0;
          border-bottom: none;
          box-sizing: content-box;
          .school_infor {
            width: 100%;
            height: 57px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #f5f5f5;
            opacity: 1;
            border-bottom: 1px solid #d0d0d0;
            font-size: 22px;
            color: #383838;
          }
          .photo{
            width: 220px;
            height: 300px;
            float: right;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: center;
            align-items: center;
            img{
              width: 90%;
              max-width: 90%;
              max-height: 90%;
            }
          }
          .school_name{
            width: 150px;
            height: 60px;
            float: left;
            border-right: 1px solid #d0d0d0;
            border-bottom: 1px solid #d0d0d0;
            text-align: center;
            font-size: 18px;
            color: #888;
            word-wrap: break-word;
            word-break: break-all;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .school_input{
             width: 300px;
            height: 60px;
            float: left;
            border-right: 1px solid #d0d0d0;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding:5px 10px;
            color: #383838;
            word-wrap: break-word;
            word-break: break-all;
            input{
              width: 100%;
              height: 100%;
              border: none;
              background: none;
              font-size: 18px;
              outline: none;
              padding: 0 10px;
            }
          }
          .school_input1{
            width: 520px;
            height: 60px;
            float: left;
            border-right: none;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding:5px 10px;
            word-wrap: break-word;
            word-break: break-all;
            input{
              width: 100%;
              height: 100%;
              border: none;
              background: none;
              outline: none;
            }
          }
          .school_input2{
            width: 970px;
            height: 60px;
            float: left;
            border-right: none;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding:5px 10px;
            word-wrap: break-word;
            word-break: break-all;
            input{
              width: 100%;
              height: 100%;
              border: none;
              background: none;
              outline: none;
            }
          }
          .school_input3{
            width: 100%;
            height: 60px;
            float: left;
            border-right: none;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding:5px 10px;
            word-wrap: break-word;
            word-break: break-all;
            input{
              width: 100%;
              height: 100%;
              border: none;
              background: none;
              outline: none;
            }
          }
          .school_tr{
            width: 150px;
            height: auto;
            float: left;
            border-right: 1px solid #d0d0d0;
            border-bottom: 1px solid #d0d0d0;
            text-align: center;
            font-size: 18px;
            color: #888;
            word-wrap: break-word;
            word-break: break-all;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .school_tr1 {
            height: 141px !important;
          }
          .school_tr2 {
            height: 281px !important;
          }
          .school_td{
            width: 300px;
            height: auto;
            float: left;
            border-right: 1px solid #d0d0d0;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding:5px 10px;
            .cont_bottom{
              width: 100%;
              padding: 5px;
              display: flex;
              flex-wrap: wrap;
              gap: 20px;

              .small_img{
                width: 120px;
                height: 120px;
                ::v-deep .el-image{
                  img{
                    background: #fafafa!important;
                    border: 1px solid #f1f1f1;
                  }
                }
              }
            }
          }
          .school_td1 {
            width: 520px !important;
            border-right: none !important;
          }
          .school_td2 {
            width: 970px !important;
            border-right: none !important;
          }
        }
        .middle_cont{
          width: 100%;
          display: flex;
          flex-direction: column;
          .cont_top{
            width: 100%;
            height: 29px;
            font-size: 22px;
            font-weight: bold;
            color: #3570F6;
            margin: 27px 42px 0px 42px;
          }
        }
        .cont_bottom{
          width: 100%;
          padding: 20px 42px 42px 42px;
          display: flex;
          flex-wrap: wrap;
          gap: 20px;

          .small_img{
            width: calc(25% - 16px);
            height: 260px;
            ::v-deep .el-image{
              img{
                background: #fafafa!important;
                border: 1px solid #f1f1f1;
              }
            }
          }
        }
      }
    }
  }
  .choose_foot {
    width: 100%;
    height: 100px;
    opacity: 1;
    display: none;
  }
}
@media screen and (max-width: 720px) {
  .top_layout,.foot_layout{
    display: none;
  }
  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
  }
  .el-icon-arrow-down {
    font-size: 1rem;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;

    .choose_body {
      flex: 1;
      display: flex;
      justify-content: center;
      margin-top: 6rem;
      .body_middle {
        width: calc(100% - 3rem);
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 1rem 3rem 1px rgba(93,132,177,0.11);
        border-radius: 1.2rem;
        padding:0 1rem 1rem 1rem;
        .middle_title {
          width: 100%;
          padding: 1rem 0;
          display: flex;
          justify-content: center;
          align-items: center;
          .title_left {
            font-size: 1.4rem;
            font-weight: bold;
            color: #3570f6;
          }
          .title_right {
            display: none;
          }
        }
        .middle_content {
          width: 100%;
          height: auto;
          border: 1px solid #d0d0d0;
          border-bottom: none;
          box-sizing: content-box;
          display: flex;
          flex-wrap: wrap;
          .school_infor {
            width: 100%;
            height: auto;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #f5f5f5;
            opacity: 1;
            border-bottom: 1px solid #d0d0d0;
            font-size: 1.2rem;
            padding:0.5rem 0;
            color: #383838;
            font-weight: bold;
          }
          .photo{
            width: 100%;
            height: 15rem;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: center;
            align-items: center;
            img{
              width: 60%;
              max-width: 90%;
              max-height: 90%;
            }
          }
          .school_name{
            width: 8rem;
            height: auto;
            border-right: 1px solid #d0d0d0;
            border-bottom: 1px solid #d0d0d0;
            font-size: 1rem;
            color: #888;
            padding: 0.5rem 0;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
          }
          .school_input{
            width: calc(100% - 8rem);
            height: auto;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 0.5rem;
            color: #383838;
            font-size: 1rem;
            word-wrap: break-word;
            word-break: break-all;
          }
          .school_input1{
            width: calc(100% - 8rem);
            height: auto;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 0.5rem;
            color: #383838;
            font-size: 1rem;
            word-wrap: break-word;
            word-break: break-all;
          }
          .school_input2{
            width: calc(100% - 8rem);
            height: auto;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 0.5rem;
            color: #383838;
            font-size: 1rem;
            word-wrap: break-word;
            word-break: break-all;
          }
          .school_input3{
            width: 100%;
            height: auto;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 0.5rem;
            color: #383838;
            font-size: 1rem;
            word-wrap: break-word;
            word-break: break-all;
          }
          .school_tr{
            width: 8rem;
            height: auto;
            border-right: 1px solid #d0d0d0;
            border-bottom: 1px solid #d0d0d0;
            font-size: 1rem;
            color: #888;
            padding: 0.5rem 0;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
          }
          .school_td{
            width: calc(100% - 8rem);
            height: auto;
            border-bottom: 1px solid #d0d0d0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 0.5rem;
            color: #383838;
            font-size: 1rem;
            word-wrap: break-word;
            word-break: break-all;
          }
        }
        .middle_cont{
          width: 100%;
          display: flex;
          flex-direction: column;
          .cont_top{
            width: 100%;
            font-size: 1.4rem;
            padding:1rem 0 0.5rem 0;
            text-align: center;
            font-weight: bold;
            color: #3570F6;
          }
        }
        .cont_bottom{
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          padding:0 0 0 0;

          .small_img{
            width: calc(50% - 0.4rem);
            height: 9rem;
            margin-bottom:0.8rem;
            margin-right: 0.8rem;
            ::v-deep .el-image{
              img{
                background: #fafafa!important;
                border: 1px solid #f1f1f1;
              }
            }
          }
        }
      }
    }
  }
}
</style>
