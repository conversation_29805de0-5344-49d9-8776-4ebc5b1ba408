<template>
  <div class="choose">
    <Topbox class="top_layout" />
    <top />
    <div class="choose_body">
      <div class="body_middle">
        <div class="middle_title">
          <div class="title_left">录取通知书</div>
        </div>

        <div class="middle_content" ref="printArea">
          <div class="div_main">
            <div class="div_box">
              <div class="div_title">{{ allData.name }}</div>
              <div class="div_title_2">同学:</div>
            </div>
            <div class="div_cont">
              <div class="cont_1">根据<span class="cont_span">{{ allData.parentAreaName ? allData.parentAreaName : '' }}{{ allData.areaName ? allData.areaName : '' }}</span>小学新生招生有关政策，经审核、批准，你已经被 <span class="cont_span">{{ allData.schoolName ? allData.schoolName : '' }}</span> 录取为一年级新生。</div>
              <div class="cont_1">具体报名、注册等相关工作等待学校另行通知。</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Footbox class="foot_layout" />
    <foot />
  </div>
</template>

<script>
import top from '../../components/top/top.vue'
import foot from '../../components/foot/foot.vue'
import { getApplyInfo } from '@/api/apply'
import Topbox from "@/views/layout/newtop";
import Footbox from "@/views/layout/foot";
export default {
  components: {
    top,foot,
    Topbox,
    Footbox
  },
  data() {
    return {
      allData: {},
      id: '',
      defaultValue: '-/-',
    }
  },
  mounted() {},
  beforeDestroy() {},
  created() {
    this.routerQuery = this.$route.query || {}
    this.id = this.routerQuery.id
    if (this.id) {
      this.getDataInfo(this.id)
    }
  },
  methods: {
    getDataInfo(id) {
      getApplyInfo(id).then(response => {
        const data = response.data || {}
        this.allData = data
      })
    }
  },
}
</script>
<style lang="scss" scoped>
//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .choose_body {
      flex: 1;
      display: flex;
      justify-content: center;
      margin-top: 0px;
      .body_middle {
        width: 1200px;
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 10px 30px 1px rgba(93,132,177,0.11);
        border-radius: 0px 0px 16px 16px;
        .middle_title {
          width: 100%;
          padding: 33px 42px;
          display: flex;
          justify-content: center;
          align-items: center;
          text-align: center;
          .title_left {
            height: 50px;
            font-size: 40px;
            font-weight: bold;
            color: #3570f6;
          }
        }
        .middle_content {
          width: 1120px;
          height: auto;
          margin: 20px 40px 40px 40px;
          box-sizing: content-box;
          background-image: url('../../assets/img/zs_bg.png');
          background-size: 100% 100%;
          background-position: center;
          background-repeat: no-repeat;
          .div_main {
            font-family: "Helvetica", "Arial", sans-serif;
            padding: 20px 150px;
            font-size: 30px;
            margin-top: 160px;
            margin-bottom: 200px;
            .div_box {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              line-height: 50px;
              margin-top: 50px;
              .div_title {
                width: 240px;
                border-bottom: 3px solid #d0d0d0;
                text-align: center;
                margin: 0 15px 0 0;
                padding: 0 30px;
              }
              .div_title_2 {
                font-weight: bold;
              }
            }
            .div_cont {
              margin-top: 35px;
              line-height: 70px;
              .cont_1 {
                text-indent: 3em;
                word-break: break-all;
                font-weight: bold;
                .cont_span {
                  border-bottom: 3px solid #d0d0d0;
                  margin: 0 15px;
                  padding: 0 30px;
                  font-weight: lighter;
                }
              }
            }
          }
        }
      }
    }
  }
}
@media screen and (max-width: 720px) {
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;

    .choose_body {
      flex: 1;
      display: flex;
      justify-content: center;
      margin-top: 6rem;
      .body_middle {
        width: calc(100% - 3rem);
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 1rem 3rem 1px rgba(93,132,177,0.11);
        border-radius: 1.2rem;
        padding:0 1rem 1rem 1rem;
        .middle_title {
          width: 100%;
          padding: 1rem 0;
          display: flex;
          justify-content: center;
          align-items: center;
          .title_left {
            font-size: 1.4rem;
            font-weight: bold;
            color: #3570f6;
          }
        }
        .middle_content {
          width: 100%;
          height: auto;
          box-sizing: content-box;
          display: flex;
          flex-wrap: wrap;
          background-image: url('../../assets/img/zs_bg.png');
          background-size: 100% 100%;
          background-position: center;
          background-repeat: no-repeat;
          .div_main {
            font-family: "Helvetica", "Arial", sans-serif;
            padding: 1em 2.5em;
            font-size: 1em;
            margin-top: 3em;
            margin-bottom: 5em;
            .div_box {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              line-height: 1.2em;
              margin-top: 2.3em;
              .div_title {
                width: 6em;
                border-bottom: 3px solid #d0d0d0;
                text-align: center;
                margin: 0 1em 0 0;
                padding: 0 1em;
              }
              .div_title_2 {
                font-weight: bold;
              }
            }
            .div_cont {
              margin-top: 1em;
              line-height: 2em;
              .cont_1 {
                text-indent: 1.5em;
                word-break: break-all;
                font-weight: bold;
                .cont_span {
                  border-bottom: 3px solid #d0d0d0;
                  margin: 0 1em;
                  padding: 0 0.6em;
                  font-weight: lighter;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
