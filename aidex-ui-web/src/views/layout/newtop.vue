<template>
  <div class="top_box">
    <div class="top">
      <div class="top_box_t">
        <div class="logo">
          <a class="logo_img" href="https://www.smartedu.cn/"><img src="../../assets/index_logo.png"/></a>
          <a href="https://www.gs.smartedu.cn/"><strong>甘肃智慧教育平台</strong>SMART EDUCATION OF GANSU</a>
        </div>
        <div class="top_box_t_right">
          <div style="height: 35px;"></div>
<!--          <p v-if="!isToken" @click="tologin()">登录</p>-->
          <div class="top_icon" v-if="isToken">
            <span><img src="../../assets/mr_img.jpg" class="img"/>{{ userInfo.name }}</span>
            <el-dropdown>
              <span class="el-dropdown-link"><img src="../../assets/icon_grzx.svg"/>个人空间</span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item><div @click="tomessage">我的消息(<span>{{ messageTotal }}</span>)</div></el-dropdown-item>
                <el-dropdown-item><div @click="toapplication()">我的报名</div></el-dropdown-item>
                <el-dropdown-item><div @click="handlePassword">修改密码</div></el-dropdown-item>
                <el-dropdown-item><div @click="handleLogout">退出登录</div></el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span @click="handleLogout"><img src="../../assets/icon_tc.svg"/>退出登录</span>
          </div>
          <div class="top_box_t_nav">
            <a href="https://www.gs.smartedu.cn/">首页</a>
            <a href="http://basic.gs.smartedu.cn/">中小学智慧教育</a>
            <a href="http://vocational.gs.smartedu.cn/">智慧职教</a>
            <a href="https://higher.gs.smartedu.cn/portal?pageId=802485&websiteId=434022&wfwfid=196662">智慧高教</a>
            <a href="https://www.smartedu.cn/special/ServiceHall">服务大厅</a>
            <a href="https://www.smartedu.cn/">国家平台</a>
          </div>
        </div>
      </div>
    </div>

    <div class="top_box_b" style="display: none">
      <a href="https://basic.gs.smartedu.cn/">首页</a>
      <a href="https://www.gs.smartedu.cn/studyzyk/bank/searchHeaderGs?staid=28119&typeid=11612">课程教学</a>
      <a href="https://basic.gs.smartedu.cn/page/802490/show">竞赛活动</a>
      <a href="https://basic.gs.smartedu.cn/page/802932/show">课后服务</a>
      <a href="https://study.gsedu.cn/#/jspx">教师培训</a>
      <a href="https://study.gsedu.cn/#/dyxt">德育学堂</a>
      <a href="https://basic.gs.smartedu.cn/page/802933/show">资源推广</a>
      <a href="#" target="_blank" class="now">新生招录</a>
    </div>

  </div>
</template>

<script>
import {checkApplyState} from "@/api/apply";
import {logout} from "@/utils/logout";
import {userMessageList} from "@/api/msgSubject";

export default {
  name: "top",
  data() {
    return {
      isReloginState:false,
      isToken: false,
      messageTotal: 0,
      userInfo: {},
    }
  },
  created() {
    this.isToken = this.checkAuthToken();
    if (this.isToken) {
      this.userInfo = this.$store.getters.userInfo || {}
      this.loadData()
    }
  },
  methods: {
    loadData() {
      userMessageList({
        ...this.queryParam,
        readFlag: false,
      }).then((messageResponse) => {
        this.messageTotal = messageResponse.data.total
      })
    },
    tologin(){
      this.$router.push({path: "/login"})
    },
    tochoose(val = {}, type){
      const data = val || {}
      if (this.isToken) {
        checkApplyState({id: data.id}).then(response => {
          if (response && response.code === 200) {
            this.$router.push({path: "/choose", query: {type: type}})
          } else {
            this.$message.error("暂未开启报名!")
          }
        })
      } else {
        const that = this
        that.$confirm('请先登录', '系统提示', {
          confirmButtonText: '确定',
          cancelButtonText: '关闭',
          type: 'warning',
          showClose: false,
        }).then(() => {
          that.$router.replace('/login')
        }).catch(() => {});
      }
    },
    handleLogout() {
      logout()
    },
    tomessage() {
      this.$router.push({path: "/message"})
    },
    toapplication(){
      this.$router.push({path: "/application"})
    },
    handlePassword() {
      this.$router.push({path: "/password"})
    },

  }
};
</script>

<style lang="less" scoped>

.top_box {
  width: 100%;
  //height: 280px;
  height: 110px;
  background: url("../../assets/img/bg.png") no-repeat center 0;
.top{
  width: 100%;
  height: 110px;
  background: #0356cb;
  .top_box_t {
    width: 1200px;
    height: 110px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    .logo{
      width: 340px; height: 110px; display: flex; align-items: center; justify-content: flex-start; margin-right: 10%;
      .logo_img{
        margin: 0;
        img{
          width:auto; height: 60px; cursor: pointer;
        }
      }
      a{
        font-family: Helvetica Neue,Helvetica,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Arial,sans-serif !important;
        font-size: 14px;
        color: #fff;
        letter-spacing: 0;
        margin-left: 15px;
        cursor: pointer;
        text-decoration: none;
        strong{ width: 100%; font-size: 20px;letter-spacing: 7px; color: #fff; line-height: 30px;  cursor: pointer;}
      }
    }

    .top_box_t_right {
      height: 110px;
      display: flex;
      flex-flow: column;
      justify-content: flex-start;
      align-items: flex-end;
      .top_icon{
        width: auto;
        height: 40px;
        display: flex;
        align-items: center;
        margin-top: 15px;
        span{
          width: auto;
          padding-left: 15px;
          font-size: 14px;
          color: #fff;
          display: flex;
          align-items: center;
        }
        img{
          width: 17px; height: 17px; display: inline-block; margin-right: 3px;
        }
        .img{ width: 24px; height: 24px; border-radius: 24px;}
      }

      p{
        width: 83px;
        height: 33px;
        border: 1px solid #fff;
        color: #FFFFFF;
        border-radius: 32px;
        font-size: 16px;
        line-height: 33px;
        text-align: center;
        margin-top: 20px;
        text-decoration: none;
      }
      p:hover{ background: #fff; color: #0356cb; }



      .top_box_t_nav {
        margin: 14px 0 0 0;

        a {
          font-family: "Microsoft YaHei", "Helvetica Neue", "PingFang SC", sans-serif;
          letter-spacing: 0.04em;
          text-decoration: none;
          font-size: 18px;
          color: #fff;
          -webkit-font-smoothing: antialiased;
          cursor: pointer;
          margin: 0 0 0 25px;
        }
      }
    }
  }
}
  .top_box_b {
    width: 1200px;
    height: 60px;
    margin: 114px auto 0 auto;

    a {
      display: inline-block;
      margin-right: 45px;
      height: 48px;
      font-size: 20px;
      font-family: "SF Pro Display", "Helvetica Neue", Helvetica, Tahoma, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
      -webkit-font-smoothing: antialiased;
      cursor: pointer;
      text-decoration: none;
      color: #000000;
      &.now {
        border-bottom: 2px solid #0076FF;
      }
    }
  }

}
</style>
