<template>
  <div class="footbox">
    <div class="foot">
      <div class="logo">
        <a class="logo_img" href="https://www.smartedu.cn/"><img src="../../assets/index_logo.png"/></a>
        <a href="https://www.gs.smartedu.cn"><strong>甘肃智慧教育平台</strong>SMART EDUCATION OF GANSU</a>
      </div>
    <p><span>指导单位：甘肃省教育厅</span>
      <span>主办单位：甘肃省电化教育中心</span>
      <span>地&emsp;&emsp;址：兰州市南滨河东路571号教育大厦</span></p>
    <p><span>网站声明</span>
      <a href="http://beian.miit.gov.cn/" target="_blank">陇ICP备10001022号-8</a>
      <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=62010202004058" target="_blank">甘公网安备 62010202004058号</a></p>
    </div>
<!--    <div v-if="yincang">技术支持：151****4657</div>-->
<!--    <div @click="exit()">甘肃省教育厅 版权所有</div>-->
<!--    <div v-if="yincang"><a href="http://************:8091"><img src="../assets/img/icon5.svg" />管理后台</a></div>-->
  </div>
</template>

<script>
export default {
  name: "foot",
  data(){
    return{
      yincang:0,
    }
  },
  methods:{
    exit(){
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/index';
        })
      }).catch(() => {});

    },
  }
};
</script>

<style lang="less" scoped>
.footbox{
 width: 100%;
  height:130px;
  background: #013fce;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  position: relative;
  z-index: 8;
  margin-top: 30px;
  .foot{
    width: 1200px;
    height: 130px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .logo{
      width: 340px; height: 110px; display: flex; align-items: center; justify-content: flex-start; margin-right: 10%;
      .logo_img{
        margin: 0;
        img{
          width:auto; height: 60px; cursor: pointer;
        }
      }
      a{
        font-family: Helvetica Neue,Helvetica,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Arial,sans-serif !important;
        font-size: 14px;
        color: #fff;
        letter-spacing: 0;
        margin-left: 15px;
        cursor: pointer;
        text-decoration: none;
        strong{ width: 100%; font-size: 20px;letter-spacing: 7px; color: #fff; line-height: 30px;  cursor: pointer;}
      }
    }

    p{
      width: 350px;
      height: 130px;
      display: block;
      padding: 28px 0;
      span{
        width: 100%;
        height: 25px;
        line-height: 25px;
        font-size: 14px;
        color: #fff;
        display: block;
      }
      a{
        font-family: Helvetica Neue,Helvetica,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Arial,sans-serif !important;
        font-size: 14px;
        color: #fff;
        text-decoration: none;
        display: block;
        height: 25px;
        line-height: 25px;
        cursor: pointer;
        &:hover{
          text-decoration: underline;
        }
      }
    }
  }

  //div:first-child{
  //  width: 225px;
  //  padding:0 0 0 25px;
  //}
  //div:last-child{
  //  width: 225px;
  //  text-align: right;
  //  padding:0 25px 0 0;
  //  color: #4A9EF5;
  //  a{
  //    color: #4A9EF5;
  //    cursor: pointer;
  //  }
  //  img{
  //    height: 20px;
  //    margin-right: 10px;
  //  }
  //}
}
</style>
