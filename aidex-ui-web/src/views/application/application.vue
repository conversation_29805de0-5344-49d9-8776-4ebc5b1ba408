<template>
  <div class="choose">
    <Topbox class="top_layout" /><!--顶部-->
    <top />
    <div class="choose_body">
      <div class="body_middle">
        <div class="middle_infor">
          <div class="middle_title">我的报名信息</div>
          <div class="middle_tab">
            <a @click="handleTabsClick('tab1')" :class="{'now': currentTab === 'tab1'}">小学报名</a>
<!--            <a @click="handleTabsClick('tab2')" :class="{'now': currentTab === 'tab2'}">初中报名</a>-->
          </div>
          <div class="middle_btn" @click="handleAdd"><i class="el-icon-circle-plus-outline"></i>新增报名</div>
        </div>
        <div class="tabBox" v-if="currentTab === 'tab1'">
          <div class="big_infor" v-if="dataList && dataList.length > 0">
            <div class="infor_box" v-for="(item, index) in dataList" :key="index">
<!--              <div class="inforImg">-->
<!--                <img :src="baseUrl + item.pictureUrl" alt="" />-->
<!--              </div>-->
              <div class="inforCon">
                <div class="inforName">{{ item.name || '-/-' }}<i v-show="item.gender == '1'" class="el-icon-male"></i><i v-show="item.gender == '2'" class="el-icon-female"></i></div>
                <div class="inforContent"><a>报名学校：</a>{{ item.schoolName || '-/-' }}</div>
                <div class="inforContent"><a>生源类型：</a>{{ item.sourceTypeName || '-/-' }}</div>
                <div class="inforContent"><a>报名时间：</a>{{ item.applyTime || '-/-' }}</div>
                <div class="inforContent"><a>报名状态：</a><span :class="addclass(item.status)">{{ selectDictLabel(status, item.status) }}</span></div>
              </div>
              <div class="inforBtn">
                <a @click="handleApply(item)" v-if="item.status === '0'"><i class="el-icon-circle-check"></i>提交报名</a>
                <a @click="handleEdit(item)" v-if="item.status === '0' || item.status === '2'"><i class="el-icon-edit-outline"></i>编辑修改</a>
                <a @click="handleDetail(item)" v-if="item.status !== '9'"><i class="el-icon-document"></i>入学申请表</a>
                <a @click="handleAdviceNote(item)" v-if="item.status === '9'"><i class="el-icon-tickets"></i>录取通知书</a>
<!--                <a @click="handlePerfect(item)" v-if="item.status === '9' && item.basicDataState === '0'"><i class="el-icon-edit"></i>完善信息</a>-->
              </div>
<!--              <div class="tips" v-if="item.status === '2' || item.status === '4'">-->
<!--                <a class="el-icon-warning">审核意见：</a><span>{{ item.auditOpinion || '-/-' }}</span>-->
<!--              </div>-->
            </div>
          </div>
          <div v-else>
            <el-empty description="暂无数据"></el-empty>
          </div>
        </div>
        <div class="tabBox" v-if="currentTab === 'tab2'">
          <div class="big_infor" v-if="dataList && dataList.length > 0">
            <div class="infor_box" v-for="(item, index) in dataList" :key="index">
              <div class="inforImg">
                <img :src="baseUrl + item.pictureUrl" alt="" />
              </div>
              <div class="inforCon">
                <div class="inforName">{{ item.name || '-/-' }}<i v-show="item.gender == '0'" class="el-icon-male"></i><i v-show="item.gender == '1'" class="el-icon-female"></i></div>
                <div class="inforContent"><a>报名学校：</a>{{ item.schoolName || '-/-' }}</div>
                <div class="inforContent"><a>报名时间：</a>{{ item.applyTime || '-/-' }}</div>
                <div class="inforContent"><a>报名状态：</a><span :class="addclass(item.status)">{{ selectDictLabel(status, item.status) }}</span></div>
              </div>
              <div class="inforBtn">
                <a @click="handleApply(item)" v-if="item.status === '0'"><i class="el-icon-circle-check"></i>提交报名</a>
                <a @click="handleEdit(item)" v-if="item.status === '0' || item.status === '2' || item.status === '4'"><i class="el-icon-edit-outline"></i>编辑修改</a>
                <a @click="handleDetail(item)" v-if="item.status !== '9'"><i class="el-icon-document"></i>入学申请表</a>
                <a @click="handleAdviceNote(item)" v-if="item.status === '9'"><i class="el-icon-tickets"></i>录取通知书</a>
<!--                <a @click="handlePerfect(item)" v-if="item.status === '9' && item.basicDataState === '0'"><i class="el-icon-edit"></i>完善信息</a>-->
              </div>
              <div class="tips" v-if="item.status === '2' || item.status === '4'">
                <a class="el-icon-warning">审核意见：</a><span>{{ item.auditOpinion || '-/-' }}</span>
              </div>
            </div>
          </div>
          <div v-else>
            <el-empty description="暂无数据"></el-empty>
          </div>
        </div>

      </div>
    </div>

    <Footbox class="foot_layout" />
    <foot />
  </div>
</template>

<script>
import top from '../../components/top/top.vue'
import foot from '../../components/foot/foot.vue'
import { getApplyStudentList, submitApply } from '@/api/apply'
import Topbox from "@/views/layout/newtop";
import Footbox from "@/views/layout/foot";
export default {
  components: {
    top,foot,
    Topbox,
    Footbox
  },
  data() {
    return {
      dataList: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      status:[],
      currentTab:'tab1',
      routerQuery: {},
    }
  },
  mounted() {},
  beforeDestroy() {},
  created() {
    this.routerQuery = this.$commonUtils.getApplyParams(this.$constants.STORE_CACHE_KEY_APPLY) || {}
    this.getList()
    this.getDicts('biz_apply_student_status').then(response => {
      this.status = response.data
    })
  },
  methods: {
    getList() {
      let type = '-1'
      if (this.currentTab === 'tab1') {
        type = this.$constants.SCHOOL_XX
      } else if (this.currentTab === 'tab2') {
        type = this.$constants.SCHOOL_CZ
      }
      const data = {
        applyType: type
      }
      getApplyStudentList(data).then(response => {
        this.dataList = response.data || []
      })
    },
    handleApply(val) {
      const that = this
      that.$confirm('我承诺，如实填写登记信息，核实无误后提交，若因信息不明确或填报虚假信息造成的一切后果，由本人承担。', '提示', {
        confirmButtonText: '提交',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      }).then(() => {
        submitApply({id: val.id}).then(response => {
          that.$message.success("操作成功")
          that.getList()
        })
      }).catch(() => {});
    },
    handleDetail(val) {
      this.$router.push({path: "/Information", query: {id: val.id}})
    },
    handleAdviceNote(val) {
      this.$router.push({path: "/adviceNote", query: {id: val.id}})
    },
    handleEdit(val) {
      this.routerQuery.id = val.id
      this.routerQuery.operate = 'edit'
      this.$commonUtils.setApplyParams(this.routerQuery, this.$constants.STORE_CACHE_KEY_APPLY)
      let path = "/submit"
      if (val.dataType === '2') {
        path = "/policySubmit"
      }
      this.$router.push({path: path})
    },
    handleAdd() {
      const data = {
        cityId: this.routerQuery.cityId,
        cityName: this.routerQuery.cityName,
        areaId: this.routerQuery.areaId,
        areaName: this.routerQuery.areaName
      }
      this.$commonUtils.setApplyParams(data, this.$constants.STORE_CACHE_KEY_APPLY)
      this.$router.push({path: "/index"})
    },
    addclass(i){
      switch (i){
        case('0'):
        case('1'):
          return 'color1';
        case('2'):
        case('4'):
        case('6'):
          return 'color2';
        case('3'):
        case('5'):
          return 'color3';
        case('9'):
          return 'color4';
      }
    },
    handlePerfect(val) {
      this.routerQuery.id = val.id
      this.$commonUtils.setApplyParams(this.routerQuery, this.$constants.STORE_CACHE_KEY_APPLY)
      this.$router.push({path: "/perfectSubmit"})
    },
    handleTabsClick(tab) {
      this.currentTab = tab
      this.dataList = []
      this.getList()
    }
  },
}
</script>
<style lang="scss" scoped>
//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {
    .map_box_pc{
    display: block;
    display: flex;
    justify-content: space-between;
  }
  .map_box_yd{
    display: none!important;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;

    .choose_body {
      flex: 1;
      display: flex;
      justify-content: center;
      margin-top: 0px;
      .body_middle {
        width: 1200px;
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 10px 30px 1px rgba(93, 132, 177, 0.11);
        border-radius: 0 0 16px 16px;
        .middle_infor {
          width: auto;
          margin: 33px 42px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .middle_title{
            font-size: 22px;
            font-weight: bold;
            color: #3570f6;
            white-space: nowrap;
          }
          .middle_tab{
            display: flex;
            width: 100%;
            padding:0 0 0 50px;
            gap: 25px;
            a{
              display: block;
              font-size: 18px;
              color: #383838;
              cursor: pointer;
              transition: ease-in-out all .2s;
              &::after{
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                bottom: -5px;
                height: 3px;
                background: #3570f6;
                border-radius: 100px;
                transition: ease-in-out all .2s;
                opacity: 0;
                transform: translateY(5px);
              }
              &.now{
                color: #3570f6;
                font-weight: bold;
                position: relative;
                transform: scale(1.05);
                &::after{
                  content: "";
                  position: absolute;
                  left: 0;
                  right: 0;
                  bottom: -5px;
                  height: 3px;
                  background: #3570f6;
                  border-radius: 100px;
                  opacity: 1;
                  transform: translateY(0);
                }
              }
            }
          }
          .middle_btn{
            white-space: nowrap;
            background: #3570f6;
            color: #ffffff;
            font-weight: bold;
            display: flex;
            justify-content: center;
            align-items: center;
            padding:12px 25px;
            border-radius: 1000px;
            box-shadow: 0 5px 10px #3570f690;
            transition: ease-in-out all .2s;
            cursor: pointer;
            i{
              margin-right: 10px;
            }
            &:hover{
              box-shadow: 0 1px 1px #3570f6;
              transform: translateY(2px);
            }
          }
        }
        .tabBox{
          width: 100%;
          height: auto;
        }
        .big_infor {
          width: auto;
          min-height: 450px;
          display: flex;
          flex-direction: column;
        }
        .infor_box {
          width: 1117px;
          height: auto;
          background: #fafafa;
          border-radius: 6px;
          border: 1px solid #f1f1f1;
          margin: 0 42px;
          margin-bottom: 20px;
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          flex-wrap: wrap;
          padding: 16px 0 16px 16px;
          position: relative;
          .inforImg{
            width: 120px;
            height: 150px;
            background: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            padding:5px;
            border-radius: 3px;
            border: 1px solid #e1e1e1;
            img {
              max-width: 108px;
              max-height: 138px;
            }
          }
          .inforCon{
            width:calc(100% - 280px);
            min-height: 150px;
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            flex-flow: column;
            padding:0 0 0 15px;
            .inforName{
              width: 100%;
              font-size: 20px;
              font-weight: bold;
              color: #000000;
              margin-bottom: 5px;
              display: flex;
              align-items: center;
              justify-content: flex-start;
              i{
                font-size: 18px;
                font-weight: bold;
                margin-left: 10px;
              }
              .el-icon-male{
                color: #1C65EF;
              }
              .el-icon-female{
                color: #E3378D;
              }
            }
            .inforContent{
              width: 100%;
              line-height: 20px;
              font-size: 16px;
              color: #222;
              padding:4px 0;
              a{
                color: #666;
              }
              span{
                font-size: 14px;
                padding:2px 12px;
                border-radius: 100px;
              }
              span.color1{
                background: #d1d1d1;
                color: #333;
              }
              span.color2{
                background: #FFC4C488;
                color: #BF2626;
              }
              span.color3{
                background: #C1D6FF80;
                color: #1C65EF;
              }
              span.color4{
                background: #B6EDB380;
                color: #32A32D;
              }
            }
          }
          .inforBtn{
            width: 160px;
            height: 150px;
            display: flex;
            justify-content:center;
            align-items: flex-end;
            flex-flow: column;
            gap:10px;
            a{
              display: flex;
              justify-content:center;
              align-items: center;
              font-size: 16px;
              cursor: pointer;
              color: #3570f6;
              background: #ffffff;
              padding:8px 12px 8px 14px;
              border: 1px solid #f1f1f1;
              border-right: none;
              border-radius:100px 0 0 100px;
              transition: ease-in-out all .2s;
              i{
                font-size: 22px;
                margin-right: 5px;
              }
              &:hover{
                padding:8px 16px 8px 14px;
                color: #ffffff;
                background: #3570f6;
              }
            }
          }
          .tips{
            width: calc(100% - 16px);
            height: auto;
            color: #BF2626;
            font-size: 16px;
            background: #ff330011;
            padding: 10px 15px;
            border-radius: 8px;
            word-wrap: break-word;
            word-break: break-all;
            margin-top: 10px;
          }
          .infor_right_wenzi {
            display: none;
          }
        }
      }
      .border_top {
        width: 95%;
        margin: 0 2.5%;
        height: 7px;
        background: #f1f1f1;
        border-radius: 12px 12px 12px 12px;
        opacity: 1;
      }
      .button {
        width: 100%;
        padding: 28px 391px;
        div {
          height: 59px;
          line-height: 59px;
          background: #3570f6;
          border-radius: 10px 10px 10px 10px;
          opacity: 1;
          text-align: center;
          color: #fff;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 18px;
          font-weight: 400;
          i {
            width: 23px;
            height: 59px;
            line-height: 59px;
            font-size: 22px;
            margin-right: 5px;
          }
        }
      }
    }
    .choose_foot {
      width: 100%;
      height: 100px;
      opacity: 1;
      display: none;
    }
  }
}
// 移动
@media screen and (max-width: 720px) {
  .top_layout,.foot_layout{
    display: none;
  }
  .map_box_pc{
    display: none;
  }
  .map_box_yd{
    display: block;
    display: flex;
    justify-content: space-around;
  }
  .choose {
    width: 100%;
    //min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    margin-top: 5.8rem;
    z-index: 1;
    .choose_body {
      width: calc(100% - 2rem);
      margin-left: 1rem;
      display: flex;
      justify-content: center;
      margin-top: 0.2rem;
      .body_middle {
        width: 100%;
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 0.6rem 1.2rem rgba(93, 132, 177, 0.11);
        border-radius: 0.8rem;
        padding-bottom: 1rem;
        .middle_infor {
          width: auto;
          padding: 1rem 1.2rem 5rem 1.2rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          position: relative;
          .middle_title{
            font-size: 1.4rem;
            font-weight: bold;
            color: #3570f6;
            white-space: nowrap;
          }
          .middle_tab{
            position: absolute;
            left: 0;
            bottom:1.4rem;
            display: flex;
            justify-content: center;
            width: 100%;
            padding:0 1rem;
            gap: 0;
            a{
              display: block;
              width: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
              font-size: 1rem;
              color: #383838;
              cursor: pointer;
              background: #fafafa;
              border: 1px solid #f1f1f1;
              padding:0.6rem 0;
              &:nth-child(1){
                border-radius: 100px 0 0 100px;
                border-right: none;
              }
              &:nth-child(2){
                border-radius: 0 100px 100px 0;
                border-left: none;
              }
              &.now{
                background: #3570f6;
                color: #ffffff;
                font-weight: bold;
                border: 1px solid #3570f670;
              }
            }
          }
          .middle_btn{
            white-space: nowrap;
            color: #3570f6;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.2rem;
            i{
              margin-right: 0.4rem;
            }
          }
        }
        .big_infor {
          width: auto;
          display: flex;
          flex-direction: column;
        }
        .infor_box {
          width: calc(100% - 2rem);
          margin-left: 1rem;
          height: auto;
          background: #fafafa;
          border-radius: 0.4rem;
          border: 1px solid #f1f1f1;
          display: flex;
          flex-wrap: wrap;
          padding: 0.8rem;
          margin-bottom: 1rem;
          .inforImg{
            width: 7rem;
            height: 9rem;
            background: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            padding:0.3rem;
            border-radius: 0.2rem;
            border: 1px solid #e1e1e1;
            img {
              max-width: 6.4rem;
              max-height: 8.4rem;
            }
          }
          .inforCon{
            width:calc(100% - 7rem);
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            flex-flow: column;
            padding:0 0 0 0.8rem;
            .inforName{
              width: 100%;
              font-size: 1.3rem;
              font-weight: bold;
              color: #000000;
              margin-bottom: 0.3rem;
              display: flex;
              align-items: center;
              justify-content: flex-start;
              i{
                font-size: 1.4rem;
                font-weight: bold;
                margin-left: 0.5rem;
              }
              .el-icon-male{
                color: #1C65EF;
              }
              .el-icon-female{
                color: #E3378D;
              }
            }
            .inforContent{
              width: 100%;
              line-height: 1.2rem;
              font-size: 1rem;
              color: #222;
              padding:0.2rem 0;
              a{
                color: #666;
              }
              span{
                font-size: 0.8rem;
                padding:0.1rem 0.6rem;
                border-radius: 100px;
              }
              span.color1{
                background: #d1d1d1;
                color: #333;
              }
              span.color2{
                background: #FFC4C488;
                color: #BF2626;
              }
              span.color3{
                background: #C1D6FF80;
                color: #1C65EF;
              }
              span.color4{
                background: #B6EDB380;
                color: #32A32D;
              }
            }
          }
          .inforBtn{
            width: 100%;
            height: auto;
            display: flex;
            justify-content:center;
            align-items: center;
            background: #ffffff;
            border: 1px solid #f1f1f1;
            border-radius: 0.2rem;
            margin-top: 0.5rem;
            a{
              display: flex;
              width: 33.3%;
              justify-content:center;
              align-items: center;
              flex-flow: column;
              font-size: 0.9rem;
              cursor: pointer;
              color: #3570f6;
              padding:0.5rem 0;
              border-right: none;
              transition: ease-in-out all .2s;
              i{
                font-size: 1.4rem;
              }
            }
          }
          .tips{
            width: 100%;
            height: auto;
            color: #BF2626;
            font-size: 0.9rem;
            background: #ff330011;
            padding: 0.5rem 0.8rem;
            border-radius: 0.2rem;
            word-wrap: break-word;
            word-break: break-all;
            margin-top: 0.5rem;
          }
          .infor_right_wenzi {
            display: none;
          }
        }
      }
      .border_top {
        display: none;
      }
      .button {
        width: calc(100% - 2rem);
        div {
          height: 3rem;
          background: #3570f6;
          border-radius: 10px;
          color: #fff;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 1.2rem;
          box-sizing: content-box;
          transform: translateX(1rem);
          i {
            font-size: 1.3rem;
            margin-right: 0.5rem;
          }
        }
      }
    }
    .choose_foot {
      width: 100%;
      height: auto;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 1.5rem 1.2rem;
    }
  }
}
</style>
