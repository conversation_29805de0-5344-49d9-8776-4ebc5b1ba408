<template>
  <div class="home">
    <Topbox class="top_layout" /><!--顶部-->
    <top />

    <div class="main_box">
      <div class="className"><span>我的消息</span><a @click="handleAllRead" v-if="dataList && dataList.length > 0">全部标记为已读</a></div>
      <div class="list_box" v-if="dataList && dataList.length > 0">
        <ul>
          <li :class="item.readFlag ? 'read' : 'unread'" v-for="(item, index) in dataList" :key="index" @click="handleClick(item)">
            <p>{{ item.readFlag ? '已读' : '未读' }}</p>
            <a>{{ item.subject }}</a>
            <span>{{ parseTime(item.sendDatetime, '{y}-{m}-{d}') }}</span>
          </li>
        </ul>
      </div>
      <div class="list_box" v-else>
        <el-empty description="暂无内容"></el-empty>
      </div>
    </div>
    <Footbox class="foot_layout" />
    <foot />
  </div>
</template>

<script>
import top from '../../components/top/top.vue'
import foot from '../../components/foot/foot.vue'
import { userMessageList, setRead, setUserMessageAllRead } from '@/api/msgSubject'
import Topbox from "@/views/layout/newtop";
import Footbox from "@/views/layout/foot";
export default {
  components: {
    top,foot,
    Topbox,
    Footbox
  },
  data() {
    return {
      // 查询参数
      queryParam: {
        pageNum: 1,
        pageSize: 10,
      },
      // 数据集合
      dataList: [],
      // 总条数
      total: 0
    }
  },
  mounted() {},
  beforeDestroy() {},
  created() {},
  watch: {
    $route: {
      handler: function(route) {
        this.initData()
      },
      immediate: true
    }
  },
  methods: {
    initData() {
      this.getList()
    },
    getList() {
      userMessageList(this.queryParam).then((messageResponse) => {
        this.dataList = messageResponse.data.list
        this.total = messageResponse.data.total
      })
    },
    handleClick(val){
      this.setMsgRead(val)
      this.$router.push({path: "/messageDetail", query: {id: val.id}})
    },
    setMsgRead (row = {}) {
      const that = this
      const msgSubjectIds = row.id
      setRead(msgSubjectIds).then(() => {
        that.getList()
      })
    },
    handleAllRead() {
      const that = this
      that.$confirm('确定全部标记为已读吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        setUserMessageAllRead().then(response => {
          that.$message.success("操作成功")
          that.getList()
        })
      }).catch(() => {});
    }
  },
}
</script>
<style lang="scss" scoped>
//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {

  .home {
    width: 100%;
    display: flex;
    flex-direction: column;
    background: #eef6ff;

    .main_box{
      width: 1200px;
      height: auto;
      background: rgba(255, 255, 255, 0.62);
      box-shadow: 0px 10px 30px rgba(93, 132, 177, 0.11);
      border-radius: 0 0 16px 16px;
      padding:40px 45px;
      margin-top: 0px;
      margin-left: 50%;
      transform: translateX(-50%);
      .className{
        width: 100%;
        height: auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        span{
          font-size: 28px;
          font-weight: bold;
          color: #383838;
        }
        a{
          color: #3570F6;
          font-size: 16px;
          border: 1px solid #3570F6;
          border-radius: 8px;
          padding:4px 10px;
          cursor: pointer;
          &:hover{
            background: #3570F6;
            color: #fff;
          }
        }
      }
      .list_box{
        width: 100%;
        height: auto;
        min-height: 450px;
        padding:15px 0;
        li{
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          padding:15px 0 15px 0;
          border-bottom: 1px dotted #ccc;
          cursor: pointer;
          background-size: 14px auto;
          &.read{
            p{
              color:#0B7DFB;
              border: 1px solid #0B7DFB;
              background: #0B7DFB10;
            }
          }
          &.unread{
            p{
              color:#ff5a00;
              border: 1px solid #ff5a00;
              background: #ff5a0010;
            }
          }
          p{
            white-space: nowrap;
            font-size: 14px;
            width: 50px;
            text-align: center;
            height: 26px;
            line-height: 26px;
            border-radius: 6px;
            margin-right: 10px;
          }
          a{
            width: calc(100% - 260px);
            font-size: 18px;
            text-decoration: none;
            color: #383838;
            cursor: pointer;
          }
          span{
            white-space: nowrap;
            font-size: 18px;
            color: #999;
            cursor: pointer;
            margin-left: 10px;
          }
          &:hover{
            a{
              color: #3570F6;
            }
          }
        }
      }
    }
    .choose_foot {
      display: none;
    }
  }
}
//移动端
@media screen and (max-width: 720px) {
  .top_layout,.foot_layout{
    display: none;
  }
  .home {
    width: 100%;
    display: flex;
    flex-direction: column;
    background: #eef6ff;

    .main_box{
      width: calc(100% - 2rem);
      height: auto;
      background: rgba(255, 255, 255, 0.62);
      box-shadow: 0px 0.5rem 1rem rgba(93, 132, 177, 0.11);
      border-radius: 0.8rem;
      padding:1.5rem;
      margin-top: 30px;
      margin-left: 50%;
      margin-top: 6rem;
      transform: translateX(-50%);
      .className{
        width: 100%;
        height: auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        span{
          font-size: 1.6rem;
          font-weight: bold;
          color: #383838;
        }
        a{
          color: #3570F6;
          font-size: 1rem;
          border: 1px solid #3570F6;
          border-radius: 0.3rem;
          padding:0.2rem 0.6rem;
          cursor: pointer;
        }
      }
      .list_box{
        width: 100%;
        height: auto;
        li{
          display: flex;
          flex-flow: column;
          width: 100%;
          padding:1rem 1rem 1rem 1rem;
          cursor: pointer;
          background-size: 0.8rem auto;
          margin-top: 1rem;
          border: 1px solid #DCEBFA;
          border-radius: 0.6rem;
          &.read{
            p{
              color:#0B7DFB;
              border: 1px solid #0B7DFB;
              background: #0B7DFB10;
            }
          }
          &.unread{
            p{
              color:#ff5a00;
              border: 1px solid #ff5a00;
              background: #ff5a0010;
            }
          }
          p{
            font-size: 1rem;
            text-align: center;
            width: 3.8rem;
            padding:0.1rem 0;
            border-radius: 0.3rem;
            margin-bottom: 0.4rem;
          }
          a{
            width: 100%;
            font-size: 1.2rem;
            text-decoration: none;
            color: #000000;
            cursor: pointer;
          }
          span{
            white-space: nowrap;
            font-size: 1.2rem;
            color: #999;
            cursor: pointer;
            margin-top: 0.4rem;
          }
        }
      }
    }


  }

}
</style>
