<template>
  <div class="choose">
    <Topbox class="top_layout" /><!--顶部-->
    <top />
    <div class="choose_body">
      <div class="body_middle">
        <div class="changePasswordBox">
          <div class="title">修改密码</div>
          <div class="center_box">
            <el-form
                :model="ruleForm"
                status-icon
                :rules="rules"
                ref="ruleForm"
                class="demo-ruleForm"
            >
              <el-form-item prop="username">
                <div class="formList">
                  <el-input placeholder="用户名" v-model="userInfo.name" :readonly="true">
                    <template slot="prepend"><div class="prepend_label">用户名</div></template>
                  </el-input>
                </div>
              </el-form-item>
              <el-form-item prop="oldPassword">
                <div class="formList">
                  <el-input type="password" placeholder="请输入原密码" show-password v-model="ruleForm.oldPassword">
                    <template slot="prepend"><div class="prepend_label">原密码<span class="prepend_span">*</span></div></template>
                  </el-input>
                </div>
              </el-form-item>
              <el-form-item prop="newPassword">
                <div class="formList">
                  <el-input type="password" placeholder="请输入新密码" show-password v-model="ruleForm.newPassword">
                    <template slot="prepend"><div class="prepend_label">新密码<span class="prepend_span">*</span></div></template>
                  </el-input>
                </div>
              </el-form-item>
              <el-form-item prop="confirmPassword">
                <div class="formList">
                  <el-input type="password" autocomplete="off" placeholder="请再次输入新密码" show-password v-model="ruleForm.confirmPassword">
                    <template slot="prepend"><div class="prepend_label">确认密码<span class="prepend_span">*</span></div></template>
                  </el-input>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="border_top"></div>
        <div class="middle_bottom">
          <el-button type="primary" class="submit_button" :loading="loading" @click="submitForm('ruleForm')">提交修改</el-button>
        </div>
      </div>
    </div>
    <Footbox class="foot_layout" />
     <foot />
  </div>
</template>

<script>
import top from '../../components/top/top.vue'
import foot from '../../components/foot/foot.vue'
import { updateUserPwd } from '@/api/user'
import { getSysConfig } from '@/api/common'
import { rsaEncrypt } from '@/utils/rsaUtil'
import {validPassword, validPasswordTops} from '@/utils/validate'
import Topbox from "@/views/layout/newtop";
import Footbox from "@/views/layout/foot";
export default {
  components: {
     top,foot,
    Topbox,
    Footbox
  },
  data() {
    const validateOldPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入原密码'))
      } else {
        callback()
      }
    }
    const validateNewPass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入新密码'))
      } else if (!validPassword(value)) {
        callback(new Error(validPasswordTops()))
      } else {
        if (this.ruleForm.confirmPassword !== '') {
          this.$refs.ruleForm.validateField('confirmPassword')
        }
        callback()
      }
    }
    const validateConfirmPass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入新密码'))
      } else if (value !== this.ruleForm.newPassword) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      ruleForm: {
        username: '',
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      rules: {
        oldPassword: [{ validator: validateOldPassword, trigger: 'blur' }],
        newPassword: [{ validator: validateNewPass, trigger: 'blur' }],
        confirmPassword: [{ validator: validateConfirmPass, trigger: 'blur' }],
      },
      loading: false,
      publicKey: '',
      userInfo: {}
    }
  },
  mounted() {},
  beforeDestroy() {},
  created() {
    this.userInfo = this.$store.getters.userInfo || {}
    this.getSysConfigData()
  },
  methods: {
    getSysConfigData () {
      getSysConfig().then(response => {
        if (response && response.publicKey) {
          this.publicKey = response.publicKey
        }
      })
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.loading = true
          this.ruleForm.username = this.userInfo.name
          const submitForm = JSON.parse(JSON.stringify(this.ruleForm))
          const username = rsaEncrypt(submitForm.username + '', this.publicKey) || ''
          const oldPassword = rsaEncrypt(submitForm.oldPassword + '', this.publicKey) || ''
          const newPassword = rsaEncrypt(submitForm.newPassword, this.publicKey) || ''
          const data = {
            username: username,
            oldPassword: oldPassword,
            newPassword: newPassword
          }
          updateUserPwd(data).then(response => {
            this.$message.success('重置成功', 3)
            this.ruleForm = {}
            const that = this
            setTimeout(() => {
              that.loading = false
              that.$store.dispatch('LogOut').then(() => {
                location.href = '/login';
              })
            }, 2000)
          }).catch(() => {
            this.loading = false
          })
        } else {
          return false
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
//媒体查询 自适应
//PC端
@media screen and (min-width: 720px) {


    .map_box_pc{
    display: block;
    display: flex;
    justify-content: space-between;
  }
  .map_box_yd{
    display: none!important;
  }
  .choose {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    .choose_body {
      display: flex;
      justify-content: center;
      margin-top: 0px;
      .body_middle {
        width: 1200px;
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 10px 30px 1px rgba(93,132,177,0.11);
        border-radius: 0 0 16px 16px;

        .changePasswordBox{
          width: 100%;
          height: auto;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-flow: column;
          padding:40px 0 40px 0;
          .title{
            width: 100%;
            font-size: 32px;
            font-weight:bold;
            text-align: center;
          }
          .center_box{
            width: 50%;
            height: auto;
            border: 1px solid #f1f1f1;
            background: #fafafa;
            border-radius: 10px;
            margin-top: 30px;
            padding:30px;
            display: flex;
            flex-flow: column;
            gap: 20px;
            .formList{
              display: flex;
              img{
                width: 160px;
                height: 100%;
                background: #f1f1f1;
              }
            }
          }
        }

        .middle_bottom {
          width: 100%;
          height: 140px;
          display: flex;
          text-align: center;
          justify-content: center;
          align-items: center;
          padding:0 0 40px 0;
          .button {
            width: 418px;
            height: 59px;
            line-height: 59px;
            background: #3570f6;
            border-radius: 10px 10px 10px 10px;
            opacity: 1;
            color: #fff;
          }
          .submit_button {
            width: 418px;
            height: 59px;
            font-size: 20px;
          }
        }
      }
    }
  }
  .choose_foot {
    display: none;
  }
}
//移动端
@media screen and (max-width: 720px) {
  .top_layout,.foot_layout{
    display: none;
  }
    .map_box_pc{
    display: none;
  }
  .map_box_yd{
    display: block;
    display: flex;
    justify-content: space-around;
  }
  .choose {
    width: 100%;
    display: flex;
    flex-direction: column;
    background: #eef6ff;
    margin-top: 1rem;
    .choose_body {
      display: flex;
      justify-content: center;
      margin-top: 5rem;
      .body_middle {
         width: calc(100% - 2rem);
        height: auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 0.5rem 1rem rgba(93,132,177,0.11);
        border-radius: 0.8rem;
        .changePasswordBox{
          width: 100%;
          height: auto;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-flow: column;
          padding:2rem 0 2rem 0;
          .title{
            width: 100%;
            font-size: 2rem;
            font-weight:bold;
            text-align: center;
          }
          .center_box{
            width: 90%;
            height: auto;
            border-radius: 0.8rem;
            margin-top: 2rem;
            padding:0 1rem;
            display: flex;
            flex-flow: column;
            gap: 1.2rem;
            .formList{
              display: flex;
              img{
                width: 10rem;
                height: 100%;
                background: #f1f1f1;
              }
            }
            ::v-deep .el-input-group__prepend{
              font-size: 1rem;
            }
            ::v-deep .el-input__inner{
              height: 3rem;
              font-size: 1rem;
            }
          }
        }

        .middle_bottom {
          width: 100%;
          height: auto;
          display: flex;
          text-align: center;
          justify-content: center;
          align-items: center;
          padding:0 0 3rem 0;
          .button {
            width: calc(100% - 4rem);
            height: auto;
            background: #3570f6;
            border-radius: 0.5rem;
            padding:1rem 0;
            opacity: 1;
            color: #fff;
          }
          .submit_button {
            width: calc(100% - 4rem);
            height: auto;
            font-size: 20px;
          }
        }
      }
    }
    .choose_foot {
      width: 100%;
      height:auto;
      display: flex;
      justify-content: center;
      align-items: center;
      padding:1.5rem 1.2rem;
    }
  }

  ::v-deep .el-form-item {
    margin-bottom: 3rem;
  }
}
::v-deep .is-finish{
  color: #383838;
  font-size: 18px;
  .el-step__line{
    background: #3570F6;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #3570F6;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    border-color: #3570F6;
  }
}

::v-deep .is-process{
  color: #383838;
  font-size: 18px;
  font-weight: normal;
  .el-step__line{
    background: #f1f1f1;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #ffffff;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    border-width: 5px;
    color: #3570F6;
    border-color: #3570F6;
  }
}

::v-deep .is-wait{
  color: #383838;
  font-size: 18px;
  font-weight: normal;
  .el-step__line{
    background: #f1f1f1;
    top: 50%;
    transform: translateY(-50%);
    height: 5px;
  }
  .is-text{
    background: #EEF6FF;
    width: 48px;
    height: 48px;
    font-size: 18px;
    font-weight: bold;
    border-width: 5px;
    color: #3570F6;
    border-color: #EEF6FF;
  }
}
.prepend_label {
  width: 4em;
}
.prepend_span {
  color: red;
  margin-left: 2px;
  text-align: center;
}
</style>
