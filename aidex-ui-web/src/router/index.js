import Vue from "vue";
import VueRouter from "vue-router";

// hack router push callback
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push (location, onResolve, onReject) {
  if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
  return originalPush.call(this, location).catch(err => err)
}

Vue.use(VueRouter);

export const routes = [
  // {
  //   path: '/index',// 首页
  //   component: () => import("@/views/index/index.vue")
  // },
  // {
  //   path: '/login',// 登录
  //   component: () => import("@/views/login")
  // },
  // {
  //   path: '/register',// 注册
  //   component: () => import("@/views/register/register.vue")
  // },
  // {
  //   path: '/revise',// 记住密码
  //   component: () => import("@/views/revise/revise.vue")
  // },
  // {
  //   path: '/choose',// 选择入学区域
  //   component: () => import("@/views/choose/choose.vue")
  // },
  // {
  //   path: '/school',// 选择要报名的学校
  //   component: () => import("@/views/school/school.vue")
  // },
  // {
  //   path: '/application',// 我的报名
  //   component: () => import("@/views/application/application.vue")
  // },
  // {
  //   path: '/Information',// 信息采集表
  //   component: () => import("@/views/Information/Information.vue")
  // },
  // {
  //   path: '/submit',// 提交报名资料
  //   component: () => import("@/views/submit/submit.vue")
  // },
  // {
  //   path: '/list',// 列表页面
  //   component: () => import("@/views/list/list.vue"),
  //   meta: { keepAlive: false, noCache: false},
  // },
  // {
  //   path: '/article',// 文章正文
  //   component: () => import("@/views/article/article.vue"),
  //   meta: { keepAlive: false, noCache: false},
  // },
  // {
  //   path: '/password',// 修改密码
  //   component: () => import("@/views/password/password.vue")
  // },
  // {
  //   path: '/newsList',// 未登录列表页面
  //   component: () => import("@/views/list/newsList.vue"),
  //   meta: { keepAlive: false, noCache: false},
  // },
  // {
  //   path: '/newsArticle',// 未登录文章正文
  //   component: () => import("@/views/article/newsArticle.vue"),
  //   meta: { keepAlive: false, noCache: false},
  // },
  // {
  //   path: '/message',// 列表页面
  //   component: () => import("@/views/message/list.vue"),
  //   meta: { keepAlive: false, noCache: false},
  // },
  // {
  //   path: '/messageDetail',// 列表页面
  //   component: () => import("@/views/message/detail.vue"),
  //   meta: { keepAlive: false, noCache: false},
  // },
  // {
  //   path: '/perfectSubmit',// 信息完善
  //   component: () => import("@/views/submit/perfectSubmit.vue")
  // },
  // {
  //   path: '/zxSubmit',// 初中报名
  //   component: () => import("@/views/submit/zxSubmit.vue")
  // },
  {
    path: '/guide',// 引导页
    component: () => import("@/views/index/guide.vue")
  },
  // {
  //   path: '/main',// 引导页
  //   component: () => import("@/views/index/main.vue")
  // },
  {
    path: '/explain',// 生源类型说明
    component: () => import("@/views/index/explain.vue")
  },
  {
    path: '/explain_ysx',// 生源类型说明
    component: () => import("@/views/index/explain_ysx.vue")
  },
  {
    path: '/explain_xsc',// 生源类型说明
    component: () => import("@/views/index/explain_xsc.vue")
  },
  // {
  //   path: '/zs',// 录取通知书
  //   component: () => import("@/views/index/certificates.vue")
  // },
  {
    path: '/explainArticle',// 文章正文
    component: () => import("@/views/article/explainArticle.vue"),
    meta: { keepAlive: false, noCache: false},
  },
  {
    path: '/explainArticle_xsc',// 文章正文
    component: () => import("@/views/article/explainArticle_xsc.vue"),
    meta: { keepAlive: false, noCache: false},
  },
  {
    path: '/help',// 文章正文
    component: () => import("@/views/article/help.vue"),
    meta: { keepAlive: false, noCache: false},
  },
  // {
  //   path: '/adviceNote',// 引导页
  //   component: () => import("@/views/Information/adviceNote.vue")
  // },
  // {
  //   path: '/policySubmit',// 优抚政策提交报名表单
  //   component: () => import("@/views/submit/comfortingPolicySubmit.vue")
  // }

];

export default new VueRouter({
  mode: "history", //在创建的 router 对象中，如果不配置 mode，就会使用默认的 hash 模式，该模式下会将路径格式化为 #! 开头.
  base: process.env.BASE_URL,
  routes
});
