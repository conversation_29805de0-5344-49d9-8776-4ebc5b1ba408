import { login, logout, getInfo } from '@/api/login';
import { getToken, setToken, removeToken } from '@/utils/auth';
import { getSysConfig } from '@/api/common'
import commonUtils from "@/utils/commonUtils";
import constants from '@/utils/constants.js'

const user = {
  state: {
    token: getToken(),
    name: '',
    avatar: '',
    roles: [],
    userId: '',
    permissions: [],
    loginFormVisible: false,  //  登录框显隐状态
    accountAmount: 0,
    info: {},
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_USERID: (state, userId) => {
      state.userId = userId
    },
    SET_LOGINFORMVISIBLE: (state, loginFormVisible) => {
      state.loginFormVisible = loginFormVisible
    },
    SET_SIGNRECORD: (state, signRecord) =>{
      state.signRecord = signRecord
    },
    SET_ACCOUNTAMOUNT: (state, accountAmount) =>{
      state.accountAmount = accountAmount
    },
    SET_INFO: (state, info) => {
      state.info = info
    },
  },

  actions: {
    // 登录
    Login({commit}, userInfo) {
      return new Promise((resolve, reject) => {
        login(userInfo).then(res => {
          if (res && res.code === 200) {
            const token = res.data || null
            setToken(token)
            commit('SET_TOKEN', token)
            commit('SET_LOGINFORMVISIBLE', false);
          }
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 获取用户信息
    GetInfo({commit, state}) {
      return new Promise((resolve, reject) => {
        getInfo().then(res => {
          if (res) {
            const user = res.user || {}
            const avatar = user.avatar == "" ? require("@/assets/styles/images/user.png") : process.env.VUE_APP_BASE_API + user.avatar;
            if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
              commit('SET_ROLES', res.roles)
              commit('SET_PERMISSIONS', res.permissions)
            } else {
              commit('SET_ROLES', ['ROLE_DEFAULT'])
            }
            commit('SET_NAME', user.userName);
            commit('SET_AVATAR', avatar);
            commit('SET_USERID', user.userId);
            commit('SET_INFO', user);
            commit('SET_ACCOUNTAMOUNT', res.accountAmount)
          }
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },
    //跳转到登录页面
    showLoginForm({commit}) {
      return new Promise(resolve => {
        commit('SET_LOGINFORMVISIBLE', true);
        resolve();
      });

    },
    // 退出系统
    LogOut({commit, state}) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          commit('SET_USERID', '')
          commit('SET_INFO', {})
          removeToken();
          commit('SET_SIGNRECORD', {
            signinTodayFlag: 0,
            seriesDays: 0,
            continuityDays: 0
          });
          commit('SET_ACCOUNTAMOUNT', 0)
          commonUtils.removeApplyParams(constants.STORE_CACHE_KEY_APPLY)
          commonUtils.removeApplyParams(constants.STORE_CACHE_KEY_QRCODE)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 获取系统配置信息
    GetSysConfig({commit, state}) {
      return new Promise((resolve, reject) => {
        getSysConfig().then(res => {
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    }
  }
}

export default user
