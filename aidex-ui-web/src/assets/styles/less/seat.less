.container{
    display: block;
    .nav-img{
        width: 1200px;
        margin: 0 auto;
        height: 200px;
        background: url(../images/seatings-img.png) ;
    }
    .main{
        width: 1200px;
        margin-top: 20px;
        border: 1px solid #efefef;
        display: flex;
        justify-content: space-between;
        .seats-container{
            width: 740px;
            height: 100%;
            padding: 30px;
            .seatkinds{
                width: 400px;
                margin: 0 auto;
                ul{
                    display: flex;
                    justify-content: space-between;
                    .seatskind-model{
                        width: 90px;
                        display: inline-block;
                        img{
                            margin-right: 8px;
                        }
                    }
                }
            }
            .screen{
                width: 550px;
                height: 80px;
                margin: 0 auto;
                margin-left: 100px;
                margin-top: 40px;
                background: url(../images/seatscreen.png);
            }
            .seats-body{
                width: 100%;
                height:500px;
                display: flex;
                justify-content: space-between;
                .seatnum{
                    width: 10%;
                    height: 100%;
                    ul{
                        li{
                            width: 100%;
                            height: 55px;
                            line-height: 55px;
                            text-align: center;
                        }
                    }
                }
                .seats{
                    width: 85%;
                    height: 100%;
                    font-size: 0;                    
                    .seat{
                        display: inline-block;
                        width: 68px;
                        height: 55px;
                        background: url(../images/seat.png) no-repeat center center;
                        cursor:pointer;
                    }
                    .disableseat{
                        cursor:url(../images/not.png),auto;
                        background: url(../images/seatdisable.png) no-repeat center center;
                    }
                    .ableseat{
                        background: url(../images/seat-readly.png) no-repeat center center;

                    }
                    
                }
            }

        }
        .data{
            width: 400px;
            height: 100%;
            padding: 10px;
            background-color:#f9f9f9;
            .movie-data{
                width: 76%;
                margin: 0 auto;
                padding: 10px;
                display: flex;
                justify-content: space-between;
                img{
                    width: 156px;
                    display: inline-block;
                    padding: 2px;
                    background-color: #fff;
                    box-shadow: 0 0 5px 5px #e4e4e4;
                }
                p:nth-of-type(1){
                    font-size: 20px;
                    font-weight: 800;
                    height: 30px;
                    width: 150px;
                    color: #333333;
                    line-height: 30px;
                    overflow: hidden !important;  //溢出隐藏
                    text-overflow:ellipsis ; //省略号
                    white-space: nowrap;
                }
                p{
                    font-size: 12px;
                    color: #999999;
                    height: 30px;
                    line-height: 30px;
                    span{
                        margin-left: 5px;
                        color: #333333;
                        font-weight: 800;
                    }
                }
            }
            .cinema-data{
                width: 76%;
                margin: 0 auto;
                padding: 10px;

                p{
                    font-size: 12px;
                    color: #999999;
                    height: 30px;
                    line-height: 30px;
                    span{
                        margin-left: 5px;
                        color: #333333;
                        font-weight: 800;
                    }
                   .active{
                       color: #f13d37;
                   }
                }
            }
            .price{
                width: 76%;
                min-height: 150px;
                padding: 10px;
                margin: 0 auto;
                p{
                    font-size: 12px;
                    color: #999999;
                    height: 30px;
                    line-height: 30px;
                }
                #seatmini{
                    display: flex;
                    justify-content: space-between;
                    flex-wrap: wrap;
                    .seat-num{
                        display: inline-block;
                        width: 48%;
                        box-sizing: border-box;
                        border: 3px solid #d4b2b2;
                        border-radius: 10px;
                        text-align: center;
                        margin-bottom: 10px;
                        color: #333333;
                    }
                    p{
                        width: 100%;
                        text-align: center;
                    }
                    .none{
                        display: none;
                    }
                }
            }
            .countmoney{
                width: 90%;
                height:48px;
                font-size: 16px;
                font-weight: 600;
                margin: 0 auto;
                margin-top: 50px;
                border-bottom: 1px solid #cfcfcf;
                span:nth-of-type(2){
                    color: #f13d37;
                    span{
                        font-size: 26px;
                    }
                }
            }
            .user-data{
                width: 76%;
                padding: 10px;
                margin: 0 auto;
                margin-bottom: 20px;
                p{
                    width: 100%;
                    text-align: center;
                    font-size: 14px;
                    color: #999999;
                    span{
                        color: #333333;
                        font-weight: 600;
                    }
                }
                a{
                    display: block;
                    width: 100%;
                    height: 50px;
                    line-height: 50px;
                    text-align: center;
                    border-radius: 40px;
                    margin-top: 20px;
                    background-color: #dedede;
                    font-size: 16px;
                    color: #f9f9f9;
                    pointer-events:none;
                }
                .isable{
                    background-color: #f13d37;
                    pointer-events: all;
                    
                }
            }
        }
    }
}