
body{
    overflow: hidden;
    background: url(../images/login-body-bg.png);
    background-size: cover;
}
video {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
}
#login_wrap{
        width: 1200px;
        min-height: 500px;
        height:600px;
        border-radius: 10px;
        font-family: "neo";
        overflow: hidden;
        box-shadow: 0px 0px 120px rgba(0, 0, 0, 0.25);
        position: fixed;
        top: 50%;
        right:50%;
        margin-top: -300px;
        margin-right: -600px;
        .disnone{
            display: none;
        }
        .login{
            width: 500px;
            height: 100%;
            min-height: 500px;
            background: linear-gradient(45deg, #308df7f1, #01a2ff);
            position: relative;
            float: right;
            #status{
                width: 90px;
                height: 35px;
                margin: 40px auto;
                color: #fff;
                font-size: 30px;
                font-weight: 600;
                position: relative;
                overflow: hidden;
                i{
                    font-style: normal;
                    position: absolute;
                    transition: .5s
                }

            }
            span{
                text-align: center;
                position: absolute;
                left: 50%;
                margin-left: -150px;
                top: 52%;
                margin-top: -140px;
                a{
                    text-decoration: none;
                    color: #fff;
                    display: block;
                    margin-top: 20px;
                    font-size: 18px;
                }
                .form {
                    input{
                    width: 300px;
                    height: 30px;
                    font-size: 18px;
                    background: none;
                    border: none;
                    border-bottom: 1px solid #fff;
                    color: #fff;
                    margin-bottom: 20px;
                    &::placeholder{
                        color: rgba(255,255,255,0.8);
                        font-size: 18px;
                        font-family: "neo";
                    }
                    }
                    }
                .btn{
                    width:140px;
                    height: 40px;
                    border: 1px solid #fff;
                    background: none;
                    font-size:20px;
                    color: #fff;
                    cursor: pointer;
                    margin-top: 25px;
                    font-family: "neo";
                    transition: .25s;
                    &:hover{
                        background: rgba(255,255,255,.25);
                    }
                }
            }

        }
        .sign{
            width: 500px;
            height: 100%;
            min-height: 500px;
            background: linear-gradient(45deg, #308df7f1, #01a2ff);
            position: relative;
            float: right;
            #status{
                width: 90px;
                height: 35px;
                margin: 40px auto;
                color: #fff;
                font-size: 30px;
                font-weight: 600;
                position: relative;
                overflow: hidden;
                i{
                    font-style: normal;
                    position: absolute;
                    transition: .5s
                }

            }
            span{
                text-align: center;
                position: absolute;
                left: 50%;
                margin-left: -150px;
                top: 52%;
                margin-top: -140px;
                a{
                    text-decoration: none;
                    color: #fff;
                    display: block;
                    margin-top: 20px;
                    font-size: 18px;
                }
                .form {
                    input{
                    width: 300px;
                    height: 30px;
                    font-size: 18px;
                    background: none;
                    border: none;
                    border-bottom: 1px solid #fff;
                    color: #fff;
                    margin-bottom: 20px;
                    &::placeholder{
                        color: rgba(255,255,255,0.8);
                        font-size: 18px;
                        font-family: "neo";
                    }
                    }
                    }
                .btn{
                    width:140px;
                    height: 40px;
                    border: 1px solid #fff;
                    background: none;
                    font-size:20px;
                    color: #fff;
                    cursor: pointer;
                    margin-top: 25px;
                    font-family: "neo";
                    transition: .25s;
                    &:hover{
                        background: rgba(255,255,255,.25);
                    }
                }
            }

        }
        .login-img{
            width: 700px;
            .img{
                display: block;
                width: 100%;
                height: 210px;
                margin-top: -8px;
                background: url(../images/login-bg.png) 50% 100%;
				background-size: 100%;

            }
        }
    
}
    

/* 响应式 */
@media screen and (max-width:1000px ) {
    .login-img{
        display: none;
    }
    #login_wrap{
        width: 490px;
        margin-right: -245px;
    }
    .login{
        width: 100% !important;
    }
    .sign{
        width: 100% !important;
    }
}
@media screen and (max-width:560px ) {
    #login_wrap{
        width: 330px;
        margin-right: -165px;
    }
    .login span{
        margin-left: -125px;
    }
    .sign{
        margin-left: -125px;
    }
    .form input{
        width: 250px;
    }
    .btn{
        width: 113px;
    }
}
@media screen and (max-width:345px ) {
    #login_wrap {
        width: 22rem;
        height: .1rem !important;
        margin-right: -10rem;
        margin-top: -15.6rem;
    }
    .login {
        height: .1rem !important;
        #status{
            padding: .1rem !important;
            margin-bottom: -0.5rem !important;
        }
        span{
        width: 90%;
        margin-left: -125px;
            form{
                margin-left: 1rem;
                p{
                input{
                    width: 100% !important;
                } 
                }
                .btn{
                    width: 10rem !important;
                    margin: 1rem !important;
                }
            }
    }
}
    .sign span{
        width: 90%;
        margin-left: -125px;
        form{
            margin-left: 1rem;
            p{
               input{
                   width: 100% !important;
               } 
            }
            .btn{
                width: 10rem !important;
                margin: 1rem !important;
            }
        }
    }
}