.fsize12{font-size:12px}.fsize14{font-size:14px}.fsize16{font-size:16px}
.fsize18{font-size:18px}.fsize20{font-size:20px}.fsize24{font-size:24px}.fsize34{font-size:34px}
.mt5{margin-top:5px}.mr5{margin-right:5px}.ml5{margin-left:5px}
.mt10{margin-top:10px}.mr10{margin-right:10px}.mb10{margin-bottom:10px}.ml10{margin-left:10px}
.mt15{margin-top:15px}.mr15{margin-right:15px}.mb15{margin-bottom:15px}.ml15{margin-left:15px}
.mt20{margin-top:20px}.mr20{margin-right:20px}.mb20{margin-bottom:20px}.ml20{margin-left:20px}
.mt30{margin-top:30px}.mr30{margin-right:30px}.mb30{margin-bottom:30px}.ml30{margin-left:30px}
.mt40{margin-top:40px}.mr40{margin-right:40px}.mb40{margin-bottom:40px}.ml40{margin-left:40px}
.mt50{margin-top:50px}.mr50{margin-right:50px}.mb50{margin-bottom:50px}.ml50{margin-left:50px}
.mt60{margin-top:60px}.mr60{margin-right:60px}.mb60{margin-bottom:60px}.ml60{margin-left:60px}
.mt70{margin-top:70px}.mr70{margin-right:70px}.mb70{margin-bottom:70px}.ml70{margin-left:70px}
.pt10{padding-top:10px}.pr10{padding-right:10px}.pb10{padding-bottom:10px}.pl10{padding-left:10px}
.pt15{padding-top:15px}.pr15{padding-right:15px}.pb15{padding-bottom:15px}.pl15{padding-left:15px}
.pt20{padding-top:20px}.pr20{padding-right:20px}.pb20{padding-bottom:20px}.pl20{padding-left:20px}
.pt30{padding-top:30px}
.pt50{padding-top:50px}.pr50{padding-right:50px}.pb50{padding-bottom:50px}
.pl50{padding-left:50px}
.icon14{display:inline-block;height:14px;width:14px;vertical-align:middle}
.icon16{display:inline-block;height:16px;width:16px;vertical-align:middle}
.icon18{display:inline-block;height:18px;width:18px;vertical-align:middle}
.icon20{display:inline-block;height:20px;width:20px;vertical-align:middle}
.icon24{display:inline-block;height:24px;width:24px;vertical-align:middle}
.icon30{display:inline-block;height:30px;width:30px;vertical-align:middle}

.text-center {
  text-align: center
}

.el-card__header {
  padding: 14px 15px 7px;
  min-height: 40px;
}

.el-card__body {
  padding: 15px 20px 20px 20px;
}

.card-box {
  padding-right: 15px;
  padding-left: 15px;
  margin-bottom: 10px;
}

.el-table .el-dropdown-link,.el-table .el-dropdown-selfdefine {
  cursor: pointer;
  margin-left: 5px;
}

/* image */
.img-circle {
  border-radius: 50%;
}

.img-lg {
  width: 120px;
  height: 120px;
}

.avatar-upload-preview {
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border-radius: 50%;
  box-shadow: 0 0 4px #ccc;
  overflow: hidden;
}

.el-cascader-panel {
  height: 300px;
}

.el-scrollbar__wrap {
}
