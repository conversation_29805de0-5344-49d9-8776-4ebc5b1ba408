.emoji-yum {
    width: 64px;
    height: 64px;
    background-position: -0px -0px !important;
}

.emoji-angry {
    width: 64px;
    height: 64px;
    background-position: -64px -0px !important;
}

.emoji-anguished {
    width: 64px;
    height: 64px;
    background-position: -128px -0px !important;
}

.emoji-astonished {
    width: 64px;
    height: 64px;
    background-position: -192px -0px !important;
}

.emoji-cold_sweat {
    width: 64px;
    height: 64px;
    background-position: -256px -0px !important;
}

.emoji-cry {
    width: 64px;
    height: 64px;
    background-position: -320px -0px !important;
}

.emoji-blush {
    width: 64px;
    height: 64px;
    background-position: -384px -0px !important;
}

.emoji-confounded {
    width: 64px;
    height: 64px;
    background-position: -448px -0px !important;
}

.emoji-dizzy_face {
    width: 64px;
    height: 64px;
    background-position: -512px -0px !important;
}

.emoji-flushed {
    width: 64px;
    height: 64px;
    background-position: -0px -64px !important;
}

.emoji-fearful {
    width: 64px;
    height: 64px;
    background-position: -64px -64px !important;
}

.emoji-grimacing {
    width: 64px;
    height: 64px;
    background-position: -128px -64px !important;
}

.emoji-disappointed_relieved {
    width: 64px;
    height: 64px;
    background-position: -192px -64px !important;
}

.emoji-disappointed {
    width: 64px;
    height: 64px;
    background-position: -256px -64px !important;
}

.emoji-expressionless {
    width: 64px;
    height: 64px;
    background-position: -320px -64px !important;
}

.emoji-innocent {
    width: 64px;
    height: 64px;
    background-position: -384px -64px !important;
}

.emoji-grin {
    width: 64px;
    height: 64px;
    background-position: -448px -64px !important;
}

.emoji-frowning {
    width: 64px;
    height: 64px;
    background-position: -512px -64px !important;
}

.emoji-hushed {
    width: 64px;
    height: 64px;
    background-position: -0px -128px !important;
}

.emoji-open_mouth {
    width: 64px;
    height: 64px;
    background-position: -64px -128px !important;
}

.emoji-pensive {
    width: 64px;
    height: 64px;
    background-position: -128px -128px !important;
}

.emoji-sleepy {
    width: 64px;
    height: 64px;
    background-position: -192px -128px !important;
}

.emoji-confused {
    width: 64px;
    height: 64px;
    background-position: -256px -128px !important;
}

.emoji-persevere {
    width: 64px;
    height: 64px;
    background-position: -320px -128px !important;
}

.emoji-relieved {
    width: 64px;
    height: 64px;
    background-position: -384px -128px !important;
}

.emoji-scream {
    width: 64px;
    height: 64px;
    background-position: -448px -128px !important;
}

.emoji-stuck_out_tongue_winking_eye {
    width: 64px;
    height: 64px;
    background-position: -512px -128px !important;
}

.emoji-stuck_out_tongue {
    width: 64px;
    height: 64px;
    background-position: -0px -192px !important;
}

.emoji-satisfied {
    width: 64px;
    height: 64px;
    background-position: -64px -192px !important;
}

.emoji-no_mouth {
    width: 64px;
    height: 64px;
    background-position: -128px -192px !important;
}

.emoji-smiley {
    width: 64px;
    height: 64px;
    background-position: -192px -192px !important;
}

.emoji-smile {
    width: 64px;
    height: 64px;
    background-position: -256px -192px !important;
}

.emoji-stuck_out_tongue_closed_eyes {
    width: 64px;
    height: 64px;
    background-position: -320px -192px !important;
}

.emoji-sweat {
    width: 64px;
    height: 64px;
    background-position: -384px -192px !important;
}

.emoji-joy {
    width: 64px;
    height: 64px;
    background-position: -448px -192px !important;
}

.emoji-heart_eyes {
    width: 64px;
    height: 64px;
    background-position: -512px -192px !important;
}

.emoji-grinning {
    width: 64px;
    height: 64px;
    background-position: -0px -256px !important;
}

.emoji-smirk {
    width: 64px;
    height: 64px;
    background-position: -64px -256px !important;
}

.emoji-relaxed {
    width: 64px;
    height: 64px;
    background-position: -128px -256px !important;
}

.emoji-kissing_closed_eyes {
    width: 64px;
    height: 64px;
    background-position: -192px -256px !important;
}

.emoji-sunglasses {
    width: 64px;
    height: 64px;
    background-position: -256px -256px !important;
}

.emoji-sweat_smile {
    width: 64px;
    height: 64px;
    background-position: -320px -256px !important;
}

.emoji-sob {
    width: 64px;
    height: 64px;
    background-position: -384px -256px !important;
}

.emoji-wink {
    width: 64px;
    height: 64px;
    background-position: -448px -256px !important;
}

.emoji-worried {
    width: 64px;
    height: 64px;
    background-position: -512px -256px !important;
}

.emoji-neutral_face {
    width: 64px;
    height: 64px;
    background-position: -0px -320px !important;
}

.emoji-unamused {
    width: 64px;
    height: 64px;
    background-position: -64px -320px !important;
}

.emoji-weary {
    width: 64px;
    height: 64px;
    background-position: -128px -320px !important;
}

.emoji-tired_face {
    width: 64px;
    height: 64px;
    background-position: -192px -320px !important;
}

.emoji-triumph {
    width: 64px;
    height: 64px;
    background-position: -256px -320px !important;
}

.emoji-laughing {
    width: 64px;
    height: 64px;
    background-position: -320px -320px !important;
}

.emoji-kissing_heart {
    width: 64px;
    height: 64px;
    background-position: -384px -320px !important;
}
