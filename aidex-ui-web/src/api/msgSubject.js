import request from '@/utils/request'

// 我的消息列表
export function userMessageList (query) {
  return request({
    url: '/front/msgSubject/userMessageList',
    method: 'get',
    params: query
  })
}

// 查询消息主体详细
export function getUserMsgSubject (id) {
  return request({
    url: '/front/msgSubject/userDetail/' + id,
    method: 'get'
  })
}

// 阅读消息主体
export function setRead (id) {
  return request({
    url: '/front/msgSubject/setRead/' + id,
    method: 'get'
  })
}

// 阅读消息全部主体
export function setUserMessageAllRead () {
  return request({
    url: '/front/msgSubject/setAllRead',
    method: 'get'
  })
}
