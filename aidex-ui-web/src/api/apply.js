import request from '@/utils/request'

// 查询报名分类列表
export function getClassifyList (query) {
  return request({
    url: '/front/external/getClassifyList',
    method: 'get',
    params: query
  })
}

// 查询报名分类列表
export function getClassifyById (id) {
  return request({
    url: '/front/external/getClassifyById',
    method: 'get',
    params: {id}
  })
}

// 查询报名地区列表
export function getExternalApplyAreaList (query) {
  return request({
    url: '/front/external/getAreaList',
    method: 'get',
    params: query
  })
}

// 查询报名地区列表
export function getApplyAreaList (query) {
  return request({
    url: '/front/apply/getAreaList',
    method: 'get',
    params: query
  })
}

// 查询报名学校列表
export function getApplySchoolList (query) {
  return request({
    url: '/front/apply/getSchoolByAreaId',
    method: 'get',
    params: query
  })
}

// 查询生源类型列表
export function getSourceTypeList (query) {
  return request({
    url: '/front/apply/getSourceTypeList',
    method: 'get',
    params: query
  })
}

// 查询报名学生列表
export function getApplyStudentList (query) {
  return request({
    url: '/front/apply/getApplyStudentList',
    method: 'get',
    params: query
  })
}

// 提交报名
export function submitApply (data) {
  return request({
    url: '/front/apply/submitApply',
    method: 'post',
    data: data
  })
}

// 查看报名详情
export function getApplyInfo (id) {
  return request({
    url: '/front/apply/getApplyInfo',
    method: 'get',
    params: {id}
  })
}

// 保存报名信息
export function saveApply (data) {
  return request({
    url: '/front/apply/saveApply',
    method: 'post',
    data: data
  })
}

// 提交报名
export function savePerfectData (data) {
  return request({
    url: '/front/apply/submitPerfect',
    method: 'post',
    data: data
  })
}

export function checkApplyState (data) {
  return request({
    url: '/front/apply/checkApplyState',
    method: 'post',
    data: data
  })
}

export function checkApplyEnable (data) {
  return request({
    url: '/front/apply/checkApplyEnable',
    method: 'post',
    data: data
  })
}

// 查询表单配置
export function getFormConfigList (query) {
  return request({
    url: '/front/apply/getFormConfigList',
    method: 'get',
    params: query
  })
}

// 绑定二维码
export function bindUserAndQrcode (data) {
  return request({
    url: '/front/apply/bindQrcode',
    method: 'post',
    data: data
  })
}

/**
 * 获取报名首页时间线数据
 */
export function getApplyTimeTimeLine (data) {
  return request({
    url: '/front/external/getApplyTimeTimeLine',
    method: 'post',
    data: data
  })
}

/**
 * 获取生源类型说明
 */
export function getBizSourceTypeExplain (areaId, dataType) {
  return request({
    url: '/front/external/getBizSourceTypeExplain?areaId=' + areaId + "&dataType=" + dataType,
    method: 'get'
  })
}

// 获取录取通知书数据
export function getCertificatesInfo (id) {
  return request({
    url: '/front/apply/getCertificatesInfo',
    method: 'get',
    params: {id}
  })
}

// 保存优抚政策报名
export function savePolicyApply (data) {
  return request({
    url: '/front/apply/savePolicyApply',
    method: 'post',
    data: data
  })
}

// 提交优抚政策报名
export function submitPolicyApply (data) {
  return request({
    url: '/front/apply/submitPolicyApply',
    method: 'post',
    data: data
  })
}
