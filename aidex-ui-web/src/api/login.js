import request from '@/utils/request'

// 登录方法
export function login(data) {
  return request({
    url: '/security/token',
    method: 'post',
    headers: {
      isToken: false
    },
    params: data
  })
}

// 用户密码重置
export function resetUserPwd (data) {
  return request({
    url: '/front/resetPwd',
    method: 'post',
    headers: {
      isToken: false
    },
    data: data
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/front/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/front/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/security/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}

// 获取手机验证码
export function getSmsCaptcha (data) {
  return request({
    url: '/front/sendSms',
    method: 'post',
    headers: {
      isToken: false
    },
    data: data
  })
}
