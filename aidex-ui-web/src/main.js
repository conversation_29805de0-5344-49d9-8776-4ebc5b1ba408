import Vue from 'vue'
import './element-ui' // @PC.element-ui
import router from './router'
import store from './store'
import App from './App.vue'
import element from '@/utils/element'
import './assets/icons' // icon
import './permission' // permission control
import '../src/assets/styles/css/global.css'
import '@/assets/styles/less/index.less'
import "@/assets/iconfont/iconfont.css";
import { getDicts } from '@/api/system';
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, download, handleTree } from "@/utils/ruoyi";
import { firstAuditFormat, secondAuditFormat, fileDownloadUrl } from '@/utils/business'
import Pagination from "@/components/Pagination";
import hljs from 'highlight.js';
import 'highlight.js/styles/atom-one-dark.css' //样式
import globalFunction from '@/utils/globalFunction'
import plugins from './plugins' // plugins
// 图片预览组件
import ImagePreview from "@/components/ImagePreview"
import scroll from 'vue-seamless-scroll'
// 富文本组件
import Editor from "@/components/Editor"
import VueAnimateNumber from 'vue-animate-number'
import constants from "@/utils/constants"
import commonUtils from "@/utils/commonUtils";

// import ElementUI from 'element-ui';
// import 'element-ui/lib/theme-chalk/index.css';


import vueAwesomeSwiper from 'vue-awesome-swiper'

import 'swiper/dist/css/swiper.min.css';


Vue.config.devtools = process.env.VUE_APP_ENV === 'dev' || process.env.VUE_APP_ENV === 'stage'
Vue.config.silent = process.env.VUE_APP_ENV === 'prod'
Vue.config.productionTip = false

// 全局方法挂载
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.$constants = constants
Vue.prototype.getDicts = getDicts
Vue.prototype.firstAuditFormat = firstAuditFormat
Vue.prototype.secondAuditFormat = secondAuditFormat
Vue.prototype.$commonUtils = commonUtils
Vue.prototype.$fileDownloadUrl = fileDownloadUrl
// Vue.use(ElementUI);
// 全局组件挂载
Vue.component('Pagination', Pagination)
Vue.component('Editor', Editor)

for(let key in globalFunction) {
  Vue.prototype[key] = globalFunction[key]
}



Vue.use(plugins)
Vue.use(scroll)
Vue.use(VueAnimateNumber)

// 全局过滤器设置
Vue.filter('ellipsis', function (msg, num) {
  const currentNum = num || 5
  if(!msg) {
    return ''
  }
  if(msg.length > currentNum) {
    return msg.slice(0, currentNum) + '...'
  }
  return msg
})


Vue.directive('highlight',function(el){
  let blocks = el.querySelectorAll('pre code');
  blocks.forEach((block)=>{
    hljs.highlightBlock(block)
  })
})


Vue.use(vueAwesomeSwiper)
Vue.use(element)
new Vue({
  el: '#vue_web',
  router,
  render: h => h(App),
  store
})

