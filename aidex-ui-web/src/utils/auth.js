import Cookies from 'js-cookie'

const TokenKey = 'access_token_web'
const userSchoolInfo = 'user_school_info'

export function getTokenKey() {
  return TokenKey
}

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(Token<PERSON>ey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}
export function setUserSchool(userSchool) {
  return Cookies.set(userSchoolInfo,userSchool)
}
export function getUserSchoolCooke() {
  return Cookies.get(userSchoolInfo)
}
