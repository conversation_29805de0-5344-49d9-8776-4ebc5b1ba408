/**
 * 通用参数封装处理
 * Copyright (c) 2019 aidex
 */

export default {
  /** 学校类型-幼儿园 */
  SCHOOL_YRY: 'kindergarten',
  /** 学校类型-小学 */
  SCHOOL_XX: 'elementary',
  /** 学校类型-初中 */
  SCHOOL_CZ: 'juniorhigh',


  /** 学校性质-公办 */
  SCHOOL_NATURE_GB: 'government',
  /** 学校性质-民办 */
  SCHOOL_NATURE_MB: 'civilian',


  /** 户口类型-片区内户籍儿童 */
  RESIDENT_TYPE_HJ: 'huji',
  /** 户口类型-进城务工人员随迁子女 */
  RESIDENT_TYPE_SQ: 'suiqian',

  /** 招生政策主键 */
  POLICY_KEY: 'policy',
  POLICY_ID: '9954ed855c90457aa06247d47b7ac870',
  /** 招生政策主键 */
  ADMISSION_KEY: 'admission',
  ADMISSION_ID: 'd8da857c2dd44759a51b7c2423295ed0',
  /** 操作指南主键 */
  GUIDE_KEY: 'guide',
  GUIDE_ID: 'a820f27b8c3e4a91917da1dcefbac56f',
  /** 咨询电话主键 */
  CONSULTING_KEY: 'consulting',
  CONSULTING_ID: '551e61686e6b4853babd21edf9154d74',

  /** 户口簿首页 */
  HOUSEHOLD_REGISTER: '1',
  HOUSEHOLD_REGISTER_NAME: '户口簿首页',
  /** 户口簿户主 */
  HOUSEHOLD_REGISTER_HEAD: '2',
  HOUSEHOLD_REGISTER_HEAD_NAME: '户口簿户主页',
  /** 户口簿户主 */
  HOUSEHOLD_REGISTER_CHILD: '3',
  HOUSEHOLD_REGISTER_CHILD_NAME: '户口簿儿童页',
  /** 房产证 */
  HOUSE_PROPRIETARY_CERTIFICATE: '4',
  HOUSE_PROPRIETARY_CERTIFICATE_NAME: '房产证',
  /** 居住证正面 */
  RESIDENCE_PERMIT_FRONT: '5',
  RESIDENCE_PERMIT_FRONT_NAME: '居住证正面',
  /** 居住证反面 */
  RESIDENCE_PERMIT_BACK: '6',
  RESIDENCE_PERMIT_BACK_NAME: '居住证反面',
  /** 社会保险缴纳凭证 */
  SOCIAL_INSURANCE_PAYMENT_CERTIFICATE: '8',
  SOCIAL_INSURANCE_PAYMENT_CERTIFICATE_NAME: '社会保险缴纳凭证',
  /** 政策保障性入学证明材料 */
  POLICY_GUARANTEE: '9',
  POLICY_GUARANTEE_NAME: '政策保障性入学证明材料',
  /** 扶贫手册及县扶贫办证明 */
  POVERTY_ALLEVIATION: '10',
  POVERTY_ALLEVIATION_NAME: '扶贫手册及县扶贫办证明',
  /** 无房证明 */
  NO_HOUSE_CERTIFICATE: '11',
  NO_HOUSE_CERTIFICATE_NAME: '无房证明',
  /** 房屋租赁证明 */
  HOUSE_LEASE_CERTIFICATE: '12',
  HOUSE_LEASE_CERTIFICATE_NAME: '房屋租赁证明',
  /** 延缓入学申请批复照片 */
  POSTPONE_ENTER_SCHOOL_APPLY: '13',
  POSTPONE_ENTER_SCHOOL_APPLY_NAME: '延缓入学申请批复照片',
  /** 其他证明材料照片 */
  OTHER_CERTIFICATE: '14',
  OTHER_CERTIFICATE_NAME: '其他证明材料照片',
  /** 房产证（盖章页和产权信息页） */
  HOUSE_CERTIFICATE: '15',
  HOUSE_CERTIFICATE_NAME: '房产证（盖章页和产权信息页）',
  /** 购房合同 */
  HOUSE_SALES_AGREEMENT: '16',
  HOUSE_SALES_AGREEMENT_NAME: '购房合同',
  /** 其他类型的房产证明文件 */
  OTHER_HOUSE_CERTIFICATE: '17',
  OTHER_HOUSE_CERTIFICATE_NAME: '其他类型的房产证明文件',
  /** 居住或就业证明 */
  RESIDE_TAKE_CERTIFICATE: '18',
  RESIDE_TAKE_CERTIFICATE_NAME: '居住或就业证明',
  /** 优抚政策证明 */
  PREFERENTIAL_TREATMENT_CERTIFICATE: '19',
  PREFERENTIAL_TREATMENT_CERTIFICATE_NAME: '优抚政策证明',
  /** 公房证明 */
  HOUSE_PUBLIC_AGREEMENT: '20',
  HOUSE_PUBLIC_AGREEMENT_NAME: '公房证明',

  /** 数据步骤流程文本 */
  DATA_STEP_FIRST_TEXT: '线上审核',
  DATA_STEP_SECOND_TEXT: '现场审核',
  DATA_STEP_THREE_TEXT: '确认接收',

  /** 默认部门根节点 */
  DEFAULT_DEPT_ROOT_ID: '173697',
  DEFAULT_DEPT_AREA_CODE: '62',

  /** 头像 */
  FILE_TYPE_AVATAR: 'avatar',
  FILE_TYPE_AVATAR_NAME: '免冠照',

  /** 默认区划目录 */
  DEFAULT_AREA_ROOT_ID: '620000',

  /** 学生基本信息-免冠照片 */
  DICT_INFO_PHOTO: 'info-photo',
  /** 监护人-居住证 */
  DICT_PARENTS_RESIDENCE_PERMIT: 'parents-residencePermit',
  /** 房产信息-无房证明 */
  DICT_HOUSE_NO_ROOM_CERTIFICATE: 'house-noRoomCertificate',
  /** 房产信息-租房证明 */
  DICT_HOUSE_RENT_ROOM_CERTIFICATE: 'house-rentRoomCertificate',
  /** 申请入学信息-是否为政策保障性入学儿童 */
  DICT_APPLY_POLICY_GUARANTEE: 'apply-policyGuarantee',
  /** 申请入学信息-是否选择建档立卡户登记 */
  DICT_APPLY_POVERTY: 'apply-poverty',
  /** 申请入学信息-是否申请多子女登记 */
  DICT_APPLY_MANY_CHILDREN: 'apply-manyChildren',

  /** localstore cache key */
  STORE_CACHE_KEY_APPLY: 'apply_params',
  STORE_CACHE_KEY_QRCODE: 'qrcode_params',

  /** 招生政策类型 - 小学 **/
  SOURCE_TYPE_EXPLAIN_DATATYPE_XX : '0',
  /** 招生政策类型 - 初中 **/
  SOURCE_TYPE_EXPLAIN_DATATYPE_CZ : '1',

  /** 招生政策类型 - 招生方案 **/
  SOURCE_TYPE_EXPLAIN_TYPE_ZSFA : '0',
  /** 招生政策类型 - 划片方案 **/
  SOURCE_TYPE_EXPLAIN_TYPE_HPFA : '1',
  /** 招生政策类型 - 政策解释 **/
  SOURCE_TYPE_EXPLAIN_TYPE_ZCJS : '2',
  /** 招生政策类型 - 学校联系电话 **/
  SOURCE_TYPE_EXPLAIN_TYPE_XXLXDH : '3'
}
