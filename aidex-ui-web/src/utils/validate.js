/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}

/**
 * @param {string} url
 * @returns {Boolean}
 */
export function validURL(url) {
  const reg = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return reg.test(url)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validLowerCase(str) {
  const reg = /^[a-z]+$/
  return reg.test(str)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUpperCase(str) {
  const reg = /^[A-Z]+$/
  return reg.test(str)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validAlphabets(str) {
  const reg = /^[A-Za-z]+$/
  return reg.test(str)
}


/**
 * @param {string} str
 * @returns {Boolean}
 */
export function isString(str) {
  if (typeof str === 'string' || str instanceof String) {
    return true
  }
  return false
}

/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray(arg) {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]'
  }
  return Array.isArray(arg)
}

/**
 * @param {string} val
 * @returns {Boolean}
 */
export function validPassword (val) {
  const reg = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@_#$%^&+=./*~!()<>;|`?-])(?=.*[a-zA-Z0-9@_#$%^&+=./*~!()<>;|`?-]).{8,20}$/
  return reg.test(val)
}

/**
 * @param {boolean} val
 * @returns {String}
 */
export function validPasswordTops (val) {
  if (val) {
    return val + ''
  } else {
    return '请输入8-20位大小写英文字母、数字或者符号（除空格），且至少包含三种'
  }
}

/**
 * @param {string} val
 * @returns {Boolean}
 */
export function validIdCard (value) {
  // 15位和18位身份证号码的正则表达式
  const regIDCard = /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/
  // 如果通过该验证，说明身份证格式正确，但准确性还需计算
  if (regIDCard.test(value)) {
    if (value.length === 18) {
      const IDCardWi = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]// 将前17位加权因子保存在数组里
      const idCardY = [1, 0, 10, 9, 8, 7, 6, 5, 4, 3, 2] // 这是除以11后，可能产生的11位余数、验证码，也保存成数组
      let idCardWiSum = 0 // 用来保存前17位各自乖以加权因子后的总和
      for (let i = 0; i < 17; i++) {
        idCardWiSum += value.substring(i, i + 1) * IDCardWi[i]
      }
      const idCardMod = idCardWiSum % 11 // 计算出校验码所在数组的位置
      const idCardLast = value.substring(17) // 得到最后一位身份证号码
      // 如果等于2，则说明校验码是10，身份证号码最后一位应该是X
      if (idCardMod === 2) {
        if (idCardLast !== 'X' && idCardLast !== 'x') {
          return false
        }
      } else {
        // 用计算出的验证码与最后一位身份证号码匹配，如果一致，说明通过，否则是无效的身份证号码
        if (Number(idCardLast) !== idCardY[idCardMod]) {
          return false
        }
      }
    }
    return true
  } else {
    return false
  }
}

/**
 * @param {string} val
 * @returns {Boolean}
 */
export function validMobile (val) {
  const reg = /^(((13[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[3-8]{1})|(18[0-9]{1})|(19[0-9]{1})|(14[5-7]{1}))+\d{8})$/
  return reg.test(val)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUrlDomains (str) {
  if (!str) {
    return false
  }
  const validMap = ['https://', 'http://']
  return validMap.some(item => str.trim().indexOf(item) > -1 )
}

/**
 * @param {string} val
 * @returns {Boolean}
 */
export function validNumber (val) {
  const reg = /^\d+(\.\d+)?$/
  return reg.test(val)
}

/**
 * @param {string} val
 * @returns {Boolean}
 */
export function validPositiveInteger(val) {
  const reg = /^[0-9]\d*$/
  return reg.test(val)
}
