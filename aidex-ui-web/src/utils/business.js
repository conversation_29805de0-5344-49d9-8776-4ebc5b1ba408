import constants from "@/utils/constants"

/** 预审状态 */
export function firstAuditFormat(status) {
  if (!status) {
    return '-/-'
  } else if (status === '0' || status === '1') {
    return '待审'
  } else if ((status - 0) >= 3) {
    return '审核通过'
  } else if (status === '2') {
    return "审核不通过"
  } else {
    return '-/-'
  }
}

/** 现场审核状态 */
export function secondAuditFormat(status) {
  if (!status) {
    return '-/-'
  } else if ((status - 0) <= 3) {
    return '待审'
  } else if ((status - 0) >= 5) {
    return '审核通过'
  } else if (status === '4') {
    return "审核不通过"
  } else {
    return '-/-'
  }
}


/** 审核状态 */
export function auditFormat(status) {
  if (!status) {
    return '-/-'
  } else if (status === '0' || status === '1') {
    return '待' + constants.DATA_STEP_FIRST_TEXT
  } else if (status === '2') {
    return constants.DATA_STEP_FIRST_TEXT + '不通过'
  } else if (status === '3') {
    return '待' + constants.DATA_STEP_SECOND_TEXT
  } else if (status === '4') {
    return  constants.DATA_STEP_SECOND_TEXT + "不通过"
  } else if (status === '5') {
    return "待确认"
  } else {
    return '-/-'
  }
}

/**
 * 附件下载地址
 * @param fileId 文件主键
 * @param filename 文件名称
 * @returns {string} 文件下载地址
 */
export function fileDownloadUrl(fileId, filename) {
  if (fileId === undefined || fileId === null) {
    fileId = ''
  }
  if (filename === undefined || filename === null) {
    filename = ''
  }
  let requestUrl = process.env.VUE_APP_BASE_API + '/common/download/other?fileId=' + fileId + "&filename=" + filename
  return decodeURIComponent(requestUrl)
}
