/** 写入申请参数 */
function setApplyParams(params = {}, key) {
  if (key === undefined || key === null || key === '') {
    return false
  }
  if (params === undefined || params === null || params === '') {
    params = {}
  }
  try{
    const data = JSON.stringify(params)
    const encodeData = window.encodeURIComponent(data)
    window.localStorage.setItem(key, window.btoa(unescape(encodeData)));
    return true
  } catch (e) {
    return false
  }
}

/** 读取申请参数 */
function getApplyParams(key) {
  if (key === undefined || key === null || key === '') {
    return {}
  }
  try {
    const cacheData = window.localStorage.getItem(key)
    let cacheParams = {}
    if (cacheData && typeof cacheData === 'string') {
      const atobData = window.atob(cacheData) || '';
      const escapeData = escape(atobData) || '';
      const decodeData = window.decodeURIComponent(escapeData) || ''
      cacheParams = JSON.parse(decodeData)
    }
    return cacheParams;
  } catch (e) {
    return {}
  }
}

/** 清空申请参数 */
function removeApplyParams(key) {
  return setApplyParams({}, key)
}

export default {
  setApplyParams,
  getApplyParams,
  removeApplyParams
}
