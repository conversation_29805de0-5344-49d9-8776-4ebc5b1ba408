import {JSEncrypt} from 'jsencrypt'

/**
 * RSA非对称加密
 * @param data 需要加密的数据
 * @param publicKey 公钥
 */
export function rsaEncrypt(data, publicKey) {
  // 公钥
  const PUBLIC_KEY = publicKey || ''
  // 使用公钥加密
  const encrypt = new JSEncrypt()
  encrypt.setPublicKey(PUBLIC_KEY)
  return encrypt.encrypt(data)
}

/**
 * RSA 解密
 * @param data 需要解密的数据
 * @param privateKey 私钥
 */
export function rsaDecrypt(data, privateKey) {
  //私钥
  const PRIVATE_KEY = privateKey || ''   //使用私钥解密
  const decrypt = new JSEncrypt()
  decrypt.setPrivateKey(PRIVATE_KEY)
  return decrypt.decrypt(data)
}
