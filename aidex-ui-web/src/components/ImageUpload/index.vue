<template>
  <div class="upload-file">
    <el-upload
        ref="upload"
        list-type="picture-card"
        :class="{hide: (this.fileList && this.fileList.length >= this.limit) || false }"
        :drag="drag"
        :action="uploadFileUrl"
        :headers="headers"
        :limit="limit"
        :file-list="fileList"
        :multiple="multiple"
        :before-upload="beforeUpload"
        :on-success="handleSuccess"
        :on-error="handleError"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
    >
      <!-- 上传按钮 -->
      <div class="el-upload__text">{{ uploadText }}</div>
      <!-- 上传提示 -->
      <div class="el-upload__tip" slot="tip" v-if="showTip" style="color: #f56c6c; font-weight: bolder">
        <div v-if="tipText !== undefined && tipText != null && tipText !== ''">
          {{ tipText }}
        </div>
        <div v-else>
          请上传
          <template v-if="fileSize"> 大小不超过 <b>{{ fileSize }}KB</b> </template>
          <template v-if="fileType"> 格式为 <b>{{ fileType.join("/") }}</b> </template>
          的图片
        </div>
      </div>
    </el-upload>
    <div v-show="false" class="preview-container">
      <el-image
          ref="imageView"
          style="width: 100%; height: 100%"
          :preview-src-list="srcList"
          fit="contain"
          @close="handlePreviewClose"
      ></el-image>
    </div>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { validUrlDomains } from '@/utils/validate'
import { ACCESS_AUTHORIZATION, ACCESS_AUTHORIZATION_PREFIX } from '@/utils/mutation-types'
let prevOverflow = '';
export default {
  name: "ImageUpload",
  components: {},
  props: {
    // 数量限制
    limit: {
      type: Number,
      default: 5,
    },
    // 大小限制(KB)
    fileSize: {
      type: Number,
      default: 1024,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ["png", "jpg", "jpeg"],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 上传按钮文本
    uploadText: {
      type: String,
      default: '选择图片'
    },
    // 提示内容
    tipText: {
      type: String,
      default: undefined
    },
    // 拖拽
    drag: {
      type: Boolean,
      default: false
    },
    // 单位
    fileUnit: {
      type: String,
      default: 'Kb',
    },
  },
  data() {
    return {
      loading: false,
      baseUrl: process.env.VUE_APP_BASE_API,
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/common/upload", // 上传的图片服务器地址
      headers: {},
      fileList: [],
      srcList: []
    };
  },
  created() {
    this.headers[ACCESS_AUTHORIZATION] = ACCESS_AUTHORIZATION_PREFIX + getToken()
  },
  watch: {},
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    },
  },
  methods: {
    // 附件回显
    showFiles(val) {
      if (val) {
        let temp = 1;
        // 首先将值转为数组
        const list = Array.isArray(val) ? val : [];
        // 然后将数组转为对象数组
        this.fileList = list.map(item => {
          if (item.id) {
            let fileUrl = null;
            if (!validUrlDomains(item.url) && item.url.indexOf(this.baseUrl) === -1) {
              fileUrl = this.baseUrl + item.url;
            } else {
              fileUrl = item.url;
            }
            item = {
              id: item.id,
              name: item.name,
              url: fileUrl,
              thumbUrl: item.thumbUrl || fileUrl,
              path: item.url,
              size: item.size || 0
            };
          }
          item.uid = item.uid || new Date().getTime() + temp++;
          return item;
        });
      } else {
        this.fileList = [];
      }
    },
    // 上传前校检格式和大小
    beforeUpload(file) {
      // 校检文件类型
      if (this.fileType) {
        let fileExtension = "";
        if (file.name.lastIndexOf(".") > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
        }
        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true;
          if (fileExtension && fileExtension.indexOf(type) > -1) return true;
          return false;
        });
        if (!isTypeOk) {
          this.$message.error(`文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`);
          return false;
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const fileUnit = this.fileUnit ? this.fileUnit.toUpperCase() : 'KB';
        let value = 1
        if (fileUnit.includes("M")) {
          value = 1024
        } else if (fileUnit.includes("G")) {
          value = 1024 * 1024
        } else if (fileUnit.includes("T")) {
          value = 1024 * 1024 * 1024
        }
        const fileSize = file.size;
        const compareFileSize = this.fileSize * (1024 * value);
        const isLt = fileSize < compareFileSize;
        if (!isLt) {
          this.$message.error(`上传文件大小不能超过 ${this.fileSize} ${this.fileUnit}!`);
          return false;
        }
      }
      // 校检文件名称长度
      if (file.name.length > 100) {
        this.$message.error(`上传文件名称长度不能超过100！`);
        return false;
      }
      return true;
    },
    // 上传选择校验
    handleExceed(files, fileList) {
      if (fileList.length >= this.limit) {
        this.$message.warning('只能上传' + this.limit + '个文件');
      }
    },
    // 上传成功
    handleSuccess(res, file, fileList) {
      const { code, id, url, fileName } = res;
      if(code === 200) {
        this.$message.success("上传成功!");
        file.id = id;
        let fileUrl = ''
        if (!validUrlDomains(url) && url.indexOf(this.baseUrl) === -1) {
          fileUrl = this.baseUrl + url;
        } else {
          fileUrl = url;
        }
        file.thumbUrl = fileUrl
        file.path = fileName
      }
      this.fileList = fileList.map(f => {
        if (file.uid === f.uid) {
          f = file;
        }
        return f;
      });
      this.getFiles(this.fileList);
    },
    // 上传失败
    handleError() {
      this.$message.error("上传失败");
    },
    // 删除文件
    handleRemove(file, fileList) {
      const index = this.fileList.map(f => f.uid).indexOf(file.uid);
      if(index > -1) {
        this.fileList.splice(index, 1);
        this.getFiles(this.fileList);
      }
    },
    // 预览
    handlePreview(file) {
      this.srcList = []
      let url = file.thumbUrl || '';
      if(url) {
        this.srcList.push(url)
      }
      this.handlePreviewShow()
    },
    // 获取文件
    getFiles(fileList) {
      this.$emit('ok', fileList)
    },
    handlePreviewShow() {
      prevOverflow = document.body.style.overflow;
      document.body.style.overflow = 'hidden'
      this.$refs.imageView.showViewer = true
    },
    handlePreviewClose() {
      this.$refs.imageView.showViewer = false
      document.body.style.overflow = prevOverflow
    }

  }
};
</script>

<style scoped>
::v-deep .hide .el-upload--picture-card {
  display: none;
}
</style>
