<template>
  <div class="choose_foot"></div>
</template>

<script>

export default {


  data() {
    return {
    }
  },
  mounted() {},
  beforeDestroy() {},
  created () {},
  methods: {},
};

</script>
<style lang="scss" scoped>
@media screen and (min-width: 720px) {
  .choose_foot{
    width: 100%;
    text-align: center;
    align-items: center;
    font-size: 16px;
    color: #A0A0A0;
    padding: 30px 0;
  }

}
@media screen and (max-width: 720px) {
  .choose_foot{
    width: 100%;
    text-align: center;
    align-items: center;
    padding: 1.5rem 1.2rem;
    font-size: 1.2rem;
    color: #A0A0A0;
  }

}

</style>
