<template>
  <ul class="timeline-wrapper" @scroll="scrollEvent">
    <li class="timeline-item" :class="{'timeline-item11': index1 === (timelineList.length - 1) }"  v-for="(item, index1) in timelineList" :key="index1">
      <div class="timeline-box">
        <div class="out-circle">
          <div class="in-circle"></div>
          <div class="timeline-date" v-if="index1 < (timelineList.length - 1)">
<!--            <el-popover-->
<!--              placement="bottom"-->
<!--              title="标题"-->
<!--              width="200"-->
<!--              trigger="hover"-->
<!--              :content="item.content"-->
<!--            >-->
<!--              <el-button type="text" slot="reference" class="father-text">{{-->
<!--                  item.date-->
<!--                }}</el-button>-->
<!--            </el-popover>-->

            <el-button type="text" slot="reference" class="father-text">{{ item.date }}</el-button>
          </div>
        </div>
        <div v-if="index1 < (timelineList.length - 1)" class="long-line">
          <div class="sub-item-box">
            <div class="sub-line-box top-line-box" v-show="item.content">
              <div class="children-box top-children-box">
                {{ item.content }}
              </div>
              <div class="children-line-box top-line"></div>
            </div>
          </div>
        </div>
      </div>
    </li>
  </ul>
</template>
<script>
export default {
  name: "Timeline",
  props: {
    timelineList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  mounted() {},
  methods: {
    scrollEvent(e) {
      this.$emit("scrollEvent", e);
    },
    handleBottomClick() {
      this.$emit("handleBottomClick");
    },
  },
}
</script>
<style scoped lang="scss">
//PC端
@media screen and (min-width: 720px) {
  ul.timeline-wrapper {
    list-style: none;
    margin: 0;
    padding: 0;
    padding: 62px 0 20px 20px;
    white-space: nowrap;
  }

  /* 时间线 */
  .timeline-item {
    position: relative;
    display: inline-block;
    width: 153px;

    .timeline-box {
      text-align: center;

      // position: absolute;
      display: flex;
      align-items: center;

      .out-circle {
        width: 16px;
        height: 16px;
        background: rgba(14, 116, 218, 0.3);
        /*opacity: 0.1;*/
        border-radius: 50%;
        display: flex;
        align-items: center;
        cursor: pointer;

        .in-circle {
          width: 8px;
          height: 8px;
          margin: 0 auto;
          background: rgba(14, 116, 218, 1);
          border-radius: 50%;
        }

        .timeline-date {
          color: #333;
          margin-top: 40px;

          .father-text {
            font-weight: 900;
            font-size: 16px;
            margin-left: -42px;
          }
        }
      }

      .long-line {
        width: 153px;
        height: 2px;
        background: rgba(14, 116, 218, 0.2);
        display: flex;
        flex-direction: revert;
        justify-content: space-around;

        .sub-item-box {
          margin-top: -20px;
          position: relative;

          .sub-line-box {
            // cursor: pointer;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .children-line-box {
              width: 0px;
              border-left: 1px solid rgba(14, 116, 218, 0.3);
            }

            .children-box {
              flex-wrap: wrap;
              display: flex;
              justify-content: center;
              align-items: center;
              border: 1px solid rgba(14, 116, 218, 0.3);
              white-space: break-spaces;
              text-align: center;
              padding: 5px;
              font-size: 16px;
              border-radius: 10px;
            }
          }

          .top-line-box {
            margin-top: -46px;
            margin-left: -153px;
          }

          .bottom-line-box {
            margin-top: 5px;
            height: 150px;
          }

          .top-line {
            height: 8px;
          }

          .bottom-line {
            height: 153px;
          }

          .top-children-box {
            margin-top: -10px;
            // height: 30px;
            width: 126px;
          }

          .bottom-children-box {
            // height: 120px;
            width: 150px;
          }
        }
      }
    }

    .timeline-content {
      box-sizing: border-box;
      margin-left: 20px;
      height: 106px;
      padding: 0 0 0 20px;
      text-align: left;
      margin-bottom: 30px;

      .timeline-title {
        font-size: 14px;
        word-break: break-all;
        margin-bottom: 16px;
        color: #333;
        font-weight: 500;
        /*display: inline;*/
      }

      .timeline-desc {
        font-size: 14px;
        color: #999999;
      }
    }
  }

  .timeline-item11 {
    width: 20px;
  }

  .timeline-item:last-of-type .timeline-content {
    margin-bottom: 0;
  }
}
//移动端
@media screen and (max-width: 720px) {
  ul.timeline-wrapper {
    list-style: none;
    margin: 0;
    padding: 0;
    padding: 8.5rem 0 3rem 1rem;
    white-space: nowrap;
    overflow-x: auto;
  }

  /* 时间线 */
  .timeline-item {
    position: relative;
    display: inline-block;
    width: 10rem;

    .timeline-box {
      text-align: center;

      // position: absolute;
      display: flex;
      align-items: center;

      .out-circle {
        width: 16px;
        height: 16px;
        background: rgba(14, 116, 218, 0.3);
        box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.4);
        /*opacity: 0.1;*/
        border-radius: 50%;
        display: flex;
        align-items: center;
        cursor: pointer;

        .in-circle {
          width: 8px;
          height: 8px;
          margin: 0 auto;
          background: rgba(14, 116, 218, 1);
          border-radius: 50%;
          box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.1);
        }

        .timeline-date {
          color: #333;
          margin-top: 40px;

          .father-text {
            font-weight: 900;
            font-size: 16px;
            margin-left: -42px;
          }
        }
      }

      .long-line {
        width: 10rem;
        height: 2px;
        background: rgba(14, 116, 218, 0.2);
        box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.3);
        display: flex;
        flex-direction: revert;
        justify-content: space-around;

        .sub-item-box {
          margin-top: -20px;
          position: relative;

          .sub-line-box {
            // cursor: pointer;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .children-line-box {
              width: 0px;
              border-left: 1px solid rgba(14, 116, 218, 0.3);
            }

            .children-box {
              flex-wrap: wrap;
              display: flex;
              justify-content: center;
              align-items: center;
              border: 1px solid rgba(14, 116, 218, 0.3);
              white-space: break-spaces;
              text-align: center;
              padding: 5px;
            }
          }

          .top-line-box {
            margin-top: -3.7rem;
            margin-left: -10rem;
          }

          .bottom-line-box {
            margin-top: 5px;
            height: 150px;
          }

          .top-line {
            height: 3.8rem;
          }

          .bottom-line {
            height: 153px;
          }

          .top-children-box {
            margin-top: -2.8rem;
            width: 8rem;
          }

          .bottom-children-box {
            // height: 120px;
            width: 150px;
          }
        }
      }
    }

    .timeline-content {
      box-sizing: border-box;
      margin-left: 20px;
      height: 106px;
      padding: 0 0 0 20px;
      text-align: left;
      margin-bottom: 30px;

      .timeline-title {
        font-size: 14px;
        word-break: break-all;
        margin-bottom: 16px;
        color: #333;
        font-weight: 500;
        /*display: inline;*/
      }

      .timeline-desc {
        font-size: 14px;
        color: #999999;
      }
    }
  }

  .timeline-item11 {
    width: 3rem;
  }

  .timeline-item:last-of-type .timeline-content {
    margin-bottom: 0;
  }
}
</style>
