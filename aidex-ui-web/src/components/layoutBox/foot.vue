<template>
  <div class="login_foot_pc">
    <foot />
  </div>
</template>

<script>
import foot from '@/components/foot/foot.vue'
export default {
  components: {
    foot,
  },
  data() {
    return {}
  },
  mounted() {},
  beforeDestroy() {},
  created () {},
  methods: {},
};

</script>
<style lang="scss" scoped>
@media screen and (min-width: 720px) {
  .pc_show{}
  .mob_show{
    display: none;
  }
  .login_foot_pc {
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 2;
    width: 100%;
    height: 80px;
    background: url('../../../public/image/foot_bg.png') no-repeat 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

}
@media screen and (max-width: 720px) {
  .pc_show{
    display: none;
  }
  .mob_show{
  }
  .login_foot_pc {
    width: 100%;
    padding:1.2rem 1.8rem;
    height: auto;
    background: url('../../../public/image/foot_bg.png') no-repeat 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    div{
      width: 100%;
      font-size: 1.2rem;
      height: auto;
      text-align: center;
      align-items: center;
      color: #A0A0A0;
    }
  }

}

</style>
