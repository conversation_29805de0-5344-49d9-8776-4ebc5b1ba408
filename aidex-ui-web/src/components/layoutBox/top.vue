<template>
  <div class="login_head_pc">
    <div class="head_left" @click="toIndex()">
      <img class="pc_show" src="../../../public/image/new_logo.svg" alt="" />
      <img class="mob_show" src="../../../public/image/new_logo1.svg" alt="" />
    </div>
<!--    <div class="head_right">-->
<!--      <div @click="handleClick(item)" v-for="(item, index) in navData" :key="index">-->
<!--        <img :src="item.url" alt="" />-->
<!--        <span>{{ item.name }}</span>-->
<!--      </div>-->
<!--    </div>-->
  </div>
</template>

<script>

export default {
  data() {
    return {
      navData:[]
    }
  },
  mounted() {},
  beforeDestroy() {},
  created () {
    this.navData = [
      {name:"招生政策", link:'newsList', type: this.$constants.POLICY_KEY, id: this.$constants.POLICY_ID, url: require("/public/image/icon2.svg")},
      {name:"操作指南",link:'newsArticle', type: this.$constants.GUIDE_KEY, id: this.$constants.GUIDE_ID, url: require('/public/image/icon4.svg')},
    ]
  },
  methods: {
    handleClick(val) {
      this.$router.push({
        path: '/' + val.link,
        query: {
          id: val.id,
          name: val.name,
        }
      })
    },
    toIndex () {
      window.location.href = '/'
    }
  },
};

</script>
<style lang="scss" scoped>
  //媒体查询 自适应
  //PC端
  @media screen and (min-width: 720px) {
    .pc_show{}
    .mob_show{
      display: none;
    }
    .login_head_pc {
      position: fixed;
      top: 0px;
      left: 0px;
      width: 100%;
      height: 140px;
      display: flex;
      padding: 0 20px 0 40px;
      justify-content: space-between;
      align-items: center;
      z-index: 2;
      background: url('../../../public/image/top_bg.png') no-repeat 100% 100%;
      .head_left {
        img {
          height: 80px;
        }
      }
      .head_right {
        display: flex;
        div {
          width: 168px;
          height: 46px;
          background: #fff;
          margin-right: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 25px;
          box-shadow: 0 10px 30px rgba(93, 132, 177, .11);
          cursor: pointer;
          span {
            display: block;
            font-size: 18px;
            color: #242424;
            margin-left: 10px;
          }
          img {
            width: 22px;
            height: 24px;
          }
        }
      }
    }
  }
  //移动端
  @media screen and (max-width: 720px) {

    .pc_show{
      display: none;
    }
    .mob_show{
    }
    .login_head_pc {
      width: 100%;
      height: auto;
      display: flex;
      padding: 1.5rem 0;
      justify-content: center;
      flex-flow: column;
      align-items: center;
      z-index: 2;
      background: url('../../../public/image/top_bg.png') no-repeat 100% 100%;
      .head_left {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 80%;
          transform: translateX(1%);
          margin-bottom: 0.5rem;
        }
      }
      .head_right {
        display: flex;
        justify-content: center;
        div {
          width: 11rem;
          height: 3.2rem;
          background: #fff;
          margin:1rem 0.6rem 0 0.6rem;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 100px;
          box-shadow: 0 1rem 1.8rem rgba(93,132,177,.11);
          cursor: pointer;

          span {
            display: block;
            font-size: 1.2rem;
            color: #242424;
            margin-left: 0.5rem;
          }
          img {
            width: auto;
            height: 1.5rem;
          }
        }
      }
    }
  }
</style>
