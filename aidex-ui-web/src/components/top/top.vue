<template>
  <div class="choose_head">
    <div class="head_left">
      <img src="../../../public/image/new_logo.svg" alt="" />
      <img src="../../../public/image/top_logo.svg" alt="" />
    </div>
    <div class="head_right">
      <div class="right_box pc_show">
        <div v-for="(item, index) in navData" :key="index"><span @click="linkto(item)">{{ item.name }}</span></div>
      </div>
      <div class="head_right1" v-if="isToken">
          <div class="mob_menu" @click="drawer = true"><img src="../../../public/image/icon8.svg" alt="" /></div><!--这里点击打开菜单-->
          <el-dropdown class="pc_show">
            <span class="el-dropdown-link" :class="{'el-icon-bell': messageTotal > 0}"><img src="../../../public/image/icon7.svg" alt="" /></span><!--有新消息时插入class:el-icon-bell-->
            <el-dropdown-menu slot="dropdown">
              <div class="username">{{ userInfo.name }}</div>
              <el-dropdown-item><div @click="tomessage">我的消息(<span>{{ messageTotal }}</span>)</div></el-dropdown-item>
              <el-dropdown-item><div @click="handlePassword">修改密码</div></el-dropdown-item>
              <el-dropdown-item><div @click="handleLogout">退出登录</div></el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
      </div>
<!--      <div class="head_right2" @click="tologin" v-else>-->
<!--        <img src="../../../public/image/icon7.svg" alt="" />-->
<!--        <span>用户登录</span>-->
<!--      </div>-->
    </div>
    <el-drawer
      title="我是标题"
      :visible.sync="drawer"
      :with-header="false"
      :append-to-body="true"
      :modal-append-to-body="false"
    >
      <div class="icon" @click="iconClick"><i class="el-icon-circle-close"></i></div>
      <div class="user">
        <div class="user_name">{{ userInfo.name }}</div>
<!--        <div class="user_but">-->
<!--          <button @click="handlePassword">修改密码</button>-->
<!--          <button @click="handleLogout">退出登录</button>-->
<!--        </div>-->
  </div>
      <div class="mob_menu_con">
        <div v-for="(item, index) in navData" :key="index" @click="linkto(item)">{{ item.name }}</div>
        <div @click="tomessage">我的消息(<span>{{ messageTotal }}</span>)</div>
        <div @click="handlePassword">修改密码</div>
        <div @click="handleLogout">退出登录</div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {logout} from '@/utils/logout'
import { userMessageList } from '@/api/msgSubject'

export default {


  data() {
    return {
      drawer: false,
      navData:[],
      userInfo: {},
      messageTotal: 0,
      queryParam: {
        pageNum: 1,
        pageSize: 10
      },
      isToken: false,
      routerQuery: {},
    }
  },
  mounted() {},
  beforeDestroy() {},
  created () {
    this.routerQuery = this.$commonUtils.getApplyParams(this.$constants.STORE_CACHE_KEY_APPLY) || {}
    this.navData = [
      {name:"招录首页",link:'index'},
      {name:"我的报名",link:'application'},
      {name:"招生政策",link:'list', type: this.$constants.POLICY_KEY},
      {name:"各校招生信息",link:'list', type: this.$constants.ADMISSION_KEY},
      {name:"操作指南",link:'list', type: this.$constants.GUIDE_KEY},
      {name:"咨询电话",link:'list', type: this.$constants.CONSULTING_KEY}
    ],
    this.isToken = this.checkAuthToken();
    if (this.isToken) {
      this.userInfo = this.$store.getters.userInfo || {}
      this.loadData()
    }
  },
  methods: {
    loadData() {
      userMessageList({
        ...this.queryParam,
        readFlag: false
      }).then((messageResponse) => {
          this.messageTotal = messageResponse.data.total
      })
    },
    linkto(val) {
      const queryParams = {}
      let classId = '', className = ''
      if (val.type === this.$constants.POLICY_KEY) {
        className = val.name
        classId = this.$constants.POLICY_ID
      } else if (val.type === this.$constants.ADMISSION_KEY) {
        className = val.name
        classId = this.$constants.ADMISSION_ID
      } else if (val.type === this.$constants.GUIDE_KEY) {
        className = val.name
        classId = this.$constants.GUIDE_ID
      } else if (val.type === this.$constants.CONSULTING_KEY) {
        className = val.name
        classId = this.$constants.CONSULTING_ID
      }
      queryParams.classId = classId
      queryParams.className = className
      queryParams.cityId = this.routerQuery.cityId
      queryParams.cityName = this.routerQuery.cityName
      queryParams.areaId = this.routerQuery.areaId
      queryParams.areaName = this.routerQuery.areaName
      this.$commonUtils.setApplyParams(queryParams, this.$constants.STORE_CACHE_KEY_APPLY)
      this.$router.push({
        path: '/' + val.link,
        query: {
          name: className
        }
      })
      this.iconClick()
    },
    iconClick(){
      this.drawer=false
    },
    handleLogout() {
      logout()
    },
    handlePassword() {
      this.$router.push({path: "/password"})
    },
    tomessage() {
      this.$router.push({path: "/message"})
    },
    tologin(){
      this.$router.push({path: "/login"})
    },
  },
};

</script>
<style lang="scss" scoped>
@media screen and (min-width: 720px) {
  .username{
    width: calc(100% - 8px);
    font-size: 16px;
    background: #fafafa;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    margin-left: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding:5px 10px;
    margin: 0 5px;
  }
  .el-dropdown-menu__item{
    text-align: center!important;
    span{
      color: #ff3300!important;
    }
  }
  .choose_head {
    width: 1200px;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    box-shadow: 0 5px 25px rgba(53,112,246,.1);
    margin-left: 50%;
    border-bottom: 3px solid #e1e1e1;
    transform: translateX(-50%);
    border-radius: 16px 16px 0 0;
    margin-top: 30px;
    .head_left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      img {
        width: auto;
        height: 36px;
        margin-left: 42px;
        &:nth-child(1){
          display: none;
        }
      }
    }
    .head_right {
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: flex-end;
    }
    .right_box {
      display: flex;
      align-items: center;
      justify-content: center;
      padding:  0 20px 0 0;
      div {
        width: auto;
        margin: 0 15px;
        cursor: pointer;
        span{
          color: #383838;
          cursor: pointer;
        }
        &:hover{
          color: #0B7DFB;
          span{
            color: #0B7DFB;
            cursor: pointer;
          }
        }
      }
    }
    .head_right1 {
      display: none;
      width: 46px;
      height: 46px;
      background: #F1F1F1;
      box-shadow: 0px 5px 10px rgba(93,132,177,0.3);
      border-radius: 100px 100px 100px 100px;
      text-align: center;
      margin-right: 46px;
      .el-dropdown-link{
        width: 46px;
        height: 46px;
        font-size: 18px;
        color: #242424;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        &.el-icon-bell::before{
          display: none;
        }
        &.el-icon-bell::after{
          content: "\e725";
          font-size: 11px;
          color: #ffffff;
          width: 16px;
          height: 16px;
          background: #ff3300;
          position: absolute;
          top: -5px;
          right: -5px;
          border-radius: 100px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        img{
          width: 70%;
          cursor: pointer;
        }
      }

      ::v-deep .el-icon-arrow-down{
        width: 168px;
        line-height: 46px;
      }
    }
    .head_right2 {
      width: 168px;
      height: 46px;
      background: #fff;
      margin-right: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 25px;
      box-shadow: 0 10px 30px rgba(93, 132, 177, .11);
      cursor: pointer;
      span {
        display: block;
        font-size: 18px;
        color: #242424;
        margin-left: 10px;
      }
      img {
        width: 22px;
        height: 24px;
      }
    }
  }
  .mob_menu{
    display: none;
  }
}
@media screen and (max-width: 720px) {
  .pc_show{
    display: none;
  }
  .choose_head {
    width: 100%;
    height: auto;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(255,255,255,.9);
    box-shadow: 0 5px 25px rgba(53, 112, 246, 0.1);
    .head_left {
      width: calc(100% - 3.4rem);
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 0.8rem 0 0.8rem 1rem;
      img {
        width: 90%;
        transform: translateY(0.1rem);
        &:nth-child(2){
          display: none;
        }
      }
    }
    .head_right {
      display: flex;
      align-items: center;
      margin-right: 0.8rem;
      width: 2.4rem;
      height: 2.4rem;
    }

    .head_right1 {
      width: 2.4rem;
      height: 2.4rem;
      text-align: center;
      .user {
        width: 100%;
        display: flex;
        justify-content: space-evenly;
        padding: 1rem 0;
        .icon{
          font-size: 1.2rem;
        }
      }
      .mob_menu{
        display: flex;
        width: 2.4rem;
        height: 2.4rem;
        justify-content: center;
        align-items: center;
        img{
          width: 90%;
        }
      }
    }
    .head_right2 {
      width: 2.4rem;
      height: 2.4rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color:#242424;
      cursor: pointer;
      margin-right: 1rem;
      span {
        display: none;
      }
      img {
        height: 90%;
        cursor: pointer;
      }
    }
  }
  ::v-deep .el-drawer {
    width: 20rem!important;
    background: #fafafa;
    .el-drawer__body{
      .icon{
        position: absolute;
        left: 0.5rem;
        top:0.5rem;
        width: 2rem;
        height: 2rem;
        font-size: 1.8rem;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgba(0,0,0,0.5);
        color: #ffffff;
        border-radius: 100px;
      }
    }
    .user{
      width:100%;
      height: auto;
      display: flex;
      flex-flow: column;
      justify-content: center;
      align-items: center;
      background: #0b7dfb;
      background: #0b7dfb;
      background: -webkit-linear-gradient(to left bottom, #17E7FF, #0B7DFB);
      background: linear-gradient(to left bottom, #17E7FF, #0B7DFB);
      border-radius:0;
      box-shadow: 0 1rem 2rem #0b7dfb33;
      padding:2rem;
      .user_name{
        font-size: 2rem;
        color: #ffffff;
        font-weight: bold;
      }
      .user_but{
        width: 100%;
        display: flex;
        justify-content: space-between;
        button{
          width: auto;
          height: auto;
          background: rgba(255,255,255,.2);
          padding:0.3rem 1rem;
          display: flex;
          font-size: 1.1rem;
          align-items: center;
          justify-content: center;
          border-radius: 100px;
          border: 1px solid #f1f1f1;
          cursor: pointer;
          color: #ffffff;
        }
      }
    }
    .mob_menu_con{
      width: calc(100% - 3rem);
      display: flex;
      flex-flow: column;
      margin: 0 0 0 1.5rem;
      div{
        width: 100%;
        height: auto;
        padding:0.8rem 1.2rem;
        background: #ffffff;
        margin-top: 1.2rem;
        border-radius: 0.4rem;
        box-shadow: 0 0.4rem 0.7rem rgba(0,0,0,.06);
        font-size: 1.2rem;
        span{
          color: #ff3300;
        }
        &:last-child{
          color: #ff3300;
          border-radius:100px;
          text-align: center;
          box-shadow:none;
          background: #ff330010;
        }
      }
    }
  }
}

</style>
