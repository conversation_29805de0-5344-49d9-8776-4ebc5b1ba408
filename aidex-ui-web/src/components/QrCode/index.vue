<template>
  <div style="width: 100%; height: 100%;" :id="id" :ref="id"></div>
</template>

<script>
  import QRCode from 'qrcodejs2'
  export default {
    name: "QrCode",
    data() {
      return {
        qrCodeUrl: ''
      }
    },
    props: {
      id: {
        type: String,
        required: false,
        default: 'qrCode'
      },
      codeUrl: {
        type: String,
        default: ''
      },
      width: {
        type: String,
        default: '200'
      },
      height: {
        type: String,
        default: '200'
      },
      colorDark: {
        type: String,
        default: '#000000'
      },
      colorLight: {
        type: String,
        default: '#ffffff'
      }
    },
    watch: {
      codeUrl(newText) {
        this.createQrCode()
      }
    },
    mounted() {
      this.createQrCode()
    },
    methods: {
      // 有新的二维码地址了，先把之前的清除掉
      createQrCode() {
        if (this.qrcode) {
          this.$refs[this.id].innerHTML = ''
        }
        this.qrcode = new QRCode(this.$refs[this.id], {
          text: this.codeUrl,
          width: this.width,
          height: this.height,
          colorDark: this.colorDark,
          colorLight: this.colorLight,
          correctLevel: QRCode.CorrectLevel.H,
        })
      }
    }
  }
</script>

<style scoped>

</style>
