<template>
  <div id="vue_web">
    <router-view />
  </div>
</template>

<style lang="less" module>
*{
  cursor: default;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
ol, ul, li, dl, dd, dt { list-style:none; }
body{background-color: #eff6ff !important;}

.header {
  margin: 1.5em;
  text-align: center;
  a {
    margin: 0 0.3em;
    color: inherit;
    font-weight: bold;
    &:global(.router-link-exact-active) {
      color: #42b983;
    }
  }
}
</style>

<style lang="scss" >
.codeContent {
  max-width: 400px;
  margin: 0 auto;
  padding-top: 25vh;
}
.code-copy-added {
  background-color: #282c34;
  color: white;
  padding: 2px 2px;
  margin: 10px 0;
  text-align: left;
  border-radius: 3px;
  position: relative;
}
.code-copy-added:hover .copy-btn {
  opacity: 1;
}
@media screen and (max-width: 720px) {

  .el-message-box{
    width: 90%;
    padding:0 0 0.6rem 0;
    border-radius: 0.4rem;
    font-size: 1.2rem;
    .el-message-box__header{
      padding: 1rem 1rem 0.6rem;
      .el-message-box__title{
        font-size: 1.5rem;
      }
      .el-message-box__headerbtn{
        top: 1rem;
        right: 1rem;
        font-size: 1.4rem;
      }
    }
    .el-message-box__content{
      padding:0.8rem 1.2rem;
      font-size: 1.2rem;
      .el-message-box__status{
        font-size: 2rem!important;
      }
      .el-message-box__message{
        padding-left: 3rem;
        padding-right: 1rem;
        p{
          line-height: 1.8rem;
        }
      }
    }
    .el-message-box__btns{
      padding: 0.4rem 1.2rem 0;
      .el-button--small{
        padding:0.8rem 1.4rem;
        font-size: 1rem;
      }
    }
  }

}
</style>
