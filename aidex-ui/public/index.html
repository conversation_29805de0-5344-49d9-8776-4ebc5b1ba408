<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <link id="favicon" rel="icon" href="/favicon.ico" />
    <title>Welcome</title>
    <style>.first-loading-wrp{display:flex;justify-content:center;align-items:center;flex-direction:column;min-height:420px;height:100%}.first-loading-wrp>h1{font-size:128px}.first-loading-wrp .loading-wrp{padding:98px;display:flex;justify-content:center;align-items:center}.dot{animation:antRotate 1.2s infinite linear;transform:rotate(45deg);position:relative;display:inline-block;font-size:32px;width:32px;height:32px;box-sizing:border-box}.dot i{width:14px;height:14px;position:absolute;display:block;background-color:#1890ff;border-radius:100%;transform:scale(.75);transform-origin:50% 50%;opacity:.3;animation:antSpinMove 1s infinite linear alternate}.dot i:nth-child(1){top:0;left:0}.dot i:nth-child(2){top:0;right:0;-webkit-animation-delay:.4s;animation-delay:.4s}.dot i:nth-child(3){right:0;bottom:0;-webkit-animation-delay:.8s;animation-delay:.8s}.dot i:nth-child(4){bottom:0;left:0;-webkit-animation-delay:1.2s;animation-delay:1.2s}@keyframes antRotate{to{-webkit-transform:rotate(405deg);transform:rotate(405deg)}}@-webkit-keyframes antRotate{to{-webkit-transform:rotate(405deg);transform:rotate(405deg)}}@keyframes antSpinMove{to{opacity:1}}@-webkit-keyframes antSpinMove{to{opacity:1}}</style>
    <style>
      #smart-service-container {
        position: fixed;
        cursor: pointer;
        z-index: 1000;
        transition: transform 0.3s;
      }
      #smart-service-container:hover {
        transform: scale(1.1);
      }
      #smart-service-container img {
        width: 60px;
        height: 60px;
      }
      #smart-service-container .close-btn {
        position: absolute;
        top: -5px;
        right: -5px;
        background-color: #FFFFFF;
        border: 1px solid #D3D3D3;
        color: #D3D3D3;
        border-radius: 50%;
        width: 15px;
        height: 15px;
        text-align: center;
        line-height: 11px;
        cursor: pointer;
      }

      #smart-service-container-help {
        position: fixed;
        cursor: pointer;
        z-index: 1000;
        transition: transform 0.3s;
      }
      #smart-service-container-help:hover {
        transform: scale(1.1);
      }
      #smart-service-container-help img {
        width: 60px;
        height: 60px;
      }
      #smart-service-container-help .close-btn {
        position: absolute;
        top: -5px;
        right: -5px;
        background-color: #FFFFFF;
        border: 1px solid #D3D3D3;
        color: #D3D3D3;
        border-radius: 50%;
        width: 15px;
        height: 15px;
        text-align: center;
        line-height: 11px;
        cursor: pointer;
      }
    </style>
    <!-- require cdn assets css -->
    <% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.css) { %>
    <link rel="stylesheet" href="<%= htmlWebpackPlugin.options.cdn.css[i] %>" />
    <% } %>
    <script type="text/javascript" src="https://api.tianditu.gov.cn/api?v=4.0&tk=93d2382db73b87c1934deb364944e6bf"></script>
    <script type="text/javascript" src="//api.tongjiniao.com/c?_=707510743771893760" async></script>
    <script>
      var _hmt = _hmt || [];
      (function() {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?a82fdfb625b762040722d773ad77946e";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
      })();
    </script>
  </head>
  <body>
    <noscript>
      <strong>请启用JavaScript后再使用</strong>
    </noscript>
    <div id="app">
      <div class="first-loading-wrp">
        <div class="loading-wrp">
          <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
        </div>
        <!-- <div style="display: flex; justify-content: center; align-items: center;">Aidex Sharp</div> -->
      </div>
    </div>
    <div id="smart-service-container"></div>
    <div id="smart-service-container-help"></div>
    <!-- require cdn assets js -->
    <% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.js) { %>
    <script type="text/javascript" src="<%= htmlWebpackPlugin.options.cdn.js[i] %>"></script>
    <% } %>
    <!-- built files will be auto injected -->
    <script type="text/javascript">
      var baseApi = '<%= VUE_APP_BASE_API %>'
      window.onload = function () {
        getConfig()
      }
      function getConfig() {
        var xhr = new XMLHttpRequest();
        xhr.withCredentials = false;
        xhr.open('GET', baseApi + '/common/getSysConfig');
        xhr.onload = function() {
          var json = null;
          try {
            json = JSON.parse(xhr.responseText);
          } catch (err) {}
          if (json) {
            document.title = json.title || '';
            var favicon = document.getElementById('favicon');
            if (json.logo !== '' && json.logo !== 'false') {
              // 替换原来的icon
              if (favicon) {
                favicon.setAttribute("href", baseApi + json.logo)
              }
            } else {
              if (favicon) {
                favicon.setAttribute("href", '<%= BASE_URL %>')
              }
            }
          }
        };
        xhr.send();
      }
    </script>
    <script type="text/javascript">
      var url = 'https://robot.chaoxing.com/chat/web?unitId=196662&robotId=fa8cb9af0b1248039107729e8b48fef7#'
      var url_help = "https://ruxue.smartedu.gsedu.cn/help";

      var NODE_ENV = '<%= NODE_ENV %>';
      var VUE_APP_CONTEXT_PATH = ''
      if (NODE_ENV !== 'development') {
        VUE_APP_CONTEXT_PATH = '<%= VUE_APP_CONTEXT_PATH %>';
      }
      window.onload = function() {
        createService();
        createHelp();
      };

      // 创建智能客服入口
      function createService(){
        var serviceContainer = document.getElementById('smart-service-container');
        serviceContainer.style.bottom = '170px'; // 设置图标初始位置距离底部
        serviceContainer.style.right = '22px'; // 设置图标初始位置距离右侧

        var serviceIcon = document.createElement('img');
        serviceIcon.src = VUE_APP_CONTEXT_PATH + '/customer.png'; // 替换为你的智能客服图标路径
        serviceIcon.alt = '智能客服';
        serviceIcon.style.cursor = 'pointer';
        serviceContainer.appendChild(serviceIcon);

        var closeButton = document.createElement('span');
        closeButton.className = 'close-btn';
        closeButton.textContent = '×';
        closeButton.onclick = function() {
          serviceContainer.style.display = 'none';
        };
        serviceContainer.appendChild(closeButton);

        var isDragging = false;
        var startY, startBottom;

        // 点击图标打开新窗口的事件
        serviceIcon.onclick = function(event) {
          if (isDragging) {
            isDragging = false; // 重置拖动状态
            event.preventDefault(); // 阻止点击事件
          } else {
            // 执行点击事件的正常操作
            window.open(decodeURIComponent(url), '_blank');
          }
        };

        // 添加只能上下拖动的功能
        serviceContainer.onmousedown = function(event) {
          event.preventDefault(); // 防止默认拖动行为

          isDragging = false;
          startY = event.clientY;
          startBottom = parseInt(window.getComputedStyle(serviceContainer).bottom, 10);

          document.addEventListener('mousemove', onMouseMove);
          document.addEventListener('mouseup', stopDrag);
        };

        function onMouseMove(event) {
          isDragging = true; // 发生了拖动

          var delta = event.clientY - startY;
          var newBottom = startBottom - delta;

          // 限制图标的拖动范围不超过窗口的上下边界
          newBottom = Math.max(newBottom, 0);
          newBottom = Math.min(newBottom, window.innerHeight - serviceContainer.offsetHeight - 30); // 保留30px的底部空间

          serviceContainer.style.bottom = newBottom + 'px';
        }

        function stopDrag() {
          document.removeEventListener('mousemove', onMouseMove);
          document.removeEventListener('mouseup', stopDrag);
        }

        serviceContainer.ondragstart = function() {
          return false;
        };
      }

      // 创建使用帮助入口
      function createHelp(){
        var serviceContainer = document.getElementById('smart-service-container-help');
        serviceContainer.style.bottom = '90px'; // 设置图标初始位置距离底部
        serviceContainer.style.right = '22px'; // 设置图标初始位置距离右侧

        var serviceIcon = document.createElement('img');
        serviceIcon.src = VUE_APP_CONTEXT_PATH + '/help.png'; // 替换为你的智能客服图标路径
        serviceIcon.alt = '使用帮助';
        serviceIcon.style.cursor = 'pointer';
        serviceContainer.appendChild(serviceIcon);

        var closeButton = document.createElement('span');
        closeButton.className = 'close-btn';
        closeButton.textContent = '×';
        closeButton.onclick = function() {
          serviceContainer.style.display = 'none';
        };
        serviceContainer.appendChild(closeButton);

        var isDragging = false;
        var startY, startBottom;

        // 点击图标打开新窗口的事件
        serviceIcon.onclick = function(event) {
          if (isDragging) {
            isDragging = false; // 重置拖动状态
            event.preventDefault(); // 阻止点击事件
          } else {
            // 执行点击事件的正常操作
            window.open(decodeURIComponent(url_help), '_blank');
          }
        };

        // 添加只能上下拖动的功能
        serviceContainer.onmousedown = function(event) {
          event.preventDefault(); // 防止默认拖动行为

          isDragging = false;
          startY = event.clientY;
          startBottom = parseInt(window.getComputedStyle(serviceContainer).bottom, 10);

          document.addEventListener('mousemove', onMouseMove);
          document.addEventListener('mouseup', stopDrag);
        };

        function onMouseMove(event) {
          isDragging = true; // 发生了拖动

          var delta = event.clientY - startY;
          var newBottom = startBottom - delta;

          // 限制图标的拖动范围不超过窗口的上下边界
          newBottom = Math.max(newBottom, 0);
          newBottom = Math.min(newBottom, window.innerHeight - serviceContainer.offsetHeight - 30); // 保留30px的底部空间

          serviceContainer.style.bottom = newBottom + 'px';
        }

        function stopDrag() {
          document.removeEventListener('mousemove', onMouseMove);
          document.removeEventListener('mouseup', stopDrag);
        }

        serviceContainer.ondragstart = function() {
          return false;
        };
      }
    </script>
  </body>
</html>
