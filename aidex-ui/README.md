<h1 align="center">AiDex-Antdv</h1>

> AiDex-Antdv 是AiDex-Vue和Ant Design Vue Pro的结合。

<p align="center">
<p align="center">
 <a href="https://gitee.com/fuzui/AiDex-Antdv" target="_blank"><img src="https://gitee.com/fuzui/AiDex-Antdv/badge/star.svg?theme=dark" alt="Build Status"></a>
 <a href="https://github.com/fuzui/AiDex-Antdv" target="_blank"><img src="https://img.shields.io/github/stars/fuzui/AiDex-Antdv.svg?style=social" alt="Build Status"></a>
 <a href="https://gitee.com/y_project/AiDex-Vue" target="_blank"><img src="https://img.shields.io/badge/AiDex Vue-3.3.0-brightgreen" alt="Build Status"></a>
 <a href="https://github.com/vueComponent/ant-design-vue" target="_blank"><img src="https://img.shields.io/badge/Ant Design Vue-1.7.2-brightgreen" alt="Build Status"></a>
 <a href="https://github.com/vueComponent/ant-design-vue-pro" target="_blank"><img src="https://img.shields.io/badge/Ant Design Vue Pro-3.0.0-brightgreen" alt="Build Status"></a>
</p>

------------------------------

## 简介

**AiDex-Antdv**，使用[AiDex-Vue](https://gitee.com/y_project/AiDex-Vue)作为后端，改其原有Element Ui为Ant Design Vue。将会持续完全适配AiDex-Vue。

> AiDex-Vue是基于SpringBoot，Spring Security，JWT，Vue 的前后端分离权限管理系统。
>
> 拥有用户管理、部门管理、岗位管理、菜单管理、角色管理、字典管理、参数管理、通知公告、操作日志、登录日志、在线用户、定时任务、代码生成、系统接口、服务监控、在线构建器、连接池监视等功能。

*当前暂不支持AiDex中的在线构建器与前端代码生成功能。*

* 预览：[https://aidex.setworld.net/](https://aidex.setworld.net/)

* 文档: [https://docs.geekera.cn/AiDex-Antdv/](https://docs.geekera.cn/AiDex-Antdv/)

* AiDex-Vue文档: [https://doc.aidex.vip/aidex-vue/](https://doc.aidex.vip/aidex-vue/)

* Ant Design Vue文档：[https://www.antdv.com/docs/vue/introduce-cn/](https://www.antdv.com/docs/vue/introduce-cn/)



## 开始使用

1. 环境准备
   * 运行启动AiDex-Vue，参考[AiDex文档](https://doc.aidex.vip/aidex-vue/)
   * 安装[node](http://nodejs.org/)和[git](https://git-scm.com/)

1. 安装

   ```shell
   <NAME_EMAIL>:fuzui/AiDex-Antdv.git
   ```

   或

   ```shell
   <NAME_EMAIL>:fuzui/AiDex-Antdv.git
   ```

2. 本地开发

   进入项目根目录

   ```shell
   npm install
   ```

   > 若耗时太长可使用`npm install --registry=https://registry.npm.taobao.org[已弃用]`
   > 推荐使用`npm install --registry=https://registry.npmmirror.com`

   ```shell
   npm run serve
   ```

   > 打开浏览器访问 [http://localhost:8000](http://localhost:8080/)



## 致谢

* [AiDex-Vue](https://gitee.com/y_project/AiDex-Vue) 基于SpringBoot，Spring Security，JWT，Vue 的前后端分离权限管理系统
* [Ant Design Vue](https://github.com/vueComponent/ant-design-vue/) An enterprise-class UI components based on Ant Design and Vue.
* [Ant Design Vue Pro](https://github.com/vueComponent/ant-design-vue-pro) Use Ant Design Vue like a Pro

* [vditor](https://github.com/Vanessa219/vditor) 一款浏览器端的 Markdown 编辑



## 联系

如果您发现了什么bug，或者有什么界面建议或意见，

欢迎 [issue](https://github.com/fuzui/AiDex-Antdv/issues)



## 演示图

<table>
    <tr>
        <td><img src="https://oss.fuzui.net/img/20210102022024.png"/></td>
        <td><img src="https://oss.fuzui.net/img/20210102022136.png"/></td>
    </tr>
    <tr>
        <td><img src="https://oss.fuzui.net/img/20210102022247.png"/></td>
        <td><img src="https://oss.fuzui.net/img/20210102022534.png"/></td>
    </tr>
    <tr>
        <td><img src="https://oss.fuzui.net/img/20210102022749.png"/></td>
        <td><img src="https://oss.fuzui.net/img/20210102023153.png"/></td>
    </tr>
</table>



