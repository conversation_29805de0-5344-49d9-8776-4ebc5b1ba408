{"name": "vue-antd-pro", "version": "3.0.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint", "build:preview": "vue-cli-service build --mode preview", "build:gsedu": "vue-cli-service build --mode gsedu", "build:test": "vue-cli-service build --mode test", "lint:nofix": "vue-cli-service lint --no-fix"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@riophae/vue-treeselect": "0.4.0", "@tinymce/tinymce-vue": "^3.0.1", "ant-design-vue": "1.7.2", "axios": "^0.19.0", "bpmn-js": "^11.1.0", "clipboard": "^2.0.6", "core-js": "^3.1.2", "diagram-js": "^5.0.0", "echarts": "^5.0.0", "element-ui": "2.15.0", "enquire.js": "^2.1.6", "file-saver": "2.0.4", "font-awesome": "^4.7.0", "highlight.js": "^10.5.0", "html2canvas": "^1.4.1", "js-cookie": "2.2.1", "jsencrypt": "^3.3.1", "lodash.clonedeep": "^4.5.0", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "md5": "^2.2.1", "mockjs2": "1.0.8", "moment": "^2.24.0", "monaco-editor": "^0.23.0", "monaco-editor-webpack-plugin": "3.1.0", "nprogress": "^0.2.0", "qrcode": "^1.5.3", "quill": "1.3.7", "sass": "^1.77.6", "sass-loader": "7.0.3", "sortablejs": "^1.10.2", "store": "^2.0.12", "svg-sprite-loader": "^6.0.11", "tinymce": "^5.1.0", "v-viewer": "^1.5.1", "vcolorpicker": "^1.1.0", "vditor": "^3.7.3", "vkbeautify": "^0.99.3", "vue": "^2.6.12", "vue-amap": "^0.5.10", "vue-clipboard2": "^0.2.1", "vue-codemirror-lite": "^1.0.4", "vue-container-query": "^0.1.0", "vue-copy-to-clipboard": "^1.0.3", "vue-cropper": "0.4.9", "vue-grid-layout": "^2.3.12", "vue-i18n": "^8.17.4", "vue-print-nb": "^1.7.5", "vue-quill-editor": "^3.0.6", "vue-router": "^3.1.2", "vue-svg-component-runtime": "^1.0.1", "vue-upload-component": "^2.8.20", "vuedraggable": "^2.24.1", "vuex": "^3.1.1", "workflow-bpmn-modeler-antdv": "^1.0.7", "xcrud": "^0.4.19", "xlsx": "^0.18.5", "xlsx-style": "^0.8.13"}, "devDependencies": {"@ant-design/colors": "^3.2.1", "@vue/cli-plugin-babel": "^4.0.4", "@vue/cli-plugin-router": "^4.0.4", "@vue/cli-plugin-unit-jest": "^4.0.4", "@vue/cli-plugin-vuex": "^4.0.4", "@vue/cli-service": "^4.0.4", "@vue/test-utils": "^1.0.0-beta.29", "babel-plugin-import": "^1.12.2", "babel-plugin-transform-remove-console": "^6.9.4", "compression-webpack-plugin": "^5.0.1", "git-revision-webpack-plugin": "^3.0.6", "less": "^3.0.4", "less-loader": "^5.0.0", "opencollective": "^1.0.3", "opencollective-postinstall": "^2.0.2", "vue-svg-icon-loader": "^2.1.1", "vue-template-compiler": "^2.6.12", "webpack-theme-color-replacer": "^1.3.12"}}