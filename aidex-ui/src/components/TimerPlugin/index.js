/**
 * 定时任务插件
 */
export const TimerPlugin = {
  install(Vue) {
    // 定义全局方法
    Vue.prototype.$startGlobalTimer = function(callback, interval = 3000) {
      if (!this._globalTimer) {
        this._globalTimer = setInterval(callback, interval);
      }
    };

    Vue.prototype.$stopGlobalTimer = function() {
      if (this._globalTimer) {
        clearInterval(this._globalTimer);
        this._globalTimer = null;
      }
    };

    // 使用混入在组件生命周期中管理定时器
    Vue.mixin({
      beforeCreate() {
        // 页面加载时启动全局定时器
        if (this.$options.globalTimer) {
          const { callback, interval } = this.$options.globalTimer;
          this.$startGlobalTimer(callback.bind(this), interval);
        }
      },
      beforeDestroy() {
        // 组件销毁时清除定时器防止内存泄漏
        this.$stopGlobalTimer();
      }
    });
  }
};