<template>
  <div>
    <a-popover
      :visible="visible"
      trigger="click"
      placement="bottomLeft"
      overlayClassName="header-notice-wrapper"
      contentClass="FilterColumn"
      :getPopupContainer="popupContainer"
      :autoAdjustOverflow="true"
      :arrowPointAtCenter="true"
      :overlayStyle="{ width: '300px', top: '50px' }"
    >
      <template slot="content">
        <a-spin :spinning="loading">
          <a-tabs>
            <a-tab-pane tab="通知" key="1">
              <div>
                <a-list :data-source="noticeList">
                  <a-list-item slot="renderItem" slot-scope="item, index" @click="handleNoticeClick(item)">
                    <a-list-item-meta :key="index">
                      <a-avatar slot="avatar" size="large" :style="{backgroundColor: item.noticeType === '1' ? 'orange' : 'blue'}">
                        {{ selectDictLabel(typeOptions, item.noticeType) }}
                      </a-avatar>
                      <a slot="title">
                        <a-badge :status="item.isRead === '1' ? 'processing' : 'error'" />
                        <span>{{ item.noticeTitle }}</span>
                      </a>
                      <span slot="description" style="font-size: 12px">
                        {{ parseTime(item.createTime, '{y}-{m}-{d}') }} 来自： {{ item.createByName }}
                      </span>
                    </a-list-item-meta>
                  </a-list-item>
                </a-list>
              </div>
              <div class="more">
                <a @click="toNotice">查看更多</a>
              </div>
            </a-tab-pane>
            <a-tab-pane tab="消息" key="2">
              <div>
              <a-list :data-source="messageList">
                <a-list-item slot="renderItem" slot-scope="item, index" @click="handleMessageClick(item)">
                  <a-list-item-meta :key="index">
                    <a slot="title">
                      <a-badge :status="item.readFlag ? 'processing' : 'error'" />
                      <span>{{ item.subject }}</span>
                    </a>
                    <span slot="description" style="font-size: 12px">
                      {{ parseTime(item.sendDatetime, '{y}-{m}-{d}') }} 来自： {{ item.appName }} {{ item.senderName }}
                    </span>
                  </a-list-item-meta>
                </a-list-item>
              </a-list>
              </div>
              <div class="more">
                <a @click="toMessage">查看更多</a>
              </div>
            </a-tab-pane>
          </a-tabs>
        </a-spin>
      </template>
      <span @click="fetchNotice" class="header-notice" ref="noticeRef">
        <a-badge :count="msgCount">
          <a-icon style="font-size: 16px; padding: 4px" type="bell" />
        </a-badge>
      </span>
    </a-popover>
    <div>
      <div>
        <notice-view
            v-if="showNoticeViewModal"
            ref="noticeViewModal"
            @ok="getNoticeList"
            @close="showNoticeViewModal = false"
        />
      </div>
      <div>
        <message-view
            v-if="showMessageViewModal"
            ref="messageViewModal"
            @ok="getMessageList"
            @close="showMessageViewModal = false"
        />
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import NoticeView from '@/views/system/notice/modules/NoticeView'
import MessageView from '@/views/message/msgsubject/modules/MessageView'

export default {
  name: 'HeaderNotice',
  components: {
    NoticeView,
    MessageView
  },
  data () {
    return {
      loading: false,
      visible: false,
      queryParam: {
        pageNum: 1,
        pageSize: 10
      },
      noticeList: [],
      noticeTotal: 0,
      messageList: [],
      messageTotal: 0,
      // 类型数据字典
      typeOptions: [],
      showNoticeViewModal: false,
      showMessageViewModal: false,
    }
  },
  created() {
    this.getDicts('sys_notice_type').then(response => {
      this.typeOptions = response.data
    })
    this.loadData()
  },
  computed: {
    msgCount() {
      return this.noticeTotal + this.messageTotal
    }
  },
  methods: {
    loadData() {
      this.loading = true
      axios.all([
        this.listNoticeByUser({
          ...this.queryParam,
          isRead: '0'
        }),
        this.userMessageList({
          ...this.queryParam,
          readFlag: false
        })
      ]).then(axios.spread((noticeResponse, messageResponse) => {
          this.noticeList = noticeResponse.data.list
          this.noticeTotal = noticeResponse.data.total
          this.messageList = messageResponse.data.list
          this.messageTotal = messageResponse.data.total
          this.loading = false
      }))
    },
    async fetchNotice () {
      if (!this.visible) {
        await this.loadData()
        this.visible = true
      } else {
        this.visible = false
      }
    },
    toNotice () {
      this.$router.push({
        path: '/system/notice/NoticeReadIndex'
      })
      this.visible = false
    },
    toMessage () {
      this.$router.push({
        path: '/message/UserMessageList'
      })
      this.visible = false
    },
    /** 设为已读按钮操作 */
    setMsgRead (row = {}) {
      const msgSubjectIds = row.id
      this.setRead(msgSubjectIds).then(() => {})
    },
    popupContainer () {
      return this.$refs.noticeRef.parentElement
    },
    handleNoticeClick(record) {
      this.showNoticeViewModal = true
      this.$nextTick(() => (
        this.$refs.noticeViewModal.handleView(record)
      ))
    },
    getNoticeList() {
      this.listNoticeByUser({
        ...this.queryParam,
        isRead: '0'
      }).then((noticeResponse) => {
        this.noticeList = noticeResponse.data.list
        this.noticeTotal = noticeResponse.data.total
      })
    },
    handleMessageClick(record) {
      this.showMessageViewModal = true
      this.$nextTick(() => (
        this.$refs.messageViewModal.handleView(record)
      ))
    },
    getMessageList() {
      this.userMessageList({
        ...this.queryParam,
        readFlag: false
      }).then((messageResponse) => {
        this.messageList = messageResponse.data.list
        this.messageTotal = messageResponse.data.total
      })
    }
  }
}
</script>

<style lang="less" scoped>
  .header-notice{
    display: inline-block;
    transition: all 0.3s;
    cursor: pointer;
    span {
      vertical-align: middle;
    }
  }
  .more {
    text-align: center;
    padding: 8px 0 0 0;
    border-top: dotted 1px #eee;
  }
</style>
