<template>
  <div style="padding-top: 6px;">
    <a-tooltip>
      <template slot="title">{{ '下载中心' }}</template>
      <a-icon :style="{color: 'inherit', fontSize: '18px', cursor: 'pointer'}" :component="allIcon.downloadIcon" @click="toDownload"/>
    </a-tooltip>
    <div>
      <div>
        <download-view
            v-if="showDownloadViewModal"
            ref="downloadViewModal"
            @close="showDownloadViewModal = false"
        />
      </div>
    </div>
  </div>
</template>

<script>
import allIcon from '@/core/icons'
import DownloadView from '@/components/DownloadIcon/DownloadView'

export default {
  name: 'HeaderDownload',
  components: {
    allIcon,
    DownloadView
  },
  data () {
    return {
      allIcon,
      showDownloadViewModal: false
    }
  },
  created() {},
  computed: {},
  methods: {
    toDownload() {
      this.showDownloadViewModal = true
      this.$nextTick(() => (
        this.$refs.downloadViewModal.handleView()
      ))
    }
  }
}
</script>

<style lang="less" scoped>
</style>
