<template>
  <ant-modal
    modalWidth="980"
    modalHeight="650"
    :visible="open"
    :modal-title="modalTitle"
    :footer="false"
    :adjust-size="true"
    @cancel="cancel"
  >
    <template slot="content">
      <advance-table
          class="custom-table-download"
          :title="tableTitle"
          :pagination="{
            current: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total: total,
            showSizeChanger: true,
            showLessItems: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，总计 ${total} 条`,
            onChange: changeSize,
            onShowSizeChange: onShowSizeChange
          }"
          tableKey="base-download-index-table"
          rowKey="id"
          size="middle"
          @refresh="getList"
          :columns="columns"
          :data-source="tableDataList"
          :loading="loading"
          :format-conditions="true"
      >
        <div class="table-operations" slot="button">
          <a-button type="primary" @click="handleReload">
            <a-icon type="redo" />刷新
          </a-button>
        </div>
        <span slot="fileName" slot-scope="{text, record}">
          {{ record.fileName || '无' }}
        </span>
        <span slot="fileCreateTime" slot-scope="{text, record}">
          {{ parseTime(record.fileCreateTime, '{y}-{m}-{d} {h}:{i}:{s}') || '-/-' }}
        </span>
        <span slot="status" slot-scope="{text, record}">
          {{ formatStatus(record) }}
          <a-tooltip placement="topRight" style="cursor: pointer" v-if="record.status === '2'">
            <template slot="title">
              <span>{{ record.errorMsg || '生成错误' }}</span>
            </template>
            <a-icon type="exclamation-circle" theme="twoTone" two-tone-color="#eb2f96" />
          </a-tooltip>
        </span>
        <span slot="operation" slot-scope="{text, record}">
          <a @click="handleDownload(record)" v-if="(record.status === '1' || record.status === '9') && !disabled">
            下载
          </a>
          <span v-if="disabled"><a-icon type="stop" theme="twoTone" /></span>
        </span>
      </advance-table>
    </template>
  </ant-modal>
</template>
<script>
import AntModal from '@/components/pt/dialog/AntModal'
import AdvanceTable from "@/components/pt/table/AdvanceTable";
export default {
  name: 'DownloadView',
  components: {
    AntModal,
    AdvanceTable
  },
  data () {
    return {
      open: false,
      modalTitle: '下载中心',
      tableTitle: ' ',
      disabled: false,
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      tableDataList: [],
      // 查询参数
      queryParam: {
        pageNum: 1,
        pageSize: 10,
      },
      columns: [
        {
          title: '文件名称',
          dataIndex: 'fileName',
          align: 'center',
          width: '40%',
          scopedSlots: { customRender: 'fileName' }
        },
        {
          title: '创建时间',
          dataIndex: 'fileCreateTime',
          align: 'center',
          width: '25%',
          scopedSlots: { customRender: 'fileCreateTime' }
        },
        {
          title: '状态',
          dataIndex: 'status',
          align: 'center',
          width: '25%',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '操作',
          dataIndex: 'operation',
          align: 'center',
          width: '10%',
          scopedSlots: { customRender: 'operation' }
        }
      ]
    }
  },
  methods: {
    getList() {
      this.loading = true
      this.userDownloadFilesList(this.queryParam).then(response => {
        this.tableDataList = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 翻页操作 */
    changeSize (current, pageSize) {
      this.queryParam.pageNum = current
      this.queryParam.pageSize = pageSize
      this.getList()
    },
    /** 翻页操作 */
    onShowSizeChange (current, pageSize) {
      this.queryParam.pageSize = pageSize
      this.getList()
    },
    /** 刷新操作 */
    handleReload() {
      this.getList()
    },
    formatStatus(row) {
      const val = row ? row.status : ''
      if (val === '0') {
        return '正在生成中'
      } else if (val === '1') {
        return '已生成'
      } else if (val === '2') {
        return '生成失败'
      } else if (val === '9') {
        return '已下载'
      }
      return '生成异常'
    },
    handleDownload(row) {
      const that = this
      if (this.disabled) {
        this.$message.warning('文件正在下载中...', 3)
      }
      const id = row ? row.id : ''
      this.disabled = true
      this.readDownloadFile(id).then((response) => {
        const {code, data, msg} = response
        if (code === 200 && data && data.result && data.result === 'success') {
          const fileUrl = data.fileUrl || ''
          if (fileUrl) {
            this.download(fileUrl)
            this.$message.success('导出成功', 3)
            row.status = '9'
          } else {
            this.$message.warning('文件异常，请稍后重试', 3)
          }
        } else {
          let message = msg || '下载失败'
          this.$message.error(message, 3)
        }
        setTimeout(() => {
          that.disabled = false
        }, 1500)
      }).catch(() => {
        this.disabled = false
      })
    },
    /** 取消按钮 */
    cancel () {
      this.open = false
      this.reset()
      this.$emit('close')
    },
    /** 数据重置 */
    reset () {
      const initData = this.$options.data()
      if (initData) {
        this.disabled = initData.disabled
        this.total = initData.total
        this.tableDataList = initData.tableDataList
        this.queryParam = initData.queryParam
      }
    },
    /** 弹窗显示 */
    handleView () {
      this.reset()
      this.open = true
      this.getList()
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .custom-table-download {
  .header-bar {
    padding: 0 16px !important;
  }
  .table-box .filter-tabs-container {
    border-top: none !important;
  }
}
</style>
