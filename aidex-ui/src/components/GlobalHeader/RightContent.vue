<template>
  <div :class="wrpCls" style="margin-right:16px">
    <a-space size="middle">
      <header-notice />
      <header-download />
      <a-tooltip>
        <template slot="title">
          {{ fullScreen ? '退出全屏' : '切为全屏' }}
        </template>
        <a-icon
          :type="fullScreen ? 'fullscreen-exit' : 'fullscreen'"
          @click="toggleFullScreen"
          :style="{ fontSize: '18px'}" />
      </a-tooltip>
      <avatar-dropdown :menu="showMenu" :current-user="currentUser" :class="prefixCls" />
      <!-- 暂只支持中文，国际化可自行扩展 -->
      <!-- <select-lang :class="prefixCls" /> -->
    </a-space>
    <platform-version
      v-if="modalVisible"
      ref="platformVersionModal"
      @close="modalVisible = false"
    />
  </div>
</template>

<script>
  import AvatarDropdown from './AvatarDropdown'
  import SelectLang from '@/components/SelectLang'
  import PlatformVersion from './PlatformVersion'
  import {
    mapGetters
  } from 'vuex'
  import HeaderNotice from '@/components/NoticeIcon/NoticeIcon'
  import HeaderDownload from '@/components/DownloadIcon/DownloadIcon'
  export default {
    name: 'RightContent',
    components: {
      AvatarDropdown,
      SelectLang,
      PlatformVersion,
      HeaderNotice,
      HeaderDownload,
    },
    props: {
      prefixCls: {
        type: String,
        default: 'ant-pro-global-header-index-action'
      },
      isMobile: {
        type: Boolean,
        default: () => false
      },
      topMenu: {
        type: Boolean,
        required: true
      },
      theme: {
        type: String,
        required: true
      }
    },
    data () {
      return {
        modalVisible: false,
        showMenu: true,
        showPortalDefined: false,
        currentUser: {},
        fullScreen: false,
        msgCount: 0,
        docUrl: 'https://docs.geekera.cn/AiDex-Antdv/',
        githubUrl: 'https://github.com/fuzui/AiDex-Antdv'
      }
    },
    computed: {
      wrpCls () {
        return {
          'ant-pro-global-header-index-right': true,
          [`ant-pro-global-header-index-${(this.isMobile || !this.topMenu) ? 'light' : this.theme}`]: true
        }
      },
      ...mapGetters([
        'userType',
        'portalConfigs',
        'defaultPortal',
        'sysNoticeList'
      ])
    },
    mounted () {
      setTimeout(() => {
        this.currentUser = {
          name: 'RuoYi'
        }
      }, 1500)
      this.msgCount = this.sysNoticeList.length
    },
    methods: {
      showColorSetting () {
        this.$emit('showSetting')
      },
      toConsole () {
        this.$message.success(
          '尚未实现',
          3
         )
      },
      toNotice () {
        this.$router.push({
          path: '/system/notice/NoticeReadIndex'
        })
        this.msgCount = 0
      },
      toIndex (item) {
        this.$router.push({
          name: 'index',
          params: {
            key: item.id
          }
        })
        if (item.applicationRange === 'U') {
          // 当选中小页时用户自定义时，修改选中小页为默认小页
          this.defaultPortal.id = item.id
        }
        this.$emit('reloadTab', item)
      },
      toDesignIndex (item, type) {
        this.$message.success(
          '尚未实现',
          3
         )
      },
      // 全屏切换
      toggleFullScreen () {
        if (!document.fullscreenElement) {
          document.documentElement.requestFullscreen()
        } else {
          if (document.exitFullscreen) {
            document.exitFullscreen()
          }
        }
        this.fullScreen = !this.fullScreen
      },
      versionInfo () {
        this.modalVisible = true
        this.$nextTick(() => (
          this.$refs.platformVersionModal.showVersion()
        ))
      }
    }
  }
</script>
<style lang="less" scoped="scoped">
  .ant-pro-global-header {
    .anticon {
      vertical-align: middle;
    }
  }
.ant-modal-confirm-content{
  p {
    height: 15px;
  }
}
  .setUl {
    .ant-dropdown-menu-item {
      padding: 5px 32px;
      font-size: 12px;
    }
    .ant-dropdown-menu-item>.anticon:first-child {
      font-size: 12px;
    }
    .ant-dropdown-menu-item i {
      position: absolute;
      top: 10px;
      font-size: 12px;
      color:#969696;
    }
    .ant-dropdown-menu-item>a.homeTit {
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #333;
    }
    .menu-operation{
      text-align: center;
      i{
        position: relative;
        top:0px;
        margin-right: 5px;
      }
    }
    .menu-operation:hover{
      i{
        color:#1890ff
      }
    }
  }
</style>
