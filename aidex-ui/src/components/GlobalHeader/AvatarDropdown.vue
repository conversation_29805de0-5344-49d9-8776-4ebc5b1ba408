<template>
  <a-dropdown v-if="name" placement="bottomRight">
    <span class="ant-pro-account-avatar">
      <span>{{ name }}</span>
    </span>
    <template v-slot:overlay>
      <a-menu class="ant-pro-drop-down menu" :selected-keys="[]">
        <a-menu-item v-if="menu" key="settings" @click="handleToSettings">
          <a-icon type="setting" />
          个人中心
        </a-menu-item>
        <a-menu-divider v-if="menu" />
        <a-menu-item key="logout" @click="handleLogout">
          <a-icon type="logout" />
          退出登录
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
  <span v-else>
    <a-spin size="small" :style="{ marginLeft: 8, marginRight: 8 }" />
  </span>
</template>

<script>
import { Modal } from 'ant-design-vue'
import { mapGetters } from 'vuex'

export default {
  name: 'AvatarDropdown',
  props: {
    menu: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    ...mapGetters([
      'avatar',
      'name'
    ])
  },
  methods: {
    handleToCenter () {
      this.$router.push({ path: '/account/center' })
    },
    handleToSettings () {
      this.$router.push({ path: '/account/settings' })
    },
    handleLogout () {
      const that = this
      Modal.confirm({
        title: '提示',
        content: '确定注销并退出系统吗？',
        onOk: () => {
          that.$addOperaLog("登出")
          return that.$store.dispatch('Logout').then(() => {
            setTimeout(() => {
              if(process.env.VUE_APP_OAUTH_ENABLE && process.env.VUE_APP_OAUTH_ENABLE === 'true'){
                window.location.href = "http://szjz.gsedu.cn/passport/logout.html?refer=http%3A%2F%2Fwww.gs.smartedu.cn%2Fpassport%2Flogout.html%3Frefer%3Dhttps%3A%2F%2Fruxue.smartedu.gsedu.cn%2Fadmin%2F";
              }else{
                location.href = process.env.NODE_ENV === 'production' ? process.env.VUE_APP_CONTEXT_PATH + '/index' : '/index'
              }
            }, 1000)
          })
        },
        onCancel () {}
      })
    }
  }
}
</script>

<style lang="less" scoped>
.ant-pro-drop-down {
  /deep/ .action {
    margin-right: 8px;
  }
  /deep/ .ant-dropdown-menu-item {
    min-width: 160px;
  }
}
</style>
