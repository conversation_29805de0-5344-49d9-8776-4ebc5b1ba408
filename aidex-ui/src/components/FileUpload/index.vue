<template>
  <div class="upload-file">
    <a-upload
      ref="upload"
      class="upload-file-uploader"
      :multiple="false"
      :show-file-list="true"
      :action="uploadFileUrl"
      :headers="headers"
      :max-count="limit"
      :file-list="fileList"
      :remove="handleDelete"
      @preview="handlePreview"
      @change="handleUploadSuccess"
    >
      <!-- 上传按钮 -->
      <a-button :disabled="fileList.length >= limit && !loading" size="small" type="primary">
        <a-icon :type="loading ? 'loading' : 'upload'" />
        选取文件
      </a-button>
      <!-- 上传提示 -->
      <div v-if="showTip">
        请上传
        <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
        <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>
        的文件
      </div>
    </a-upload>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { validUrlDomains } from '@/utils/validate'

export default {
  name: "FileUpload",
  props: {
    // 值
    showFiles: Array,
    // 数量限制
    limit: {
      type: Number,
      default: 5,
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ["doc", "xls", "ppt", "txt", "pdf"],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    // 获取文件列表
    getFiles: {
      type: Function
    }
  },
  data() {
    return {
      loading: false,
      baseUrl: process.env.VUE_APP_BASE_API,
      uploadFileUrl: (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API : process.env.VUE_APP_BASE_URL + process.env.VUE_APP_BASE_API) + "/storage/upload", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: []
    };
  },
  watch: {
    showFiles: {
      handler(val) {
        if (val) {
          let temp = 1;
          // 首先将值转为数组
          const list = val;
          // 然后将数组转为对象数组
          this.fileList = list.map(item => {
            if (item.id) {
              let fileUrl = null;
              if (!validUrlDomains(item.url) && item.url.indexOf(this.baseUrl) === -1) {
                fileUrl = this.baseUrl + item.url;
              } else {
                fileUrl = item.url;
              }
              item = { id: item.id, name: item.name, url: fileUrl, status: 'done' };
            }
            item.uid = item.uid || new Date().getTime() + temp++;
            return item;
          });
        } else {
          this.fileList = [];
          return [];
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    },
  },
  methods: {
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType) {
        let fileExtension = "";
        if (file.name.lastIndexOf(".") > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
        }
        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true;
          if (fileExtension && fileExtension.indexOf(type) > -1) return true;
          return false;
        });
        if (!isTypeOk) {
          this.$message.error(`文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`);
          return false;
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }
      return true;
    },
    // 上传失败
    handleUploadError() {
      this.$message.error("上传失败");
    },
    // 上传成功回调
    handleUploadSuccess(res) {
      let file = res.file;
      let fileList = res.fileList;
      if(!this.handleBeforeUpload(file)) {
        return;
      }
      if (file.status === 'uploading') {
        this.loading = true;
      } else if (file.status === 'done') {
        this.loading = false;
        if(file.response) {
          const { code, data } = file.response;
          const { path, key } = data;
          if(code === 200) {
            this.$message.success("上传成功!");
            file.url = path;
            file.id = key;
          } else {
            file.status = 'error';
            this.handleUploadError();
          }
        }
      } else if (file.status === 'error') {
        this.loading = false;
        this.handleUploadError();
      }
      this.fileList = fileList.map(f => {
        if (file.uid === f.uid) {
          f = file;
        }
        return f;
      });
      if(file.status === 'done') {
        this.getFiles(this.fileList);
      }
    },
    // 删除文件
    handleDelete(file) {
      const index = this.fileList.map(f => f.uid).indexOf(file.uid);
      if(index > -1) {
        this.fileList.splice(index, 1);
        this.getFiles(this.fileList);
      }
    },
    // 预览
    handlePreview(file) {
      let url = file.response ? file.response.url : file.url;
      if(url) {
        window.open(url, "_blank");
      }
    },
    // 获取文件名称
    getFileName(name) {
      if (name && name.lastIndexOf("/") > -1) {
        return name.slice(name.lastIndexOf("/") + 1);
      } else {
        return "";
      }
    },
    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = "";
      separator = separator || ",";
      for (let i in list) {
        strs += list[i].url + separator;
      }
      return strs != '' ? strs.substr(0, strs.length - 1) : '';
    }
  }
};
</script>

<style>
.upload-file-uploader {
  margin-bottom: 5px;
}
</style>
