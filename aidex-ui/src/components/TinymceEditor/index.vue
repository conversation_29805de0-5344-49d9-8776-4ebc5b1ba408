<template>
  <div class="tinymce-editor">
    <editor v-if="open" :id="tinymceId" :key="tinymceFlag" :disabled='disabled' v-model="content" :init="init"></editor>
  </div>
</template>

<script>

  import {uploadFile} from "@/api/common";
  /**
   * 官网：https://www.tiny.cloud/
   *
   * 中文文档：http://tinymce.ax-z.cn/
   */
  import tinymce from 'tinymce/tinymce'
  import Editor from '@tinymce/tinymce-vue'
  /**
   * 引入本地托管版本的 tinymce 资源
   *
   * 进入官网的自托管版本下载页面（https://www.tiny.cloud/get-tiny/self-hosted/），下载到本地
   * 解压缩后打开该文件夹，发现里面有 bin、js、modules、patches 等文件夹和文件
   * 然后双击打开 js 文件夹，发现里面有 tinymce 文件夹
   * 将这个 tinymce 文件夹整个复制到项目的 public 文件夹内，进行本地引用，即资源位置：public/tinymce
   */
  import 'tinymce/themes/silver/theme' // 编辑器主题
  // 这里引入报错可以替换成 import 'tinymce/themes/modern/theme'
  import 'tinymce/icons/default/icons.min.js' // 编辑器图标
  import 'tinymce/skins/ui/oxide/skin.css'
  // 相关插件
  import 'tinymce/plugins/preview'// 预览插件
  import 'tinymce/plugins/spellchecker' // 拼写检查插件, 并不是开启此插件就能支持拼写检查，还需搭个拼写服务，详情参看https://www.tiny.cloud/docs/general-configuration-guide/spell-checking/
  import 'tinymce/plugins/searchreplace' // 查找替换插件
  import 'tinymce/plugins/autolink' // 自动链接插件
  import 'tinymce/plugins/directionality' // 文字方向插件
  import 'tinymce/plugins/visualblocks' // 显示出块级元素插件
  import 'tinymce/plugins/visualchars' // 显示出隐藏字符插件
  import 'tinymce/plugins/fullscreen' // 全屏插件
  import 'tinymce/plugins/image' // 插入上传图片插件
  import 'tinymce/plugins/imagetools' // 图片编辑工具插件
  import 'tinymce/plugins/media' // 插入视频插件插件
  import 'tinymce/plugins/table' // 插入表格插件
  import 'tinymce/plugins/link' // 超链接插件
  import 'tinymce/plugins/code' // 代码块插件
  import 'tinymce/plugins/codesample' // 代码示例插件
  import 'tinymce/plugins/advlist' // 高级列表插件
  import 'tinymce/plugins/lists' // 列表插件
  import 'tinymce/plugins/template' // 内容模板插件
  import 'tinymce/plugins/charmap' // 特殊字符插件
  import 'tinymce/plugins/hr' // 水平线插件
  import 'tinymce/plugins/wordcount' // 字数统计插件
  import 'tinymce/plugins/colorpicker' // 选择颜色插件
  import 'tinymce/plugins/textcolor' // 文本颜色插件
  import 'tinymce/plugins/help' // 帮助插件
  import 'tinymce/plugins/paste' // 粘贴插件
  import 'tinymce/plugins/print' // 打印插件
  import 'tinymce/plugins/anchor' // 锚点插件
  import 'tinymce/plugins/pagebreak' // 分页符插件
  import 'tinymce/plugins/insertdatetime' // 插入时间日期插件
  import 'tinymce/plugins/nonbreaking' // 插入不间断空格插件
  import 'tinymce/plugins/autosave' // 自动存稿插件
  import 'tinymce/plugins/autoresize' // 编辑器自适应插件
  import 'tinymce/plugins/contextmenu' // 右键菜单插件
  import 'tinymce/plugins/toc' // 目录生成器插件
  import 'tinymce/plugins/textpattern' // 快速排版插件
  require('./tinymce/plugins/indent2em') // 首行缩进

  /* 定义编辑器字体 */
  const fontFormats = [
    '宋体=宋体',
    '苹果苹方=PingFang SC',
    '微软雅黑=微软雅黑',
    '新宋体=新宋体',
    '黑体=黑体',
    '楷体=楷体',
    '隶书=隶书',
    'Courier New=courier new,courier',
    'AkrutiKndPadmini=Akpdmi-n',
    'Andale Mono=andale mono,times',
    'Arial=arial,helvetica,sans-serif',
    'Arial Black=arial black,avant garde',
    'Book Antiqua=book antiqua,palatino',
    'Comic Sans MS=comic sans ms,sans-serif',
    'Courier New=courier new,courier',
    'Georgia=georgia,palatino',
    'Helvetica=helvetica',
    'Impact=impact,chicago',
    'Symbol=symbol',
    'Tahoma=tahoma,arial,helvetica,sans-serif',
    'Terminal=terminal,monaco',
    'Times New Roman=times new roman,times',
    'Trebuchet MS=trebuchet ms,geneva',
    'Verdana=verdana,geneva',
    'Webdings=webdings',
    'Wingdings=wingdings,zapf dingbats'
  ];

  const contextPath = process.env.NODE_ENV === 'production' ? process.env.VUE_APP_CONTEXT_PATH : ''

  export default {
    name: "TinymceEditor",
    props: {
      /***** 编辑器必须参数 **** */
      // 编辑器内容
      value: {
        type: String,
        default: ''
      },
      // 编辑器上传图片的地址
      uploadImageUrl: {
        type: String,
        default: ''
      },
      // 编辑器高度
      height: {
        type: Number,
        default: 600
      },
      // 编辑器是否禁用
      disabled: {
        type: Boolean,
        default: false
      },
      /***** 编辑器插件 **** */
      plugins: {
        type: [String, Array],
        default:
            'preview searchreplace autolink directionality visualblocks visualchars fullscreen \
            image imagetools link media template code table charmap hr nonbreaking insertdatetime advlist \
            lists wordcount textpattern autosave autoresize searchreplace pagebreak paste indent2em'
      },
      /***** 编辑器工具栏，根据需要写上对应的工具名称、顺序、分割线等 **** */
      toolbar: {
        type: [String, Array],
        default:
            'fullscreen preview | \
            undo redo | \
            styleselect fontselect fontsizeselect | \
            removeformat | \
            bold italic underline strikethrough subscript superscript | \
            backcolor forecolor | \
            bullist numlist | \
            outdent indent indent2em | \
            alignleft aligncenter alignright alignjustify | \
            lineheight | \
            table image media link | \
            insertdatetime blockquote charmap hr pagebreak nonbreaking | \
            searchreplace code wordcount'
      },
      // 大小限制(MB)
      fileSize: {
        type: Number,
        default: 5,
      },
      // 单位
      fileUnit: {
        type: String,
        default: 'Mb',
      },
      // 图片类型
      imageFileType: {
        type: Array,
        default: () => ['jpg', 'jpeg', 'png', 'gif'],
      },
      // 视频类型
      mediaFileType: {
        type: Array,
        default: () => [
          'mp3',
          'mp4', 'm4v',
          'wmv', 'asf', 'asx',
          'rm', 'rmvb',
          'mpg', 'mpeg', 'mpe',
          '3gp',
          'mov',
          'avi', 'dat', 'mkv', 'flv', 'vob'
        ],
      },
      // 链接文件类型
      linkFileType: {
        type: Array,
        default: () => [
          'pdf',
          'txt',
          'zip', 'rar', '7z',
          'doc', 'docx',
          'xls', 'xlsx',
          'ppt', 'pptx'
        ],
      },
    },
    components: {Editor},
    data() {
      return {
        baseApi: process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API : process.env.VUE_APP_BASE_URL + process.env.VUE_APP_BASE_API,
        open: true,
        tinymceId: 'tinymce' + new Date().getTime(),
        tinymceFlag: 1,
        content: this.value,
        // 编辑器初始化参数
        init: {
          selector: '#' + this.tinymceId, // DOM选择器
          language: 'zh-Hans', // 设置编辑器语言类型
          language_url: contextPath + '/tinymce/langs/zh-Hans.js', // 设置编辑器汉化脚本地址，具体位置：public/tinymce/langs/zh-Hans.js，下载地址：https://www.tiny.cloud/get-tiny/language-packages/
          skin_url: contextPath + '/tinymce/skins/ui/oxide', // 设置编辑器皮肤类型，具体位置：public/tinymce/skins/ui/oxide
          content_css: contextPath + '/tinymce/skins/content/default/content.css',// 设置编辑区域的样式，具体位置：public/tinymce/skins/content/default/content.css

          branding: false,// 是否显示品牌
          menubar: false, // 顶部菜单栏显示
          min_height: this.height, // 设置编辑器最小高度
          max_height: null, // 设置编辑器最大高度
          toolbar: this.toolbar, // 设置编辑器工具栏
          plugins: this.plugins,// 需要用到的功能插件，如链接，列表等等

          placeholder: '请输入内容', // 设置编辑器占位内容
          content_style: 'body, td, pre { font-size: 14px; }', // 设置编辑区域自定义样式，如：设置编辑器默认字体大小
          font_formats: fontFormats.join(';'),
          fontsize_formats: '12px 14px 16px 18px 20px 22px 24px 28px 32px 36px 48px 56px 72px', // 设置编辑器字体大小
          lineheight_formats: '0.5 0.8 1 1.2 1.5 1.75 2 2.5 3 4 5', // 设置编辑器行高，也可配置成 '12px 14px 16px 20px' 的形式
          letterspacing_formats: '0 0.5pt 1pt 1.5pt 2pt 3pt 5pt 10pt 15pt 20pt 30pt', // 设置编辑器文字间距

          toolbar_mode: 'sliding', // 设置编辑器工具栏模式，取值：floating / sliding / scrolling / wrap
          draggable_modal: true, // 设置编辑器模态窗口是否可拖动
          paste_data_images: true, // 设置编辑器允许粘贴图像，图片粘贴自动上传功能需要用到此配置
          relative_urls: false, // 设置将当前域名中的所有URL转换为相对URL，参考见：http://tinymce.ax-z.cn/configure/url-handling.php#relative_urls
          remove_script_host: true, // 设置是否删除URL的域名部分，参考见：http://tinymce.ax-z.cn/configure/url-handling.php#remove_script_host
          convert_urls: true, // 设置是否自动转换URL，参考见：http://tinymce.ax-z.cn/configure/url-handling.php#convert_urls
          image_uploadtab: false, // 图片对话框中上传标签开关 启用后回调 images_upload_handler
          image_advtab: false, // 高级参数
          image_description: false, // 显示隐藏图片说明(描述)
          image_dimensions: true, // 显示隐藏图片尺寸
          image_caption: false, // 图片标题
          image_title: false, // 显示隐藏图片标题, 与image_caption配合使用
          media_live_embeds: true, // 设置视频实时预览
          media_alt_source: false,  // 显示隐藏资源备用地址输入框
          media_poster: false, // 显示隐藏图片封面输入框
          media_dimensions: true, // 显示隐藏宽高尺寸输入框
          link_context_toolbar: false, // 链接的右键增强菜单
          link_title: false, // 对话框中“标题”的显示开关
          file_picker_types: 'file image media', // 此选项可以通过空格或逗号分隔的类型名称指定允许上传的类型。取值：file、image、media
          imagetools_toolbar: 'rotateleft rotateright flipv fliph editimage imageoptions', // 图片快速工具栏出现的按钮 取值：rotateleft 逆时针旋转 rotateright 顺时针旋转 flipv 垂直翻转 fliph 水平翻转 editimage 编辑图片 imageoptions 图片选项

          /**
           * 初始化开始前执行
           */
          setup: this.tinymceSetup,

          /**
           * 初始化结束后执行
           */
          init_instance_callback: this.initInstanceCallback,

          /**
           * 图片上传自定义实现
           *
           * 参考见：http://tinymce.ax-z.cn/configure/file-image-upload.php#images_upload_handler
           */
          images_upload_handler: this.imagesUploadHandler,

          /**
           * 文件上传回调
           *
           * 参考见：http://tinymce.ax-z.cn/configure/file-image-upload.php#file_picker_callback
           */
          file_picker_callback: this.filePickerCallback
        },

      }
    },
    watch: {
      value: {
        handler(newValue) {
          this.content = newValue || ''
        },
        immediate: true,
        deep: true
      },
      content: {
        handler(newValue) {
          this.$emit("input", newValue)
        }
      }
    },
    created() {
    },
    mounted() {
      tinymce.init({});
      this.reload()
    },
    activated() {
      this.tinymceFlag++;
    },
    methods: {
      reload() {
        this.open = false;
        this.$nextTick(() => this.open = true)
      },
      tinymceSetup(editor) {
      },
      initInstanceCallback(editor) {
        // 监听编辑器 INPUT 事件
        editor.on('input', (e) => {
          if (e && e.target && e.target.innerHTML) {
            let html = e.target.innerHTML;
          }
        });

        // 监听编辑器 CHANGE 事件
        editor.on('change', (e) => {
          if (e && e.target && e.target.innerHTML) {
            let html = e.target.innerHTML;
          }
        });

        // 监听编辑器 FOCUS 事件
        editor.on('focus', (e) => {
          if (e && e.target && e.target.innerHTML) {
            let html = e.target.innerHTML;
          }
        });
      },
      imagesUploadHandler(blobInfo, success, failure, progress) {
        var that = this;
        var formData = new FormData();
        formData.append('file', blobInfo.blob(), blobInfo.filename());
        uploadFile(formData).then(response => {
          const {code, msg, data} = response;
          const {path, key} = data;
          if (code === 200) {
            let filepath = that.baseApi + path;
            success(filepath);
          } else {
            failure('HTTP Error: ' + msg);
          }
        });
      },
      filePickerCallback(callback, value, meta) {
        var that = this;
        //文件分类
        var fileTypes = [];
        //为不同插件指定文件类型及后端地址
        switch (meta.filetype) {
          case 'image':
            fileTypes = that.imageFileType;
            break;
          case 'media':
            fileTypes = that.mediaFileType;
            break;
          case 'file':
            fileTypes = that.linkFileType;
            break;
          default:
            fileTypes = [];
        }
        var fileType = fileTypes.map(item => `.${item}`).join(",");
        //模拟出一个input用于添加本地文件
        var input = document.createElement('input');
        input.setAttribute('type', 'file');
        input.setAttribute('accept', fileType);
        input.click();
        input.onchange = function () {
          var file = this.files[0];
          if (fileType.length) {
            let fileExtension = "";
            if (file.name && file.name.lastIndexOf(".") > -1) {
              fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
            }
            var isTypeOk = fileTypes.some(type => {
              type = type ? type.trim() : type;
              if (file.type.indexOf(type) > -1) return true;
              if (fileExtension && fileExtension.indexOf(type) > -1) return true;
              return false;
            });
            if (!isTypeOk) {
              that.$message.error(`文件格式不正确, 请上传 ${fileTypes} 格式文件!`);
              return;
            }
          }
          if (that.fileSize) {
            var fileUnit = that.fileUnit ? that.fileUnit.toUpperCase() : 'MB';
            var value = 1
            if (fileUnit.includes("M")) {
              value = 1024
            } else if (fileUnit.includes("G")) {
              value = 1024 * 1024
            } else if (fileUnit.includes("T")) {
              value = 1024 * 1024 * 1024
            }
            var fileSize = file.size;
            var compareFileSize = that.fileSize * (1024 * value);
            var isLt = fileSize < compareFileSize;
            if (!isLt) {
              that.$message.error(`上传内容大小不能超过 ${that.fileSize} ${that.fileUnit}!`);
              return;
            }
          }
          var formData = new FormData();
          formData.append('file', file, file.name);
          uploadFile(formData).then(response => {
            const {code, msg, data} = response;
            const {path, key} = data;
            if (code === 200) {
              let filepath = that.baseApi + path;
              callback(filepath);
            } else {
              callback(null);
            }
          });
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  /* Tinymce 编辑器 */
  .t-e-for-vue {
    width: 800px;
    margin: 100px auto;

  /deep/ .tox-tinymce {
    border-radius: unset;
  }

  }
  /* / Tinymce 编辑器 */
</style>

<style lang="less">
  /* Tinymce 编辑器模态框弹窗 */
  .tox-dialog {

  .tox-dialog__body {

  table {

  th {
    font-size: 15px;
    text-align: center;
    font-weight: normal !important;
  }

  td {
    font-size: 14px;
    text-align: center;
  }

  }

  .tox-button--secondary {
    font-weight: normal !important;
  }

  }

  .tox-dialog__footer {

  .tox-button {
    font-weight: normal !important;
    padding: 2px 15px 3px 15px !important;
    letter-spacing: 1px !important;
    font-size: 13px !important;
  }

  }
  }
  /* / Tinymce 编辑器模态框弹窗 */

  /* Tinymce 编辑器下拉框菜单弹窗 */
  .tox-tiered-menu {

  .tox-menu {
    max-height: 300px !important;

  /* 滚动条 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background-color: #f8f8f8;
  }

  ::-webkit-scrollbar-track {
    background-color: #f2f2f2;
    border-bottom: 1px solid #ebeef5;
    border-left: 1px solid #ebeef5;
    border-right: 1px solid #ebeef5;
  }

  ::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
    background-color: #999;
    border-radius: 5px;
  }

  ::-webkit-scrollbar-thumb:hover {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
    background-color: #666;
  }

  /* / 滚动条 */
  }
  }
  /* / Tinymce 编辑器下拉框菜单弹窗 */
 /* 确保 message 的 z-index 始终高于其他元素 */
 .ant-message {
   z-index: 9999 !important;
 }
</style>
