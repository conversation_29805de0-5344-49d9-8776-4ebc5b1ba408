<template>
  <div class="component-upload-image">
    <a-upload
        list-type="picture-card"
        name="file"
        :multiple="false"
        :show-file-list="true"
        :show-upload-list="{
          showPreviewIcon: previewIconShow,
          showRemoveIcon: removeIconShow
        }"
        :action="uploadImgUrl"
        :headers="headers"
        :max-count="limit"
        :file-list="uploadList"
        :remove="handleRemove"
        @preview="handlePictureCardPreview"
        @change="handleUploadSuccess"
    >
      <div v-if="uploadList.length < limit && uploadButtonShow">
        <a-icon :type="loading ? 'loading' : 'plus'" />
      </div>
    </a-upload>

    <!-- 上传提示 -->
    <div class="ant-upload-text" v-if="showTip">
      <div v-if="tipText !== undefined && tipText != null && tipText !== ''" style="color: #f56c6c; font-weight: bolder">
        {{ tipText }}
      </div>
      <div v-else>
      请上传
      <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
      <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>
      的文件
      </div>
    </div>

  </div>
</template>

<script>
  import { getToken } from "@/utils/auth";
  import { validUrlDomains } from '@/utils/validate'

  export default {
    props: {
      showFiles: Array,
      // 图片数量限制
      limit: {
        type: Number,
        default: 5,
      },
      // 大小限制(MB)
      fileSize: {
        type: Number,
        default: 5,
      },
      // 文件类型, 例如['png', 'jpg', 'jpeg']
      fileType: {
        type: Array,
        default: () => ["png", "jpg", "jpeg"],
      },
      // 是否显示提示
      isShowTip: {
        type: Boolean,
        default: true
      },
      getFiles: {
        type: Function,
        default: function() {}
      },
      // 提示内容
      tipText: {
        type: String,
        default: undefined
      },
      // 是否显示上传按钮
      uploadButtonShow: {
        type: Boolean,
        default: true
      },
      // 查看按钮显示
      previewIconShow: {
        type: Boolean,
        default: true
      },
      // 删除按钮显示
      removeIconShow: {
        type: Boolean,
        default: true
      },
      // 单位
      fileUnit: {
        type: String,
        default: 'Mb',
      },
    },
    data() {
      return {
        loading: false,
        uploadList: [],
        previewVisible: false,
        previewImage: '',
        baseUrl: process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API : process.env.VUE_APP_BASE_URL + process.env.VUE_APP_BASE_API,
        uploadImgUrl: (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API : process.env.VUE_APP_BASE_URL + process.env.VUE_APP_BASE_API) + "/storage/upload", // 上传的图片服务器地址
        headers: {
          Authorization: "Bearer " + getToken(),
        }
      };
    },
    watch: {
      showFiles: {
        handler(val) {
          this.handleShowFiles(val)
        },
        deep: true,
        immediate: true
      }
    },
    computed: {
      // 是否显示提示
      showTip() {
        return this.isShowTip && (this.fileType || this.fileSize);
      },
    },
    methods: {
      // 附件回显
      handleShowFiles(val) {
        if (val) {
          // 首先将值转为数组
          const list = val;
          // 然后将数组转为对象数组
          this.uploadList = list.map(item => {
            if (item.id) {
              let fileUrl = null;
              if (item.url && !validUrlDomains(item.url) && item.url.indexOf(this.baseUrl) === -1) {
                fileUrl = this.baseUrl + item.url;
              } else {
                fileUrl = item.url;
              }
              item = {
                uid: item.id,
                id: item.id,
                name: item.name,
                status: 'done',
                url: fileUrl,
                thumbUrl: fileUrl,
                path: item.url,
                size: item.size || 0
              };
            }
            return item;
          });
        } else {
          this.uploadList = [];
        }
      },
      // 删除图片
      handleRemove(file) {
        const findex = this.uploadList.map(f => f.uid).indexOf(file.uid);
        if(findex > -1) {
          this.uploadList.splice(findex, 1);
          this.getFiles(this.uploadList);
          this.handleGetFiles(this.uploadList);
        }
      },
      // 上传前loading加载
      handleBeforeUpload(file) {
        let isImg = false;
        if (this.fileType.length) {
          let fileExtension = "";
          if (file.name.lastIndexOf(".") > -1) {
            fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
          }
          isImg = this.fileType.some(type => {
            if (file.type.indexOf(type) > -1) return true;
            if (fileExtension && fileExtension.indexOf(type) > -1) return true;
            return false;
          });
        } else {
          isImg = file.type.indexOf("image") > -1;
        }

        if (!isImg) {
          this.$message.error(`文件格式不正确, 请上传${this.fileType.join("/")}图片格式文件!`);
          return false;
        }
        if (this.fileSize) {
          const fileUnit = this.fileUnit ? this.fileUnit.toUpperCase() : 'MB';
          let value = 1
          if (fileUnit.includes("M")) {
            value = 1024
          } else if (fileUnit.includes("G")) {
            value = 1024 * 1024
          } else if (fileUnit.includes("T")) {
            value = 1024 * 1024 * 1024
          }
          const fileSize = file.size;
          const compareFileSize = this.fileSize * (1024 * value);
          const isLt = fileSize < compareFileSize;
          if (!isLt) {
            this.$message.error(`上传图片大小不能超过 ${this.fileSize} ${this.fileUnit}!`);
            return false;
          }
        }
        return true;
      },
      // 上传成功回调
      handleUploadSuccess(res) {
        let file = res.file;
        let fileList = res.fileList;
        if(!this.handleBeforeUpload(file)) {
          return;
        }
        if (file.status === 'uploading') {
          this.loading = true;
        } else if (file.status === 'done') {
          this.loading = false;
          if(file.response) {
            const { code, data } = file.response;
            const { path, key } = data;
            if(code === 200) {
              this.$message.success("上传成功!");
              file.thumbUrl = this.baseUrl + path;
              file.url = path;
              file.id = key;
              file.path = path;
            } else {
              file.status = 'error';
              this.handleUploadError();
            }
          }
        } else if (file.status === 'error') {
          this.loading = false;
          this.handleUploadError();
        }
        this.uploadList = fileList.map(f => {
          if (file.uid === f.uid) {
            f = file;
          }
          return f;
        });
        if(file.status === 'done') {
          this.getFiles(this.uploadList);
          this.handleGetFiles(this.uploadList);
        }
      },
      // 上传失败
      handleUploadError() {
        this.$message.error("上传失败");
      },
      // 预览
      handlePictureCardPreview(file) {
        this.$fileCommonUtils.fileView({id: file.id, name: file.name, url: file.url});
      },
      //取消
      handleCancel() {
        this.previewVisible = false;
      },
      // 获取文件
      handleGetFiles(fileList) {
        this.$emit('ok', fileList)
      },
    }
  };
</script>
<style>
  /* you can make up upload button and sample style by using stylesheets */
  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }
</style>
