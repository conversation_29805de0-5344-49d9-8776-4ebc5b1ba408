<template>
  <div class="mapcontainer">
    <div class="flex-row margin-bottom-10">
      <el-radio-group v-model="radioSearch" v-if="!see">
        <el-radio label="0">按关键字搜索</el-radio>
        <el-radio label="1"> 按坐标搜索</el-radio>
      </el-radio-group>
    </div>
    <el-form ref="ruleForm" class="demo-form-inline" :inline="true">
      <el-form-item class="search-box">
        <el-input
          v-model="searchKey"
          v-if="!see"
          type="search"
          id="search"
          style="width: 219px"
          placeholder="请输入关键字进行搜索"
          :disabled="radioSearch == '1' ? true : false"
        />
      </el-form-item>
      <div class="tip-box" id="searchTip"></div>
      <el-form-item label="经度" prop="lng">
        <el-input
          maxlength="11"
          v-model="lng"
          style="width: 219px"
          :disabled="radioSearch == '0' ? true : false"
        ></el-input>
      </el-form-item>
      <el-form-item label="维度：" prop="lat">
        <el-input
          maxlength="10"
          v-model="lat"
          style="width: 219px"
          :disabled="radioSearch == '0' ? true : false"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          v-if="!see"
          class="btn submit"
          type="primary"
          @click="searchByHand"
        >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-amap
      class="amap-box"
      :amap-manager="amapManager"
      :vid="'amap-vue'"
      :zoom="zoom"
      :plugin="plugin"
      :events="events"
      :center="center"
    >
      <!-- 标记 -->
      <el-amap-marker
        v-for="(marker, index) in markers"
        :position="marker"
        :key="index"
      ></el-amap-marker>
    </el-amap>
    <div class="dialog-footer flex-row flex-center" v-if="!see">
      <el-button
        class="btn submit"
        :disabled="lng == '' || lat == '' ? true : false"
        @click="clickSureMap()"
      >确定</el-button
      >
      <el-button class="btn reset" @click="clickCancleMap()"
      >取消</el-button
      >
    </div>
  </div>
</template>

<script>
import { AMapManager, lazyAMapApiLoaderInstance } from "vue-amap";
const amapManager = new AMapManager();
export default {
  name: "Map",
  props: {
    lnglat: {
      default: () => {
        return {
          lng: "",
          lat: "",
        };
      },
      type: Object,
    },
    see: {
      type: Boolean,
      default: false,
    }, //为true则是查看位置
  },
  data() {
    const self = this;
    return {
      radioSearch: "0",
      address: null,
      searchKey: "",
      amapManager,
      markers: [],
      searchOption: {
        city: "全国",
        citylimit: false,
      },
      saveData: {
        pos: [],
        adname: "",
        name: "",
      },
      center: [121.329402, 31.228667],
      zoom: 12,
      lng: 0,
      lat: 0,
      loaded: false,
      events: {
        init() {
          lazyAMapApiLoaderInstance.load().then(() => {
            self.initSearch();
          });
        },
        // 点击获取地址的数据
        click(e) {
          // console.log(e)
          self.markers = [];
          const { lng, lat } = e.lnglat;
          self.lng = lng;
          self.lat = lat;
          self.center = [lng, lat];
          self.markers.push([lng, lat]);
          // 这里通过高德 SDK 完成。
          const geocoder = new AMap.Geocoder({
            radius: 1000,
            extensions: "all",
          });
          geocoder.getAddress([lng, lat], function (status, result) {
            if (status === "complete" && result.info === "OK") {
              if (result && result.regeocode) {
                // console.log(result);
                self.address =
                  result.regeocode.formattedAddress;
                self.searchKey =
                  result.regeocode.formattedAddress;
                self.$nextTick();
                const poi = result.regeocode.addressComponent;
                const d = {
                  pos: [lng, lat],
                  adname: poi.district,
                  // name: poi.name,
                  address: self.address,
                };

                self.$emit("poi", d);
              }
            }
          });
        },
      },
      // 一些工具插件
      plugin: [
        // {
        //   pName: 'Geocoder',
        //   events: {
        //     init (o) {
        //       console.log(o.getAddress())
        //     }
        //   }
        // },
        {
          // 定位
          pName: "Geolocation",
          events: {
            init(o) {
              // o是高德地图定位插件实例
              o.getCurrentPosition((status, result) => {
                if (result && result.position) {
                  // 设置经度
                  self.lng =
                    self.lnglat.lng || result.position.lng;
                  // 设置维度
                  self.lat =
                    self.lnglat.lat || result.position.lat;
                  // 设置坐标
                  self.center = [self.lng, self.lat];
                  self.markers.push([self.lng, self.lat]);
                  // load
                  self.loaded = true;
                  // 页面渲染好后
                  self.$nextTick();
                }
              });
            },
          },
        },
        {
          // 工具栏
          pName: "ToolBar",
          events: {
            init(instance) {
              console.log(instance);
            },
          },
        },
        {
          // 鹰眼
          pName: "OverView",
          events: {
            init(instance) {
              console.log(instance);
            },
          },
        },
        {
          // 地图类型
          pName: "MapType",
          defaultType: 0,
          events: {
            init(instance) {
              console.log(instance);
            },
          },
        },
        {
          // 搜索
          pName: "PlaceSearch",
          events: {
            init(instance) {
              console.log(instance);
            },
          },
        },
      ],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.searchKey = this.lnglat.address;
      this.center = [this.lnglat.lng, this.lnglat.lat];
    });
  },
  methods: {
    initSearch() {
      const vm = this;
      const map = this.amapManager.getMap();
      AMapUI.loadUI(["misc/PoiPicker"], function (PoiPicker) {
        var poiPicker = new PoiPicker({
          input: "search",
          placeSearchOptions: {
            map: map,
            pageSize: 10,
          },
          suggestContainer: "searchTip",
          searchResultsContainer: "searchTip",
        });
        vm.poiPicker = poiPicker;
        // 监听poi选中信息
        poiPicker.on("poiPicked", function (poiResult) {
          const source = poiResult.source;
          const poi = poiResult.item;
          if (source !== "search") {
            poiPicker.searchByKeyword(poi.name);
          } else {
            console.log(22);
            poiPicker.clearSearchResults();
            vm.markers = [];
            const lng = poi.location.lng;
            const lat = poi.location.lat;
            const address = poi.cityname + poi.adname + poi.name;
            vm.center = [lng, lat];
            vm.markers.push([lng, lat]);
            vm.lng = lng;
            vm.lat = lat;
            vm.address = address;
            vm.searchKey = address;
            const d = {
              pos: [lng, lat],
              adname: poi.adname,
              name: poi.name,
              address:
                poi.pname +
                " " +
                poi.cityname +
                " " +
                poi.adname +
                " " +
                poi.address +
                " " +
                poi.name,
            };
            vm.$emit("poi", d);
          }
        });
      });
    },
    searchByHand() {
      if (this.radioSearch == 0) {
        if (this.searchKey !== "") {
          this.poiPicker.searchByKeyword(this.searchKey);
        }
      } else {
        this.center = [this.lng, this.lat]; //设置中心点
        this.markers = []; //清空
        this.markers.push([this.lng, this.lat]); //设置点位置
        const geocoder = new AMap.Geocoder({
          radius: 1000,
          extensions: "all",
        });
        let self = this;
        //移动
        geocoder.getAddress(
          [self.lng, self.lat],
          function (status, result) {
            if (status === "complete" && result.info === "OK") {
              if (result && result.regeocode) {
                self.address =
                  result.regeocode.formattedAddress;
                self.searchKey =
                  result.regeocode.formattedAddress;
                self.$nextTick();
                const poi = result.regeocode.addressComponent;
                const d = {
                  pos: [self.lng, self.lat],
                  adname: poi.district,
                  address: self.address,
                };
                self.$emit("poi", d);
              }
            }
          }
        );
      }
    },
    clickSureMap() {
      this.$emit("clickClose", {
        lng: this.lng,
        lat: this.lat,
        address: this.address,
      });
    },
    clickCancleMap() {
      this.$emit("clickClose");
    },
  },
};
</script>

<style  scoped lang="less">
.mapcontainer {
  width: 100%;
  height: 400px;
  position: relative;
}
.margin-bottom-10 {
  margin-bottom: 10px;
}
.dialog-footer {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}
.amap-box {
  border: 1px solid #ccc;
  padding: 5px;
}
.tip-box {
  width: 100%;
  max-height: 260px;
  position: absolute;
  border: 1px solid #dcdfe6;
  overflow-y: auto;
  background-color: #fff;
  z-index: 1111;
}
.btn {
  border-radius: 4px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  padding: 0 22px;
  margin-left: 24px;
  &.submit {
    background: #3c77fe;
    border-radius: 4px;
    color: #ffffff;
    border: 1px solid #3c77fe;

    &:hover {
      background: #3365d8;
      border: 1px solid #3365d8;
    }

    &.is-disabled {
      background: #9ebbff;
      border: 1px solid #9ebbff;
    }
  }
  &.reset {
    background: #ffffff;
    border: 1px solid #dadce0;
    margin-left: 30px;
    &:hover {
      border: 1px solid #3c77fe;
      color: #3c77fe;
    }
  }
}
</style>