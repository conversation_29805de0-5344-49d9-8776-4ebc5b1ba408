<template>
  <div class="m-e" id="m-e-id">
    <div class="m-e-main">
      <div class="m-e-main_toolbar" :class="isThemeLightOrBlack ? 'themeLight' : 'themeBlack'">
        <div class="m-e-main_toolbar_left">
          <span>{{ title }}</span>
        </div>
        <div class="m-e-main_toolbar_right" :class="isThemeLightOrBlack ? 'themeLightToolbar' : 'themeBlackToolbar'">
          <a title="查找" @click="findByKeyword"><i class="fa fa-search"/></a>
          <a title="回到顶部" @click="scrollToTop"><i class="fa fa-chevron-circle-up"/></a>
          <a title="回到底部" @click="scrollToBottom"><i class="fa fa-chevron-circle-down"/></a>
          <a title="切换白天或暗夜模式" @click="setEditorTheme"><i class="fa fa-adjust"/></a>
          <select title="语言" id="language-picker" class="language-picker" :class="isThemeLightOrBlack ? 'themeLightSelect' : 'themeBlackSelect'" @change="languagePickerChange">
            <option v-for="(item, index) in languageOptions" :key="index" :value="item.value">{{ item.value }}</option>
          </select>
        </div>
      </div>
      <div :id="monacoId" class="m-e-main_container" :style="{width: width, height: height}"></div>
    </div>
  </div>
</template>

<script>
  // 关于语言包以及控件的配置，请在vue.config.js配置文件中configureWebpack参数下plugins中进行相关设置
  // 引用 font-awesome 资源
  import 'font-awesome/css/font-awesome.min.css'
  // 再加载 monaco
  const monaco = require('monaco-editor/esm/vs/editor/editor.api')

  export default {
    name: "MonacoEditor",
    props: {
      monacoId: {
        type: String,
        default: 'monacoEditor'
      },
      title: {
        type: String
      },
      value: {
        type: String,
        default: ''
      },
      readOnly: {
        type: Boolean,
        default: false
      },
      selectOnLineNumbers: {
        type: Boolean,
        default: true
      },
      minimap: {
        type: Boolean,
        default: true
      },
      language: {
        type: String,
        default: 'html'
      },
      fontSize: {
        type: Number,
        default: 12
      },
      theme: {
        type: String,
        default: 'vs'
      },
      width: {
        type: String,
        default: '100%',
      },
      height: {
        type: String,
        default: '600px'
      }
    },
    data() {
      return {
        monacoEditor: null, // 编辑器对象
        isThemeLightOrBlack: true, // 明亮或暗夜模式，true 为白天模式，false 为暗夜模式
        content: this.value, // 编辑内容
        languageOptions: []
      }
    },
    mounted() {
      this.getLanguageOptions()
      this.initEditor()
    },
    beforeDestroy() {
      /**
       * 销毁在线代码编辑器
       */
      if (this.monacoEditor) {
        this.monacoEditor.dispose()
      }
      this.content = ''
    },
    methods: {
      /**
       * 实例化在线代码编辑器
       * 文档地址：https://microsoft.github.io/monaco-editor/api/index.html
       */
      async initEditor() {
        const that = this
        // 异步获取节点，确保 Dom 节点已经渲染完成，不可删
        await document.getElementById(that.monacoId)
        // 初始化编辑器，确保dom已经渲染
        that.monacoEditor = monaco.editor.create(document.getElementById(that.monacoId), {
          value: that.content, // 编辑器显示内容
          language: that.language, // 支持语言
          theme: that.theme, // 主题 可选值为：vs, hc-black, vs-dark
          selectOnLineNumbers: that.selectOnLineNumbers, // 显示行号
          readOnly: that.readOnly, // 只读
          minimap: {enabled: that.minimap}, // 是否开启小地图
          fontSize: that.fontSize,
          scrollBeyondLastLine: false, // 滚动完最后一行后再滚动一屏幕
          contextmenu: false // 设置上下文菜单
        })
        // 编辑器内容change事件
        that.monacoEditor.onDidChangeModelContent(function(event){
          that.$emit('onchange', that.monacoEditor.getValue())
        });
        // 设置编辑器滚动到最顶部
        that.scrollToTop()
      },

      /**
       * 设置编辑器的内容且滚动到最顶部
       */
      setEditorContent(val) {
        this.monacoEditor.setValue(val)
        this.scrollToTop()
      },

      /**
       * 获取编辑器的内容
       */
      getEditorContent() {
        return this.monacoEditor.getValue()
      },

      /**
       * 打开编辑器查找功能
       */
      findByKeyword () {
        try {
          // 先聚焦编辑器
          this.monacoEditor.focus()

          // 从模型中获取要查找的字符串范围 new Range(startLineNumber, startColumn, endLineNumber, endColumn)
          this.monacoEditor.setSelection(new monaco.Range(1, 9999, 1, 10000))

          // 触发查找操作
          // this.monacoEditor.getAction('actions.find').run() // 查找方式一
          this.monacoEditor.trigger('', 'actions.find') // 查找方式二
        } catch (error) {
          console.log(error)
        }
      },

      /**
       * 设置编辑器从只读变成可写
       */
      setEditorRW() {
        this.monacoEditor.updateOptions({readOnly: false})
      },

      /**
       * 设置编辑器明亮或暗夜模式
       */
      setEditorTheme() {
        this.isThemeLightOrBlack = !this.isThemeLightOrBlack
        if (this.isThemeLightOrBlack) {
          this.monacoEditor.updateOptions({theme: 'vs'})
        } else {
          this.monacoEditor.updateOptions({theme: 'vs-dark'})
        }
      },

      /**
       * 设置编辑器语言
       */
      setEditorLanguage(val) {
        if(val) {
          //获取旧模型
          const oldModel = this.monacoEditor.getModel()
          //获取旧的文本
          const value = this.monacoEditor.getValue()
          //创建新模型，value为旧文本，id为modeId，即语言（language.id）
          const newModel = monaco.editor.createModel(value, val)
          //将旧模型销毁
          if(oldModel){
            oldModel.dispose()
          }
          //设置新模型
          this.monacoEditor.setModel(newModel);
        }
      },

      /**
       * 设置编辑器滚动到最顶部
       */
      scrollToTop() {
        this.monacoEditor.setScrollPosition({scrollTop: 0})
      },

      /**
       * 设置编辑器滚动到最底部
       */
      scrollToBottom() {
        this.monacoEditor.revealLine(this.monacoEditor.getModel().getLineCount())
      },

      /**
       * 语言选择器change事件
       */
      languagePickerChange(event) {
        const val = event.target.value
        this.setEditorLanguage(val)
      },

      /**
       * 获取编辑器语言集合
       */
      getLanguageOptions() {
        const data = monaco.languages.getLanguages().map(function (lang) {
          return lang.id;
        });
        data.sort();
        let arr = []
        data.map(function (item) {
          arr.push({
            value: item
          })
        })
        // 去重
        const res = new Map()
        this.languageOptions = arr.filter((obj) => !res.has(obj.value) && res.set(obj.value, 1))
      }
    }
  }
</script>

<style lang="less" scoped>
  .m-e {
    width: 100%;
    height: 100%;

    .m-e-main {
      width: calc(100% - 2px);
      height: calc(100% - 2px);
      // border-top: 1 px solid rgb(235, 238, 245);
      // border-right: 1 px solid rgb(235, 238, 245);
      // border-bottom: 1 px solid rgb(235, 238, 245);

      .m-e-main_toolbar {
        width: 100%;
        height: 40px;
        box-shadow: 0px 2px 5px #000;
        display: flex;
        position: relative;
        /*z-index: 99;*/

        .m-e-main_toolbar_left {
          flex: 1;

          span {
            font-size: 15px;
            padding-left: 10px;
            line-height: 26px;
            line-height: 40px;
            user-select: none;
          }
        }

        .m-e-main_toolbar_right {
          margin-right: 15px;

          a {
            width: 16px;
            height: 16px;
            transition: ease all 0.3s;
            text-align: center;
            display: inline-block;
            padding: 5px;
            cursor: pointer;
            border-radius: 2px;
            margin: 7px 0 7px 5px;

            i {
              font-size: 15px;
            }

            &:hover {
              background-color: rgba(255, 255, 255, 0.1);
            }

          }
        }
      }
    }
  }

  .themeLight {
    background-color: #fff;
    box-shadow: 0px 2px 5px #ddd;
  }

  .themeBlack {
    background-color: #1e1e1e;
    box-shadow: 0px 2px 5px #111;
  }

  .themeLightToolbar {
    color: #000
  }

  .themeBlackToolbar {
    color: #fff
  }

  .themeLightSelect {
    background-color: #fff;
    color: #000
  }

  .themeBlackSelect {
    background-color: #000;
    color: #fff
  }

  .language-picker {
    margin-left: 10px;
    width: 128px;
  }

</style>
