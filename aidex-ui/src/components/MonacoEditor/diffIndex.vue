<template>
  <div class="m-e" id="m-e-id">
    <div class="m-e-main">
      <div class="m-e-main_toolbar" :class="isThemeLightOrBlack ? 'themeLight' : 'themeBlack'">
        <div class="m-e-main_toolbar_left">
          <span>{{ title }}</span>
        </div>
        <div class="m-e-main_toolbar_right" :class="isThemeLightOrBlack ? 'themeLightToolbar' : 'themeBlackToolbar'">
          <a title="查找" @click="findByKeyword"><i class="fa fa-search"/></a>
          <a title="回到顶部" @click="scrollToTop"><i class="fa fa-chevron-circle-up"/></a>
          <a title="回到底部" @click="scrollToBottom"><i class="fa fa-chevron-circle-down"/></a>
          <a title="切换白天或暗夜模式" @click="setEditorTheme"><i class="fa fa-adjust"/></a>
          <a title="切换显示方式" @click="setRenderSide"><i class="fa fa-columns"/></a>
          <select title="语言" id="language-picker" class="language-picker" :class="isThemeLightOrBlack ? 'themeLightSelect' : 'themeBlackSelect'" @change="languagePickerChange">
            <option v-for="(item, index) in languageOptions" :key="index" :value="item.value">{{ item.value }}</option>
          </select>
        </div>
      </div>
      <div :id="monacoId" :key="Math.random()" class="m-e-main_container" :style="{width: width, height: height}"></div>
    </div>
  </div>
</template>

<script>
  // 关于语言包以及控件的配置，请在vue.config.js配置文件中configureWebpack参数下plugins中进行相关设置
  // 引用 font-awesome 资源
  import 'font-awesome/css/font-awesome.min.css'
  // 再加载 monaco
  const monaco = require('monaco-editor/esm/vs/editor/editor.api')

  export default {
    name: "MonacoDiffEditor",
    props: {
      monacoId: {
        type: String,
        default: 'monacoDiffEditor'
      },
      title: {
        type: String
      },
      value: {
        type: String,
        default: ''
      },
      readOnly: {
        type: Boolean,
        default: false
      },
      selectOnLineNumbers: {
        type: Boolean,
        default: true
      },
      minimap: {
        type: Boolean,
        default: true
      },
      language: {
        type: String,
        default: 'css'
      },
      fontSize: {
        type: Number,
        default: 12
      },
      theme: {
        type: String,
        default: 'vs'
      },
      width: {
        type: String,
        default: '100%',
      },
      height: {
        type: String,
        default: '600px'
      },
      diffValue: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        monacoEditor: null, // 编辑器对象
        isThemeLightOrBlack: true, // 明亮或暗夜模式，true 为白天模式，false 为暗夜模式
        content: this.value, // 编辑内容
        languageOptions: [],
        diffContent: this.diffValue, // 比对的数据
        renderSideBySide: true, // 展示方式
        useTheme: this.theme // 使用主题
      }
    },
    mounted() {
      this.getLanguageOptions()
      this.initDiffEditor()
    },
    beforeDestroy() {
      /**
       * 销毁在线代码编辑器
       */
      if (this.monacoEditor) {
        this.monacoEditor.dispose()
      }
      this.content = ''
      this.diffContent = ''
    },
    methods: {
      /**
       * 实例化比对编辑器
       * 文档地址：https://microsoft.github.io/monaco-editor/api/index.html
       */
      async initDiffEditor() {
        const that = this
        // 异步获取节点，确保 Dom 节点已经渲染完成，不可删
        await document.getElementById(that.monacoId)
        // 初始化编辑器，确保dom已经渲染
        that.monacoEditor = monaco.editor.createDiffEditor(document.getElementById(that.monacoId), {
          automaticLayout: true, //自动布局
          theme: that.useTheme, // 主题 可选值为：vs, hc-black, vs-dark
          selectOnLineNumbers: that.selectOnLineNumbers, // 显示行号
          readOnly: true, // 只读
          fontSize: that.fontSize,
          contextmenu: false, // 设置上下文菜单
          roundedSelection: false, // 右侧显示编辑器预览
          minimap: {enabled: false}, // 是否开启小地图
          overviewRulerLanes: 0,
          overviewRulerBorder: true,
          renderSideBySide: this.renderSideBySide
        })
        // 设置比对model
        that.monacoEditor.setModel({
          original: monaco.editor.createModel(this.content, this.language), // 原始版本
          modified: monaco.editor.createModel(this.diffContent, this.language) // 修改版本
        })
        // 设置编辑器滚动到最顶部
        that.scrollToTop()
      },

      /**
       * 打开编辑器查找功能
       */
      findByKeyword () {
        try {
          // 先聚焦编辑器
          this.monacoEditor.focus()

          // 从模型中获取要查找的字符串范围 new Range(startLineNumber, startColumn, endLineNumber, endColumn)
          this.monacoEditor.setSelection(new monaco.Range(1, 9999, 1, 10000))

          // 触发查找操作
          // this.monacoEditor.getAction('actions.find').run() // 查找方式一
          this.monacoEditor.trigger('', 'actions.find') // 查找方式二
        } catch (error) {
          console.log(error)
        }
      },

      /**
       * 设置编辑器明亮或暗夜模式
       */
      setEditorTheme() {
        this.isThemeLightOrBlack = !this.isThemeLightOrBlack
        this.useTheme = this.isThemeLightOrBlack ? 'vs' : 'vs-dark'
        if (this.monacoEditor) {
          this.monacoEditor.dispose()
        }
        this.initDiffEditor()
      },

      /**
       * 设置编辑器语言
       */
      setEditorLanguage(val) {
        if(val) {
          const newModel = {}
          //获取旧模型
          const oldModel = this.monacoEditor.getModel()
          //获取比对数据
          Object.keys(oldModel).map(key => {
            // 获取旧的文本
            const value = oldModel[key].getValue()
            // 设置新
            newModel[key] = monaco.editor.createModel(value, val)
            //将旧模型销毁
            if (oldModel[key]) {
              oldModel[key].dispose()
            }
          })
          //设置新模型
          this.monacoEditor.setModel(newModel);
        }
      },

      /**
       * 设置编辑器滚动到最顶部
       */
      scrollToTop() {
        this.monacoEditor.revealLine(0);
      },

      /**
       * 设置编辑器滚动到最底部
       */
      scrollToBottom() {
        let count = 0
        const model = this.monacoEditor.getModel()
        if (model) {
          Object.keys(model).map(item => {
            const lineCount = model[item].getLineCount() || 0
            if (lineCount > count) { count = lineCount }
          })
        }
        this.monacoEditor.revealLine(count)
      },

      /**
       * 设置显示方式
       */
      setRenderSide() {
        this.renderSideBySide = !this.renderSideBySide
        if (this.monacoEditor) {
          this.monacoEditor.dispose()
        }
        this.initDiffEditor()
      },

      /**
       * 语言选择器change事件
       */
      languagePickerChange(event) {
        const val = event.target.value
        this.setEditorLanguage(val)
      },

      /**
       * 获取编辑器语言集合
       */
      getLanguageOptions() {
        const data = monaco.languages.getLanguages().map(function (lang) {
          return lang.id;
        });
        data.sort();
        let arr = []
        data.map(function (item) {
          arr.push({
            value: item
          })
        })
        // 去重
        const res = new Map()
        this.languageOptions = arr.filter((obj) => !res.has(obj.value) && res.set(obj.value, 1))
      }
    }
  }
</script>

<style lang="less" scoped>
  .m-e {
    width: 100%;
    height: 100%;

    .m-e-main {
      width: calc(100% - 2px);
      height: calc(100% - 2px);
      // border-top: 1 px solid rgb(235, 238, 245);
      // border-right: 1 px solid rgb(235, 238, 245);
      // border-bottom: 1 px solid rgb(235, 238, 245);

      .m-e-main_toolbar {
        width: 100%;
        height: 40px;
        box-shadow: 0px 2px 5px #000;
        display: flex;
        position: relative;
        /*z-index: 99;*/

        .m-e-main_toolbar_left {
          flex: 1;

          span {
            font-size: 15px;
            padding-left: 10px;
            line-height: 26px;
            line-height: 40px;
            user-select: none;
          }
        }

        .m-e-main_toolbar_right {
          margin-right: 15px;

          a {
            width: 16px;
            height: 16px;
            transition: ease all 0.3s;
            text-align: center;
            display: inline-block;
            padding: 5px;
            cursor: pointer;
            border-radius: 2px;
            margin: 7px 0 7px 5px;

            i {
              font-size: 15px;
            }

            &:hover {
              background-color: rgba(255, 255, 255, 0.1);
            }

          }
        }
      }
    }
  }

  .themeLight {
    background-color: #fff;
    box-shadow: 0px 2px 5px #ddd;
  }

  .themeBlack {
    background-color: #1e1e1e;
    box-shadow: 0px 2px 5px #111;
  }

  .themeLightToolbar {
    color: #000
  }

  .themeBlackToolbar {
    color: #fff
  }

  .themeLightSelect {
    background-color: #fff;
    color: #000
  }

  .themeBlackSelect {
    background-color: #000;
    color: #fff
  }

  .language-picker {
    margin-left: 10px;
    width: 128px;
  }

</style>
