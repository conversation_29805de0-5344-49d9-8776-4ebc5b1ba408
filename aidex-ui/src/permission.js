import router from './router'
import store from './store'
import NProgress from 'nprogress' // progress bar
import '@/components/NProgress/nprogress.less' // progress bar custom style
import { setDocumentTitle } from '@/utils/domUtil'
import { i18nRender } from '@/locales'
import { getToken, setToken } from '@/utils/auth'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const allowList = ['login', 'applyLicense', 'oAuth', '404', '500', '403', 'error'] // no redirect allowList
const loginRoutePath = '/oAuth'
const defaultRoutePath = '/index'

router.beforeEach((to, from, next) => {
  NProgress.start() // start progress bar
  to.meta && (typeof to.meta.title !== 'undefined' && setDocumentTitle(`${i18nRender(to.meta.title)}`))
  /* has token */
  if (getToken()) {
    if (to.path === loginRoutePath || to.path === '/') {
      next({ path: defaultRoutePath })
      NProgress.done()
    } else {
      // check login user.roles is null
      if (store.getters.roles.length === 0) {
        // request login userInfo
        store
          .dispatch('GetInfo')
          .then(res => {
            const roles = res.roles || []
            // generate dynamic router
            store.dispatch('GenerateRoutes', { roles }).then(() => {
              // 根据roles权限生成可访问的路由表
              // 动态添加可访问路由表
              router.addRoutes(store.getters.addRouters)
              // 请求带有 redirect 重定向时，登录自动重定向到该地址
              next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
            })
          })
          .catch(() => {
            // 失败时，获取用户信息失败时，调用登出，来清空历史保留信息
            store.dispatch('Logout').then(() => {
              next({ path: '/' })
            })
          })
      } else {
        next()
      }
    }
  } else {
    if(to.query.token){
      setToken(to.query.token);
      next(to.path);
    }else if(to.query.message){
      const obj = JSON.parse(to.query.message);
      console.log(obj);
      if(obj && obj.code === "404"){
        next({ path: '/403', query: { code: obj.userInfo.username } })
      }else{
        next({ path: '/500', query: { msg: obj.msg } })
      }
    }else{
      if (allowList.includes(to.name)) {
        // 在免登录名单，直接进入
        next()
      } else {
        next({ path: loginRoutePath, query: { redirect: to.fullPath } })
        NProgress.done() // if current page is login will not trigger afterEach hook, so manually handle it
      }
    }
  }
})

router.afterEach(() => {
  NProgress.done() // finish progress bar
})
