@import "assets/styles/default.less";
html,
body,
#app, #root {
  height: 100%;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout.layout-basic {
  height: 100vh;
  min-height: 100vh;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

// 数据列表 样式
.table-alert {
  margin-bottom: 16px;
}
// 数据列表 操作
.table-operator {
  margin-bottom: 18px;

  button {
    margin-right: 8px;
  }
}
// 数据列表 搜索条件
.table-page-search-wrapper {

  .ant-form-inline {
    .ant-form-item {
      display: flex;
      margin-bottom: 24px;
      margin-right: 0;

      .ant-form-item-control-wrapper {
        flex: 1 1;
        display: inline-block;
        vertical-align: middle;
      }

      > .ant-form-item-label {
        line-height: 32px;
        padding-right: 8px;
        width: auto;
      }
      .ant-form-item-control {
        height: 32px;
        line-height: 32px;
      }
    }
  }

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
  .ant-avatar-sm{
    width: 32px;
    height: 32px;
    margin:calc((50px - 32px) / 2) 0;
    border-radius:8px ;
  }
}
//表格上方操作条
.table-operations {
  margin-bottom: 16px;
}
.table-operations > button,
.table-operations .ant-input-affix-wrapper{
  margin-right: 8px;
}
// 详情页底部按钮
.bottom-control {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
  border-radius: 0 0 4px 4px;
}
.ant-drawer-body {
  padding: 24px 24px 50px 24px;
  font-size: 14px;
  line-height: 1.5;
}


.ant-pro-multi-tab {
    margin: 0 0 0 0;
    background: #fff;
}
/* 谷歌美化滚动条 */
*::-webkit-scrollbar {
	width: 6px;
	// height: 6px;
  height: 10px;
}
*::-webkit-scrollbar-track {
	background: #f0f2f5;
	border-radius: 2px;
}
*::-webkit-scrollbar-thumb {
	background: #c8d2e0;
	border-radius: 10px;
}
*::-webkit-scrollbar-thumb:hover {
		background: #c8d2e0;
}
*::-webkit-scrollbar-corner {
	background: #c8d2e0;
}
.drag-list-color {
    background-color: @primary-1!important;
}
.dasboard_body{
  .ant-statistic-title{
    color: #97a4ba;
    margin-bottom: 8px;
  }

}
/* 重新label样式解决label对不齐问题 */
.ant-form-item-label {
    line-height: 30px !important;
    padding-right: 8px;
  }
.table-card{
  min-height:calc(100vh - 196px);
}

.table-page-search-wrapper .ant-form-item{
  margin-bottom: 8px;
}
main{
  .ant-pro-grid-content{
	 height:calc(100vh - 90px);
	padding: 16px;
	overflow: auto;
  }
}
/* 正常引用card样式 */
  body .padding-card .ant-card-body {
      padding: 24px;
  }
