package com.aidex.system.service;

/**
 * 登录校验服务接口
 * 
 * <AUTHOR>
 */
public interface ISysLoginService
{
    /**
     * 登录验证
     * 
     * @param username 用户名
     * @param password 密码
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid);

    /**
     * 记录登录信息
     * 
     * @param username 用户名
     * @param status 状态
     * @param message 消息
     */
    public void recordLogininfor(String username, String status, String message);

    /**
     * 用户注册
     * 
     * @param username 用户名
     * @param password 密码
     */
    public void register(String username, String password);
}
