package com.aidex.system;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 系统服务启动程序
 * 
 * <AUTHOR>
 */
@EnableDiscoveryClient
@SpringBootApplication
public class SystemApplication
{
    private static final Logger logger = LoggerFactory.getLogger(SystemApplication.class);

    public static void main(String[] args)
    {
        SpringApplication.run(SystemApplication.class, args);
        logger.info("(♥◠‿◠)ﾉﾞ  系统服务启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
