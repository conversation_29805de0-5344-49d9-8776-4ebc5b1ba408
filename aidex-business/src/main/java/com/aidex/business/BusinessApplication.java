package com.aidex.business;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 业务服务启动程序
 * 
 * <AUTHOR>
 */
@EnableDiscoveryClient
@SpringBootApplication
public class BusinessApplication
{
    private static final Logger logger = LoggerFactory.getLogger(BusinessApplication.class);

    public static void main(String[] args)
    {
        SpringApplication.run(BusinessApplication.class, args);
        logger.info("(♥◠‿◠)ﾉﾞ  业务服务启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
