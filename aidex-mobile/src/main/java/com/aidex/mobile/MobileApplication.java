package com.aidex.mobile;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 移动端服务启动程序
 * 
 * <AUTHOR>
 */
@EnableDiscoveryClient
@SpringBootApplication
public class MobileApplication
{
    private static final Logger logger = LoggerFactory.getLogger(MobileApplication.class);

    public static void main(String[] args)
    {
        SpringApplication.run(MobileApplication.class, args);
        logger.info("(♥◠‿◠)ﾉﾞ  移动端服务启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
